.el-descriptions {
  box-sizing: border-box;
  font-size: 14px;
  color: #303133
}

.el-descriptions__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px
}

.el-descriptions__title {
  font-size: 16px;
  font-weight: 700
}

.el-descriptions__body {
  color: #606266;
  background-color: #fff
}

.el-descriptions__body .el-descriptions__table {
  border-collapse: collapse;
  width: 100%;
  table-layout: fixed
}

.el-descriptions__body .el-descriptions__table .el-descriptions-item__cell {
  box-sizing: border-box;
  text-align: left;
  font-weight: 400;
  line-height: 1.5
}

.el-descriptions__body .el-descriptions__table .el-descriptions-item__cell.is-left {
  text-align: left
}

.el-descriptions__body .el-descriptions__table .el-descriptions-item__cell.is-center {
  text-align: center
}

.el-descriptions__body .el-descriptions__table .el-descriptions-item__cell.is-right {
  text-align: right
}

.el-descriptions .is-bordered {
  table-layout: auto
}

.el-descriptions .is-bordered .el-descriptions-item__cell {
  border: 1px solid #ebeef5;
  padding: 12px 10px
}

.el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
  padding-bottom: 12px
}

.el-descriptions--medium.is-bordered .el-descriptions-item__cell {
  padding: 10px
}

.el-descriptions--medium:not(.is-bordered) .el-descriptions-item__cell {
  padding-bottom: 10px
}

.el-descriptions--small {
  font-size: 12px
}

.el-descriptions--small.is-bordered .el-descriptions-item__cell {
  padding: 8px 10px
}

.el-descriptions--small:not(.is-bordered) .el-descriptions-item__cell {
  padding-bottom: 8px
}

.el-descriptions--mini {
  font-size: 12px
}

.el-descriptions--mini.is-bordered .el-descriptions-item__cell {
  padding: 6px 10px
}

.el-descriptions--mini:not(.is-bordered) .el-descriptions-item__cell {
  padding-bottom: 6px
}

.el-descriptions-item {
  vertical-align: top
}

.el-descriptions-item__container {
  display: flex
}

.el-descriptions-item__container .el-descriptions-item__content,.el-descriptions-item__container .el-descriptions-item__label {
  display: inline-flex;
  align-items: baseline
}

.el-descriptions-item__container .el-descriptions-item__content {
  flex: 1
}

.el-descriptions-item__label.has-colon:after {
  content: ":";
  position: relative;
  top: -.5px
}

.el-descriptions-item__label.is-bordered-label {
  font-weight: 700;
  color: #909399;
  background: #fafafa
}

.el-descriptions-item__label:not(.is-bordered-label) {
  margin-right: 10px
}

.el-descriptions-item__content {
  word-break: break-word;
  overflow-wrap: break-word
}