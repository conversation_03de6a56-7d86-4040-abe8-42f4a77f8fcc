import request from '@/utils/request'

export function fetchList(data) {
  return request({
    url: '/life/report/list',
    method: 'post',
    data: data
  })
}

export function store(data) {
  return request({
    url: '/life/report/store',
    method: 'post',
    data: data
  })
}

export function remove(data) {
  return request({
    url: '/life/report/remove',
    method: 'post',
    data: data
  })
}
