<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <!--<span>套餐列表</span>-->
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <el-button icon="el-icon-arrow-left" style="float: right; padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
          <el-button style="float: right; padding: 3px 0" type="text" @click="createView">新增套餐</el-button>
        </div>
      </div>
      <el-table
        v-loading="tableLoading"
        highlight-current-row
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="65"
        />
        <el-table-column
          prop="name"
          label="套餐名称"
          width="180"
        />
        <el-table-column
          prop="price"
          label="购买价格"
        />
        <el-table-column
          prop="renew_price"
          label="续费价格"
        />
        <el-table-column label="状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status" type="success" size="small" effect="dark">启用</el-tag>
            <el-tag v-else type="danger" effect="dark" size="small">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="在售状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.sale_status" type="success" size="small" effect="dark">在售</el-tag>
            <el-tag v-else type="danger" effect="dark" size="small">停售</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-link :underline="false" type="primary" @click="editView(scope.row)">编辑</el-link>
            <el-popconfirm
              title="确定删除当前套餐吗？"
              @onConfirm="deleteHandle(scope.row)"
            >
              <el-link slot="reference" :underline="false" type="danger">删除</el-link>
            </el-popconfirm>

            <el-link :underline="false" type="primary" @click="supplement(scope.row.id)">配套产品</el-link>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!--表单-->
    <el-dialog
      title="套餐管理"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="套餐名称：" prop="name">
          <el-col :span="12">
            <el-input v-model="ruleForm.name" />
          </el-col>
        </el-form-item>
        <el-form-item label="购买价格：" prop="price">
          <el-col :span="10">
            <el-input-number v-model="ruleForm.price" :precision="2" :min="0" :max="999999999" label="请输入购买价格" />
          </el-col>
        </el-form-item>
        <el-form-item label="续费价格：" prop="renew_price">
          <el-col :span="10">
            <el-input-number v-model="ruleForm.renew_price" :precision="2" :min="0" :max="999999999" label="请输入续费价格" />
          </el-col>
        </el-form-item>
        <el-form-item label="排序:" prop="level">
          <el-col :span="10">
            <el-input-number v-model="ruleForm.level" :min="0" :max="999" label="数字越小越考前" />
          </el-col>
        </el-form-item>
        <el-form-item label="套餐状态：" prop="status">
          <el-switch
            v-model="ruleForm.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item label="售卖状态:" prop="sale_status">
          <el-select v-model="ruleForm.sale_status" placeholder="请选择">
            <el-option label="在售" :value="1" />
            <el-option label="停售" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="套餐描述：" prop="content">
          <el-col :span="18">
            <!--<Editor :content.sync="ruleForm.content" />-->
            <el-input v-model="ruleForm.content" type="textarea" maxlength="200" rows="3" placeholder="请输入套餐描述" />
          </el-col>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="saveHandle">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { list, store, deletePackage } from '@/api/packages'
import { parent_apps } from '@/api/app'
// import Editor from '../../components/editor'
export default {
  name: 'Packages',
  // components: { Editor },
  data() {
    return {
      tableLoading: false,
      loading: false,
      dialogVisible: false,
      ruleForm: {},
      tableData: [],
      rules: {
        name: [
          { required: true, message: '请输入套餐名称', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '价格不能为空', trigger: 'blur' }
        ],
        renew_price: [
          { required: true, message: '续费价格不能为空', trigger: 'blur' }
        ],
        level: [
          { required: true, message: '排序不能为空且只能是数字', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '套餐描述必须填写', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getPackageList()
    this.getApps()
  },
  methods: {
    getApps() {
      parent_apps({ id: this.$route.query.id }).then(response => {
        this.apps = response.data
      })
    },
    getPackageList() {
      this.tableLoading = true
      list(this.$route.query).then(response => {
        this.tableData = response.data
        this.tableLoading = false
      })
    },
    createView() {
      this.dialogVisible = true
      this.ruleForm = {
        status: 1,
        price: 0,
        renew_price: 0,
        level: 0
      }
    },
    saveHandle() {
      this.ruleForm.app_id = this.$route.query.id
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.loading = true
          store(this.ruleForm).then(response => {
            this.dialogVisible = false
            this.loading = false
            this.getPackageList()
          }).catch(() => {
            this.loading = false
          })
        }
      })
    },
    editView(row) {
      this.dialogVisible = true
      this.ruleForm = row
    },
    deleteHandle(row) {
      deletePackage(row).then(response => {
        this.$message.success('删除成功')
        this.getPackageList()
      })
    },
    supplement(id) {
      this.$router.push({
        path: '/supplement',
        query: {
          'id': id
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .list-wrap {
    height: 100%;

    .box-card {
      border: none;
      height: 100%;

      ::v-deep .el-card__body {
        height: calc(100% - 59px);
        overflow: auto;
      }
    }
  }
</style>
