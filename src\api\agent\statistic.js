import request from '@/utils/request'

export function drawGeographical(data) {
  return request({
    url: '/dashboard/drawGeographical',
    method: 'post',
    data: data
  })
}

export function drawTendency(data) {
  return request({
    url: '/dashboard/drawTendency',
    method: 'post',
    data: data
  })
}

export function drawRegisterList(data) {
  return request({
    url: '/statistic/drawRegisterList',
    method: 'post',
    data: data
  })
}

export function agentLoginAndRegisterCount(data) {
  return request({
    url: '/dashboard/agentLoginAndRegisterCount',
    method: 'post',
    data: data
  })
}

export function userLoginAndRegisterCount(data) {
  return request({
    url: '/dashboard/userLoginAndRegisterCount',
    method: 'post',
    data: data
  })
}

export function agentLoginAndRegisterLoginList(data) {
  return request({
    url: '/statistic/agentLoginAndRegisterLoginList',
    method: 'post',
    data: data
  })
}

export function userLoginAndRegister(data) {
  return request({
    url: '/statistic/userLoginAndRegister',
    method: 'post',
    data: data
  })
}

export function userVisitIP(data) {
  return request({
    url: '/statistic/userVisitIP',
    method: 'post',
    data: data
  })
}

export function userVisitPV(data) {
  return request({
    url: '/statistic/userVisitPV',
    method: 'post',
    data: data
  })
}

export function yunProduct(data) {
  return request({
    url: '/statistic/yunProduct',
    method: 'post',
    data: data
  })
}

export function moduleList(data) {
  return request({
    url: '/statistic/moduleList',
    method: 'post',
    data: data
  })
}

export function moduleUserList(data) {
  return request({
    url: '/statistic/moduleUserList',
    method: 'post',
    data: data
  })
}

