<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <!--<span>版本管理</span>-->
        <el-button style="float: right; padding: 3px 0" type="text" @click="addView">新增更新</el-button>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        height="calc(100% - 96px)"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="65"
        />
        <el-table-column
          prop="name"
          label="名称"
          width="400"
        />
        <el-table-column
          prop="version"
          label="外部版本"
        />
        <el-table-column
          prop="internal_version"
          label="内部版本"
        />
        <el-table-column label="平台">
          <template slot-scope="scope">
            <svg-icon v-if="scope.row.platform.indexOf(1) !== -1" icon-class="apple" style="font-size: 24px " />
            <svg-icon v-if="scope.row.platform.indexOf(2) !== -1" icon-class="android" style="font-size: 24px;"  />
          </template>
        </el-table-column>
        <el-table-column
          prop="content"
          label="说明"
        />
        <el-table-column label="模式">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.update_model === 1">全量更新</el-tag>
            <el-tag v-if="scope.row.update_model === 2">策略更新</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="强制">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.is_force" type="danger">是</el-tag>
            <el-tag v-else type="warning">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="publish_at"
          label="发布时间"
          width="155px"
        />
        <el-table-column label="状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status" type="success">启用</el-tag>
            <el-tag v-else type="danger">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-link type="primary" :underline="false" @click="editView(scope.row)">编辑</el-link>
            <el-popconfirm
              title="确定删除当前记录吗？"
              @onConfirm="del(scope.row)"
            >
              <el-link slot="reference" :underline="false" type="danger">删除</el-link>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </el-card>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import { list, del } from '@/api/version'
export default {
  components: { Pagination },
  data() {
    return {
      loading: false,
      listQuery: {
        page: 1,
        limit: 20
      },
      total: 1,
      tableData: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      list(this.listQuery).then(response => {
        this.tableData = response.data.data
        this.total = response.data.total
        this.loading = false
      })
    },
    addView() {
      this.$router.push({
        name: 'add-version'
      })
    },
    editView(row) {
      this.$router.push({
        name: 'edit-version',
        query: {
          id: row.id
        }
      })
    },
    del(row) {
      del(row).then(() => {
        this.$message.success('删除成功')
        this.getList()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap {
  height: 100%;

  .box-card {
    height: 100%;
    border: none;

    ::v-deep.el-card__header {
      padding-top: 0;
    }

    ::v-deep .el-card__body {
      height: calc(100% - 59px);
      overflow: auto;
      padding: 0;
      margin-top: 20px;
    }
  }
}
</style>
