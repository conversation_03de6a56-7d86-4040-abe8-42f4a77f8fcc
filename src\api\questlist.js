import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/questlist/list',
    method: 'post',
    data
  })
}

export function performtypeindex(data) {
  return request({
    url: '/questlist/performtypeindex',
    method: 'post',
    data
  })
}

export function storeRole(data) {
  return request({
    url: '/questlist/store',
    method: 'post',
    data
  })
}

export function deleteRole(data) {
  return request({
    url: '/questlist/delete',
    method: 'post',
    data
  })
}
