import request from '@/utils/request'

// 线索列表
export function getClueList(data) {
  return request({
    url: '/salesLead/list',
    method: 'post',
    data
  })
}
// 线索详情
export function getClueDetail(data) {
  return request({
    url: '/salesLead/detail',
    method: 'post',
    data
  })
}
// 线索设置成单状态
export function setClueSingle(data) {
  return request({
    url: '/salesLead/setstatus',
    method: 'post',
    data
  })
}
// 线索跟进
export function saveClueFollow(data) {
  return request({
    url: '/salesLead/store',
    method: 'post',
    data
  })
}
// 线索跟进记录
export function getClueFollowList(data) {
  return request({
    url: '/salesLead/slog',
    method: 'post',
    data
  })
}
