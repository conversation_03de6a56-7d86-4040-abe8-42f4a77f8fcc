<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>角色管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="createRole">新增角色</el-button>
      </div>
      <el-table
        :data="tableData"
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="180"
        />
        <el-table-column
          prop="name"
          label="角色名称"
          width="180"
        />
        <el-table-column
          prop="desc"
          label="角色描述"
        />
        <el-table-column label="角色状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status" type="success" size="small" effect="dark">启用</el-tag>
            <el-tag v-else type="danger" effect="dark" size="small">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-link :underline="false" type="primary" @click="editRole(scope.row)">编辑</el-link>
            <el-popconfirm
              title="确定删除当前记录吗？"
              @onConfirm="deleteHandle(scope.row)"
            >
              <el-link slot="reference" :underline="false" type="danger">删除</el-link>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <!--    表单-->
      <el-dialog
        title="角色管理"
        :visible.sync="dialogVisible"
        width="50%"
      >
        <el-form v-if="dialogVisible" ref="ruleForm" :model="formData" :rules="rules" label-width="100px" class="demo-ruleForm">
          <el-form-item label="角色名称：" prop="name">
            <el-input v-model="formData.name" />
          </el-form-item>
          <el-form-item label="角色描述" prop="desc">
            <el-input v-model="formData.desc" type="textarea" />
          </el-form-item>
          <el-form-item label="角色状态：" prop="status">
            <el-switch
              v-model="formData.status"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
            />
          </el-form-item>
          <el-form-item label="分配权限">
            <el-tree
              ref="tree"
              :default-checked-keys="formData.menu_id"
              :check-strictly="checkStrictly"
              :data="routesData"
              :props="defaultProps"
              show-checkbox
              node-key="id"
              class="permission-tree"
            />
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false;">取 消</el-button>
          <el-button type="primary" :loading="loading" @click="saveHandle">确 定</el-button>
        </span>
      </el-dialog>
    </el-card>

  </div>
</template>

<script>
import { getRoles, storeRole, updateRole, deleteRole } from '../../api/role'
import { getOrgMenus } from '../../api/menu'
export default {
  data() {
    return {
      loading: false,
      formData: {},
      dialogVisible: false,
      tableData: [],
      routes: [],
      checkStrictly: false,
      defaultProps: {
        children: 'children',
        label: 'title'
      },
      rules: {
        name: [
          { required: true, message: '缺少角色名称', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    routesData() {
      return this.routes
    }
  },
  created() {
    this.getRolesTable()
    this.getRolesTree()
  },
  methods: {
    getRolesTree() {
      getOrgMenus().then(response => {
        this.routes = response.data
      })
    },
    getRolesTable() {
      getRoles().then(response => {
        this.tableData = response.data
      })
    },
    createRole() {
      this.formData = {
        name: '',
        desc: '',
        status: 1
      }
      this.dialogVisible = true
    },
    editRole(row) {
      this.formData = row
      this.dialogVisible = true
    },
    saveHandle() {
      this.$refs.ruleForm.validate((valid) => {
        this.formData.menu_id = this.$refs.tree.getCheckedKeys(true)
        if (valid) {
          this.loading = true
          if (this.formData.id) {
            updateRole(this.formData).then(response => {
              if (response.code === 200) {
                this.dialogVisible = false
                this.loading = false
                this.getRolesTable()
              }
            })
          } else {
            storeRole(this.formData).then(response => {
              if (response.code === 200) {
                this.dialogVisible = false
                this.loading = false
                this.getRolesTable()
              }
            })
          }
        }
      })
    },
    deleteHandle(row) {
      deleteRole(row).then(response => {
        if (response.code === 200) {
          this.getRolesTable()
        }
      })
    }
  }
}
</script>

<style scoped>
  .app-container {
  .roles-table {
    margin-top: 30px;
  }
  .permission-tree {
    margin-bottom: 30px;
  }
  }
</style>
