<template>
  <div v-loading="pageLoading" class="list-wrap">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
        <span v-if="info.type === 1" style="margin-left: 30px;">{{ info.user.name }}的提现审核</span>
        <span v-if="info.type === 2" style="margin-left: 30px;">{{ info.company.name }}的提现审核</span>
      </div>
      <el-card style="margin-bottom: 20px" shadow="never">
        <div slot="header" class="clearfix">
          <span>提现信息</span>
        </div>
        <el-form label-width="120px">
          <el-form-item label="账号类型:">
            <span v-if="info.type === 1">个人</span>
            <span v-if="info.type === 2">企业</span>
          </el-form-item>
          <el-form-item v-if="info.type == 2" label="账号名称:">
            <span>{{ info.account.name }}</span>
          </el-form-item>
          <el-form-item v-if="info.type == 2" label="开户银行:">
            <span>{{ info.account.bank_name }}</span>
          </el-form-item>
          <el-form-item v-if="info.type == 2" label="开户账号:">
            <span>{{ info.account.bank_number }}</span>
          </el-form-item>
          <template v-if="info.type == 1">
            <el-form-item label="账号名称:">
              <span>{{ info.account_name }}</span>
            </el-form-item>
            <el-form-item label="开户银行:">
              <span>{{ info.bank_name }}</span>
            </el-form-item>
            <el-form-item label="开户账号:">
              <span>{{ info.account_no }}</span>
            </el-form-item>
          </template>
          <el-form-item label="提现金额:">
            <span>&#165; {{ info.amount }} 元</span>
          </el-form-item>
          <el-form-item label="申请日期:">
            <span>{{ info.create_at }}</span>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card v-if="info.status !== 0" style="margin-bottom: 20px" shadow="never">
        <div slot="header" class="clearfix">
          <span>审核信息</span>
        </div>
        <el-form ref="ruleForm" :model="form" :rules="rules" class="demo-ruleForm" label-width="120px">
          <el-form-item label="审核状态:" prop="status">
            <el-select v-model="info.status" placeholder="请选择" disabled="disabled">
              <el-option label="审核通过并打款" :value="1" />
              <el-option label="驳回请求" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="info.status === 1" label="备注说明:" prop="remark">
            <el-col :span="10">
              <el-input v-model="info.remark" type="textarea" placeholder="请输入内容" disabled="disabled" />
            </el-col>
          </el-form-item>
          <el-form-item v-if="info.status === 2" label="驳回理由:" prop="remark">
            <el-col :span="10">
              <el-input v-model="info.remark" type="textarea" placeholder="请输入内容" disabled="disabled" />
            </el-col>
          </el-form-item>
          <el-form-item>
            <el-button v-if="info.status === 0 " type="primary" size="small" :loading="btnLoading" @click="saveHandle">保存并返回</el-button>
            <el-button v-else type="primary" size="small" @click="$router.go(-1)">返回</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card v-if="info.status === 0" style="margin-bottom: 20px" shadow="never">
        <div slot="header" class="clearfix">
          <span>提现审核</span>
        </div>
        <el-form ref="ruleForm" :model="form" :rules="rules" class="demo-ruleForm" label-width="120px">
          <el-form-item label="审核状态:" prop="status">
            <el-select v-model="form.status" placeholder="请选择">
              <el-option label="审核通过并打款" :value="1" />
              <el-option label="驳回请求" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="form.status === 1" label="备注说明:">
            <el-col :span="10">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-col>
          </el-form-item>
          <el-form-item v-if="form.status === 2" label="驳回理由:" prop="remark">
            <el-col :span="10">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-col>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" :loading="btnLoading" @click="saveHandle">保存并返回</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </el-card>
  </div>
</template>

<script>
import { detail, store } from '../../api/withdrawal'
export default {
  name: 'Detail',
  data() {
    return {
      btnLoading: false,
      pageLoading: false,
      info: {
        invoice_info: {},
        address: {},
        company: {}
      },
      form: {
        status: 1
      },
      rules: {
        status: [
          { required: true, message: '请选择审核状态', trigger: 'change' }
        ],
        remark: [
          { required: true, message: '请输入驳回理由', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getDetail()
  },
  methods: {
    getDetail() {
      this.pageLoading = true
      detail({ id: this.$route.query.id }).then(response => {
        this.info = response.data
        this.pageLoading = false
      })
    },
    saveHandle() {
      this.form.id = this.info.id
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.btnLoading = true
          store(this.form).then(response => {
            this.$message.success('审核成功')
            this.$router.go(-1)
            this.btnLoading = false
          }).catch(() => {
            this.$message.error('审核失败')
            this.btnLoading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap {
  height: 100%;

  .box-card {
    border: none;
    height: 100%;

    & > ::v-deep .el-card__header {
      padding-top: 0;
    }

    ::v-deep .el-card__body {
      height: calc(100% - 59px);
      overflow: auto;
    }
  }
}
</style>
