<template>
  <div class="list-wrap">
    <el-card v-loading="datalogLoading" shadow="never" style="border: none">
      <div slot="header" class="clearfix" style="text-align: right">
        <!--<span>域名列表</span>-->
        <el-button type="text" @click="dialogDomainVisible=true">域名白名单</el-button>
        <el-button type="text" :loading="downloadLoading" @click="exportExcel">导出</el-button>
        <el-button type="text" @click="updateVspRefresh">刷新域名状态</el-button>
        <!--<el-button type="text" @click="dialogFormShow">申领域名</el-button>-->
        <!--<el-button style="float: right; padding: 0; margin-right:50px;" type="text" @click="idcyunList()">移动云主机</el-button>-->
        <!--<el-button style="float: right; padding: 0; margin-right:50px;" type="text" @click="idchostList()">空间</el-button>-->
        <!--<el-button style="float: right; padding: 0; margin-right:50px;" type="text" @click="idcdomainList()">域名</el-button>-->
      </div>
      <div class="filter">
        <el-form ref="form" :inline="true" :model="searchForm" label-width="90px" size="small">
          <el-row>
            <el-col :span="24">
              <el-form-item label="用户名称">
                <el-input v-model="searchForm.name" placeholder="用户名称" clearable />
              </el-form-item>
              <el-form-item label="域名名称">
                <el-input v-model="searchForm.domain" placeholder="域名名称" clearable />
              </el-form-item>
              <el-form-item label="状态">
                <el-select v-model="searchForm.status" placeholder="请选择" clearable>
                  <el-option label="正常" value="DOMAIN_NORMAL" />
                  <el-option label="欠费" value="DOMAIN_RENEW" />
                  <el-option label="删除" value="DOMAIN_DELETE" />
                  <el-option label="转入中" value="DOMAIN_TRANSFERRING" />
                  <el-option label="转出中" value="DOMAIN_TRANSFERING_OUT" />
                  <el-option label="已转出" value="DOMAIN_TRANSFER_OUT" />
                </el-select>
              </el-form-item>
              <el-form-item label="到期时间">
                <el-date-picker
                  v-model="searchForm.end_at"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" size="small" icon="el-icon-search" @click="getList">搜索</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="table mt20">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          style="width: 100%"
          height="calc(100% - 96px)"
        >
          <el-table-column
            label="#"
            type="index"
            width="50"
          />
          <el-table-column label="用户名称" prop="company" />
          <el-table-column label="代理商名称" prop="agent" />
          <el-table-column
            prop="name"
            label="域名"
          />
          <el-table-column
            prop="status"
            label="费用状态"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.status==='DOMAIN_NORMAL'" style="color: #1890ff">正常</span>
              <span v-else-if="scope.row.status==='DOMAIN_RENEW'" style="color: #ff4d51">欠费</span>
              <span v-else-if="scope.row.status==='DOMAIN_DELETE'" style="color: #ff4d51">删除</span>
              <span v-else-if="scope.row.status==='DOMAIN_TRANSFERRING'" style="color: #5a5e66">转入中</span>
              <span v-else-if="scope.row.status==='DOMAIN_TRANSFERING_OUT'" style="color: #5a5e66">转出中</span>
              <span v-else-if="scope.row.status==='DOMAIN_TRANSFER_OUT'" style="color: #5a5e66">已转出</span>
            </template>
          </el-table-column>
          <el-table-column label="VSP实名状态">
            <template slot-scope="scope">
              <span v-if="scope.row.vsp_status==='PASS'" style="color: #0fdb6a">已通过</span>
              <span v-else-if="scope.row.vsp_status==='AUDITING'" style="color: #1890ff">审核中</span>
              <span v-else-if="scope.row.vsp_status==='WAITING'" style="color: #5c5c5b">待审核</span>
              <span v-else-if="scope.row.vsp_status==='UNPASS'" style="color: #ff4d51">未通过</span>
              <el-button v-else type="text" size="small" style="color: #0bd1b3" @click="updateVspStatus(scope.row)">刷新</el-button>
            </template>
          </el-table-column>
          <el-table-column
            prop="created_at"
            label="开通日期"
          />
          <el-table-column
            prop="expired_at"
            label="到期时间"
          />
          <el-table-column
            label="操作"
            width="100"
          >
            <template slot-scope="scope">
              <!--<span>
                <el-button type="text" @click="showPwdDialog(scope.row.id)">密码</el-button>
                <el-divider direction="vertical" />
              </span>-->
              <el-button type="text" @click="openManager(scope.row)">管理</el-button>
              <!--<el-button v-if="(scope.row.status==='DOMAIN_NORMAL' || scope.row.status==='DOMAIN_RENEW')" type="text" @click="renew(scope.row)">续费</el-button>-->
              <!-- <el-button type="text" @click="dialogtransferFormShow(scope.row)">域名转移</el-button>
              <el-button style="color: red" type="text" @click="deleteDomain(scope.row)">删除</el-button> -->
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="searchForm.total>0"
          :total="searchForm.total"
          :page.sync="searchForm.page"
          :limit.sync="searchForm.perPage"
          style="text-align:center;"
          @pagination="getList"
        />
      </div>
    </el-card>

    <el-dialog
      title="领取域名"
      :visible.sync="dialogFormVisible"
      width="500px"
      top="10vh"
    >
      <el-form ref="form" :model="form" label-width="120px" :rules="rules" style="width: 90%">
        <el-form-item label="选择用户" prop="customer_id">
          <el-select v-model="form.customer_id" style="width: 100%" placeholder="请选择" filterable clearable>
            <el-option v-for="customer in customers" :key="customer.id" :label="customer.name" :value="customer.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="域名" prop="domain">
          <el-input v-model="form.domain" placeholder="请输入域名" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="auth">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="域名转移"
      :visible.sync="dialogtransferFormVisible"
      width="500px"
      top="10vh"
    >
      <el-form ref="form" :model="transferform" label-width="120px" :rules="trrules" style="width: 90%">
        <el-form-item label="域名" disabled prop="domain" filterable clearable>
          <el-input v-model="transferform.domain" placeholder="请输入域名" disabled />
        </el-form-item>
        <el-form-item label="选择用户" prop="customer_id">
          <el-select v-model="transferform.customer_id" style="width: 100%" placeholder="请选择" filterable clearable>
            <el-option v-for="customer in customers" :key="customer.id" :label="customer.name" :value="customer.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogtransferFormVisible = false">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="transferauth">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="查看域名密码"
      :visible.sync="dialogPwdVisible"
      width="500px"
      top="10vh"
    >
      <el-form v-loading="dialogPwdLoading">
        <el-form-item>
          <span>密码 :</span>
          <span>{{ domainPwd }}</span>
          <el-button type="text" style="color: #0e90d2;margin-left: 20px" @click="setPwd">{{ domainPwd?'重置':'开启' }}</el-button>
        </el-form-item>
      </el-form>

    </el-dialog>
    <el-dialog
      title="修改域名白名单"
      :visible.sync="dialogDomainVisible"
      :loading="dialogDomainLoading"
      width="950px"
      top="10vh"
    >
      <el-form ref="domainForm" :model="domainForm" label-width="120px" :rules="domainRule">
        <el-form-item label="域名白名单" prop="domain">
          <el-input v-model="domainForm.domain" placeholder="请输入域名白名单" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogDomainVisible = false">取 消</el-button>
        <el-button :loading="dialogDomainLoading" type="primary" @click="domainApply">提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { mapGetters } from 'vuex'
import { domainList, domainFinanceId, domainClaim, domainGetPwd, domainSetPwd, domainDestroy, domainCalibration, domainRefresh, domainTransfer, domainWhite, domainWdaile } from '@/api/agent/domain'
import { index } from '@/api/agent/customers'
import { download } from '../utils/download'
import { domainListExport } from '@/api/agent/downloads'
export default {
  name: 'Domain',
  components: { Pagination },
  data() {
    return {
      ruleForm: {},
      searchForm: {
        page: 1,
        perPage: 10,
        total: 0,
        end_at: null
      },
      tableData: [],
      tableLoading: false,
      domainForm: { domain: '' },
      form: { customer_id: '', domain: '' },
      transferform: { customer_id: '', domain: '', domainid: 0, yun_id: '' },
      customers: [],
      dialogDomainVisible: false,
      dialogDomainLoading: false,
      dialogFormVisible: false,
      dialogtransferFormVisible: false,
      dialogLoading: false,
      datalogLoading: false,
      rules: {
        customer_id: [{ required: true, message: '请选择用户', trigger: 'blur' }],
        domain: [{ required: true, message: '域名必须填写', trigger: 'blur' }]
      },
      domainRule: {
        // domain: [{ required: true, message: '域名必须填写', trigger: 'blur' }]
      },
      trrules: {
        customer_id: [{ required: true, message: '请选择用户', trigger: 'blur' }],
        domain: [{ required: true, message: '域名必须填写', trigger: 'blur' }]
      },
      dialogPwdVisible: false,
      dialogPwdLoading: false,
      domainPwd: '',
      domain_id: '',
      downloadLoading: false
    }
  },
  computed: {
    // 别名处理防止冲突
    ...mapGetters({
      userType: 'agentUserType'
    })
  },
  created() {
    this.getList()
    this.getCustomers()
    this.getdomainWdaile()
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.list-wrap > .el-card > .el-card__body')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  methods: {
    getList() {
      this.tableLoading = true
      domainList(this.searchForm).then(response => {
        if (response.code === 200 && response.data) {
          this.tableData = response.data.list
          this.searchForm.total = response.data.meta.total
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    openManager(row) {
      window.open('http://idc.china9.cn/panel/login?model=dc&name=' + row.name + '&tokens=' + row.hash + '&time=' + row.time)
    },
    // 获取要开通产品的客户
    getCustomers() {
      index({ all: true }).then(response => {
        if (response.code === 200 && response.data) {
          this.customers = response.data
        }
      })
    },
    getdomainWdaile() {
      domainWdaile().then(response => {
        this.domainForm.domain = response.data.domain
      }).catch(error => {
        console.log(error)
      })
    },
    domainApply() {
      this.$refs.domainForm.validate((valid) => {
        if (!valid) {
          return false
        }
        this.dialogDomainLoading = true

        domainWhite(this.domainForm).then(response => {
          this.$message.success('提交成功')
          this.dialogDomainVisible = false
          this.dialogDomainLoading = false
          this.getList()
        }).catch(error => {
          console.log(error)
          this.dialogDomainVisible = false
          this.dialogDomainLoading = false
        })
      })
    },
    auth() {
      this.$confirm('请核对用户与域名，确保用户与域名匹配, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$refs['form'].validate((res) => {
          if (res) {
            this.dialogLoading = true
            domainClaim(this.form).then(res => {
              this.dialogLoading = false
              this.$message.success('域名认领成功')
              this.form['domain'] = ''
            }).catch(() => {
              this.dialogLoading = false
            })
          }
        })
      })
    },
    transferauth() {
      this.$confirm('请核对用户与域名，确保用户与域名匹配, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.dialogLoading = true
        domainTransfer(this.transferform).then(res => {
          this.dialogLoading = false
          this.$message.success('域名转移成功')
          this.transferform['domain'] = ''
        }).catch(() => {
          this.dialogLoading = false
        })
      }).then((data) => {
        this.dialogtransferFormVisible = false
      })
    },
    getPwd() {
      this.dialogPwdLoading = true
      domainGetPwd({ id: this.domain_id }).then(res => {
        this.domainPwd = res.data.password
        this.dialogPwdLoading = false
      }).catch(() => {
        this.dialogPwdLoading = false
      })
    },
    setPwd() {
      domainSetPwd({ id: this.domain_id }).then(res => {
        this.$message.success('操作成功')
        this.getPwd()
      })
    },
    updateVspStatus(item) {
      domainCalibration({ domain: item.name }).then(res => {
        this.getList()
      }).catch(() => {
      })
    },
    updateVspRefresh(item) {
      this.tableLoading = true
      domainRefresh({ domain: item.name }).then(res => {
        this.tableLoading = false
        this.getList()
      }).catch(() => {
      })
    },
    async exportExcel() {
      try {
        this.downloadLoading = true
        const params = {
          date: this.searchForm.date,
          name: this.searchForm.name,
          status: this.searchForm.status,
          end_at: this.searchForm.end_at
        }
        const res = await domainListExport(params)
        if (res.code === 200) {
          this.$message.success('导出成功，请到下载中心下载')
        } else {
          this.$message.error(res.msg || '导出失败')
        }
      } catch (error) {
        this.$message.error(error.message || '导出失败')
      } finally {
        this.downloadLoading = false
      }
    },

  }

}
</script>

<style scoped>
  .demo-table-expand {
    font-size: 0;
  }
  .demo-table-expand label {
    width: 90px;
    color: #99a9bf;
  }
  .demo-table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    width: 50%;
  }
</style>

<style scoped lang="scss">
.list-wrap {
  height: 100%;
  & > ::v-deep .el-card{
    height: 100%;
    .el-card__header{
      padding-top: 0;
    }
    .el-card__body{
      padding: 0;
      height: calc(100% - 75px);
      overflow: auto;
      margin-top: 20px;
    }
  }
  .filter {
    border-bottom: 1px solid #e5e5e5;
  }
}
</style>
