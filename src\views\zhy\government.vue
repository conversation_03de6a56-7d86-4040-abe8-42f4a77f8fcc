<!-- 媒体投放 -->
<template>
  <div class="list-wrap">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="列表" name="list">
        <government-list />
      </el-tab-pane>
      <el-tab-pane label="分类" name="cate">
        <government-cate />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import GovernmentList from './components/government/list.vue'
import GovernmentCate from './components/government/cate.vue'

export default {
  components: {
    GovernmentList, GovernmentCate
  },
  data() {
    return {
      activeName: 'list'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event)
    }
  }
}
</script>

<style scoped lang="scss">
  .list-wrap {
    height: 100%;

    ::v-deep .el-tabs {
      height: 100%;
      overflow: auto;

      .el-tabs__content {
        height: calc(100% - 41px - 15px);
      }
    }

    ::v-deep .card-wrap {
      height: 100%;
      border: none;

      .el-card__body {
        height: 100%;
      }

      .box-card {
        height: 100%;
        border: none;

        .el-card__body {
          height: calc(100% - 59px);
          overflow: auto;
        }
      }
    }
  }
</style>
