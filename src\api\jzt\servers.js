import request from '@/utils/requestJzt'

export function servers(data) {
  return request({
    url: '/zhyman/serverlst',
    method: 'post',
    data
  })
}

export function refresh() {
  return request({
    url: '/servers/refresh',
    method: 'post'
  })
}

export function update(data) {
  return request({
    url: '/servers/update',
    method: 'post',
    data
  })
}

export function destroy(data) {
  return request({
    url: '/servers/delete',
    method: 'post',
    data
  })
}
