import request from '@/utils/requestOpen'
import serviceTask from '@/utils/requestTask'
import serviceTaskManage from '@/utils/requestTaskManage'

export function getList(data) {
  return request({
    url: '/tripartite/getList',
    method: 'POST',
    data
  })
}

export function getDetail(data) {
  return request({
    url: '/tripartite/getInfo',
    method: 'POST',
    data
  })
}

export function approval(data) {
  return request({
    url: '/tripartite/modStatus',
    method: 'POST',
    data
  })
}

// 批量通过
export function approvalTrue(data) {
  return request({
    url: '/tripartite/modStatusListTrue',
    method: 'POST',
    data
  })
}

// 批量驳回
export function approvalFalse(data) {
  return request({
    url: '/tripartite/modStatusListFalse',
    method: 'POST',
    data
  })
}

// 个人主页
export function getTaskUserInfo(data) {
  return serviceTask({
    url: '/taskorder/userInfo',
    method: 'POST',
    data
  })
}

// 灵活用工列表
export function getTaskList(data) {
  return request({
    url: '/tripartite/getPositionUserList',
    method: 'POST',
    data
  })
}

// 灵活用工修改星级
export function updateGrade(data) {
  return request({
    url: '/tripartite/modPositionUserInfo',
    method: 'POST',
    data
  })
}

// 佣金修改
export function updateMoneyList(data) {
  return request({
    url: '/tripartite/getEditCommission',
    method: 'POST',
    data
  })
}

// 终止项目
export function suspendProject(data) {
  return request({
    url: '/tripartite/getTermination',
    method: 'POST',
    data
  })
}

// 修改金额审批
export function approvalUpdateMoney(data) {
  return request({
    url: '/tripartite/modEditCommissionStatus',
    method: 'POST',
    data
  })
}

// 终止项目
export function approvalSuspend(data) {
  return request({
    url: '/tripartite/modTerminationStatus',
    method: 'POST',
    data
  })
}

// 项目发布TOP榜
export function getPublishRank(data) {
  return serviceTaskManage({
    url: '/China_To_Article/console_top',
    method: 'post',
    data: data
  })
}

// 抢单完结TOP榜
export function getOverRank(data) {
  return serviceTaskManage({
    url: '/China_To_Article/console_usertop',
    method: 'post',
    data: data
  })
}

// 人员认证统计
export function getCertificate(data) {
  return serviceTaskManage({
    url: '/China_To_Article/console_users ',
    method: 'post',
    data: data
  })
}

/* 控制台 */
export function getConsole(data) {
  return serviceTaskManage({
    url: '/China_To_Article/console_data',
    method: 'post',
    data: data
  })
}

// 抢单趋势
export function getTrend(data) {
  return serviceTaskManage({
    url: '/China_To_Article/console_trend',
    method: 'post',
    data: data
  })
}

/**
 * 城市认证统计列表
 */
export function getCityCertificate(data) {
  return serviceTaskManage({
    url: '/China_To_Article/console_city',
    method: 'post',
    data: data
  })
}

/**
 * 获取地图数据
 */
export function getMapData(data) {
  return serviceTaskManage({
    url: '/China_To_Article/console_map',
    method: 'post',
    data: data
  })
}
