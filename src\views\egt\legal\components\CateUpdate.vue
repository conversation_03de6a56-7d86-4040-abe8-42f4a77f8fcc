<script>
import { addContractTemplateApi, getContractTemplateAddApi, getContractTemplateUpdateApi, updateContractTemplateApi } from '@/api/egt/contract'
import SelfFormTemp from '@/views/task/components/form/SelfFormTemp.vue'
import update from '@/views/task/mixins/update'

export default {
  name: 'CateUpdate',
  components: { SelfFormTemp },
  mixins: [update],
  data() {
    return {
      formData: {
        id: '',
        model_name: '',
        contract_category: '',
        model_des: ''
      },
      filters: [
        {
          label: '名称',
          type: 'input',
          prop: 'model_name'
        },
        {
          label: '分类',
          type: 'select',
          prop: 'contract_category',
          settings: {
            options: [
              {
                value: 0,
                label: '全部'
              }
            ]
          }
        },
        {
          label: '简介',
          type: 'textarea',
          prop: 'model_des'
        }
      ],
      rules: {
        model_name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        contract_category: [
          { required: true, message: '请选择分类', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    _visible(val) {
      if (val) {
        this.getParams()
      }
    }
  },
  methods: {
    async getParams() {
      const api = this.data.id ? getContractTemplateUpdateApi : getContractTemplateAddApi
      if (!api) return
      try {
        const res = await api({ id: this.data.id })
        if (res.code === 200 && res.data) {
          if (res.data.category) {
            const cate = this.filters.find(item => item.value === 'contract_category')
            if (!cate.settings) {
              cate.settings = {}
            }
            cate.settings.options = []
            cate.settings.options = res.data.category.map(item => ({
              value: item.id,
              label: item.cate_name
            }))
            cate.settings.options.unshift({
              value: 0,
              label: '全部'
            })
          }
          if (res.data.data) {
            this.formData = res.data.data
            this.formData.contract_category = res.data.data.cid
          }
        }
      } catch (error) {
        console.log(error)
      }
    },
    async submitCallback() {
      const api = this.data.id ? updateContractTemplateApi : addContractTemplateApi
      if (!api) return
      this.formData.model_id = this.data.model_id
      if (this.data.id) {
        this.formData.id = this.data.id
      }
      try {
        this.submitLoading = true
        const res = await api(this.formData)
        if (res.code === 200) {
          this.$emit('submit-success')
          return Promise.resolve({ success: true })
        } else {
          return Promise.reject({ success: false, msg: this.data.id ? '编辑失败' : '新增失败' })
        }
      } catch (error) {
        return Promise.reject({ success: false, msg: this.data.id ? '编辑失败' : '新增失败' })
      } finally {
        this.submitLoading = false
      }
    }
  }
}
</script>

<template>
  <el-dialog :close-on-click-modal="false" :visible.sync="_visible" :title="title + '合同类型'" width="700px">
    <SelfFormTemp ref="formWrap" :form-data.sync="formData" :filters="filters" :inline="false" label-width="80px" :rules="rules" />
    <div slot="footer" class="dialog-footer">
      <el-button @click="_visible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
