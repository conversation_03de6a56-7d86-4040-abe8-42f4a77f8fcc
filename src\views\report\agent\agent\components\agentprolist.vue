<template>
  <SelfCard title="代理商产品开通数据" class="box-card">
    <template v-slot:right>
      <el-select
        v-model="product_id"
        size="medium"
        filterable
        multiple
        collapse-tags
        clearable
        run
        placeholder="请选择"
        style="width:280px;height:36px;"
        @change="selectDay()"
      >
        <el-option v-for="item in productsList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-date-picker
        v-model="value2"
        value-format="yyyy-MM"
        type="monthrange"
        align="right"
        unlink-panels
        range-separator="至"
        start-placeholder="开始月份"
        end-placeholder="结束月份"
        :picker-options="pickerOptions"
        style="width: 200px"
        @change="changeDay()"
      />
    </template>
    <div v-loading="mapLoading" class="view-body">
      <table border="1">
        <tr>
          <th>排名</th>
          <th style="text-align: left">代理商名称</th>
          <th style="text-align: left">开通金额</th>
          <th>订单量</th>
        </tr>
        <tr v-for="(item, index) in lists" :key="index">
          <td>{{ index + 1 }}</td>
          <td style="text-align: left">{{ item.name }}</td>
          <td style="text-align: left">{{ item.price }}</td>
          <td>{{ item.num }}</td>
        </tr>
      </table>
      <pagination
        v-show="searchForm.total > 0"
        :total="searchForm.total"
        :page.sync="searchForm.page"
        :limit.sync="searchForm.perPage"
        :auto-scroll="false"
        style="text-align:center;"
        @pagination="getSystem"
      />
      <div v-if="lists.length === 0" class="null">暂无数据</div>
    </div>
  </SelfCard>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { productspx } from '@/api/agent/product'
import { mapGetters } from 'vuex'
import { authproorder } from '@/api/agent/dashboard'
import SelfCard from '@/views/dashboard/admin/components/SelfCard.vue'
require('echarts/theme/macarons')
require('echarts/extension/bmap/bmap')
export default {
  name: 'Agentprolist',
  components: { Pagination, SelfCard },
  data() {
    return {
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      value2: '',
      chartMap: null,
      mapLoading: false,
      imgurl: 'https://scrm-api.china9.cn/web/images/ktje.png',
      allData: {},
      lists: [],
      product_id: [],
      productsList: [],
      graph: {},
      legenddata: [],
      max: 0,
      indexOfDate: 0,
      indexOfCate: 0,
      searchForm: {
        status: '',
        page: 1,
        perPage: 10,
        total: 0,
        is_buy: false
      },
      options_user: [
        // { name: '用户注册', key: 'user_register_number' },
        // { name: '用户登录', key: 'user_login_number' },s
        { name: 'IP访问量', key: 'user_visit_ip_number' },
        { name: '浏览次数', key: 'user_visit_all_number' },
        { name: '独立访问', key: 'user_visit_number' }
      ],
      options_agent: ['代理商注册', '代理商登录', 'IP访问量', '浏览次数', '独立访问'],
      days: 0,
      keys: 'user_visit_all_number',
      client: 'cloud'
    }
  },
  computed: {
    ...mapGetters([
      'type'
    ])
  },
  created() {

  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      this.mapLoading = true
      await this.getSystem()
      this.getProducts()
    },
    // 获取产品列表
    getProducts() {
      productspx({ all: true }).then(response => {
        if (response.code === 200 && response.data) {
          this.productsList = response.data
        }
      })
    },
    changeDay() {
      this.getSystem()
    },
    selectDay() {
      this.getSystem()
    },
    selectType() {
      this.getSystem()
    },
    selectCate(index) {
      this.indexOfCate = index
      this.client = ['cloud', 'agent'][index]
      this.getSystem()
    },
    async getSystem() {
      this.mapLoading = true
      await authproorder({ product_id: this.product_id, date: this.value2, page: this.searchForm.page }).then(response => {
        this.mapLoading = false
        this.value2 = response.data.date
        this.allData = response.data
        this.lists = this.allData.prolist.data
        this.searchForm.total = this.allData.prolist.total
      }).catch(error => {
        this.mapLoading = false
        console.log(error)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .box-card {
    .view-body {
      height: 444px;
      margin-top: 42px;

      table {
        display: inline-block;
        width: 100%;
        //height: 360px;
        border: 0 solid transparent;

        tr {
          height: 40px;

          th:nth-child(1) {
            width: 50px;
          }

          th {
            width: 191px;
          }

          th:nth-child(2) {
            width: 350px;
          }

          td {
            font-size: 14px;
            text-align: center;
          }
        }

        tr:nth-child(1) {
          background-color: #eff4f7;

          th {
            color: #3c3c3c;
            font-weight: 100;
            font-size: 14px;
          }
        }

        tr:nth-child(2n) {
          background-color: #f9f9f9;
        }
      }

      .null {
        position: absolute;
        top: 50px;
        width: 100%;
        line-height: 200px;
        text-align: center;
        color: #8c939d;
        font-size: 30px;
        font-weight: bold;
      }

      .pagination-container {
        width: 100%;
        padding: 22px 0;
      }

    }
  }
</style>
