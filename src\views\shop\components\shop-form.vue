<template>
  <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="120px" class="demo-ruleForm">
    <el-form-item label="商品分类：" prop="category_id">
      <el-select v-model="ruleForm.category_id" placeholder="请选择">
        <el-option
          v-for="item in categories"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="商品属性：" prop="is_hot">
      <el-checkbox v-model="ruleForm.is_hot">推荐</el-checkbox>
    </el-form-item>
    <el-form-item label="首页推荐：" prop="is_index">
      <el-checkbox v-model="ruleForm.is_index">推荐</el-checkbox>
    </el-form-item>
    <el-form-item label="显示状态：" prop="ck_status">
      <el-select v-model="ruleForm.ck_status" placeholder="请选择">
        <el-option label="本公司" :value="1" />
        <el-option label="全局" :value="2" />
      </el-select>
    </el-form-item>
    <el-form-item label="状态：" prop="status">
      <el-select v-model="ruleForm.status" placeholder="请选择">
        <el-option label="上架" :value="1" />
        <el-option label="下架" :value="0" />
      </el-select>
    </el-form-item>
    <el-form-item label="商品名称：" prop="name">
      <el-input v-model="ruleForm.name" />
    </el-form-item>
    <el-form-item label="商品描述：" prop="subtitle">
      <el-input v-model="ruleForm.subtitle" />
    </el-form-item>
    <el-form-item label="商品封面:" prop="pic">
      <div class="goods-upload ">
        <el-upload
          class="avatar-uploader"
          :action="upload_url"
          :headers="headers"
          :show-file-list="false"
          :on-success="handleAvatarSuccess"
          :before-upload="beforeAvatarUpload"
        >
          <img v-if="ruleForm.pic" :src="ruleForm.pic" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon" />
        </el-upload>
      </div>
    </el-form-item>
    <el-form-item label="市场价格：" prop="marketprice">
      <el-input-number v-model="ruleForm.marketprice" :min="0" :max="99999999" label="福利币" />
    </el-form-item>
    <el-form-item label="售卖模式：" prop="mode">
      <el-select v-model="ruleForm.mode" placeholder="请选择" @change="clearBuffer">
        <el-option label="全积分" :value="1" />
        <el-option label="全福利币" :value="2" />
        <el-option label="积分 + 福利币" :value="3" />
      </el-select>
    </el-form-item>
    <el-form-item v-if="ruleForm.mode === 1 || ruleForm.mode === 3" label="积分：" prop="price">
      <el-input-number v-model="ruleForm.price" :min="0" :max="99999999" label="积分" />
    </el-form-item>
    <el-form-item v-if="ruleForm.mode === 2 || ruleForm.mode === 3" label="福利币：" prop="money">
      <el-input-number v-model="ruleForm.money" :min="0" :max="99999999" label="福利币" />
    </el-form-item>
    <el-form-item label="商品库存：" prop="number">
      <el-input-number v-model="ruleForm.number" :min="0" :max="99999" label="商品库存" />
    </el-form-item>
    <el-form-item label="用户限购数量：" prop="buynumber">
      <el-input-number v-model="ruleForm.buynumber" :min="0" :max="99999" label="用户购买次数" />
    </el-form-item>
    <el-form-item v-if="ruleForm.is_show == 1" label="返利福利币：" prop="flmoney">
      <el-input-number v-model="ruleForm.flmoney" :min="0" :max="99999999" label="返利福利币" />
    </el-form-item>
    <el-form-item label="限购等级状态：" prop="xgtype">
      <el-select v-model="ruleForm.xgtype" placeholder="请选择">
        <el-option label="不限等级" :value="0" />
        <el-option label="P级" :value="1" />
        <el-option label="M级" :value="2" />
        <el-option label="O级" :value="3" />
      </el-select>
    </el-form-item>
    <el-form-item label="产品简介：" prop="describe">
      <Editor :content.sync="ruleForm.describe" />
    </el-form-item>
    <el-divider v-if="ruleForm.id" content-position="left">审核操作</el-divider>
    <el-form-item v-if="ruleForm.is_show !== 1 && ruleForm.id>0" label="审核选项">
      <el-tag v-if="ruleForm.isstatus === 0" size="small" type="warning" effect="dark">未审核</el-tag>
      <el-tag v-if="ruleForm.isstatus === 1" size="small" type="success" effect="dark">通过</el-tag>
      <el-tag v-if="ruleForm.isstatus === 2" size="small" type="danger" effect="dark">驳回</el-tag>
    </el-form-item>
    <el-form-item v-if="ruleForm.is_show !== 1 &&ruleForm.isstatus && ruleForm.isstatus === 2" label="原因">{{ ruleForm.reason }}</el-form-item>
    <el-form-item v-if="ruleForm.is_show === 1" label="审核选项" prop="isstatus">
      <el-select v-model="ruleForm.isstatus" placeholder="请选择">
        <el-option label="未审核" :value="0" />
        <el-option label="通过" :value="1" />
        <el-option label="驳回" :value="2" />
      </el-select>
    </el-form-item>
    <el-form-item v-if="ruleForm.isstatus && ruleForm.isstatus === 2 && ruleForm.is_show === 1" label="原因" prop="reason">
      <el-input
        v-model="ruleForm.reason"
        type="textarea"
        :rows="5"
        :maxlength="200"
        placeholder="请输入您驳回的原因，提示用户及时改正"
        show-word-limit
      />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" size="small" :loading="b_loading" @click="saveHandle">保存</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { mapGetters } from 'vuex'
import { getCategory } from '../../../api/shop_category'
import Editor from '../../../components/editor'
export default {
  name: 'ShopForm',
  components: { Editor },
  props: {
    type: {
      type: Boolean,
      default: () => false
    },
    formData: {
      type: Object,
      default: () => {
        return {
          status: 1,
          isstatus: 0,
          ck_status: 1,
          buynumber: 0,
          xgtype: 0,
          flmoney: 0,
          pic: '',
          mode: 1,
          price: 0,
          money: 0,
          number: 0
        }
      }
    }
  },
  data() {
    return {
      b_loading: false,
      upload_url: process.env.VUE_APP_BASE_API + '/upload',
      headers: {
        Authorization: 'Bearer ' + this.token
      },
      ruleForm: this.formData,
      categories: [],
      rules: {
        name: [
          { required: true, message: '请输入商品名称', trigger: 'blur' }
        ],
        category_id: [
          { required: true, message: '请选择产品分类', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择产品状态', trigger: 'change' }
        ],
        pic: [
          { required: true, message: '请上传产品封面', trigger: 'blur' }
        ],
        mode: [
          { required: true, message: '请选择售卖模式', trigger: 'change' }
        ],
        price: [
          { required: true, message: '请输入积分额度', trigger: 'blur' }
        ],
        money: [
          { required: true, message: '请输入产品价格', trigger: 'blur' }
        ],
        number: [
          { required: true, message: '请输入库存数量', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getCategories()
  },
  // eslint-disable-next-line vue/order-in-components
  computed: {
    ...mapGetters([
      'token'
    ])
  },
  // eslint-disable-next-line vue/order-in-components
  watch: {
    formData: {
      handler(newValue, oldValue) {
        this.ruleForm = newValue
      }
    }
  },
  methods: {
    clearBuffer(val) {
      if (val === 1) {
        this.ruleForm.money = 0
      }
      if (val === 2) {
        this.ruleForm.price = 0
      }
    },
    saveHandle() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.b_loading = true
          this.$emit('transfer', this.formData)
        } else {
          return false
        }
      })
    },
    getCategories() {
      getCategory().then(response => {
        this.categories = response.data
      })
    },
    handleAvatarSuccess(res, file) {
      this.ruleForm.pic = res.data.url
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG && !isPNG) {
        this.$message.error('上传的图标只能是 JPG 或 PNG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传的图片大小不能超过 2MB!')
      }
      return (isJPG || isPNG) && isLt2M
    }
  }
}
</script>

<style>
  .goods-upload .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .goods-upload .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }

  .goods-upload .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }

  .goods-upload .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }
</style>
