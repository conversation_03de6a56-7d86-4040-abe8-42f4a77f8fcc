<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'

export default {
  name: 'AiAttendanceCompanyMonthlyRank',
  components: { StatisticsTemplate },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      config: {
        key: 'ai_attendance_company_monthly_rank',
        filters: [
          {
            label: '关键字',
            prop: 'keyword',
            type: 'input'
          }
        ],
        tableSettings: {
          columns: [
            {
              label: 'ID',
              prop: 'id',
              width: 60,
              sortable: true
            },
            {
              label: '公司名称',
              prop: 'company_name',
              sortable: true
            },
            {
              label: '考勤使用员工数',
              prop: 'attendance_employee_count',
              sortable: true
            },
            {
              label: '考勤次数',
              prop: 'attendance_count',
              sortable: true
            }
          ]
        }
      }
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  }
}
</script>

<template>
  <el-dialog :close-on-click-modal="false" :visible.sync="_visible" title="月活跃企业排行" append-to-body>
    <StatisticsTemplate :config="config" />
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
