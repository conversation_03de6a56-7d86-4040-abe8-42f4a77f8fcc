<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
      </div>
      <div class="table">
        <el-table
          :data="tableData"
          style="width: 100%"
          height="calc(100% - 96px)"
        >
          <el-table-column
            prop="id"
            label="ID"
            width="65"
          />
          <el-table-column
            prop="name"
            label="用户名称"
          />
          <el-table-column
            prop="responsible_name"
            label="负责人"
          />
          <el-table-column
            prop="responsible_phone"
            label="手机号"
          />
          <el-table-column
            prop="address"
            label="地址"
          />
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryList.page"
          :limit.sync="queryList.perPage"
          style="text-align:center;"
          @pagination="getList"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { customers } from '@/api/agent/agent'
export default {
  components: { Pagination },
  data() {
    return {
      tableData: [],
      queryList: {
        id: this.$route.query.id,
        type: this.$route.query.type,
        status: this.$route.query.status,
        page: 1,
        perPage: 10
      },
      total: 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      customers(this.queryList).then(response => {
        if (response.code === 200) {
          // 判断response.data是否是对象
          if (typeof response.data === 'object') {
            this.tableData = response.data.list
            this.total = response.data.meta.total
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap {
  height: 100%;
  .box-card {
    border: none;
    height: 100%;
    ::v-deep.el-card__header {
      padding-top: 0;
    }
    ::v-deep.el-card__body {
      height: calc(100% - 59px);
      overflow: auto;
      padding: 0;
      margin-top: 20px;
    }
  }
  .table {
    height: 100%;
  }
}
</style>
