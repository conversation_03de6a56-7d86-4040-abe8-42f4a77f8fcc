<template>
  <div class="list-wrap">
    <div class="filter">
      <el-form :inline="true" :model="listQuery" class="demo-form-inline" size="small">
        <el-form-item label="极限词：">
          <el-input v-model="listQuery.word" placeholder="极限词" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getList">过滤</el-button>
        </el-form-item>
        <switch-package-type @switchPackageCallback="switchPackageCallback" />
        <el-form-item>
          <el-button icon="el-icon-refresh-right" type="primary" @click="getList">刷新</el-button>
          <el-button style="margin-left: 40px;" icon="el-icon-circle-plus-outline" type="primary" @click="dialogVisible = true; dialogTitle = '新增'">新增极限词</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table mt20">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        height="calc(100% - 96px)"
      >
        <el-table-column
          prop="id"
          label="ID"
        />
        <el-table-column
          prop="word"
          label="极限词"
        />
        <el-table-column
          prop="create_time"
          label="创建时间"
          width="175"
        />
        <el-table-column label="操作" width="100px" fixed="right">
          <template slot-scope="scope">
            <el-dropdown trigger="click">
              <span class="el-dropdown-link">
                更多操作<i class="el-icon-arrow-down el-icon--right" />
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="dataEdit(scope.row)">编辑</el-dropdown-item>
                <el-dropdown-item @click.native="dataDel(scope.row.id)">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>
    <el-dialog
      :title="`${dialogTitle}极限词`"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <el-form ref="ruleForm" label-position="left" :model="editQuery" label-width="80px" class="demo-ruleForm">
        <el-form-item label="极限词" prop="word">
          <el-input v-model="editQuery.word" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false; editQuery = {}">关 闭</el-button>
        <el-button type="primary" @click="addData">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { limitWord, addLimitWord, editLimitWord, delLimitWord } from '@/api/jzt/limit'
import Pagination from '@/components/Pagination'
import SwitchPackageType from '@/views/jzt/components/SwitchPackageType.vue' // secondary package based on el-pagination

export default {
  name: 'Index',
  components: { SwitchPackageType, Pagination },
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '',
      loading: false,
      listQuery: {
        page: 1,
        limit: 10
      },
      total: 0,
      tableData: [],
      editQuery: {}
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.tab-content')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      console.log(tabContentHeight, filterHeight)
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  methods: {
    switchPackageCallback() {
      this.getList()
    },
    // 获取列表
    getList() {
      this.listQuery.all = false
      this.loading = true
      limitWord(this.listQuery).then(response => {
        this.tableData = response.data.data
        this.total = response.data.total
        this.loading = false
      })
    },
    // 新增/修改
    changeData(apiUrl) {
      const that = this
      const word = that.editQuery.word
      if (word === '') {
        that.$message({
          type: 'warning',
          message: '请输入极限词'
        })
        return false
      } else {
        apiUrl(this.editQuery).then(response => {
          // console.log(response)
          that.editQuery = {}
          that.dialogVisible = false
          that.getList()
        })
      }
    },
    // 新增
    addData() {
      const dialogTitle = this.dialogTitle
      let api = addLimitWord
      switch (dialogTitle) {
        case '新增':
          api = addLimitWord
          break
        case '编辑':
          api = editLimitWord
          break
      }
      this.changeData(api)
    },
    // 查看
    dataEdit(row) {
      this.dialogVisible = true
      this.dialogTitle = '编辑'
      this.editQuery = {
        id: row.id,
        word: row.word
      }
    },
    // 删除日志
    dataDel(id) {
      const that = this
      this.$confirm('此操作将永久删除该该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delLimitWord({ id }).then(response => {
          that.$message({
            type: 'success',
            message: '删除成功!'
          })
          that.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}
.el-icon-arrow-down {
  font-size: 12px;
}
</style>

<style lang="scss" scoped>
.list-wrap{
  .filter{
    border-bottom: 1px solid #e5e5e5;
  }
 .table{
    min-height: 200px;
 }
}
</style>
