import request from '@/utils/request'

// 获取DISC测试题列表
export function getDISCListApi(data) {
  return request({
    url: '/psychology/psychology',
    method: 'POST',
    data
  })
}
// 获取添加DISC测试题需要参数
export function getAddDISCListApi(data) {
  return request({
    url: '/psychology/getPsychologyAdd',
    method: 'POST',
    data
  })
}
// 获取修改DISC测试题需要参数
export function getEditDISCListApi(data) {
  return request({
    url: '/psychology/getPsychologyEdit',
    method: 'POST',
    data
  })
}
// 添加DISC测试题
export function addDISCListApi(data) {
  return request({
    url: '/psychology/psychologyCreate',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}
// 修改DISC测试题
export function editDISCListApi(data) {
  return request({
    url: '/psychology/psychologyEdit',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}
// 删除DISC测试题
export function deleteDISCListApi(data) {
  return request({
    url: '/psychology/psychologyDelete',
    method: 'POST',
    data
  })
}
// 获取题目分类列表
export function getPsychologyTypeListApi(data) {
  return request({
    url: '/psychology/psychologyCategory',
    method: 'POST',
    data
  })
}
// 获取修改题目分类需要参数
export function getEditPsychologyTypeListApi(data) {
  return request({
    url: '/psychology/getPsychologyCategoryEdit',
    method: 'POST',
    data
  })
}
// 添加题目分类
export function addPsychologyTypeListApi(data) {
  return request({
    url: '/psychology/psychologyCategoryCreate',
    method: 'POST',
    data
  })
}
// 修改题目分类
export function editPsychologyTypeListApi(data) {
  return request({
    url: '/psychology/psychologyCategoryEdit',
    method: 'POST',
    data
  })
}
// 删除题目分类
export function deletePsychologyTypeListApi(data) {
  return request({
    url: '/psychology/psychologyCategoryDelete',
    method: 'POST',
    data
  })
}
// 获取其他测试题列表
export function getOtherTestListApi(data) {
  return request({
    url: '/psychology/question',
    method: 'POST',
    data
  })
}
// 获取其他测试题结果列表
export function getOtherTestResultListApi(data) {
  return request({
    url: '/psychology/questionyResult',
    method: 'POST',
    data
  })
}
// 获取添加其他测试题结果需要参数
export function getAddOtherTestResultListApi(data) {
  return request({
    url: '/psychology/getQuestionyResultAdd',
    method: 'POST',
    data
  })
}
// 获取修改其他测试题结果需要参数
export function getEditOtherTestResultListApi(data) {
  return request({
    url: '/psychology/getQuestionyResultEdit',
    method: 'POST',
    data
  })
}
// 添加其他测试题结果
export function addOtherTestResultListApi(data) {
  return request({
    url: '/psychology/questionyResultCreate',
    method: 'POST',
    data
  })
}
// 修改其他测试题结果
export function editOtherTestResultListApi(data) {
  return request({
    url: '/psychology/questionyResultEdit',
    method: 'POST',
    data
  })
}
// 删除其他测试题结果
export function deleteOtherTestResultListApi(data) {
  return request({
    url: '/psychology/questionyResultDelete',
    method: 'POST',
    data
  })
}
// 获取添加其他测试题需要参数
export function getAddOtherTestListApi(data) {
  return request({
    url: '/psychology/getQuestionyAdd',
    method: 'POST',
    data
  })
}
// 获取修改其他测试题需要参数
export function getEditOtherTestListApi(data) {
  return request({
    url: '/psychology/getQuestionyEdit',
    method: 'POST',
    data
  })
}
// 添加其他测试题
export function addOtherTestListApi(data) {
  return request({
    url: '/psychology/questionyCreate',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}
// 修改其他测试题
export function editOtherTestListApi(data) {
  return request({
    url: '/psychology/questionyEdit',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}
// 删除其他测试题
export function deleteOtherTestListApi(data) {
  return request({
    url: '/psychology/questionyDelete',
    method: 'POST',
    data
  })
}
// 获取DISC测试题结果列表
export function getDISCResultListApi(data) {
  return request({
    url: '/psychology/psychologyResult',
    method: 'POST',
    data
  })
}
// 获取添加DISC测试题结果需要参数
export function getAddDISCResultListApi(data) {
  return request({
    url: '/psychology/getPsychologyResultAdd',
    method: 'POST',
    data
  })
}
// 添加DISC测试题结果
export function addDISCResultListApi(data) {
  return request({
    url: '/psychology/psychologyResultCreate',
    method: 'POST',
    data
  })
}
// 获取修改DISC测试题结果需要参数
export function getEditDISCResultListApi(data) {
  return request({
    url: '/psychology/getPsychologyResultEdit',
    method: 'POST',
    data
  })
}
// 修改DISC测试题结果
export function editDISCResultListApi(data) {
  return request({
    url: '/psychology/psychologyResultEdit',
    method: 'POST',
    data
  })
}
// 删除DISC测试题结果
export function deleteDISCResultListApi(data) {
  return request({
    url: '/psychology/psychologyResultDelete',
    method: 'POST',
    data
  })
}
