<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>新增商品</span>
      </div>
      <shop-form :type="true" @transfer="sonSave" />
    </el-card>
  </div>
</template>

<script>
import { store } from '../../api/shop_goods'
import ShopForm from './components/shop-form'
export default {
  name: 'Add',
  components: { ShopForm },
  methods: {
    sonSave(form) {
      store(form).then(response => {
        this.$message.success('添加成功')
        this.$router.go(-1)
      })
    }
  }
}
</script>

<style scoped>

</style>
