import request from '@/utils/request'

export function index(data) {
  return request({
    url: '/host/index',
    method: 'post',
    data: data
  })
}

export function detail(data) {
  return request({
    url: '/host/detail',
    method: 'post',
    data: data
  })
}

export function getFinance(data) {
  return request({
    url: '/host/getFinance',
    method: 'post',
    data: data
  })
}

export function manager(data) {
  return request({
    url: '/host/manager',
    method: 'post',
    data: data
  })
}

export function hostlist(data) {
  return request({
    url: '/host/hostlist',
    method: 'post',
    data: data
  })
}

export function endtimeChange(data) {
  return request({
    url: '/host/host_endtime',
    method: 'post',
    data: data
  })
}
