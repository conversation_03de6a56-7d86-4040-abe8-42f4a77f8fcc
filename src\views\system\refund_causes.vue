<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>退款理由管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="createView">新增</el-button>
      </div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="65"
        />
        <el-table-column label="状态" width="80">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status" type="success" effect="dark" size="small">启用</el-tag>
            <el-tag v-else type="danger" effect="dark" size="small">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="退款原因"
        />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-link :underline="false" type="primary" @click="editView(scope.row)">编辑</el-link>
            <el-popconfirm
              title="确定删除当前记录吗？"
              @onConfirm="deleteHandle(scope.row)"
            >
              <el-link slot="reference" :underline="false" type="danger">删除</el-link>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!--  表单  -->
    <el-dialog
      title="文案管理"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="退款原因：" prop="name">
          <el-col :span="20">
            <el-input v-model="ruleForm.name" type="textarea" />
          </el-col>
        </el-form-item>
        <el-form-item label="状态：" prop="delivery">
          <el-switch
            v-model="ruleForm.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="saveHandle">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { causes, store, cause_delete } from '../../api/refund'

export default {
  name: 'RefundCauses',
  data() {
    return {
      dialogVisible: false,
      loading: false,
      tableLoading: false,
      tableData: [],
      ruleForm: {},
      rules: {
        name: [
          { required: true, message: '退款理由必填', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.list()
  },
  methods: {
    list() {
      this.tableLoading = true
      causes().then(response => {
        this.tableData = response.data
        this.tableLoading = false
      })
    },
    createView() {
      this.ruleForm = {
        status: 1
      }
      this.dialogVisible = true
    },
    editView(row) {
      this.ruleForm = row
      this.dialogVisible = true
    },
    saveHandle() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.loading = true
          store(this.ruleForm).then(reponse => {
            this.dialogVisible = false
            this.loading = false
            this.list()
          })
        }
      })
    },
    deleteHandle(row) {
      cause_delete(row).then(() => {
        this.$message.success('删除成功')
        this.list()
      })
    }
  }
}
</script>

<style scoped>

</style>
