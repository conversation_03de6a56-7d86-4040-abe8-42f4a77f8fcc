<template>
  <div class="list-wrap">
    <el-row style="margin-bottom: 20px">
      <el-col :span="24">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>已购买的产品</span>
            <!--                <el-button style="float: right; padding:0" type="text" @click="dialogAddProductVisible=true">开通产品</el-button>-->
          </div>
          <div>
            <el-table
              ref="multiple"
              v-loading="productLoading"
              :data="product"
              row-key="id"
              lazy
              :load="orderload"
              :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
            >
              <el-table-column label="#id" width="100">
                <template slot-scope="scope">{{ scope.$index + 1 }}</template>
              </el-table-column>

              <el-table-column label="产品名称">
                <template slot-scope="scope">{{ scope.row.product_name }}
                </template>
              </el-table-column>
              <el-table-column label="开通版本">
                <template slot-scope="scope">{{ scope.row.product_version }}</template>
              </el-table-column>
              <el-table-column label="版本价格">
                <template slot-scope="scope">{{ scope.row.price }}</template>
              </el-table-column>
              <el-table-column label="开通时间">
                <template slot-scope="scope">{{ scope.row.begin_time }}</template>
              </el-table-column>
              <el-table-column label="过期时间">
                <template slot-scope="scope">{{ scope.row.expire_end }}</template>
              </el-table-column>
              <el-table-column label="操作">
                <template slot-scope="scope">
                  <el-button v-if="(scope.row.category_id === 1 || scope.row.category_id === 19) && scope.row.count>=0" type="text" @click="dialogChangeProductDateVisible = true ; changeDateForm.id = scope.row.id">更改时间</el-button>
                  <div v-if="scope.row.category_id === 1 && scope.row.hasChildren">
                    <el-button type="text" style="color: red" @click="closeProduct(scope.row.id)">关闭产品</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row style="margin-bottom: 20px">
      <el-col :span="24">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>已购买的IDC产品</span>
            <el-button style="float: right; padding:0" type="text" @click="IDCForm={}; dialogAddIDCVisible=true">添加</el-button>
          </div>
          <div>
            <el-table ref="multiple" v-loading="productLoading" :data="website">
              <el-table-column label="#" width="55">
                <template slot-scope="scope">{{ scope.$index + 1 }}</template>
              </el-table-column>
              <el-table-column label="产品名称" prop="name" show-tooltip-when-overflow />
              <el-table-column label="产品类型" width="200" prop="type" />
              <el-table-column label="购买时间" width="200" prop="created_at" />
              <el-table-column label="过期时间" width="200" prop="time" />
              <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                  <el-button type="text" @click="editIDC(scope.row)">修改</el-button>
                  <el-button type="text" @click="delIDC(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row style="margin-bottom: 20px">
      <el-col :span="24">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>域名模板</span>
            <el-button style="float: right; padding:0" type="text" @click="pushDomain()">添加</el-button>
          </div>
          <div>
            <el-table ref="multiple" v-loading="domainLoading" :data="domainList">
              <el-table-column label="#" width="55">
                <template slot-scope="scope">{{ scope.$index + 1 }}</template>
              </el-table-column>
              <el-table-column label="持有者名称（中文）" prop="base.custName" show-tooltip-when-overflow />
              <el-table-column label="持有者名称（英文）" prop="base.custNameEn" />
              <el-table-column label="持有者类型" prop="custType">
                <template slot-scope="scope">
                  <span v-if="scope.row.base.custType==='CUST_PERSONAL'">个人</span>
                  <span v-else>公司</span>
                </template>
              </el-table-column>
              <el-table-column label="注册人邮箱" prop="base.email" />
              <el-table-column label="状态">
                <template slot-scope="scope">
                  <span v-if="scope.row.status === 'UNCOMMITTED'">未提交认证资料</span>
                  <span v-if="scope.row.status === 'PASS'" style="color: #0e90d2">已认证</span>
                  <span v-if="scope.row.status === 'WAITING'" style="color: #FFBA00">待审核</span>
                  <span v-if="scope.row.status === 'AUDITING'" style="color: #FFBA00">审核中</span>
                  <span v-if="scope.row.status === 'UNPASS'" style="color: red">未通过</span>
                </template>
              </el-table-column>
              <el-table-column label="拒绝原因" show-overflow-tooltip prop="message" />
              <el-table-column label="创建时间" prop="created_at" />
              <el-table-column label="操作" width="150">

                <template slot-scope="scope">
                  <el-button type="text" @click="showInfo(scope.row)">查看</el-button>
                  <el-divider v-if="scope.row.is_bind == '0'" direction="vertical" />
                  <el-button v-if="scope.row.is_bind == '0'" type="text" :disabled="scope.row.status === 'AUDITING' || scope.row.status === 'PASS'" @click="pushAuth(scope.row)">认证</el-button>
                  <el-button v-if="scope.row.is_bind == '0'" type="text" :disabled="scope.row.status === 'AUDITING' || scope.row.status === 'PASS'" style="color: #3a835d" @click="pushDomainDetail(scope.row)">修改</el-button>
                  <el-divider v-if="scope.row.is_bind == '0' && (scope.row.status === 'UNPASS' || scope.row.status === 'UNCOMMITTED')" direction="vertical" />
                  <el-button v-if="scope.row.is_bind == '0' && (scope.row.status === 'UNPASS' || scope.row.status === 'UNCOMMITTED')" type="text" style="color: red" @click="domainDestroy(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>企业管理员</span>
            <el-button style="float: right; padding:0" type="text" @click="getManager">刷新</el-button>
            <!--<el-button style="float: right; padding:0; margin-right: 5px;" type="text" @click="handelAddManager">-->
            <!--添加-->
            <!--</el-button>-->
            <el-button style="float: right; padding:0" type="text" @click="getUserManager">同步</el-button>

            <el-button style="float: right; padding:0; margin-right: 5px;" type="text" @click="handelChangeUser">
              更换管理员
            </el-button>
          </div>
          <div>
            <el-table v-loading="managerLoading" :data="manager">
              <el-table-column label="#" width="55">
                <template slot-scope="scope">{{ scope.$index + 1 }}</template>
              </el-table-column>
              <el-table-column label="姓名">
                <template slot-scope="scope">{{ scope.row.truename?scope.row.truename:scope.row.name }}</template>
              </el-table-column>
              <el-table-column label="账号" prop="phone" />
              <el-table-column label="角色" prop="role" />
              <el-table-column label="操作">
                <template slot-scope="scope">
                  <el-button type="text" @click="amendPwd(scope.row)">重置密码</el-button>
                  <!--<el-button type="text" @click="dialogEditPhoneVisible=true">修改手机号</el-button>-->
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-dialog :close-on-click-modal="false" title="更换新账户" :visible.sync="dialogAddUserVisible">
      <el-form ref="userForm" :model="smsForm" :rules="formRules">
        <el-form-item label="姓名" :label-width="formLabelWidth">
          <el-input v-model="smsForm.name" autocomplete="off" style="max-width: 300px;" placeholder="请输入账户姓名" />
        </el-form-item>
        <el-form-item label="手机账户" :label-width="formLabelWidth">
          <el-input v-model="smsForm.phone" autocomplete="off" style="max-width: 300px;" placeholder="请输入手机账户" />
        </el-form-item>
        <!--<el-form-item label="验证码" :label-width="formLabelWidth">-->
        <!--<el-input v-model="smsForm.code" autocomplete="off" style="max-width: 150px;" placeholder="请输入验证码" />-->
        <!--<el-button style="width: 130px" type="primary" :loading="dialogSmsLoading" :disabled="numTime !== 60" @click="sendSms('verify')">{{ sendCodeTxt }}</el-button>-->
        <!--</el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogAddUserVisible = false">取 消</el-button>
        <el-button type="primary" :loading="dialogAddUserButtonLoading" @click="handelChangeManagerSubmit('userForm')">确 定
        </el-button>
      </div>
    </el-dialog>
    <el-dialog :close-on-click-modal="false" title="修改新手机号" :visible.sync="dialogEditPhoneVisible">
      <el-form ref="userForm" :model="smsForm" :rules="formRules">
        <el-form-item label="手机账户" :label-width="formLabelWidth">
          <el-input v-model="smsForm.phone" autocomplete="off" style="max-width: 300px;" placeholder="请输入新手机账户" />
        </el-form-item>
        <el-form-item label="验证码" :label-width="formLabelWidth">
          <el-input v-model="smsForm.code" autocomplete="off" style="max-width: 150px;" placeholder="请输入验证码" />
          <el-button style="width: 130px" type="primary" :loading="dialogSmsLoading" :disabled="numTime !== 60" @click="sendSms('register')">{{ sendCodeTxt }}</el-button>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogEditPhoneVisible = false">取 消</el-button>
        <el-button type="primary" :loading="dialogEditPhoneButtonLoading" @click="handleAmendPhoneSubmit('userForm')">确 定
        </el-button>
      </div>
    </el-dialog>
    <el-dialog :close-on-click-modal="false" width="500px" title="添加IDC" :visible.sync="dialogAddIDCVisible">
      <el-tabs v-model="IDCForm.type" @tab-click="IDCTabs(IDCForm.type)">
        <el-tab-pane :disabled="!!IDCForm['id']" label="域名" name="域名">
          <el-form ref="IDCForm" :model="IDCForm" :rules="IDCRules">
            <el-form-item label="域名名称" prop="name">
              <el-input v-model="IDCForm.name" autocomplete="off" placeholder="请输入名称" />
            </el-form-item>
            <el-form-item label="选择到期日期">
              <el-date-picker
                v-model="IDCForm.time"
                type="date"
                placeholder="选择到期日期"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane :disabled="!!IDCForm['id']" label="空间" name="空间">
          <el-form ref="IDCForm" :model="IDCForm" :rules="IDCRules">
            <el-form-item label="空间名称" prop="name">
              <el-input v-model="IDCForm.name" autocomplete="off" placeholder="例如:龙采双线500M,资海常规500M,自备,思普,万网" />
            </el-form-item>
            <el-form-item label="ip" prop="domain">
              <el-input v-model="IDCForm.domain" autocomplete="off" placeholder="输入域名" />
            </el-form-item>
            <el-form-item label="ftp账号(选填)" prop="supplier_type">
              <el-input v-model="IDCForm.ftp" autocomplete="off" placeholder="ftp账号" />
            </el-form-item>
            <el-form-item label="ftp密码(选填)" prop="supplier_type">
              <el-input v-model="IDCForm.ftp_pass" autocomplete="off" placeholder="ftp密码" />
            </el-form-item>
            <el-form-item label="选择到期日期">
              <el-date-picker
                v-model="IDCForm.time"
                type="date"
                placeholder="选择到期日期"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane :disabled="!!IDCForm['id']" label="后台" name="后台">
          <el-form ref="IDCForm" :model="IDCForm" :rules="IDCRules">
            <el-form-item label="后台地址" prop="name">
              <el-input v-model="IDCForm.name" autocomplete="off" placeholder="请输入后台地址" />
            </el-form-item>
            <el-form-item label="账号" prop="created_at">
              <el-input v-model="IDCForm.created_at" autocomplete="off" placeholder="请输入账号" />
            </el-form-item>
            <el-form-item label="密码" prop="time">
              <el-input v-model="IDCForm.time" autocomplete="off" placeholder="请输入密码" />
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogAddIDCVisible = false">取 消</el-button>
        <el-button type="primary" :loading="dialogAddIDCButtonLoading" @click="handelAddIDCSubmit('userForm')">确 定
        </el-button>
      </div>
    </el-dialog>
    <el-dialog :close-on-click-modal="false" title="开通产品" :visible.sync="dialogAddProductVisible">
      <el-form ref="userForm" :model="openForm">
        <el-form-item label="选择产品" :label-width="formLabelWidth">
          <el-select v-model="openForm.category_id" placeholder="请选择">
            <el-option v-for="category in categoryList" :key="category.id" :label="category.name" :value="category.id" />
          </el-select>
          <el-select v-model="openForm.product_id" placeholder="请选择" filterable clearable :disabled="openForm.customer_id===''" @click.native="getProducts(openForm.category_id)">
            <el-option v-for="item in productsList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          <el-input v-if="openForm.category_id===18" v-model="openForm.domain" style="width: 200px" placeholder="请输入域名名称" clearable />
        </el-form-item>
        <el-form-item label="选择时间" :label-width="formLabelWidth">
          <el-radio-group v-model="openForm.year" size="mini">
            <el-radio-button v-for="item in [1,2,3,4,5]" :key="item" :label="item">{{ item }}年</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogAddProductVisible = false">取 消</el-button>
        <el-button type="primary" :loading="dialogAddProductButtonLoading" @click="handleOpenProductSubmit()">确 定
        </el-button>
      </div>
    </el-dialog>

    <el-dialog :close-on-click-modal="false" title="更改产品到期时间" :visible.sync="dialogChangeProductDateVisible">
      <el-form ref="userForm" :model="changeDateForm">
        <el-form-item label="结束时间" :label-width="formLabelWidth">
          <el-date-picker
            v-model="changeDateForm.expire_end"
            type="date"
            placeholder="选择日期"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogChangeProductDateVisible = false">取 消</el-button>
        <el-button type="primary" :loading="dialogChangeProductDateButtonLoading" @click="changeProductDate()">确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  manager
} from '@/api/agent/customers'
import { productList as product, orderList as order, manager_change, usermanager, manager_EditPwd, manager_EditPhone } from '@/api/agent/customers'
import { sms } from '@/api/agent/erp'
import { index, websiteCreate, websiteDestory, domainCreate, domainDestory, hostCreate, hostDestory } from '@/api/agent/idc'
import { products, category } from '@/api/agent/product'
import { adminOpen, adminChangeDate, adminClose } from '@/api/agent/adminManger'
import { index as domainList, destroy as domainDestroy } from '@/api/agent/domain'
import moment from 'moment'

export default {
  name: 'Detail',
  components: {
    // SidebarItem,
  },
  data() {
    return {
      manager: [],
      user: [],
      role: [],
      activeName: 'host',
      product: [],
      website: [],
      domainList: [],
      productLoading: false,
      managerLoading: false,
      websiteLoading: false,
      userListLoading: false,
      domainLoading: false,
      dialogFormVisible: false,
      webForm: { name: '', url: '', icon: '', account: '' },
      IDCForm: {},
      formRules: {
        code: [
          { required: true, message: '验证码不能为空', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '手机账户不能为空', trigger: 'blur' },
          { max: 11, message: '至多11位', trigger: 'blur' }
        ]
      },
      IDCRules: {
        type: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ]
      },
      dialogUserManagerVisible: false,
      dialogAddIDCVisible: false,
      roleFormButtonLoading: false,
      dialogAddUserVisible: false,
      dialogAddProductVisible: false,
      dialogEditPhoneVisible: false,
      dialogChangeProductDateVisible: false,
      managerForm: {},
      userForm: {},
      openForm: {},
      changeDateForm: {},
      formLabelWidth: '120px',
      dialogAddUserButtonLoading: false,
      dialogAddIDCButtonLoading: false,
      dialogEditPhoneButtonLoading: false,
      dialogAddProductButtonLoading: false,
      dialogChangeProductDateButtonLoading: false,
      smsForm: {
        code: '',
        scene: 'verify'
      },
      sendCodeTxt: '发送验证码',
      numTime: 60,
      dialogSmsLoading: false,
      productsList: [],
      productAllList: [],
      categoryList: [],
      category_id: '',
      cities: ['上海', '北京', '广州', '深圳']
    }
  },
  created() {
    if (!this.$route.query.id || this.$route.query.id === '0') {
      return this.$route.push({ name: 'Dashboard' })
    }
    this.getProduct()
    this.getWebsite()
    this.getManager()
    this.getCategory()
    this.getDomainList()
  },
  methods: {
    getManager() {
      this.managerLoading = true
      manager({ id: this.$route.query.id }).then(response => {
        this.manager = response.data
        this.managerLoading = false
      }).catch(() => {
        this.managerLoading = false
      })
    },
    getUserManager() {
      this.managerLoading = true
      usermanager({ id: this.$route.query.id }).then(response => {
        this.getManager()
        this.managerLoading = false
      }).catch(() => {
        this.managerLoading = false
      })
    },
    // 获取产品种类列表
    getCategory() {
      category({ all: true }).then(response => {
        if (response.code === 200 && response.data) {
          this.categoryList = response.data.list
        }
      })
    },
    // 获取产品列表
    getProducts(category_id) {
      products({ all: true, category_id: category_id }).then(response => {
        this.productsList = response.data.list
      })
    },
    getProduct() {
      this.productLoading = true
      product({ id: this.$route.query.id }).then(response => {
        this.product = response.data
        this.productLoading = false
      }).catch(() => {
        this.productLoading = false
      })
    },
    getWebsite() {
      this.websiteLoading = true
      index({ 'customer_id': this.$route.query.id }).then(response => {
        this.websiteLoading = false
        this.website = response.data
      }).catch(() => {
        this.websiteLoading = false
      })
    },
    handelChangeUser() {
      this.dialogAddUserVisible = true
    },
    handelAddIDCSubmit() {
      this.IDCForm['customer_id'] = this.$route.query.id

      this.dialogAddIDCButtonLoading = true
      if (this.IDCForm['type'] === '后台') {
        websiteCreate(this.IDCForm).then(res => {
          this.dialogAddIDCButtonLoading = false
          this.dialogAddIDCVisible = false
          this.$message.success('操作成功')
          this.getWebsite()
        }).catch(error => {
          console.log(error)
          this.dialogAddIDCButtonLoading = false
        })
      } else if (this.IDCForm['type'] === '域名') {
        this.IDCForm['time'] = moment(this.IDCForm['time']).format('YYYY-MM-DD')
        domainCreate(this.IDCForm).then(res => {
          this.dialogAddIDCButtonLoading = false
          this.dialogAddIDCVisible = false
          this.$message.success('操作成功')
          this.getWebsite()
        }).catch(error => {
          console.log(error)
          this.dialogAddIDCButtonLoading = false
        })
      } else if (this.IDCForm['type'] === '空间') {
        hostCreate(this.IDCForm).then(res => {
          this.dialogAddIDCButtonLoading = false
          this.dialogAddIDCVisible = false
          this.$message.success('操作成功')
          this.getWebsite()
        }).catch(error => {
          console.log(error)
          this.dialogAddIDCButtonLoading = false
        })
      }
    },
    delIDC(row) {
      row.customer_id = this.$route.query.id

      this.$confirm('确定要移除该条信息么', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning' }).then(() => {
        this.userListLoading = true

        if (row.type === '后台') {
          websiteDestory(row).then(res => {
            this.dialogAddIDCButtonLoading = false
            this.dialogAddIDCVisible = false
            this.$message.success('删除成功')
            this.getWebsite()
          }).catch(error => {
            console.log(error)
            this.dialogAddIDCButtonLoading = false
          })
        } else if (row.type === '域名') {
          domainDestory(row).then(res => {
            this.dialogAddIDCButtonLoading = false
            this.dialogAddIDCVisible = false
            this.$message.success('删除成功')
            this.getWebsite()
          }).catch(error => {
            console.log(error)
            this.dialogAddIDCButtonLoading = false
          })
        } else if (row.type === '空间') {
          hostDestory(row).then(res => {
            this.dialogAddIDCButtonLoading = false
            this.dialogAddIDCVisible = false
            this.$message.success('删除成功')
            this.getWebsite()
          }).catch(error => {
            console.log(error)
            this.dialogAddIDCButtonLoading = false
          })
        }
      })
    },
    editIDC(row) {
      console.log(row)
      this.IDCForm = row
      this.dialogAddIDCVisible = true
    },

    IDCTabs(type) {
      if (!this.IDCForm['id']) {
        this.IDCForm['type'] = type
      }
    },
    domainDestroy(item) {
      this.$confirm('确定要移除该条模板么', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning' }).then(() => {
        domainDestroy({ id: this.$route.query.id, t_id: item.id }).then(res => {
          this.$message.success(res.message)
          this.getDomainList()
        })
      })
    },
    amendPwd(row) {
      this.$prompt('请输入重置密码', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^.{8,14}$/,
        inputErrorMessage: '密码长度为8~14个字符，且字母数字以及标点符号至少包含2种，不允许有空格和中文'
      }).then(({ value }) => {
        manager_EditPwd({ 'phone': row['phone'], 'name': row['name'], 'password': value }).then(response => {
          this.$message.success(response.message)
        })
      })
    },
    sendSms(type) {
      this.smsForm.scene = type

      sms(this.smsForm).then(response => {
        this.$message.success('发送成功')
        this.dialogSmsLoading = false
        this.SendCodeFun()
      }).catch(error => {
        console.log(error)
        this.dialogSmsLoading = false
      })
    },
    SendCodeFun() {
      if (this.numTime === 60) {
        let timer = null
        timer = setInterval(() => {
          if (this.numTime > 0) {
            this.numTime--
            this.sendCodeTxt = '正在发送' + this.numTime + 's'
          } else {
            clearInterval(timer)
            timer = null
            this.sendCodeTxt = '重新获取验证码'
            this.numTime = 60
          }
        }, 1000)
      }
    },
    handelChangeManagerSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.dialogAddUserButtonLoading = true
          this.smsForm.id = this.$route.query.id
          manager_change(this.smsForm).then(response => {
            this.getManager()
            this.dialogAddUserVisible = false
            this.userForm = {}
            this.dialogAddUserButtonLoading = false
          }).catch(() => {
            this.$message.error('变更失败')
            this.dialogAddUserButtonLoading = false
          })
        } else {
          return false
        }
      })
    },
    handleAmendPhoneSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.dialogEditPhoneButtonLoading = true
          this.smsForm.id = this.$route.query.id
          manager_EditPhone(this.smsForm).then(response => {
            this.getManager()
            this.dialogEditPhoneVisible = false
            this.userForm = {}
            this.dialogEditPhoneButtonLoading = false
          }).catch(() => {
            this.dialogEditPhoneButtonLoading = false
          })
        } else {
          return false
        }
      })
    },
    handleOpenProductSubmit() {
      this.dialogAddProductButtonLoading = true
      this.openForm.customer_id = this.$route.query.id
      adminOpen(this.openForm).then(response => {
        this.getProduct()
        this.dialogAddProductVisible = false
        this.dialogAddProductButtonLoading = false
      }).catch(() => {
        this.dialogAddProductButtonLoading = false
      })
    },
    changeProductDate() {
      this.changeDateForm.expire_end = moment(this.changeDateForm.expire_end).format('YYYY-MM-DD')
      adminChangeDate(this.changeDateForm).then(response => {
        this.getProduct()
        this.dialogChangeProductDateVisible = false
        this.dialogChangeProductDateButtonLoading = false
      }).catch(() => {
        this.$message.error('改变时间失败')
        this.dialogChangeProductDateButtonLoading = false
      })
    },
    closeProduct(id) {
      this.$confirm('此操作将永久删除该产品, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.productLoading = true
        adminClose({ id: id }).then(response => {
          this.$message.success(response['message'])
          this.getProduct()
        }).catch(() => {
          this.productLoading = false
        })
      }).catch(() => {
      })
    },
    getDomainList() {
      this.domainLoading = true
      domainList({ id: this.$route.query.id }).then(res => {
        if (res.code === 200 && res.data) {
          this.domainList = res.data
        }
      }).catch((e) => {
        console.log(e)
      }).finally(() => {
        this.domainLoading = false
      })
    },
    showInfo(item) {
      this.$router.push({
        name: 'agentCustomersDomainDetail',
        query: { id: this.$route.query.id, t_id: item.id }
      })
    },
    pushDomainDetail(item) {
      this.$router.push({
        name: 'agentCustomersDomainHolder',
        query: { id: this.$route.query.id, t_id: item.id }
      })
    },
    pushAuth(item) {
      this.$router.push({
        name: 'agentCustomersDomainHolderAuth',
        query: { id: this.$route.query.id, t_id: item.id }
      })
    },
    orderload(data, treeNode, resolve) {
      const orderlistarr = []
      order({ id: data.orderid, category_id: data.category_id, domain: data.domain, customer_id: data.customer_id, product_id: data.product_id, other_no: data.other_no }).then(res => {
        res.data.forEach((item) => {
          orderlistarr.push(item)
        })
        resolve(orderlistarr)
      }).catch(() => {
      })
    }
  }
}
</script>

<style scoped>
  table tr td {
    padding: 8px 10px;
  }
</style>
