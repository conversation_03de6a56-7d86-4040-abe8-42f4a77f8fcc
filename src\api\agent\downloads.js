import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/download/list',
    method: 'post',
    data: data
  })
}

// 代理商列表导出接口
export function agentListExport(data) {
  return request({
    url: '/agent/exportExcel',
    method: 'post',
    data: data
  })
}

// 用户列表导出
export function userListExport(data) {
  return request({
    url: '/customers/excel',
    method: 'post',
    data: data
  })
}

// 建站通用户列表导出
export function jztUserListExport(data) {
  return request({
    url: '/otherProduct/jianzhantong/downexportExcel',
    method: 'post',
    data: data
  })
}

// 域名列表导出
export function domainListExport(data) {
  return request({
    url: '/domain/domainExcel',
    method: 'post',
    data: data
  })
}

// 主机列表导出
export function hostListExport(data) {
  return request({
    url: '/host/exportExcel',
    method: 'post',
    data: data
  })
}

// 移动云主机列表导出
export function ydHostListExport(data) {
  return request({
    url: '/yidongyun/exportExcel',
    method: 'post',
    data: data
  })
}

// 移动云月度详单列表导出
export function ydHostMonthListExport(data) {
  return request({
    url: '/yidongyun/estimatedbillexportExcel',
    method: 'post',
    data: data
  })
}

// 移动云月度账单列表导出
export function ydHostMonthBillListExport(data) {
  return request({
    url: '/yidongyun/monthexportExcel',
    method: 'post',
    data: data
  })
}

// 财务查询导出
export function financeListExport(data) {
  return request({
    url: '/product/openList/exportExcel',
    method: 'post',
    data: data
  })
}

// 到期列表导出
export function expireListExport(data) {
  return request({
    url: '/product/expireEnd/exportExcel',
    method: 'post',
    data: data
  })
}

// 系统产品列表导出
export function systemProductListExport(data) {
  return request({
    url: '/product/exportExcel',
    method: 'post',
    data: data
  })
}
