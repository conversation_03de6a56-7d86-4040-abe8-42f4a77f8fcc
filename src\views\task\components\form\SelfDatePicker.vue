<script>
export default {
  name: 'SelfDatePicker',
  props: {
    value: {
      type: [String, Array],
      default: ''
    },
    placeholder: {
      type: [String, Array],
      default: '请选择日期'
    },
    type: {
      type: String,
      default: 'date'
    },
    format: {
      type: String,
      default: 'yyyy-MM-dd'
    },
    valueFormat: {
      type: String,
      default: 'yyyy-MM-dd'
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    selectValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('update:value', val)
      }
    }
  }
}
</script>

<template>
  <el-date-picker
    v-model="selectValue"
    :type="type"
    :placeholder="placeholder[0]"
    :format="format"
    :value-format="valueFormat"
    clearable
    range-separator="至"
    :start-placeholder="placeholder[0]"
    :end-placeholder="placeholder[1]"
    :disabled="disabled"
  />
</template>

<style scoped>

</style>
