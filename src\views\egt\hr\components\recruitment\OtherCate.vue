<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import OtherCateUpdate from './OtherCateUpdate.vue'
import { deletePsychologyTypeListApi, getPsychologyTypeListApi } from '@/api/egt/recruitment'

export default {
  name: 'OtherCate',
  components: {
    StatisticsTemplate,
    OtherCateUpdate
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      config: {
        key: 'otherCate',
        tableSettings: {
          api: getPsychologyTypeListApi,
          height: '500px',
          columns: [
            {
              label: 'ID',
              prop: 'id',
              width: 60
            },
            {
              label: '分类',
              prop: 'title'
            },
            {
              label: '操作',
              prop: 'action',
              isSlot: true,
              align: 'center',
              width: '200'
            }
          ],
          field: {
            page: 'pageIndex',
            limit: 'pageSize',
            total: 'total'
          }
        }
      },
      updateVisible: false,
      row: {}
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    handleAdd() {
      this.updateVisible = true
      this.row = {}
    },
    handleUpdate(row) {
      this.updateVisible = true
      this.row = row
    },
    async handleDelete(row) {
      if(!row.id) return
      try {
        this.$refs.listWrap.setLoading(row.id, true)
        const res = await deletePsychologyTypeListApi({ id: row.id })
        if (res.code === 200) {
          this.$message.success('删除成功')
          await this.$refs.listWrap.handleGetData()
        }else{
          this.$message.error(res.msg || res.message || '删除失败')
        }
      } finally {
        this.$refs.listWrap.setLoading(row.id, false)
      }
    },
    submitSuccess() {
      console.log("🚀 ~ submitSuccess ~ this.$refs.listWrap: ", this.$refs.listWrap);
      this.$refs.listWrap.handleGetData()
    }
  }
}
</script>

<template>
  <el-dialog :close-on-click-modal="false" title="设置分类" :visible.sync="_visible" width="800px">
    <StatisticsTemplate ref="listWrap" :config="config">
      <template #topActions>
        <div style="margin-bottom: 10px;text-align: right;width: 100%;">
          <el-button type="primary" @click="handleAdd">添加</el-button>
        </div>
      </template>
      <template #otherCate_action="{row}">
        <el-button type="primary" size="mini" @click="handleUpdate(row)">编辑</el-button>
        <el-popconfirm
          title="确定删除吗？"
          @onConfirm="handleDelete(row)"
        >
          <el-button slot="reference" type="danger" size="mini">删除</el-button>
        </el-popconfirm>
      </template>
    </StatisticsTemplate>
    <OtherCateUpdate :visible.sync="updateVisible" :data="row" @submitSuccess="submitSuccess" />
  </el-dialog>
</template>
