import request from '@/utils/request'

export function getGoods(data) {
  return request({
    url: '/shop/list',
    method: 'post',
    data
  })
}

export function store(data) {
  return request({
    url: '/shop/store',
    method: 'post',
    data
  })
}
export function destroy(data) {
  return request({
    url: '/shop/delete',
    method: 'post',
    data
  })
}

export function detail(data) {
  return request({
    url: '/shop/detail',
    method: 'post',
    data
  })
}
