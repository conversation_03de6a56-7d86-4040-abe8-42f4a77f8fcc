import request from '@/utils/requestJzt'

export function login(data) {
  return request({
    url: '/auth/login',
    method: 'post',
    data
  })
}
export function zzd_login(data) {
  return request({
    url: '/auth/zzd_login',
    method: 'post',
    data
  })
}
export function khd_login(data) {
  return request({
    url: '/auth/khd_login',
    method: 'post',
    data
  })
}
export function getInfo() {
  return request({
    url: '/user',
    method: 'get'
  })
}

export function logout() {
  return request({
    url: '/user/logout',
    method: 'post'
  })
}

export function getMenus() {
  return request({
    url: '/menus/list',
    method: 'POST'
  })
}

export function getUsers(data) {
  return request({
    url: '/user/list',
    method: 'POST',
    data: data
  })
}

export function updateUser(data) {
  return request({
    url: '/user/update',
    method: 'POST',
    data: data
  })
}

export function storeUser(data) {
  return request({
    url: '/user/store',
    method: 'POST',
    data: data
  })
}

export function resetPassword(data) {
  return request({
    url: '/user/resetPassword',
    method: 'POST',
    data: data
  })
}

export function changeProfile(data) {
  return request({
    url: '/user/changeProfile',
    method: 'POST',
    data: data
  })
}

export function deleteUser(data) {
  return request({
    url: '/user/delete',
    method: 'POST',
    data: data
  })
}
