<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <!--<span>年限价格</span>-->
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
        <el-button style="float: right; padding: 3px 0" type="text" @click="save">保存</el-button>
      </div>
      <el-form ref="numberValidateForm" :model="numberValidateForm" label-width="100px" class="demo-ruleForm" v-loading="formLoading">
        <el-button type="text" @click="addGivingProduct">添加年限价格</el-button>

        <el-form-item v-for="(item,index) in list" :key="index" label="年限：">
          <el-input-number v-model="item.year" size="mini" :min="1" :max="10" label="年" />
          <span style="margin-left: 20px">价格：</span>
          <el-input v-model="item.price" style="width: 180px" type="number" placeholder="请输入价格" />

          <el-button type="text" style="color: red;margin-left: 40px" @click="deleteGivingProduct(item)">删除</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { yearPrice, yearPriceUpdate } from '@/api/agent/product'
export default {
  name: 'YearPrice',
  data() {
    return {
      id: '',
      formLoading: false,
      numberValidateForm: {},
      list: []
    }
  },
  created() {
    this.id = this.$route.query.id
    this.getList()
  },
  methods: {
    getList() {
      this.formLoading = true
      yearPrice({ id: this.id }).then(res => {
        this.formLoading = false
        if (res.code === 200) {
          this.list = res.data
        }
      }).catch(() => {
        this.formLoading = false
      })
    },
    // 添加赠送产品
    addGivingProduct() {
      this.list.push({ year: '1', price: '' })
    },
    // 删除赠送产品
    deleteGivingProduct(product) {
      for (var i = 0; i < this.list.length; i++) {
        var item = this.list[i]
        if (item.id === product.id) {
          this.list.splice(i, 1)
          break
        }
      }
    },
    save() {
      var arr = this.validate()
      if (arr === false) {
        return
      }
      if (this.refrain(arr)) {
        this.$message.error('检查年限重复')
      } else {
        this.formLoading = true
        yearPriceUpdate({ id: this.id, list: this.list }).then(res => {
          this.formLoading = false
          this.$message.success('更新成功')
          this.$router.go(-1)
        }).catch(() => {
          this.formLoading = false
        })
      }
    },
    validate() {
      var arr = []
      for (var i = 0; i < this.list.length; i++) {
        var item = this.list[i]
        if (item.year < 1) {
          this.$message.error('请输入年限')
          return false
        }
        if (item.price < 1) {
          this.$message.error('请输入价格')
          return false
        }
        arr.push(item.year)
      }

      return arr
    },
    refrain(arr) {
      var tmp = []
      if (Array.isArray(arr)) {
        arr.concat().sort().sort(function(a, b) {
          if (a === b && tmp.indexOf(a) === -1) tmp.push(a)
        })
      }
      return tmp.length > 0
    }
  }
}
</script>

<style scoped>
  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }
</style>
