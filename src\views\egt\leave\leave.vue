<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import { delLeaveApi, getLeaveListApi } from '@/api/egt/leave'

export default {
  name: 'Leave',
  components: { StatisticsTemplate },
  filters: {
    formatDate(value) {
      if (value) {
        return new Date(value * 1000).toLocaleString()
      }
      return ''
    }
  },
  data() {
    return {
      config: {
        key: 'leave',
        tableSettings: {
          api: getLeaveListApi,
          field: {
            page: 'pageIndex',
            limit: 'pageSize',
            total: 'total'
          },
          columns: [
            {
              label: 'ID',
              prop: 'id',
              width: 60
            },
            {
              label: '姓名',
              prop: 'name',
              width: 100
            },
            {
              label: '邮箱',
              prop: 'email'
            },
            {
              label: '电话',
              prop: 'telephone'
            },
            {
              label: '留言内容',
              prop: 'content'
            },
            {
              label: '留言时间',
              prop: 'create_time',
              width: 180,
              isSlot: true
            },
            {
              label: '渠道',
              prop: 'remarks'
            },
            {
              label: '操作',
              prop: 'action',
              width: 120,
              isSlot: true,
              fixed: 'right',
              align: 'center'
            }
          ]
        }
      }
    }
  },
  methods: {
    getStatus(row) {
      const status = {
        0: { text: '待处理', color: 'warning' },
        1: { text: '已处理', color: 'success' },
        2: { text: '已驳回', color: 'danger' }
      }
      return status[row.status]
    },
    async handleDelete(row) {
      if (!row.id) return
      try {
        this.$refs.listWrapRef.setLoading(row.id, true)
        const res = await delLeaveApi({ id: row.id })
        if (res.code === 200) {
          this.$message.success('删除成功')
          await this.$refs.listWrapRef.handleGetData()
        } else {
          this.$message.error(res.msg || res.message || '删除失败')
        }
      } catch (e) {
        console.log(e)
        this.$message.error('删除失败')
      } finally {
        this.$refs.listWrapRef.setLoading(row.id, false)
      }
    }
  }
}
</script>

<template>
  <div class="page-wrap">
    <statistics-template ref="listWrapRef" :config="config">
      <template v-slot:leave_create_time="{row}">
        <div>
          {{ row.create_time | formatDate }}
        </div>
      </template>
      <template v-slot:leave_action="{row}">
        <el-popconfirm
          title="确定删除吗？"
          @onConfirm="handleDelete(row)"
        >
          <el-button slot="reference" type="danger" size="mini">删除</el-button>
        </el-popconfirm>
      </template>
    </statistics-template>
  </div>
</template>

<style scoped lang="scss">
.page-wrap{
  height: 100%;
}
</style>
