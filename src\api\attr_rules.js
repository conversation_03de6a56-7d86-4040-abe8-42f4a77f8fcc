import request from '@/utils/request'

export function attrs_rule_list(data) {
  return request({
    url: '/activity/attrs_rule_list',
    method: 'POST',
    data
  })
}

export function attrs_rule_store(data) {
  return request({
    url: '/activity/attrs_rule_store',
    method: 'POST',
    data
  })
}

export function attrs_rule_delete(data) {
  return request({
    url: '/activity/attrs_rule_delete',
    method: 'POST',
    data
  })
}

