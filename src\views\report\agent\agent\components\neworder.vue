<template>
  <el-card class="box-card">
    <div class="header">
      <el-row >
        <el-col :xs="8" :sm="8" :lg="8" >
          <p>当月新增订单：<span style="color:#0e90d2">{{ num }}</span>个</p>
        </el-col>
        <el-col :xs="8" :sm="8" :lg="8" >
          <p>当月开通金额：<span style="color:#0e90d2">{{ money }}</span>元</p>
        </el-col>
        <el-col :xs="8" :sm="8" :lg="8" >
          <p style="text-align: right">
             <el-link type="primary"  @click="experience()" style="font-size: 16px; text-decoration: underline;">订单列表</el-link>
          </p>
        </el-col>
      </el-row>
    </div>
    <div  class="view-body1" >
      <table border="1">
        <tr>
          <th >客户名称</th>
          <th>产品名称</th>
          <th>开通时间</th>
          <th>价格（元）</th>
        </tr>
        <tr v-for="(item,index) in lists" :key="index">
          <td >{{ item.name  }}</td>
          <td>{{ item.product }}</td>
          <td>{{ item.created_at }}</td>
          <td>{{ item.price }}</td>
        </tr>
      </table>
      <pagination
        v-show="searchForm.total>0"
        :total="searchForm.total"
        :page.sync="searchForm.page"
        :limit.sync="searchForm.perPage"
        :autoScroll=false
        layout="total, prev, pager, next, jumper"
        style="text-align:center;"
        @pagination="getRechargedata"
      />
      <div v-if="lists.length===0" class="null">暂无数据</div>
    </div>

  </el-card>
</template>

<script>
import Pagination from '@/components/Pagination'
import { neworder } from '@/api/agent/dashboard'
import { mapGetters } from 'vuex'
export default {
  components: { Pagination },
  name: 'Agentchong',
  props: {
    allData: {
      type: Object,
      default() {
        return {}
      }
    },
    loading: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  data() {
    return {
      mapLoading: false,
      searchForm: {
        status: '',
        page: 1,
        perPage: 5,
        total: 0,
        is_buy: false
      },
      lists: [],
      money: 0,
      num: 0,
      chartLine: null,
      dataSource: [],
      legend: ['今日', '前一日'],
      trend_keys: [],
      trend_data1: [],
      trend_data2: []
    }
  },
  watch: {
    allData(value) {
      this.allData = value

      this.legend = ['今日']
      // 今日
      // this.trend_data1 = this.allData.map_today
      // this.trend_data2 = this.allData.map_last_day// 前一日
    }
  },
  computed: {
    ...mapGetters([
      'type'
    ])
  },
  created() {
    // for (var i = 0; i < 24; i++) {
    //   this.trend_keys.push(i)
    // }
  },
  mounted() {
    this.init()
    this.getActive()
  },
  methods: {
    experience() {
      this.$router.push({
        name: 'ProductListRouter',
        query: {
        }
      })
    },
    getActive() {
      // 产品活跃度排行
      this.$emit('getActive')
    },
    async init() {
      this.getRechargedata()
    },
    async getRechargedata() {
      this.mapLoading = true
      await neworder({ date: this.value2, page: this.searchForm.page }).then(response => {
        // this.mapLoading = false
        this.lists = response.data.list.data
        this.searchForm.total = response.data.list.total
        this.num = response.data.list.total
        this.money = response.data.money
        // this.allData = response.data
        // this.lists = this.allData.data
      }).catch(error => {
        this.mapLoading = false
        console.log(error)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.box-card {
  width: 100%;
  background-color: #ffffff;
  margin-top: 20px;
  .show{
    display: block;
  }
  .hide{
    display: none;
  }
  .header {
     p{
      font-size: 16px;
      font-weight: bold;
    }
    .btn-day{
      flex: 1;
      margin-left: 15px;
      text-align: right;
      span{
        margin: 0 5px;
        padding: 8px 15px;
        font-size: 10px;
        cursor: pointer;
        border:1px solid #eaeaec;
        border-radius: 5px;
      }
      .sel{
        background-color: #0e90d2;
        color: #ffffff;
      }
    }
  }
  .filter-header{
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .right{
      span{
        padding: 10px 25px;
        font-size: 10px;
        cursor: pointer;
        color: #0e90d2;
        border: 1px solid #0e90d2;
      }
      .sel{
        background-color: #0e90d2;
        color: #ffffff;
      }
    }
  }
  .view-body{
    display: flex;
    margin-top: 28px;
    .left{
      flex: 1;
      .dlsbtn{
        border:1px solid #eaeaec;
        border-radius: 10px;
        margin-bottom: 20px;
      }
      .dlsbtn:hover{
        background-color: #f0f2f5;
        border-color: #f0f2f5;
      }
    }
    .right{
      position: relative;
      width: 75%;
      //height: 360px;
      .null{
        position: absolute;
        top: 50px;
        width: 100%;
        line-height: 200px;
        text-align: center;
        color: #8c939d;
        font-size: 30px;
        font-weight: bold;
      }
    }
  }

  .view-body1{
    margin-top: 28px;
    min-height: 335px;
      position: relative;
     table{
        display:inline-block;
        width: 100%;
        //height: 360px;
        border: 0 solid transparent;
        tr{
          height: 40px;
          th{
            width: 191px;
          }
          td{
            font-size: 14px;
            text-align: center;
          }
        }
        tr:nth-child(1){
          background-color: #eff4f7;
          th{
            color: #3c3c3c;
            font-weight: 100;
            font-size: 14px;
          }
        }
        tr:nth-child(2n){
          background-color: #f9f9f9;
        }
      }
    .pagination-container{
      width: 100%;
      position: absolute;
      bottom: 0px;
      margin: 0px;
      padding:22px 0;
    }
  }
  .cate{
    float: left;
    width: 227px;
    margin-left: 20px;
    span{
      margin-right: 10px;
      font-size: 16px;
    }
    ::v-deep.el-checkbox__label{
      font-size: 16px;
    }
  }
}
</style>
