<template>
  <div class="app-container" style="height: 100%;padding-bottom: 0;">
    <div class="list-wrap">
      <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
          height="calc(100% - 96px)"
      >
        <el-table-column
            prop="id"
            label="ID"
            width="180"
        />
        <el-table-column label="类型" width="180">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.type === 1" size="small" type="success" effect="dark">福利币</el-tag>
            <el-tag v-if="scope.row.type === 2" size="small" type="danger" effect="dark">福利劵</el-tag>
          </template>
        </el-table-column>

        <el-table-column
            prop="virtual"
            label="金额"
            width="180"
            ss
        />
        <el-table-column
            prop="price"
            label="实际金额"
        />
        <el-table-column
            prop="scale"
            label="折扣"
        />

        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-link :underline="false" type="primary" @click="editView(scope.row)">编辑</el-link>
            <el-popconfirm
                title="确定删除当前配置吗？"
                @onConfirm="deleteHandle(scope.row)"
            >
              <el-link slot="reference" :underline="false" type="danger">删除</el-link>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>
    <!--    表单-->
    <el-dialog
        title="配置管理"
        :visible.sync="dialogVisible"
        width="50%"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="类型：" prop="type">
          <el-select v-model="ruleForm.type" placeholder="请选择">
            <el-option label="福利币" :value="1" />
            <el-option label="福利券" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="虚拟货币:" prop="virtual">
          <el-input-number v-model="ruleForm.virtual" :precision="2" :step="1" :min="1" :max="999999999" @change="to_calculate_input" />
        </el-form-item>
        <el-form-item label="折扣:" prop="scale">
          <el-slider
              v-model.Number="ruleForm.scale"
              show-input
              :min="0"
              :max="10"
              @input="to_calculate"
          />
        </el-form-item>
        <el-form-item label="折后价格:" prop="price">
          <el-input-number v-model="ruleForm.price" :precision="2" :step="1" :min="1" :max="999999999" :disabled="true" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" :loading="b_loading" @click="saveHandle">确 定</el-button>
        </span>
    </el-dialog>
  </div>
</template>

<script>
import { getConfig, store, destroy } from '../../api/shop_wallet_config'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
export default {
  name: 'WalletConfig',
  components: { Pagination },
  data() {
    return {
      b_loading: false,
      dialogVisible: false,
      loading: false,
      tableData: [],
      ruleForm: {},
      rules: {
        virtual: [
          { required: true, message: '虚拟货币金额必填', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '类型必选', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '折后金额必填', trigger: 'blur' }
        ],
        scale: [
          { required: true, message: '折扣额度必选', trigger: 'blur' }
        ]
      },
      listQuery: {
        page: 1,
        limit: 10
      },
      total: 1
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      getConfig(this.listQuery).then(response => {
        this.tableData = response.data.data
        this.total = response.data.total
        this.loading = false
      })
    },
    createView() {
      this.dialogVisible = true
      this.b_loading = false
      this.ruleForm = {
        virtual: 0,
        price: 0,
        scale: 10
      }
    },
    saveHandle() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.b_loading = true
          store(this.ruleForm).then(response => {
            if (response.code === 200) {
              this.dialogVisible = false
              this.b_loading = false
              this.$message.success('保存成功')
              this.getList()
            }
          })
        } else {
          console.log('error submit!!')
          this.b_loading = false
          return false
        }
      })
    },
    editView(row) {
      this.ruleForm = row
      this.ruleForm.scale = parseFloat(row.scale)
      this.dialogVisible = true
    },
    deleteHandle(row) {
      destroy({ id: row.id }).then(response => {
        this.$message.success('删除成功')
        this.getList()
      })
    },
    to_calculate_input(value, old) {
      this.ruleForm.price = value * this.ruleForm.scale / 10
    },
    to_calculate(value) {
      this.ruleForm.price = this.ruleForm.virtual * value / 10
    }
  }
}
</script>

<style scoped lang="scss">
.card-wrap ::v-deep .el-card__body {
  height: 100%;
}
.list-wrap {
  height: 100%;

  ::v-deep .card-wrap {
    height: 100%;
    border: none;

    .el-card__body {
      height: 100%;
      padding: 0;
    }

    .box-card {
      height: 100%;
      border: none;

      .el-card__body {
        height: calc(100% - 59px);
        overflow: auto;
      }
    }
  }
}
</style>
