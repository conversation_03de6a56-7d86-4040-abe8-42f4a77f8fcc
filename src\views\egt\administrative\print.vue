<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import PrintUpdate from './components/print/PrintUpdate.vue'
import { deletePrintApi, getPrintListApi } from '@/api/egt/print'

export default {
  name: 'Print',
  components: { StatisticsTemplate, PrintUpdate },
  data() {
    return {
      config: {
        key: 'print',
        tableSettings: {
          api: getPrintListApi,
          field: {
            page: 'pageIndex',
            limit: 'pageSize',
            total: 'total'
          },
          columns: [
            {
              label: 'ID',
              prop: 'id',
              width: 60
            },
            {
              prop: 'title',
              label: '文件名称'
            },
            {
              prop: 'action',
              label: '操作',
              align: 'center',
              isSlot: true,
              width: 200
            }
          ]
        }
      },
      updateVisible: false,
      row: {}
    }
  },
  methods: {
    handleAdd() {
      this.updateVisible = true
      this.row = {}
    },
    handleUpdate(row) {
      this.updateVisible = true
      this.row = row
    },
    async handleDelete(row) {
      if (!row.id) return
      try {
        this.$refs.listWrapRef.setLoading(row, true)
        const res = await deletePrintApi({ id: row.id })
        if (res.code === 200) {
          this.$message.success('删除成功')
          this.$refs.listWrapRef.handleGetData()
        } else {
          this.$message.error(res.msg || res.message || '删除失败')
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.$refs.listWrapRef.setLoading(row, false)
      }
    },
    toCate() {
      this.$router.push({
        path: '/egt/administrative/printCate'
      })
    },
    handleSubmitSuccess() {
      this.$refs.listWrapRef.handleGetData()
    }
  }
}
</script>

<template>
  <div class="page-wrap">
    <statistics-template ref="listWrapRef" :config="config" self-key="print">
      <template #topActions>
        <div style="text-align: right;">
          <el-button style="margin-right: 20px;" @click="toCate">文件类型</el-button>
          <el-button type="primary" @click="handleAdd">添加</el-button>
        </div>
      </template>
      <template #print_action="{row}">
        <el-button size="mini" @click="handleUpdate(row)">编辑</el-button>
        <el-popconfirm
          title="确定删除吗？"
          @onConfirm="handleDelete(row)"
        >
          <el-button slot="reference" type="danger" size="mini">删除</el-button>
        </el-popconfirm>
      </template>
    </statistics-template>
    <PrintUpdate :visible.sync="updateVisible" :data="row" @submit-success="handleSubmitSuccess" />
  </div>
</template>

<style lang="scss" scoped>
.page-wrap{
  height: calc(100% - 20px);
}
</style>
