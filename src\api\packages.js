import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/packages/list',
    method: 'POST',
    data
  })
}

export function store(data) {
  return request({
    url: '/packages/store',
    method: 'POST',
    data
  })
}

export function update(data) {
  return request({
    url: '/packages/update',
    method: 'POST',
    data
  })
}

export function deletePackage(data) {
  return request({
    url: '/packages/delete',
    method: 'POST',
    data
  })
}
