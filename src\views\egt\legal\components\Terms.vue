<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import TermsUpdate from '@/views/egt/legal/components/TermsUpdate.vue'
import OptionsUpdate from '@/views/egt/legal/components/OptionsUpdate.vue'
import List from '@/views/egt/legal/components/List.vue'
import { deleteContractClauseApi, getContractClauseListApi } from '@/api/egt/contract'

export default {
  name: 'Terms',
  components: { List, OptionsUpdate, TermsUpdate, StatisticsTemplate },
  filters: {
    formatDate(value) {
      if (value) {
        return new Date(value * 1000).toLocaleString()
      }
      return ''
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      updateVisible: false,
      row: {},
      optionsVisible: false,
      listVisible: false,
      optionsDialogType: 'add'
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    config() {
      return {
        key: 'terms',
        tableSettings: {
          index: true,
          api: getContractClauseListApi,
          params: {
            id: this.data.id
          },
          field: {
            page: 'pageIndex',
            limit: 'pageSize',
            total: 'total'
          },
          columns: [
            {
              label: '合同条款名称',
              prop: 'title'
            },
            {
              label: '添加时间',
              prop: 'add_time',
              isSlot: true
            },
            {
              label: '操作',
              prop: 'action',
              width: 400,
              fixed: 'right',
              align: 'center',
              isSlot: true
            }
          ]
        }
      }
    }
  },
  watch: {
    _visible(val) {
      if (val) {
        this.$nextTick(() => {
          if (this.$refs.termListRef) {
            this.$refs.termListRef.$refs.tableRef.doLayout()
          }
        })
      }
    }
  },
  methods: {
    handleAdd() {
      this.updateVisible = true
      this.row = {
        model_id: this.data.id
      }
    },
    handleUpdate(row) {
      this.updateVisible = true
      this.row = row
      this.optionsDialogType = 'update'
      this.row.model_id = this.data.id
    },
    async handleDelete(row) {
      if (!row.id) return
      if (!this.data.id) return
      try {
        this.$refs.termListRef.setLoading(row, true)
        const res = await deleteContractClauseApi({ id: row.id })
        if (res.code === 200) {
          this.$message.success('删除成功')
          await this.$refs.termListRef.handleGetData()
        } else {
          this.$message.error(res.msg || res.message || '删除失败')
        }
      } catch (error) {
        console.log(error)
        this.$message.error('删除失败')
      } finally {
        this.$refs.termListRef.setLoading(row, false)
      }
    },
    handleAddOptions(row) {
      this.optionsDialogType = 'add'
      this.row = row
      this.optionsVisible = true
    },
    handleList(row) {
      this.row = row
      this.listVisible = true
    },
    handleSubmitSuccess() {
      this.row = {}
      this.$refs.termListRef.handleGetData()
    },
    handleOptionsSubmitSuccess() {
      this.row = {}
      this.$refs.termListRef.handleGetData()
    }
  }
}
</script>

<template>
  <el-dialog :close-on-click-modal="false" :visible.sync="_visible" title="编辑合同条款" width="80vw">
    <StatisticsTemplate ref="termListRef" :config="config" style="height: 500px">
      <template v-slot:topActions>
        <el-button type="primary" @click="handleAdd()">添加合同条款</el-button>
      </template>
      <template v-slot:terms_add_time="{row}">
        {{ row.add_time | formatDate }}
      </template>
      <template v-slot:terms_action="{row}">
        <el-button type="primary" size="mini" @click="handleAddOptions(row)">添加选项</el-button>
        <el-button size="mini" @click="handleList(row)">选项列表</el-button>
        <el-button size="mini" @click="handleUpdate(row)">修改</el-button>
        <el-popconfirm
          title="确定删除吗？"
          style="margin-left: 10px;"
          @onConfirm="handleDelete(row)"
        >
          <el-button slot="reference" type="danger" size="mini">删除</el-button>
        </el-popconfirm>
      </template>
    </StatisticsTemplate>
    <TermsUpdate :visible.sync="updateVisible" :data="row" @submit-success="handleSubmitSuccess" />
    <OptionsUpdate :visible.sync="optionsVisible" :data="row" :dialog-type="optionsDialogType" @submit-success="handleOptionsSubmitSuccess" />
    <List :visible.sync="listVisible" :data="row" />
  </el-dialog>
</template>

<style scoped lang="scss">
</style>
