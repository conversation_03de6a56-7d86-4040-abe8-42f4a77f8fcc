<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>规则管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="createView">添加规则</el-button>
      </div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="65"
        />
        <el-table-column
          prop="name"
          label="规则名称"
        />
        <el-table-column label="状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status" type="success" effect="dark" size="small">启用</el-tag>
            <el-tag v-else type="danger" effect="dark" size="small">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-link :underline="false" type="primary" @click="editDialog(scope.row)">编辑</el-link>
            <el-popconfirm
              title="确定删除当前记录吗？"
              @onConfirm="deleteHandle(scope.row)"
            >
              <el-link slot="reference" :underline="false" type="danger">删除</el-link>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!--  表单  -->
    <el-dialog
      title="规则管理"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="规则名称：" prop="name">
          <el-input v-model="ruleForm.name" />
        </el-form-item>
        <el-form-item label="规则内容：" prop="content">
          待完善此处根据规则类型生成规则内容填写方式
        </el-form-item>
        <el-form-item label="状态开关：" prop="status">
          <el-col :span="20">
            <el-switch
              v-model="ruleForm.status"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
            />
          </el-col>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="saveHandle">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import { attrs_rule_list, attrs_rule_store, attrs_rule_delete } from '../../api/attr_rules'
export default {
  name: 'Rules',
  data() {
    return {
      dialogVisible: false,
      tableLoading: false,
      loading: false,
      tableData: [],
      ruleForm: {},
      rules: {
        name: [
          { required: true, message: '规则名称必须填写', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getRuleList()
  },
  methods: {
    getRuleList() {
      this.tableLoading = true
      attrs_rule_list(this.$route.query).then(response => {
        this.tableData = response.data
        this.tableLoading = false
      })
    },
    createView() {
      this.ruleForm = {
        status: 1
      }
      this.dialogVisible = true
    },
    editDialog(row) {
      this.ruleForm = row
      this.dialogVisible = true
    },
    saveHandle() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.loading = true
          this.ruleForm.attr_id = this.$route.query.id
          attrs_rule_store(this.ruleForm).then(response => {
            this.dialogVisible = false
            this.getRuleList()
            this.loading = false
          }).catch(() => {
            this.loading = false
          })
        }
      })
    },
    deleteHandle(row) {
      attrs_rule_delete(row).then(response => {
        this.$message.success('删除成功')
        this.getRuleList()
      })
    }
  }
}
</script>

<style scoped>

</style>
