export default {
  methods: {
    /**
     * 表单提交
     * @param {String} formName 表单名称
     * @param {Function} successCallback 验证成功回调
     * @param {Function} errorCallback 验证失败回调
     */
    validateForm(formName, successCallback, errorCallback = null) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (successCallback) {
            successCallback()
          }
        } else {
          console.log('error submit!!')
          if (errorCallback) {
            errorCallback()
          }
          return false
        }
      })
    },
    /**
     *  重置表单
     * @param {String} formName 表单名称
     * @param {Boolean} refresh 是否刷新父页面
     * @param close
     */
    resetForm(formName, refresh = false, close = true) {
      this.$refs[formName].resetFields()
      if (close) {
        this.$listeners.close(refresh)
      }
    }
  }
}
