<template>
  <el-dialog :close-on-click-modal="false" :title="form.id?'修改分类':'新增分类'" :visible.sync="dialogVisible">
    <el-form ref="ruleForm" :model="formData" :rules="rules" label-width="140px" class="demo-ruleForm">
      <el-form-item label="分类名称:" prop="name" label-width="140px">
        <el-col :span="10">
          <el-input v-model="formData.name" />
        </el-col>
      </el-form-item>
      <el-form-item label="父级分类:" prop="pid">
        <el-select v-model="formData.pid" placeholder="请选择">
          <el-option label="顶级分类" :value="0" />
          <el-option
            v-for="app in apps"
            :key="app.id"
            :label="app.name"
            :value="app.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="排序:" prop="level">
        <el-input-number v-model="formData.level" :min="0" :max="999" label="数字越小越靠前" />
      </el-form-item>
      <el-form-item label="状态:" prop="status">
        <el-select v-model="formData.status" placeholder="请选择">
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="small" @click="commit">保存</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import { topList } from '@/api/news_category'
export default {
  name: 'CateForm',
  props: {
    title: {
      type: String,
      default: ''
    },
    form: {
      type: Object,
      default: () => {}
    },
    dialogVisible: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      apps: [],
      formData: this.form,
      rules: {
        name: [
          { required: true, message: '请输产品名称', trigger: 'blur' }
        ],
        pid: [
          { required: true, message: '请选择分类', trigger: 'blur' }
        ],
        level: [
          { required: true, message: '请填写级别', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'blur' }
        ]
      }
    }
  },

  computed: {
    ...mapGetters([
      'token'
    ])
  },
  watch: {
    form: {
      handler(newValue, oldValue) {
        this.formData = newValue
      }
    }
  },
  created() {
    this.getCateList()
  },
  methods: {
    getCateList() {
      topList().then(response => {
        this.apps = response.data
      })
    },
    commit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.dialogVisible = false
          this.$emit('transfer', this.formData)
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style>
    .app-upload .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .app-upload .avatar-uploader .el-upload:hover {
        border-color: #409EFF;
    }

    .app-upload .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 100px;
        height: 100px;
        line-height: 100px;
        text-align: center;
    }

    .app-upload .avatar {
        width: 100px;
        height: 100px;
        display: block;
    }
</style>
