<script>
import SelfInput from '@/views/task/components/form/SelfInput.vue'
import SelfSelect from '@/views/task/components/form/SelfSelect.vue'
import SelfDatePicker from '@/views/task/components/form/SelfDatePicker.vue'
import SelfCascader from '@/views/task/components/form/SelfCascader.vue'
import SelfTextarea from '@/views/task/components/form/SelfTextarea.vue'
import SelfUpload from './SelfUpload.vue'
import SelfNumber from '@/views/task/components/form/SelfNumber.vue'
import SelfSwitch from '@/views/task/components/form/SelfSwitch.vue'
import Editor from '@/components/editor/index.vue'
import SelfRadio from './SelfRadio.vue'

export default {
  name: 'SelfFormTemp',
  components: {
    Editor
  },
  props: {
    formData: {
      type: Object,
      default: () => {}
    },
    filters: {
      type: Array,
      default: () => []
    },
    selfKey: {
      type: String,
      default: ''
    },
    inline: {
      type: <PERSON><PERSON><PERSON>,
      default: true
    },
    labelWidth: {
      type: String,
      default: ''
    },
    rules: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      filterComponents: {
        'input': SelfInput,
        'select': SelfSelect,
        'datePicker': SelfDatePicker,
        'cascader': SelfCascader,
        'textarea': SelfTextarea,
        'upload': SelfUpload,
        'number': SelfNumber,
        'switch': SelfSwitch,
        'radio': SelfRadio
      },
      displayType: ['text', 'img', 'editor']
    }
  },
  computed: {
    filter: {
      get() {
        return this.formData
      },
      set(val) {
        this.$emit('update:formData', val)
      }
    }
  },
  methods: {
    isDisplay(type) {
      return this.displayType.includes(type)
    },
    handleRemove(file, fileList, prop) {
      this.$emit('remove', { file, fileList, prop })
    },
    handlePreview(file, prop) {
      this.$emit('preview', { file, prop })
    },
    handleSuccess(response, file, fileList, prop) {
      this.$emit('success', { response, file, fileList, prop })
    },
    handleBeforeUpload(file, prop) {
      this.$emit('beforeUpload', file, prop)
    },
    //   判断是否是数组
    isArray(obj) {
      return Object.prototype.toString.call(obj) === '[object Array]'
    }
  }
}
</script>

<template>
  <el-form ref="filterRef" :model="filter" :inline="inline" :label-width="labelWidth" :rules="rules">
    <template v-for="(item, index) in filters">
      <el-form-item v-if="!isDisplay(item.type)" :key="index" :label="item.label" :prop="item.prop" :class="item.settings ? item.settings.class : ''">
        <template v-if="item.type !== 'slot'">
          <component
            :is="filterComponents[item.type]"
            :self-key="selfKey"
            :value.sync="filter[item.prop]"
            :options="item.settings ? item.settings.options : []"
            :option-params="item.settings ? item.settings.optionParams : {}"
            :format-options="item.settings ? item.settings.formatOptions : undefined"
            :props="item.settings ? item.settings.props : {}"
            :placeholder="item.settings ? (item.settings.placeholder ? item.settings.placeholder: '请填写' + item.label) : '请填写' + item.label"
            :multiple="item.settings && item.settings.multiple"
            :type="item.settings ? item.settings.type : ''"
            :value-format="item.settings ? item.settings.valueFormat : 'yyyy-MM-dd'"
            :format="item.settings ? item.settings.format : 'yyyy-MM-dd'"
            :clearable="true"
            :autosize="item.settings ? item.settings.autosize : { minRows: 2, maxRows: 6 }"
            :action="item.settings? item.settings.action : ''"
            :max-size="item.settings? item.settings.maxSize : 2"
            :accept="item.settings? item.settings.accept : '*'"
            :limit="item.settings? item.settings.limit : 1"
            :active-color="item.settings? item.settings.activeColor : '#409eff'"
            :inactive-color="item.settings? item.settings.inactiveColor : '#c0c4cc'"
            :active-value="item.settings? item.settings.activeValue : 1"
            :inactive-value="item.settings? item.settings.inactiveValue : 0"
            :max="item.settings? item.settings.max : 100"
            :min="item.settings? item.settings.min : 0"
            :disabled="item.settings? item.settings.disabled : false"
            @preview="(file) => {handlePreview(file, item.prop)}"
            @remove="(file, fileList) => {handleRemove(file, fileList, item.prop)}"
            @success="(response, file, fileList) => {handleSuccess(response, file, fileList, item.prop)}"
            @beforeUpload="file => (handleBeforeUpload(file))"
            @change="(val) => {$emit('change', val, item.prop)}"
          />
        </template>
        <template v-else>
          <slot :name="item.prop" />
        </template>
      </el-form-item>
      <el-form-item v-else :key="index" :label="item.label">
        <span v-if="item.type === 'text'">{{ filter[item.prop] }}</span>
        <div v-if="item.type === 'img'">
          <template v-if="isArray(filter[item.prop])">
            <el-image v-for="(img, imgIndex) in filter[item.prop]" :key="imgIndex" :src="img" fit="contain" style="width: 100px; height: 100px;" :preview-src-list="filter[item.prop]" />
          </template>
          <template v-else>
            <el-image :src="filter[item.prop]" fit="contain" style="width: 100px; height: 100px;" :preview-src-list="[filter[item.prop]]" />
          </template>
        </div>
        <div v-if="item.type === 'editor'">
          <Editor :content.sync="filter[item.prop]" />
        </div>
      </el-form-item>
    </template>
    <slot />
  </el-form>
</template>

<style scoped>

</style>
