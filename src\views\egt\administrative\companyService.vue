<script>
import Layout from '@/views/egt/hr/components/Layout.vue'
import Settings from '@/views/egt/administrative/components/companyService/Settings.vue'
import List from '@/views/egt/administrative/components/companyService/List.vue'

export default {
  name: 'Recruitment',
  components: { Layout },
  data() {
    return {
      activeBtn: '1',
      btns: [
        { value: '1', label: '待办业务设置', component: Settings },
        { value: '2', label: '待办列表', component: List }
      ]
    }
  }
}
</script>

<template>
  <Layout :btns="btns" :active.sync="activeBtn" />
</template>

<style scoped lang="scss">

</style>
