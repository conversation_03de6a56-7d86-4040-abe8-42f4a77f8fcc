<script>
import { getCompanyServiceListApi } from '@/api/egt/companyService'
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'

export default {
  name: 'CompanyServiceList',
  components: { StatisticsTemplate },
  data() {
    return {
      config: {
        key: 'company_service',
        tableSettings: {
          api: getCompanyServiceListApi,
          field: {
            page: 'pageIndex',
            limit: 'pageSize',
            total: 'total'
          },
          columns: [
            {
              prop: 'id',
              label: 'ID',
              width: 60
            },
            {
              prop: 'compant_name',
              label: '公司名称'
            },
            {
              prop: 'compant_phone',
              label: '联系电话'
            },
            {
              prop: 'compant_mobile',
              label: '联系电话'
            },
            {
              prop: 'agency_title',
              label: '代办业务'
            },
            {
              prop: 'addtime',
              label: '申请时间'
            }
          ]
        }
      }
    }
  }
}
</script>

<template>
  <div class="page-wrap mt20">
    <statistics-template :config="config" self-key="company_service" />
  </div>
</template>

<style lang="scss" scoped>
.page-wrap{
  height: calc(100% - 20px);
}
</style>
