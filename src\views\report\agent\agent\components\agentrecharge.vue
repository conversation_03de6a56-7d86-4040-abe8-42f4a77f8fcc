<template>
  <AgentCard title="产品开通数据" :statistics="statistics" :charge-radio="chargeRadio" :color="{card: '#f6f0ff'}" :form.sync="searchForm" :table-data="lists" class="box-card" :date.sync="value2" :map-loading="mapLoading" @get-active="getActive">
    <template v-slot:right>
      <div style="display: flex; align-items: center;">
        <div class="btn-day">
          <el-radio-group v-model="indexOfType">
            <el-radio-button v-for="charge in chargeRadio" :key="charge.value" :label="charge.value">{{ charge.label
            }}</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </template>
    <template v-slot:chart>
      <div id="qushichartMap" style="width: 100%;height: 334px;" />
    </template>
    <template v-slot:table>
      <table border="1">
        <tr>
          <th>排名</th>
          <th>产品名称</th>
          <th>开通金额</th>
          <th>订单量</th>
        </tr>
        <tr v-for="(item,index) in lists" :key="index">
          <td>{{ index+1 }}</td>
          <td>{{ item.name }}</td>
          <td>{{ item.price }}</td>
          <td>{{ item.num }}</td>
        </tr>
      </table>
    </template>
  </AgentCard>
</template>

<script>
import { authfinorder } from '@/api/agent/dashboard'
import AgentCard from './AgentCard.vue'
import * as echarts from 'echarts'
require('echarts/theme/macarons')
require('echarts/extension/bmap/bmap')
export default {
  name: 'Agentrecharge',
  components: { AgentCard },
  data() {
    return {
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      value2: '',
      chartMap: null,
      mapLoading: false,
      chargeRadio: [
        {
          label: '开通占比',
          value: 0
        },
        {
          label: '开通排名',
          value: 1
        }
      ],
      statistics: [
        {
          title: '产品累计开通金额',
          value: 0,
          img: 'https://scrm-api.china9.cn/web/images/ktje.png'
        }
      ],
      allData: {},
      searchForm: {
        status: '',
        page: 1,
        perPage: 5,
        total: 0,
        is_buy: false
      },
      lists: [],
      prolist: [],
      graph: {},
      legenddata: [],
      money: 0,
      max: 0,
      indexOfDate: 0,
      indexOfCate: 0,
      options_user: [
        // { name: '用户注册', key: 'user_register_number' },
        // { name: '用户登录', key: 'user_login_number' },s
        { name: 'IP访问量', key: 'user_visit_ip_number' },
        { name: '浏览次数', key: 'user_visit_all_number' },
        { name: '独立访问', key: 'user_visit_number' }
      ],
      options_agent: ['代理商注册', '代理商登录', 'IP访问量', '浏览次数', '独立访问'],
      days: 0,
      keys: 'user_visit_all_number',
      client: 'cloud'
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    getActive() {
      // 产品活跃度排行
      this.$emit('getActive')
    },
    async init() {
      this.mapLoading = true
      this.chartMap = echarts.init(document.getElementById('qushichartMap'))
      await this.getSystem()
    },
    selectDay(index) {
      this.indexOfDate = index
      this.days = [0, 1, 7, 30][index]
      this.getSystem()
    },
    changeDay() {
      this.getSystem()
    },
    selectType(e) {
      this.keys = e
      this.getSystem()
    },
    selectCate(index) {
      this.indexOfCate = index
      this.client = ['cloud', 'agent'][index]
      this.getSystem()
    },
    async getSystem() {
      this.mapLoading = true
      await authfinorder({ date: this.value2, page: this.searchForm.page }).then(response => {
        this.mapLoading = false
        this.allData = response.data
        this.lists = this.allData.prolist.data
        this.prolist = this.allData.list
        this.searchForm.total = this.allData.prolist.total
        this.statistics[0].value = +this.allData.money
        this.value2 = response.data.date
        this.updateMapData()
      }).catch(error => {
        this.mapLoading = false
        console.log(error)
      })
    },
    updateMapData() {
      var arr = []
      var arrcolor = [
        '#6C5CE7', '#A66EF6', '#D56EF6', // 紫色系
        '#00C2A8', '#00D4B1', '#2CD5C4', // 蓝绿系
        '#FF7675', '#FF9068', '#FFA658', // 橙红系
        '#00B4D8', '#48CAE4', '#90E0EF', // 蓝调系
        '#70E000', '#A3E000', '#D4E000' // 黄绿系
      ]
      var arrdata = []
      this.prolist.forEach(function(item, index) {
        arr.push({ 'name': item['name'], 'value': item['proportion'], itemStyle: { color: arrcolor[index] }})
        arrdata.push(item['name'])
      })
      this.graph = arr
      this.legenddata = arrdata
      // 最大值
      var temp_max = 0
      arr.forEach(function(item) {
        temp_max = item['value'] > temp_max ? item['value'] : temp_max
      })
      this.max = temp_max === 0 ? 20 : temp_max

      this.setOptions()
    },
    setOptions() {
      this.chartMap.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          right: '20%',
          bottom: 'auto',
          width: '20px',
          itemWidth: 7,
          itemHeight: 7,
          data: this.legenddata
        },
        grid: { top: 0 },
        series: [
          {
            name: '产品名称',
            type: 'pie',
            radius: '80%',
            center: ['20%', 'center'],
            data: this.graph,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              show: false,
              position: 'center'
            },
            labelLine: {
              show: false
            }
          }
        ]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
table {
      display: inline-block;
      width: 100%;
      //height: 360px;
      border: 0 solid transparent;

      tr {
        height: 40px;

        th {
          width: 191px;
        }

        th:nth-child(1) {
          width: 50px;
        }

        th:nth-child(3) {
          width: 335px;
        }

        td {
          font-size: 14px;
          text-align: center;
        }
      }

      tr:nth-child(1) {
        background-color: #eff4f7;

        th {
          color: #3c3c3c;
          font-weight: 100;
          font-size: 14px;
        }
      }

      tr:nth-child(2n) {
        background-color: #f9f9f9;
      }
    }
</style>
