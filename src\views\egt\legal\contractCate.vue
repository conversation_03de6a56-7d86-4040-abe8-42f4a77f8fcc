<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import ContractCateUpdate from '@/views/egt/legal/components/ContractCateUpdate.vue'
import { deleteContractCategoryApi, getContractCategoryListApi } from '@/api/egt/contract'

export default {
  name: 'ContractCate',
  components: { ContractCateUpdate, StatisticsTemplate },
  data() {
    return {
      config: {
        key: 'contractCate',
        tableSettings: {
          index: true,
          api: getContractCategoryListApi,
          field: {
            page: 'pageIndex',
            limit: 'pageSize',
            total: 'total'
          },
          columns: [
            {
              label: '分类名称',
              prop: 'cate_name'
            },
            {
              label: '添加时间',
              prop: 'add_time'
            },
            {
              label: '操作',
              prop: 'action',
              width: 180,
              fixed: 'right',
              align: 'center',
              isSlot: true
            }
          ]
        }
      },
      updateVisible: false,
      row: {}
    }
  },
  methods: {
    handleAdd() {
      this.updateVisible = true
      this.row = {}
    },
    handleUpdate(row) {
      this.updateVisible = true
      this.row = row
    },
    async handleDelete(row) {
      if (!row.id) return
      try {
        this.$refs.listWrapRef.setLoading(row, true)
        const res = await deleteContractCategoryApi({ id: row.id })
        if (res.code === 200) {
          this.$message.success('删除成功')
          this.$refs.listWrapRef.handleGetData()
        } else {
          this.$message.error(res.msg || res.message || '删除失败')
        }
      } catch (error) {
        this.$message.error('删除失败')
        console.log(error)
      } finally {
        this.$refs.listWrapRef.setLoading(row, false)
      }
    },
    handleSubmitSuccess() {
      this.$refs.listWrapRef.handleGetData()
    }
  }
}
</script>

<template>
  <div class="page-wrap">
    <StatisticsTemplate ref="listWrapRef" self-key="contract" :config="config">
      <template #topActions>
        <div style="text-align: right">
          <el-button type="primary" @click="handleAdd">添加合同分类</el-button>
        </div>
      </template>
      <template #contractCate_action="{row}">
        <el-button size="mini" @click="handleUpdate(row)">编辑</el-button>
        <el-popconfirm
          title="确定删除吗？"
          style="margin-left: 10px;"
          @onConfirm="handleDelete(row)"
        >
          <el-button slot="reference" type="danger" size="mini">删除</el-button>
        </el-popconfirm>
      </template>
    </StatisticsTemplate>
    <ContractCateUpdate :visible.sync="updateVisible" :data="row" @submit-success="handleSubmitSuccess" />
  </div>
</template>

<style lang="scss" scoped>
.page-wrap {
  height: calc(100% - 20px);
}
</style>
