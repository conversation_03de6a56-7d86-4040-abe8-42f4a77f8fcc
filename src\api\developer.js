import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/developer/list',
    method: 'POST',
    data: data
  })
}

export function store(data) {
  return request({
    url: '/developer/store',
    method: 'POST',
    data: data
  })
}

export function detail(data) {
  return request({
    url: '/developer/detail',
    method: 'POST',
    data: data
  })
}

export function destory(data) {
  return request({
    url: '/developer/destory',
    method: 'POST',
    data: data
  })
}
