import request from '@/utils/request'

export function getOrders(data) {
  return request({
    url: '/shop/orders/list',
    method: 'post',
    data
  })
}

export function store(data) {
  return request({
    url: '/shop/orders/store',
    method: 'post',
    data
  })
}
export function returnstore(data) {
  return request({
    url: '/shop/orders/returnstore',
    method: 'post',
    data
  })
}
export function destroy(data) {
  return request({
    url: '/shop/orders/delete',
    method: 'post',
    data
  })
}

export function detail(data) {
  return request({
    url: '/shop/orders/detail',
    method: 'post',
    data
  })
}

export function express(data) {
  return request({
    url: '/shop/orders/express',
    method: 'post',
    data
  })
}
