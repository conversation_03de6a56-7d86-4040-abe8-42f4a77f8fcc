<template>
  <div class="list-wrap">
    <el-card class="card-wrap" shadow="never" style="border: none">
      <!--<div slot="header" class="clearfix">
        <span>全域电商管理</span>
      </div>-->
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="传统电商" name="traditional">
          <tradition-view />
        </el-tab-pane>
        <el-tab-pane label="传统电商分类" name="traditionalCate">
          <tradition-cate />
        </el-tab-pane>
        <el-tab-pane label="私域、带货电商" name="private">
          <private-view />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import TraditionView from './components/Tradition.vue'
import TraditionCate from './components/tradition/cate.vue'
import PrivateView from './components/Private.vue'

export default {
  name: 'ECommerce',
  components: {
    TraditionView,
    TraditionCate,
    PrivateView
  },
  data() {
    return {
      activeName: 'traditional'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event)
    }
  }
}
</script>

<style scoped lang="scss">
  .list-wrap {
    height: 100%;

    ::v-deep .card-wrap {
      height: 100%;
      .el-card__body {
        height: 100%;
        padding: 0;
      }

      .box-card {
        height: 100%;
        border: none;

        .el-card__body {
          height: calc(100% - 59px);
          overflow: auto;
        }
      }
    }
  }
</style>
