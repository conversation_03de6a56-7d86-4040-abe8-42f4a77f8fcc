<script>
import { getHoliday<PERSON>pi, getLegalWorkdayApi, getWorkdayApi } from '@/api/egt/workday'
import Update from '@/views/egt/hr/components/workday/Update.vue'

export default {
  name: 'List',
  components: {
    Update
  },
  props: {
    // id 1 单休工作日 2 法定工作日 3 法定节假日
    id: {
      type: String,
      default: ''
    },
    // 是否显示筛选条件
    showFilter: {
      type: Boolean,
      default: false
    },
    // 当前选中的选项卡配置项
    activeData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      list: [],
      total: 0,
      listQuery: {
        page: 1,
        limit: 10
      },
      updateVisible: false,
      row: {},
      label: ''
    }
  },
  watch: {
    id: {
      handler(val) {
        this.getList()
        const labelMap = {
          '1': '工作日',
          '2': '工作日',
          '3': '节假日'
        }
        this.label = labelMap[val] || ''
      },
      immediate: true
    }
  },
  methods: {
    handleUpdate(row) {
      this.updateVisible = true
      this.row = row
    },
    async getList() {
      try {
        this.loading = true
        const apiMap = {
          '1': getWorkdayApi,
          '2': getLegalWorkdayApi,
          '3': getHolidayApi
        }
        const res = await apiMap[this.id](this.listQuery)
        if (res.code === 200 && res.data) {
          // 判断res.data是否是对象类型
          if (typeof res.data === 'object') {
            this.list = Object.values(res.data)
            if (this.list.length > 0) {
              this.list = this.list.map(item => {
                item.dates = Object.values(item.working_day_array).reduce((acc, cur) => {
                  acc.push(...cur)
                  return acc
                })
                return item
              })
            }
          }
        } else {
          this.list = []
          this.$message.error(res.msg || res.message || '获取数据失败')
        }
      } catch (err) {
        this.list = []
        console.log(err)
      } finally {
        this.loading = false
      }
    },
    handleAdd() {
      this.updateVisible = true
      this.row = {
        year: '',
        dates: []
      }
    },
    handleRefresh(data) {
      this.list.map(item => {
        if (item.id === data.id) {
          item.year = data.year
          item.dates = data.dates
        }
        return item
      })
      this.getList()
    }
  }
}
</script>

<template>
  <div ref="listRef" class="list">
    <div class="table mt20">
      <el-table :data="list" height="100%" v-loading="loading">
        <el-table-column type="index" label="序号" width="80" :sortable="true" />
        <el-table-column prop="year" label="年度" :sortable="true" />
        <el-table-column prop="action" label="操作" width="100">
          <template #default="{row}">
            <el-button type="text" @click="handleUpdate(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <update :id="id" :visible.sync="updateVisible" :data="row" :show-year="id === '1'" :label="label" :active-data="activeData" @refresh="handleRefresh" />
  </div>
</template>

<style scoped lang="scss">
.list{
  height: 100%;
  .filter{
    border-bottom: 1px solid #e5e5e5;
  }
  .table{
    height: calc(100% - 20px);
  }
}
</style>
