import request from '@/utils/request'

export function index(data) {
  return request({
    url: '/financeSupplement/index',
    method: 'post',
    data: data
  })
}

export function store(data) {
  return request({
    url: '/financeSupplement/store',
    method: 'post',
    data: data
  })
}

export function destroy(data) {
  return request({
    url: '/financeSupplement/destroy',
    method: 'post',
    data: data
  })
}

export function finance(data) {
  return request({
    url: '/financeSupplement/finance',
    method: 'post',
    data: data
  })
}

export function auth(data) {
  return request({
    url: '/financeSupplement/auth',
    method: 'post',
    data: data
  })
}
