<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <!--<span>移动云月度详单列表</span>-->
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
        <el-button style="float: right; padding: 0" type="text" :loading="downloadLoading" @click="exportExcel">导出</el-button>
      </div>
      <div class="filter">
        <el-form :inline="true" :model="queryList" class="demo-form-inline" size="small">
          <el-form-item label="用户名称">
            <el-input v-model="queryList.name" placeholder="清输入用户名称" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="table mt20">
        <el-table
          :data="tableData"
          stripe
          style="width: 100%"
          height="calc(100% - 96px)"
        >
          <el-table-column
            prop="id"
            label="id"
            width="80"
          />
          <el-table-column
            prop="account_duetime"
            label="账单推送日期"
            width="120"
          />
          <el-table-column
            label="账期"
            width="100"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.bill_month }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="customer_name"
            label="客户名称"
          />
          <el-table-column
            prop="product_offer_name"
            label="商品名称"
          />
          <el-table-column
            prop="product_desc"
            label="产品描述"
          />
          <el-table-column
            prop="resource_pool"
            label="资源池"
          />
          <el-table-column
            prop="available_zone"
            label="可用资源池"
          />
          <el-table-column
            prop="charge_type"
            label="计费模式"
          />
          <el-table-column
            prop="bill_type"
            label="出账类型"
          />
          <el-table-column
            prop="charge_item_price"
            label="计费项单价"
          />
          <el-table-column
            label="使用量"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.consume_amount !== 'null'">{{ scope.row.consume_amount }}({{ scope.row.consume_amount_unit }})</span>
            </template>
          </el-table-column>
          <el-table-column
            label="服务时长"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.serivce_duration }}({{ scope.row.serivce_duration_unit }})</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="sub_inure_time"
            label="订单生效时间"
          />
          <el-table-column
            prop="pay_mode"
            label="付费方式"
          />

          <el-table-column
            prop="fee_before_dis"
            label="目录金额(元) "
          />
          <el-table-column
            prop="dis_fee"
            label="优惠金额(元) "
          />
          <el-table-column
            prop="fee_after_dis"
            label="应收金额(元) "
          />
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryList.page"
          :limit.sync="queryList.perPage"
          style="text-align:center;"
          @pagination="getList"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { yidongyun_estimatedbill, destroy } from '@/api/agent/product'
import { download } from '../utils/download'
import { ydHostMonthListExport } from '@/api/agent/downloads'

export default {
  name: 'Default',
  components: { Pagination },
  data() {
    return {
      historyQuery: {
        total: 0,
        page: 1,
        perPage: 10
      },
      historyData: [],
      timeRadio: 1,
      dialogTableVisible: false,
      dialogVisible: false,
      isUpdate: false,
      tableData: [],
      products: [],
      productDroops: [],
      packages: [],
      categories: [],
      total: 0,
      isban: 0,
      ruleForm: {},
      productValue: {},
      hosts: [],
      domains: [],
      queryList: {
        page: 1,
        perPage: 10
      },
      loading: false,
      downloadLoading: false
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.list-wrap > .el-card > .el-card__body')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  methods: {
    getList() {
      this.loading = true
      yidongyun_estimatedbill(this.queryList).then(response => {
        if (response.code === 200 && response.data) {
          this.tableData = response.data.list
          this.total = response.data.meta.total
        }
      }).finally(() => {
        this.loading = false
      })
    },
    destroy(row) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        destroy({ id: row.id }).then(response => {
          if (response.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          }
        })
      })
    },
    async exportExcel() {
      try {
        this.downloadLoading = true
        const params = {
          name: this.queryList.name,
          type: this.queryList.type
        }
        const res = await ydHostMonthListExport(params)
        if (res.code === 200) {
          this.$message.success('导出成功，请到下载中心下载')
        } else {
          this.$message.error(res.msg || '导出失败')
        }
      } catch (error) {
        this.$message.error(error.message || '导出失败')
      } finally {
        this.downloadLoading = false
      }
    },
  }
}
</script>
<style lang="scss" scoped>
  ::v-deep.el-switch__label .el-switch__label--left{
      background-color: white;
  }
</style>

<style scoped lang="scss">
.list-wrap {
  height: 100%;
  .box-card {
    height: 100%;
    ::v-deep .el-card__header {
      padding-top: 0;
    }
    ::v-deep .el-card__body {
      padding: 0;
      margin-top: 20px;
      height: calc(100% - 59px);
      overflow: auto;
    }
  }
  .filter {
    border-bottom: 1px solid #E5E5E5;
  }
  .table {
    margin-top: 20px;
    min-height: 200px;
  }
}
</style>
