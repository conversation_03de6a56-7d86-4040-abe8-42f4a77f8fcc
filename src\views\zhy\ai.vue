<!-- 媒体投放 -->
<template>
  <div class="list-wrap">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="列表" name="list">
        <ai-list />
      </el-tab-pane>
      <el-tab-pane label="分类" name="cate">
        <ai-cate />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import AiList from './components/ai/list.vue'
import AiCate from './components/ai/cate.vue'

export default {
  components: {
    AiList, AiCate
  },
  data() {
    return {
      activeName: 'list'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event)
    }
  }
}
</script>

<style scoped lang="scss">
  .list-wrap {
    height: 100%;

    ::v-deep .el-tabs{
      height: 100%;
      .el-tabs__content{
        height: calc(100% - 41px - 20px);
        .el-tab-pane{
          height: 100%;
        }
      }
    }

    ::v-deep .card-wrap {
      height: 100%;
      border: none;

      .el-card__body {
        height: 100%;
      }

      .box-card {
        height: 100%;
        border: none;

        .el-card__body {
          height: calc(100% - 59px);
          overflow: auto;
        }
      }
    }
  }
</style>
