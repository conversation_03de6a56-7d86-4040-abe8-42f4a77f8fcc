<script>
export default {
  name: 'SelfInput',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入'
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    inputValue: {
      get() {
        return this.value ? this.value.trim() : this.value
      },
      set(val) {
        this.$emit('update:value', val)
      }
    }
  }
}
</script>

<template>
  <el-input v-model="inputValue" clearable :placeholder="placeholder" :disabled="disabled" />
</template>

<style scoped>

</style>
