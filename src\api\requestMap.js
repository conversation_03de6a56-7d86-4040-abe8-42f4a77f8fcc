/*eslint-disable*/
import {
  approval,
  approvalTrue,
  approvalFalse,
  getTaskUserInfo,
  getTaskList,
  updateGrade,
  updateMoneyList,
  suspendProject,
  approvalUpdateMoney,
  approvalSuspend,
  getPublishRank,
  getOverRank,
  getCertificate,
  getConsole,
  getTrend, getCityCertificate, getMapData
} from './task'
import {getCompanyAuthTree, getCompanyAuthList, getCompanyAuth, saveCompanyAuth} from "@/api/product";

const taskRequestMap = {
  approval,
  approvalTrue,
  approvalFalse,
  getTaskUserInfo,
  getTaskList,
  updateGrade,
  getCompanyAuthList,
  getCompanyAuthTree,
  getCompanyAuth,
  saveCompanyAuth,
  updateMoneyList,
  suspendProject,
  approvalUpdateMoney,
  approvalSuspend,
  getPublishRank,
  getOverRank,
  getCertificate,
  getConsole,
  getTrend,
  getCityCertificate,
  getMapData
}

export default taskRequestMap
