import request from '@/utils/request'

export function fetchList(data) {
  return request({
    url: '/shop/collects/list',
    method: 'post',
    data: data
  })
}

export function store(data) {
  return request({
    url: '/shop/collects/store',
    method: 'post',
    data: data
  })
}

export function remove(data) {
  return request({
    url: '/shop/collects/remove',
    method: 'post',
    data: data
  })
}
