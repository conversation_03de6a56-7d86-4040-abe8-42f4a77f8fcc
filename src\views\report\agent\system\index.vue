<template>
  <div class="dashboard-editor-container">
    <el-alert :title="alert" effect="dark" :closable="false" style="padding: 15px;background-color: #ff8b05" />
    <panel-group :panel="panel" />
    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="24" style="margin-bottom: 15px;">
        <el-card v-loading="CustomerLoading" class="box-card" shadow="hover">
          <div id="chart2" style="width: 100%;height: 1000px;" />
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="24">
        <el-card v-loading="ProductLoading" class="box-card" shadow="hover">
          <div id="chart1" style="width: 100%;height: 800px;" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { system } from '@/api/agent/dashboard'
import PanelGroup from './components/PanelGroup'
import * as echarts from 'echarts'
require('echarts/theme/macarons')
require('echarts/extension/bmap/bmap')

export default {
  name: 'DashboardSystem',
  components: {
    PanelGroup
  },
  data() {
    return {
      chart1: null,
      chart2: null,
      alert: '加载中...',
      panel: {
        'agent_count': 0,
        'venue_count': 0,
        'venue_today_count': 0,
        'company_count': 0,
        'company_today_count': 0,
        'order_count': 0
      },
      graph: {
        product: {
          title: null,
          label: [],
          type: 'bar',
          data: []
        },
        customer: {
          title: null,
          small_title: null,
          small_title_link: null
        }
      },
      ProductLoading: false,
      CustomerLoading: false
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async getSystem() {
      let data = false
      await system().then(response => {
        data = response.data
      }).catch(error => {
        console.log(error)
      })
      return data
    },
    async init() {
      const data = await this.getSystem()
      this.alert = '总余额￥' + data.alert.usable_count + ' 元人民币，本月产生订单 ' + data.alert.order_month_count + ' 个，共产生' + data.alert.consume_count + ' 元消耗'
      this.panel = data.panel
      this.graph = data.graph
      this.initCharts1()
      this.initCharts2()
    },
    initCharts1() {
      this.ProductLoading = true
      this.chart1 = echarts.init(document.getElementById('chart1'))
      this.setOptions1()
      this.ProductLoading = false
    },
    initCharts2() {
      this.CustomerLoading = true
      this.chart2 = echarts.init(document.getElementById('chart2'))
      this.setOptions2()
      this.CustomerLoading = false
    },
    setOptions1() {
      this.chart1.setOption({
        title: {
          text: this.graph.product.title
        },
        tooltip: {},
        xAxis: {
          data: this.graph.product.label
        },
        yAxis: {},
        series: [{
          name: '销量',
          barWidth: 40,
          type: this.graph.product.type,
          data: this.graph.product.data
        }]
      })
    },
    setOptions2() {
      this.chart2.setOption({
        title: {
          text: this.graph.customer.title
        },
        tooltip: {},
        legend: {
          orient: 'vertical',
          left: 'left',
          data: ['']
        },
        visualMap: {
          min: 0,
          max: this.graph.customer.max,
          left: '10%',
          top: 'bottom',
          text: ['高', '低'],
          calculable: true,
          color: ['#0b50b9', '#c3e2f4']
        },
        selectedMode: 'single',
        aspectScale: 0.75,
        zoom: 1.2,
        series: [
          {
            name: '',
            type: 'map',
            mapType: 'china',
            itemStyle: {
              normal: {
                borderColor: 'rgba(0, 0, 0, 0.2)'
              },
              emphasis: {
                shadowOffsetX: 0,
                shadowOffsetY: 0,
                shadowBlur: 20,
                borderWidth: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            showLegendSymbol: false,
            label: {
              normal: {
                show: true
              },
              emphasis: {
                show: true
              }
            },
            data: this.graph.customer.data
          }
        ]
      })
    },
    convertData(data) {
      const res = []
      for (let i = 0; i < data.length; i++) {
        const geoCoord = this.map[data[i].name]
        if (geoCoord) {
          res.push({
            name: data[i].name,
            value: geoCoord.concat(data[i].value)
          })
        }
      }
      return res
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  padding: 32px;
  background-color: rgb(240, 242, 245);
  width: 100%;
  height: 100%;
  overflow-y: auto;
  .github-corner {
    position: absolute;
    top: 0px;
    border: 0;
    right: 0;
  }

  .chart-wrapper {
    background: #fff;
    padding: 15px;
    margin-bottom: 30px;
  }
}

@media (max-width:1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
</style>
