<script>
export default {
  name: 'SelfSwitch',
  props: {
    value: {
      type: [String, Number],
      default: undefined
    },
    placeholder: {
      type: String,
      default: '请输入'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    activeColor: {
      type: String,
      default: '#409EFF'
    },
    inactiveColor: {
      type: String,
      default: '#C0CCDA'
    },
    activeValue: {
      type: [String, Number],
      default: 1
    },
    inactiveValue: {
      type: [String, Number],
      default: 0
    }
  },
  computed: {
    inputValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('update:value', val)
      }
    }
  },
  methods: {
    handleChange(val) {
      this.$emit('change', val)
    }
  }
}
</script>

<template>
  <el-switch
      v-model="inputValue"
      :active-color="activeColor"
      :inactive-color="inactiveColor"
      :active-value="activeValue"
      :inactive-value="inactiveValue">
  </el-switch>
</template>

<style scoped lang="scss">

</style>
