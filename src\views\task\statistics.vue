<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import {
  getAgentList,
  getPositionList,
  getProjectList, getStatisticsCompany, getStatisticsHallType,
  getStatisticsPosition,
  getStatisticsUser, getStatisticsUserTask
} from '@/api/taskStatistics'

const formatProject = res => {
  return res.data
}
const filterTime = {
  prop: 'datetime',
  type: 'datePicker',
  settings: {
    type: 'daterange',
    valueFormat: 'yyyy-MM-dd',
    format: 'yyyy-MM-dd',
    placeholder: ['开始时间', '结束时间']
  }
}
const agent = {
  label: '代理商',
  prop: 'company',
  type: 'select',
  settings: {
    options: getAgentList,
    formatOptions: formatProject,
    props: {
      label: 'enterprise_name',
      value: 'zihaiyuncID'
    },
    placeholder: '请选择代理商'
  }
}
const projectOrTaskType = (type) => {
  return {
    label: type === 1 ? '项目类型' : '任务类型',
    prop: 'typeid',
    type: 'select',
    settings: {
      options: getProjectList,
      optionParams: { type },
      props: {
        label: 'title',
        value: 'id'
      },
      formatOptions: formatProject,
      placeholder: '请选择项目类型'
    }
  }
}
const stars = {
  prop: 'stars',
  label: '星级',
  align: 'center',
  children: [
    {
      prop: 'gradeOne',
      label: '一星(人)',
      align: 'center'
    },
    {
      prop: 'gradeTwo',
      label: '二星(人)',
      align: 'center'
    },
    {
      prop: 'gradeThree',
      label: '三星(人)',
      align: 'center'
    }
  ]
}
const taskTable = (type) => {
  const title = type === 2 ? '项目' : '任务'
  return [
    {
      prop: 'projectNum',
      label: title + '数量',
      align: 'center',
      children: [
        {
          prop: 'total',
          label: '已发布' + title + '总量',
          align: 'center'
        },
        {
          prop: 'already',
          label: '已接单数量',
          align: 'center'
        },
        {
          prop: 'wait',
          label: '待接单数量',
          align: 'center'
        }
      ]
    },
    {
      prop: 'money',
      label: title + '项目佣金',
      align: 'center',
      children: [
        {
          prop: 'hall_total_money',
          label: '发布' + title + '佣金总额',
          align: 'center'
        },
        {
          prop: 'bountymoney',
          label: '基础佣金',
          align: 'center'
        },
        {
          prop: 'hall_money',
          label: '加价佣金',
          align: 'center'
        }
      ]
    },
    {
      prop: 'finish',
      label: '完结',
      align: 'center',
      children: [
        {
          prop: 'already_total',
          label: '已完结' + title + '总量',
          align: 'center'
        },
        {
          prop: 'already_hall_total_money',
          label: '完结' + title + '佣金总额',
          align: 'center'
        }
      ]
    },
    {
      prop: 'overdue',
      label: '逾期',
      align: 'center',
      children: [
        {
          prop: 'exceed_total',
          label: '逾期' + title + '总量',
          align: 'center'
        },
        {
          prop: 'exceed_hall_total_money',
          label: '逾期' + title + '佣金总额',
          align: 'center'
        }
      ]
    }
  ]
}
const agentObjectOrTaskTable = (type) => {
  return {
    api: getStatisticsCompany,
    params: (filter) => {
      return {
        company: filter.company,
        datetime: (filter && filter.datetime) ? filter.datetime.join(',') : '',
        orderType: type
      }
    },
    columns: [
      {
        prop: 'enterprise_name',
        label: '代理商名称',
        align: 'center',
        width: '200px'
      },
      ...taskTable(type)
    ]
  }
}
const agentFilter = [
  agent,
  {
    label: '发布时间',
    ...filterTime
  }
]
const objectOrTaskTypeTable = (type) => {
  const title = type === 2 ? '项目' : '任务'
  return {
    api: getStatisticsHallType,
    params: (filter) => {
      return {
        typeid: filter.typeid,
        datetime: (filter && filter.datetime) ? filter.datetime.join(',') : '',
        orderType: type
      }
    },
    columns: [
      {
        prop: 'title',
        label: title + '类型',
        align: 'center',
        width: '200px'
      },
      ...taskTable(type)
    ]
  }
}

export default {
  name: 'TaskStatistics',
  components: { StatisticsTemplate },
  data() {
    return {
      activeIndex: 'technicians',
      menuList: [
        {
          title: '认证技术人数',
          key: 'technicians',
          filters: [
            {
              label: '认证时间',
              ...filterTime
            }
          ],
          tableSettings: {
            api: getStatisticsUser,
            params: (filter) => {
              return {
                datetime: (filter && filter.datetime) ? filter.datetime.join(',') : ''
              }
            },
            columns: [
              {
                prop: 'userTotal',
                label: '认证技术人数(人)',
                align: 'center'
              },
              {
                prop: 'innerTotal',
                label: '内部认证数量(人)',
                align: 'center'
              },
              {
                prop: 'outsideTotal',
                label: '外部认证数量(人)',
                align: 'center'
              },
              stars
            ],
            formmat: res => {
              const list = res.data
              return [list]
            },
            showPage: false
          }
        },
        {
          title: '认证岗位',
          key: 'positions',
          filters: [
            {
              label: '认证时间',
              ...filterTime
            },
            {
              label: '岗位',
              prop: 'work_id',
              type: 'cascader',
              settings: {
                props: {
                  label: 'title',
                  value: 'id',
                  emitPath: false
                },
                placeholder: '请选择岗位',
                options: getPositionList,
                optionParams: {}
              }
            }
          ],
          tableSettings: {
            api: getStatisticsPosition,
            params: (filter) => {
              return {
                work_id: filter.work_id,
                datetime: (filter && filter.datetime) ? filter.datetime.join(',') : ''
              }
            },
            columns: [
              {
                prop: 'title',
                label: '认证岗位',
                align: 'center'
              },
              {
                prop: 'already',
                label: '已认证人数',
                align: 'center'
              },
              {
                prop: 'wait',
                label: '待认证人数',
                align: 'center'
              },
              stars
            ]
          }
        },
        {
          title: '代理商发布项目',
          key: 'agent-projects',
          filters: agentFilter,
          tableSettings: agentObjectOrTaskTable(2)
        },
        {
          title: '代理商发布任务',
          key: 'agent-tasks',
          filters: agentFilter,
          tableSettings: agentObjectOrTaskTable(3)
        },
        {
          title: '项目类型',
          key: 'project-types',
          filters: [
            projectOrTaskType(1),
            {
              label: '发布时间',
              ...filterTime
            }
          ],
          tableSettings: objectOrTaskTypeTable(2)
        },
        {
          title: '任务类型',
          key: 'task-types',
          filters: [
            projectOrTaskType(2),
            {
              label: '发布时间',
              ...filterTime
            }
          ],
          tableSettings: objectOrTaskTypeTable(3)
        },
        {
          title: '个人任务类型统计',
          key: 'personal-task-types',
          filters: [
            agent,
            {
              label: '姓名',
              prop: 'username',
              type: 'input',
              settings: {
                placeholder: '请输入姓名'
              }
            }
          ],
          tableSettings: {
            api: getStatisticsUserTask,
            params: (filter) => {
              return {
                username: filter.name,
                company: filter.company
              }
            },
            columns: [
              {
                prop: 'name',
                label: '姓名',
                align: 'center',
                width: 150
              },
              {
                prop: 'enterprise_name',
                label: '隶属机构',
                align: 'center',
                minWidth: 200
              },
              {
                prop: 'orderNum',
                label: '已接任务数量',
                align: 'center',
                width: 150
              },
              {
                prop: 'overNum',
                label: '已完成任务数量',
                align: 'center',
                width: 180
              },
              {
                prop: 'order_hall_total_money',
                label: '总佣金',
                align: 'center',
                width: 150
              },
              {
                prop: 'order_bountymoney',
                label: '基础佣金',
                align: 'center',
                width: 150
              },
              {
                prop: 'order_hall_money',
                label: '加价佣金',
                align: 'center',
                width: 150
              },
              {
                prop: 'overMoney',
                label: '完结佣金',
                align: 'center',
                width: 150
              }
            ]
          }
        }
      ]
    }
  },
  computed: {
    activeNav() {
      return this.menuList.find(item => item.key === this.activeIndex)
    }
  },
  methods: {
    handleSelect(key) {
      this.activeIndex = key
    }
  }
}
</script>

<template>
  <div id="statistics" class="list-wrap">
    <el-card class="box-card" shadow="never">
      <div class="box-wrap">
        <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect">
          <el-menu-item v-for="(item, index) in menuList" :key="index" :index="item.key">{{ item.title }}</el-menu-item>
        </el-menu>
        <div class="content">
          <statistics-template :config="activeNav" />
        </div>
      </div>
    </el-card>
  </div>
</template>

<style scoped lang="scss">
#statistics {
  height: 100%;
  box-sizing: border-box;

  .box-card {
    height: 100%;
    border: none;

    ::v-deep .el-card__body {
      height: 100%;
      overflow: auto;
      padding: 0;
    }

    .box-wrap {
      height: 100%;
      display: flex;
      flex-direction: column;

      .content {
        flex: 1;
        overflow: auto;
        padding: 20px 0;
      }
    }

    ::v-deep .el-menu-demo{
      margin-top: -20px;
    }
  }
}
</style>
