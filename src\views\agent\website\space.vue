<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <!--<span>空间列表</span>-->
        <el-button style="float: right; padding: 0" type="text" :loading="downloadLoading" @click="exportExcel">导出</el-button>
      </div>
      <div class="filter">
        <el-form ref="form" :inline="true" :model="searchForm" label-width="90px" size="small">
          <el-row>
            <el-col :span="24">
              <el-form-item label="用户名称">
                <el-input v-model="searchForm.name" placeholder="请输入用户名称" clearable />
              </el-form-item>
              <el-form-item label="FTP账号">
                <el-input v-model="searchForm.ftp" placeholder="请输入FTP账号" clearable />
              </el-form-item>
              <el-form-item label="绑定域名">
                <el-input v-model="searchForm.domain" placeholder="请输入绑定域名" clearable />
              </el-form-item>
              <el-form-item label="开通时间">
                <el-date-picker
                  v-model="searchForm.start_at"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" size="small" icon="el-icon-search" @click="getList">搜索</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="table mt20">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          style="width: 100%"
          height="calc(100% - 96px)"
        >
          <el-table-column
            label="#"
            type="index"
            width="50"
          />
          <el-table-column label="用户名称" prop="name" />
          <el-table-column label="代理商名称" prop="agent" />
          <el-table-column
            prop="product"
            label="主机型号"
          />
          <el-table-column
            prop="host.ftp"
            label="FTP账号"
          />
          <el-table-column
            label="运行状态"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.host.status === -2" style="color: #FFBA00">等待开通</span>
              <span v-if="scope.row.host.status === 1" style="color: #30B08F">运行中</span>
              <span v-if="scope.row.host.status === -1" style="color: #C03639">停止</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="expire_start"
            label="开通日期"
          />
          <el-table-column
            prop="expire_end"
            label="到期时间"
          />
          <el-table-column
            label="操作"
          >
            <template slot-scope="scope">
              <el-button v-if="scope.row.host.status !== -2" type="text" @click="openManager(scope.row)">管理</el-button>
              <el-button v-if="scope.row.host.status !== -2" type="text" @click="showDetail(scope.row)">详情</el-button>
              <el-button type="text" @click="dialogFormShow(scope.row)">修改到期时间</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="searchForm.total>0"
          :total="searchForm.total"
          :page.sync="searchForm.page"
          :limit.sync="searchForm.perPage"
          style="text-align:center;"
          @pagination="getList"
        />
      </div>
    </el-card>
    <el-dialog
      title="修改到期时间"
      :visible.sync="dialogFormVisible"
      width="500px"
      top="10vh"
    >
      <el-form ref="form" :model="form" label-width="120px" :rules="rules" style="width: 90%">
        <el-form-item label="到期日期：" prop="endtime">
          <el-date-picker
            v-model="form.endtime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
            clearable
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="auth">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { mapGetters } from 'vuex'
import { index, manager, endtimeChange } from '@/api/agent/host'
import { getToken } from '@/utils/auth'
import { download } from '../utils/download'
import { hostListExport } from '@/api/agent/downloads'

export default {
  name: 'Host',
  components: { Pagination },
  data() {
    return {
      ruleForm: {},
      searchForm: {
        page: 1,
        perPage: 10,
        total: 0,
        start_at: null
      },
      rules: {
        endtime: [{ required: true, message: '到期时间必须填写', trigger: 'blur' }]
      },
      form: {},
      tableData: [],
      dialogFormVisible: false,
      dialogLoading: false,
      datalogLoading: false,
      tableLoading: false,
      downloadLoading: false,
    }
  },
  computed: {
    // 别名处理防止冲突
    ...mapGetters({
      userType: 'type'
    })
  },
  created() {
    this.getList()
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.list-wrap > .el-card > .el-card__body')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  methods: {
    getList() {
      this.tableLoading = true
      index(this.searchForm).then(response => {
        if (response.code === 200 && response.data) {
          this.tableData = response.data.list
          this.searchForm.total = response.data.meta.total
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    openManager(row) {
      manager({ id: row.host.id }).then(res => {
        window.open('http://idc.china9.cn/panel/login?model=cp&name=' + res.data['host_guid'])
      }).catch(() => {
      })
    },
    dialogFormShow(row) {
      this.form = { id: row.host.id, endtime: '' }
      this.dialogFormVisible = true
    },
    auth() {
      this.$confirm('请核对主机与到期日期，确保主机与到期日期正确, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$refs['form'].validate((res) => {
          if (res) {
            this.dialogLoading = true
            endtimeChange(this.form).then(res => {
              this.dialogLoading = false
              this.$message.success('到期日期修改成功')
              this.form['endtime'] = ''
              this.dialogFormVisible = false
            }).catch(() => {
              this.dialogLoading = false
            })
          }
        })
      })
    },
    showDetail(item) {
      this.$router.push({
        name: 'agentWebsiteSpaceDetail',
        query: { id: item.host.id }
      })
    },
    update(item) {
      this.tableLoading = true
      this.$router.push({
        name: 'ProductUpdateRouter',
        query: { id: item.id }
      })
    },
    async exportExcel() {
      try {
        this.downloadLoading = true
        const params = {
          name: this.searchForm.name,
          ftp: this.searchForm.ftp,
          domain: this.searchForm.domain,
          start_at: this.searchForm.start_at
        }
        const res = await hostListExport(params)
        if (res.code === 200) {
          this.$message.success('导出成功，请到下载中心下载')
        } else {
          this.$message.error(res.msg || '导出失败')
        }
      } catch (error) {
        this.$message.error(error.message || '导出失败')
      } finally {
        this.downloadLoading = false
      }
    },
  }

}
</script>

<style scoped>
.demo-table-expand {
  font-size: 0;
}
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
</style>

<style lang="scss" scoped>
.list-wrap {
  height: 100%;
  .filter{
    border-bottom: 1px solid #e8e8e8;
  }
  & > ::v-deep .el-card {
    height: 100%;
    .el-card__header {
      padding-top: 0;
    }
    .el-card__body {
      padding: 0;
      margin-top: 20px;
      height: calc(100% - 59px);
      overflow: auto;
    }
  }
}
</style>
