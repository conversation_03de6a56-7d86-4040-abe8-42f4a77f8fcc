<template>
  <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="150px" class="demo-ruleForm">
    <el-row>
      <el-col :span="12" style="max-width: 500px">
        <el-divider content-position="left">企业信息</el-divider>
          <el-form-item label="用户类型：" prop="legal_person"  style="display:none">
          <el-radio-group v-model="ruleForm.type">
            <el-radio :label="1">常规</el-radio>
            <el-radio :label="2">渠道</el-radio>
            <el-radio :label="3">补资质</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="企业名称：" prop="name">
          <el-input v-model="ruleForm.name" />
        </el-form-item>
        <el-form-item label="企业规模：" prop="scale">
          <el-select v-model="ruleForm.scale" style="width: 100%">
            <el-option v-for="(item,index) in scales" :key="index" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="企业行业：" prop="trade_first">
          <el-cascader
            v-model="ruleForm.trade_ids"
            style="width: 100%"
            :options="trades"
            :props="{ value: 'id',label:'title' }"
            filterable
            clearable
            placeholder="可搜索"
            @change="tradeChange"
          />
        </el-form-item>
        <el-form-item label="所在省市：" prop="agentArea">
          <el-cascader
            v-model="ruleForm.agentArea"
            style="width: 100%"
            :props="{ value: 'id',label:'name' }"
            :options="provinceAndCityData"
            filterable
            clearable
            placeholder="可搜索"
          />
        </el-form-item>
        <el-form-item label="企业地址：" prop="address">
          <el-input v-model="ruleForm.address" />
        </el-form-item>
         <el-form-item label="企业主营项目：" prop="operating">
          <el-input
                v-model="ruleForm.operating"
                type="textarea"
                :rows="3"
                placeholder="请认真填写客户真实的主要营业项目或范围"
              />
        </el-form-item>
        <el-form-item label="企业网址：" prop="website">
          <el-input v-model="ruleForm.website" />
        </el-form-item>
        <el-form-item label="LOGO：" prop="logo">
          <upload-img
            :url.sync="ruleForm.logo"
            :size="300"
            accept="image/png, image/jpeg"
            tip="请选择不大于300K的JPG、PNG、图片，建议图片大小为100*100像素"
            @success="logoSuccess"
          />
        </el-form-item>
      </el-col>

      <el-col :span="12" style="max-width: 400px">
        <el-divider content-position="left">用户信息</el-divider>

        <el-form-item label="登录姓名：" prop="legal_person">
          <el-input :disabled="ruleForm.id" v-model="ruleForm.legal_person" />
        </el-form-item>
        <el-form-item label="登录手机号：" prop="legal_phone">
          <el-input :disabled="ruleForm.id" v-model="ruleForm.legal_phone" />
        </el-form-item>
        <el-form-item label="密码：" prop="password">
          <el-input  v-model="ruleForm.password" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import axios from 'axios'
import UploadImg from '@/components/Upload/uploadImg'
// import UploadFile from '@/components/Upload/uploadFile'
import { options } from '@/api/agent/customers'
export default {
  name: 'CustomerForm',
  components: { UploadImg },
  props: {
    ruleForm: {
      type: Object,
      default: () => {}
    },
    isUpdate: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      provinceAndCityData: [],
      activeName: 'first',
      yun_api: '',
      trades: [],
      trade_ids: [],
      scales: [],
      rules: {
        // type: [{ required: true, message: '必选', trigger: 'blur' }],
        name: [{ required: true, message: '必填', trigger: 'blur' }],
        legal_person: [{ required: true, message: '必填', trigger: 'blur' }],
        legal_phone: [{ required: true, message: '必填', trigger: 'blur' }],
        password: [{ required: true, message: '必填', trigger: 'blur' }],
        logo: [{ required: false, message: '必填', trigger: 'change' }],
        scale: [{ required: true, message: '必填', trigger: 'blur' }],
        trade_first: [{ required: true, message: '必填', trigger: 'blur' }],
        agentArea: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        website: [{ required: false, trigger: 'blur' }],
        address: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        operating: [{ required: true, message: '必填', trigger: ['blur', 'change'] }]
      }
    }
  },
  watch: {
    ruleForm(val) {
      this.trade_ids = [this.ruleForm.trade_first, this.ruleForm.trade_second]
    }
  },
  created() {
    this.getOptions()
  },
  methods: {
    getOptions() {
      this.yun_api = process.env.VUE_APP_YUN_API + '/api/upload'
      options().then(res => {
        this.scales = res.data['ORGANIZATION_SCALE']
        var temp = res.data['ORGANIZATION_TRADE']
        this.removeChildren(temp)
        this.trades = temp

        temp = res.data['ORGANIZATION_CITY']
        this.removeChildren(temp)
        this.provinceAndCityData = temp
      })
    },
    logoSuccess(file) {
      var that = this
      this.getYunImage(file, function(id) {
        that.ruleForm['logo_id'] = id
      })
    },
    getYunImage(file, callback) {
      const formData = new FormData()
      formData.append('files[]', file.raw)
      axios.post(this.yun_api, formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      }).then(res => {
        callback(res.data.data[0])
      })
    },
    removeChildren(temp) {
      var that = this
      temp.forEach(function(item) {
        if(item['children']){
          if (item['children'].length === 0) {
            delete (item['children'])
          } else {
            that.removeChildren(item['children'])
          }
        }else{
           delete (item['children'])
        }

      })
    },
    tradeChange(arr) {
      this.ruleForm.trade_first = arr[0]
      this.ruleForm.trade_second = arr[1]
    }
  }
}
</script>

<style scoped>
  .el-divider--horizontal{
    /*width: 50%;*/
  }
</style>
