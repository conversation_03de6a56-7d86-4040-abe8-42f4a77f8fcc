<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <el-button style="float: right; padding: 3px 0" type="text" @click="createView">新增产品</el-button>
        <!--<el-button style="float: right; margin-right: 20px; padding: 3px 0" type="text" @click="productDroop">垂类产品</el-button>-->
        <!--<el-button style="float: right; margin-right: 20px; padding: 3px 0" type="text" @click="productYiDong">移动云产品</el-button>-->
        <el-button style="float: right; margin-right: 20px; padding: 3px 0;color:red" type="text" :loading="downloadLoading" @click="exportExcel">导出</el-button>
      </div>
      <div class="filter">
        <el-form :inline="true" :model="queryList" class="demo-form-inline" size="small">
          <el-form-item label="产品名称">
            <el-input v-model="queryList.name" placeholder="请输入产品名称" clearable />
          </el-form-item>
          <el-form-item label="产品种类">
            <el-select v-model="queryList.category_id" placeholder="全部" clearable>
              <el-option v-for="item in categories" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="table mt20">
        <el-table
          :data="tableData"
          stripe
          style="width: 100%"
          height="calc(100% - 96px)"
        >
          <el-table-column
            label="产品名称"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.version">{{ scope.row.name }}({{ scope.row.version }})</span>
              <span v-else>{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="category"
            label="种类"
          />
          <el-table-column
            prop="company"
            label="所属公司"
          />
          <el-table-column prop="first_price" label="代理首购价" />
          <el-table-column prop="renew_price" label="代理续费价" />
          <el-table-column prop="sug_first_price" label="建议首购价" />
          <el-table-column prop="sug_renew_price" label="建议续费价" />
          <el-table-column prop="agent_fist" label="历史价格">
            <template slot-scope="scope">
              <el-button type="text" @click="showHistory(scope.row)">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="是否启用">
            <template slot-scope="scope">
              <el-link v-if="scope.row.status === 1" type="success" :underline="false">已启用</el-link>
              <el-link v-else type="danger" :underline="false">已禁用</el-link>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="editView(scope.row)">编辑</el-button>
              <el-divider direction="vertical" />
              <el-button type="text" style="color: #ff9d0c" @click="yearPrice(scope.row)">年限价格</el-button>
              <el-divider direction="vertical" />
              <el-button type="text" style="color: #096a94" @click="supplement(scope.row)">配套产品</el-button>
              <el-divider direction="vertical" />
              <el-button type="text" style="color: #07ab58" @click="pushContainProducts(scope.row)">包含产品</el-button>
              <el-divider direction="vertical" />
              <el-button type="text" style="color: red" @click="destroy(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryList.page"
          :limit.sync="queryList.perPage"
          style="text-align:center;"
          @pagination="getList"
        />
      </div>
    </el-card>
    <!--dialog-->
    <el-dialog
      :title="isUpdate ? '编辑产品':'新增产品'"
      :visible.sync="dialogVisible"
      width="40%"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="120px">
        <el-form-item label="产品种类:" prop="category_id" style="width: 70%">
          <el-select v-model="ruleForm.category_id" placeholder="全部" @change="selectCategory">
            <el-option v-for="item in categories" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="产品名称:" prop="name" style="width: 70%">
          <el-select v-if="ruleForm.category_id===1" v-model="ruleForm.app_id" @change="selProduct">
            <el-option v-for="(item,index) in products" :key="index" :label="item.name" :value="item.id+''" />
          </el-select>
          <el-select v-if="ruleForm.category_id===17" v-model="ruleForm.app_id" @change="selHost">
            <el-option v-for="(item,index) in hosts" :key="index" :label="item.name" :value="item.id+''" />
          </el-select>
          <el-select v-if="ruleForm.category_id===18" v-model="ruleForm.app_id" @change="setDomain">
            <el-option v-for="(item,index) in domains" :key="index" :label="item.prodName" :value="item.prodType" />
          </el-select>
          <el-select v-if="ruleForm.category_id === 19" v-model="ruleForm.app_id" @change="selDroopProduct">
            <el-option v-for="(item,index) in productDroops" :key="index" :label="item.name" :value="item.id+''" />
          </el-select>
          <el-select v-if="ruleForm.category_id===20" v-model="ruleForm.app_id" @change="selProduct">
            <el-option v-for="(item,index) in products" :key="index" :label="item.name" :value="item.id+''" />
          </el-select>
        </el-form-item>

        <el-form-item v-if="ruleForm.category_id===1" label="产品套餐:" prop="name" style="width: 70%">
          <el-select v-model="ruleForm.version_id" :disabled="ruleForm.app_id===undefined" @change="selPackage">
            <el-option v-for="(item,index) in packages" :key="index" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item v-if="ruleForm.category_id > 18" label="产品套餐:" prop="name" style="width: 70%">
          <el-input v-model="ruleForm.version" />
        </el-form-item>

        <!--产品内购 start -->
        <el-form-item v-if="ruleForm.category_id===20" label="产品介绍:" prop="name" style="width: 70%">
          <el-input v-model="ruleForm.intro" />
        </el-form-item>
        <el-form-item v-if="ruleForm.category_id===20" label="产品标识:" prop="name" style="width: 70%">
          <el-input v-model="ruleForm.product_type" />
        </el-form-item>
        <!--产品内购 end -->
        <el-form-item label="代理成本价:" prop="costprice" style="width: 70%">
          <el-input v-model="ruleForm.costprice" type="number" />
        </el-form-item>
        <el-form-item label="代理首购价:" prop="first_price" style="width: 70%">
          <el-input v-model="ruleForm.first_price" type="number" />
        </el-form-item>

        <el-form-item v-if="timeRadio !== -1" label="代理续费价:" prop="renew_price" style="width: 70%">
          <el-input v-model="ruleForm.renew_price" type="number" />
        </el-form-item>
        <el-form-item label="建议首购价:" prop="sug_first_price" style="width: 70%">
          <el-input v-model="ruleForm.sug_first_price" type="number" />
        </el-form-item>
        <el-form-item v-if="timeRadio !== -1" label="建议续费价:" prop="sug_renew_price" style="width: 70%">
          <el-input v-model="ruleForm.sug_renew_price" type="number" />
        </el-form-item>
        <el-form-item label="状态:" prop="status" style="width: 70%">
          <el-switch
            v-model="ruleForm.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>

        <el-form-item label="交易状态:" prop="status" style="width: 70%">
          <el-checkbox v-model="ruleForm.is_buy" label="购买" :true-label="1" :false-label="0" />
          <el-checkbox v-model="ruleForm.is_renew" label="续费" :true-label="1" :false-label="0" />
          <el-checkbox v-model="ruleForm.is_update" label="升级" :true-label="1" :false-label="0" />
        </el-form-item>
        <el-form-item label="排序:" prop="indexnum" style="width: 70%">
          <el-input v-model="ruleForm.indexnum" type="number" />
        </el-form-item>
        <el-form-item label="非软件产品:" prop="ishardware" style="width: 70%">
          <el-switch
            v-model="ruleForm.ishardware"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item label="可以重复购买:" prop="is_more" style="width: 70%">
          <el-switch
            v-model="ruleForm.is_more"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item label="可以定制购买:" prop="is_div" style="width: 70%">
          <el-switch
            v-model="ruleForm.is_div"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item v-if="ruleForm.category_id===19" label="永久购买:" prop="forever" style="width: 70%">
          <el-switch
            v-model="ruleForm.forever"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item label="隐藏开通产品:" prop="is_mating" style="width: 70%">
          <el-switch
            v-model="ruleForm.is_mating"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item label="密钥产品:" prop="iskey" style="width: 70%">
          <el-switch
            v-model="ruleForm.iskey"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item v-if="ruleForm.iskey===1" label="激活产品id:" prop="keypro_id" style="width: 40%">
          <el-input v-model="ruleForm.keypro_id" type="number" />
        </el-form-item>
        <el-form-item v-if="isban===1" label="同类半价产品开启:" prop="banjiack" style="width: 70%">
          <el-switch
            v-model="ruleForm.banjiack"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item v-if="ruleForm.banjiack===1" label="产品价格百分比:" prop="banjia" style="width: 40%">
          <el-input v-model="ruleForm.banjia" type="number" />
        </el-form-item>
        <el-form-item label="失效产品激活:" prop="islose" style="width: 70%">
          <el-switch
            v-model="ruleForm.islose"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item v-if="ruleForm.islose===1" label="失效天数:" prop="loseday" style="width: 40%">
          <el-input v-model="ruleForm.loseday" type="number" />
        </el-form-item>
        <el-form-item v-if="ruleForm.islose===1" label="首年续费:" prop="loseprice" style="width: 40%">
          <el-input v-model="ruleForm.loseprice" type="number" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="isUpdate ? updateHandle() : saveHandle() ">确 定</el-button>
      </span>
    </el-dialog>
    <!--历史价格弹窗-->
    <el-dialog :close-on-click-modal="false" title="历史价格" :visible.sync="dialogTableVisible">
      <el-table :data="historyData">
        <el-table-column property="created_at" label="修改日期" />
        <el-table-column property="first_price" label="代理首购价格" />
        <el-table-column property="renew_price" label="代理续费价格" />
        <el-table-column property="sug_first_price" label="建议首购价格" />
        <el-table-column property="sug_renew_price" label="建议续费价格" />
      </el-table>
      <pagination
        v-show="historyQuery.total>0"
        :total="historyQuery.total"
        :page.sync="historyQuery.page"
        :limit.sync="historyQuery.perPage"
        style="text-align:center;"
        @pagination="getHistoryData"
      />
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { products, store, update, destroy, getHistory, getYunList as getYunProducts, getYunHostList } from '@/api/agent/product'
import { getYunList as getYunPackageList } from '@/api/agent/packages'
import { list as productDroopList } from '@/api/agent/productDroop'
import { product as domainList } from '@/api/agent/domain'
import { download } from '../../utils/download'
import { categories } from '@/api/agent/productCategory'
import { systemProductListExport } from '@/api/agent/downloads'

export default {
  name: 'DefaultPrice',
  components: { Pagination },
  data() {
    return {
      historyQuery: {
        total: 0,
        page: 1,
        perPage: 10
      },
      historyData: [],
      timeRadio: 1,
      dialogTableVisible: false,
      dialogVisible: false,
      isUpdate: false,
      tableData: [],
      products: [],
      productDroops: [],
      packages: [],
      categories: [],
      total: 0,
      isban: 0,
      ruleForm: {},
      productValue: {},
      hosts: [],
      domains: [],
      queryList: {
        page: 1,
        perPage: 10
      },
      rules: {
        category_id: [{ required: true, message: '产品种类必选', trigger: 'change' }],
        app_id: [{ required: true, message: '产品必选', trigger: 'blur' }],
        version_id: [{ required: true, message: '版本必选', trigger: 'change' }],
        costprice: [{ required: true, message: '代理成本价必填', trigger: 'blur' }],
        first_price: [{ required: true, message: '代理首购价必填', trigger: 'blur' }],
        renew_price: [{ required: true, message: '代理续费价必填', trigger: 'blur' }],
        sug_first_price: [{ required: true, message: '建议首购价格必填', trigger: 'blur' }],
        sug_renew_price: [{ required: true, message: '建议续费价格必填', trigger: 'blur' }],
        status: [{ required: true, message: '状态必选', trigger: 'change' }]
      },
      loading: false,
      downloadLoading: false
    }
  },
  created() {
    this.getList()
    // this.package()
    this.getCategories()
    this.getYunProductList()
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.list-wrap > .el-card > .el-card__body')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  methods: {
    createView() {
      this.dialogVisible = true
      this.isUpdate = false
      this.ruleForm = {
        type: 1,
        apply: 1,
        day: 12,
        status: 1
      }
      this.timeRadio = 1
    },
    getList() {
      this.loading = true
      products(this.queryList).then(response => {
        if (response.code === 200 && response.data) {
          this.tableData = response.data.list
          this.total = response.data.meta.total
        }
      }).finally(() => {
        this.loading = false
      })
    },
    editView(row) {
      this.dialogVisible = true
      this.isUpdate = true
      this.ruleForm = row
      if (row.banjiack === 1) {
        this.isban = 1
      }
      if (this.ruleForm.app_id === '3' || this.ruleForm.app_id === '10014' || this.ruleForm.app_id === '10021') {
        this.isban = 1
      }
      if (this.hosts.length === 0 && this.ruleForm.category_id === 17) {
        this.getYunHostList()
      } else if (this.domains.length === 0 && this.ruleForm.category_id === 18) {
        this.getDomainList()
      } else if (this.ruleForm.category_id === 1) {
        this.getYunPackageList()
      }
    },
    destroy(row) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        destroy({ id: row.id }).then(response => {
          if (response.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          }
        })
      })
    },
    supplement(row) {
      this.$router.push(
        {
          name: 'agentSystemProductSupplement',
          query: { id: row.id }
        }
      )
    },
    pushContainProducts(row) {
      this.$router.push(
        {
          name: 'agentSystemProductContain',
          query: { id: row.id }
        }
      )
    },
    yearPrice(row) {
      this.$router.push(
        {
          name: 'agentSystemProductYear',
          query: { id: row.id }
        }
      )
    },
    getCategories() {
      categories({ all: true }).then(response => {
        if (response.code === 200 && response.data) {
          this.categories = response.data.list
        }
      })
    },
    getProductDroops() {
      productDroopList({ category_id: this.ruleForm.category_id }).then(response => {
        if (response.code === 200 && response.data) {
          this.productDroops = response.data
        }
      })
    },
    saveHandle() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          store(this.ruleForm).then(response => {
            if (response.code === 200) {
              this.$message.success('新增成功')
              this.dialogVisible = false
              this.getList()
            }
          })
        } else {
          return false
        }
      })
    },
    updateHandle() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          update(this.ruleForm).then(response => {
            if (response.code === 200) {
              this.$message.success('更新成功')
              this.dialogVisible = false
              this.getList()
            }
          })
        } else {
          return false
        }
      })
    },
    showHistory(row) {
      this.historyQuery.id = row.id
      this.dialogTableVisible = true
      this.getHistoryData()
    },
    getHistoryData() {
      getHistory(this.historyQuery).then(response => {
        if (response.code === 200 && response.data) {
          this.historyData = response.data.list
          this.historyQuery.total = response.data.meta.total
        }
      })
    },
    selectCategory(id) {
      this.ruleForm.category_id = id

      if (id === 17 && this.hosts.length === 0) {
        this.getYunHostList()
      } else if (id === 18 && this.domains.length === 0) {
        this.getDomainList()
      } else if (id > 18 && this.productDroops.length === 0) {
        this.getProductDroops()
      }
    },
    selProduct(id) {
      var that = this
      this.products.forEach(function(item) {
        if (item.id === parseInt(id)) {
          that.ruleForm.name = item.name
        }
      })
      this.ruleForm.app_id = id
      if (id === '3' || id === '10014' || id === '10021') {
        this.isban = 1
      } else {
        this.isban = 0
      }
      this.getYunPackageList()
    },
    selDroopProduct(id) {
      var that = this
      this.productDroops.forEach(function(item) {
        if (item.id === parseInt(id)) {
          that.ruleForm.name = item.name
        }
      })
      this.ruleForm.app_id = id
    },
    selPackage(id) {
      var that = this
      this.packages.forEach(function(item) {
        if (item.id === parseInt(id)) {
          that.ruleForm.version = item.name
        }
      })
      this.ruleForm.version_id = id
    },
    selHost(id) {
      var that = this
      this.hosts.forEach(function(item) {
        if (item.id === parseInt(id)) {
          that.ruleForm.name = item.name
        }
      })
      this.ruleForm.app_id = id
    },
    setDomain(id) {
      var that = this
      this.domains.forEach(function(item) {
        if (item.prodType === id) {
          that.ruleForm.name = item.prodName
        }
      })
      this.ruleForm.app_id = id
    },
    getYunProductList() {
      getYunProducts({}).then(response => {
        if (response.code === 200 && response.data) {
          this.products = response.data
        }
      })
    },
    getYunPackageList() {
      getYunPackageList({ app_id: this.ruleForm.app_id }).then(response => {
        if (response.code === 200 && response.data) {
          this.packages = response.data
        }
      })
    },
    getYunHostList() {
      getYunHostList({}).then(response => {
        if (response.code === 200 && response.data) {
          this.hosts = response.data
        }
      })
    },
    getDomainList() {
      domainList({}).then(res => {
        if (res.code === 200 && res.data) {
          this.domains = res.data
        }
      })
    },
    async exportExcel() {
      try {
        this.downloadLoading = true
        const params = {
          name: this.queryList.name,
          category_id: this.queryList.category_id,
          app_id: this.queryList.app_id,
          is_mating: this.queryList.is_mating
        }
        const res = await systemProductListExport(params)
        if (res.code === 200) {
          this.$message.success('导出成功，请到下载中心下载')
        } else {
          this.$message.error(res.msg || '导出失败')
        }
      } catch (error) {
        this.$message.error(error.message || '导出失败')
      } finally {
        this.downloadLoading = false
      }
    },
  }
}
</script>
<style lang="scss" scoped>
::v-deep.el-switch__label .el-switch__label--left{
  background-color: white;
}
</style>

<style scoped lang="scss">
.list-wrap {
  height: 100%;
  .box-card {
    height: 100%;
    ::v-deep .el-card__header {
      padding-top: 0;
    }
    ::v-deep .el-card__body {
      padding: 0;
      margin-top: 20px;
      height: calc(100% - 62px);
      overflow: auto;
    }
  }
  .filter {
    border-bottom: 1px solid #E5E5E5;
  }
  .table {
    margin-top: 20px;
    min-height: 200px;
  }
}
</style>
