<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header">
        <div style="position: relative">
          <span style="text-align: center;display: block;">对账单</span>
          <el-button style="position: absolute;right: 10px;top:-8px" type="text" @click="back">返回</el-button>
          <el-button style="position: absolute;right: 50px;top:-8px" type="text" @click="exportExcel">明细下载</el-button>
        </div>
      </div>
      <div style="margin-bottom: 20px">
        <span>代理商名称：{{ name }}</span>
        <el-date-picker
            v-model="date"
            style="float: right"
            type="datetimerange"
            :default-time="['00:00:00', '23:59:59']"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="dateChange"
            :picker-options="datePickerOptions"
        />
        <!--<span style="float: right">对账周期：{{ first_day }}~{{ last_day }}</span>-->
      </div>
      <el-table
          :data="tableData"
          stripe
          style="width: 100%"
      >
        <el-table-column label="序号" width="68">
          <template slot-scope="scope">
            <span>{{ scope.$index +1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
            prop="name"
            label="产品名称"
        />
        <el-table-column
            prop="company"
            label="归属公司"
        />
        <el-table-column
            prop="count"
            label="订单数量"
        />
        <!--<el-table-column-->
        <!--prop="price"-->
        <!--label="单价"-->
        <!--/>-->
        <el-table-column
            prop="spend"
            label="消耗金额"
        />
      </el-table>
    </el-card>
    <el-card class="box-card" style="margin-top: 20px;margin-bottom: 30px">
      <div slot="header">
        <span>本期金额</span>
      </div>
      <el-table
          :data="financeAgent"
          stripe
          style="width: 100%"
      >
        <el-table-column label="序号">
          <template slot-scope="scope">
            <span>{{ scope.$index+1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="开始时间">
          <template slot-scope="scope">
            <span>{{ scope.row.begin_time }}</span>
          </template>
        </el-table-column>
        <el-table-column label="结束时间">
          <template slot-scope="scope">
            <span>{{ scope.row.end_time }}</span>
          </template>
        </el-table-column>
        <el-table-column label="充值金额">
          <template slot-scope="scope">
            <span>{{ scope.row.depositPrice }}</span>
          </template>
        </el-table-column>
        <el-table-column label="期末余额">
          <template slot-scope="scope">
            <span>{{ scope.row.closing_balance }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <el-card class="box-card" style="margin-top: 20px;margin-bottom: 30px">
      <div slot="header">
        <span>代理商财务</span>
      </div>
      <el-table v-loading="loading" :data="table" stripe style="width: 130%; font-size:14px;">
        <el-table-column label="序号" width="80" align="center" fixed="left">
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="代理商" prop="name" width="160" fixed="left"  />
        <el-table-column label="软件账户初额" prop="last_fee" width="120"  />
        <el-table-column label="商城账户初额" prop="shoplast_fee" width="120"  />
        <el-table-column label="非软件账户初额" prop="hardlast_fee" width="120" />
        <el-table-column label="软件账户充值" width="120" prop="recharge"  />
        <el-table-column label="软件账户实际充值" width="140" prop="actualprice"  />
        <el-table-column label="商城账户充值" width="120" prop="shoprecharge"  />
        <el-table-column label="非软件账户充值" width="120" prop="hardrecharge" />
        <el-table-column label="用户量汇总" prop="allCount" width="120" />
        <el-table-column label="订单金额汇总" prop="allPrice" width="120"/>
        <el-table-column label="订单返利汇总" prop="allflPrice" width="120"/>
        <el-table-column label="商城订单金额汇总" prop="allshopPrice" width="140"/>
        <el-table-column label="商城订单返利汇总" prop="allshopflPrice" width="140"/>
        <el-table-column label="非软件订单金额汇总" prop="allhardPrice" width="150"/>
        <el-table-column label="非软件订单返利汇总" prop="allhardflPrice" width="150"/>
        <el-table-column label="软件账户退款" prop="deductPrice"  width="120"/>
        <el-table-column label="商城账户退款" prop="shopdeductPrice"  width="120" />
        <el-table-column label="非软件账户退款" prop="harddeductPrice"  width="120" />
        <el-table-column label="软件账户余额" prop="this_fee"  width="120" />
        <el-table-column label="商城账户余额" prop="shopthis_fee"  width="120" />
        <el-table-column label="非软件账户余额" prop="hardthis_fee"  width="120" />
      </el-table>
    </el-card>
    <el-card class="box-card" style="margin-top: 20px;margin-bottom: 30px">
      <div slot="header">
        <span>本期开具发票金额</span>
      </div>
      <el-table
          :data="financeAgent"
          stripe
          style="width: 100%"
      >
        <el-table-column label="序号">
          <template slot-scope="scope">
            <span>{{ scope.$index+1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="开具发票公司">
          <template slot-scope="scope">
            <span>{{ scope.row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column label="开始时间">
          <template slot-scope="scope">
            <span>{{ scope.row.begin_time }}</span>
          </template>
        </el-table-column>
        <el-table-column label="结束时间">
          <template slot-scope="scope">
            <span>{{ scope.row.end_time }}</span>
          </template>
        </el-table-column>
        <el-table-column
            prop="softprice"
            label="软件金额"
        />
        <el-table-column
            prop="hardprice"
            label="硬件金额"
        />
        <el-table-column
            prop="totleprice"
            label="总金额"
        />
        <el-table-column label="凭证">
          <template slot-scope="scope">
            <div v-if="scope.row.images">
              <div v-if="scope.row.images.length > 0">
                <el-image
                    style="width: 25px; height: 25px;vertical-align:middle"
                    :src="scope.row.images[0]"
                    :preview-src-list="scope.row.images"
                />
                ( 共 {{ scope.row.images.length }} 张凭证 )
              </div>
              <div v-else>暂无凭证</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="type === 1" label="代理商">
          <template slot-scope="scope">
            <span>{{ name }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="type === 1" label="财务操作">
          <template slot-scope="scope">
            <el-button v-if="scope.row.status === ''" type="text" >未确认</el-button>
            <el-button v-if="scope.row.status === 0" type="text" >审核中</el-button>
            <el-button v-if="scope.row.status === 1" type="text" >已确认</el-button>
            <el-button v-if="scope.row.status === -1" type="text" >已废弃</el-button>
            <el-button v-if="scope.row.status === 2"  style="color: #2ac06d" type="text">已驳回</el-button>
          </template>
        </el-table-column>
        <el-table-column v-if="type === 2" label="操作">
          <template slot-scope="scope">
            <el-button v-if="scope.row.status === ''" type="text" @click="sure(scope.row)">未确认</el-button>
            <el-button v-if="scope.row.status === 0" type="text">审核中</el-button>
            <el-button v-if="scope.row.status === 1" type="text" >已确认</el-button>
            <el-button v-if="scope.row.status === -1" type="text" >已废弃</el-button>
            <el-button v-if="scope.row.status === 2"  style="color: #2ac06d" type="text">已驳回</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { reconciliation, agentSure, financeSure, agent } from '@/api/agent/finance'
import { mapGetters } from 'vuex'
import { getToken } from '@/utils/auth'
import moment from 'moment'
export default {
  computed: {
    ...mapGetters({
      type: 'agentUserType'
    }),
  },
  data() {
    return {
      dialogVisible: false,
      id: this.$route.query.id,
      name: '',
      first_day: '',
      last_day: '',
      tableData: [],
      table: [],
      financeAgent: [],
      date: '',
      loading: false,
      datePickerOptions:{
        disabledDate:(time)=>{
          let nowDate = new Date();
          // return time.getTime() > nowDate;//注意是||不是&&
          return time.getTime() > Date.now()-1 * 24 * 60 * 60 * 1000
        }
      },
    }
  },
  created() {
    this.getList()
    console.log(this.type)
  },
  methods: {
    selectDate(item) {
      this.getList()
    },
    getagentList() {
      this.loading = true
      agent({"date":this.date,"agent_id":this.id}).then(response => {
        this.table = response.data
        this.loading = false
      }).catch(error => {
        console.log(error)
        this.loading = false
      })
    },
    dateChange() {
      if (this.date) {
        this.date[0] = moment(this.date[0]).format('YYYY-MM-DD H:m:s')
        this.date[1] = moment(this.date[1]).format('YYYY-MM-DD H:m:s')
      }
      this.getList()
    },
    getList() {
      reconciliation({ 'id': this.id, 'date': this.date }).then(response => {
        this.name = response.data.name
        this.id = response.data.id
        this.first_day = response.data.first_day
        this.last_day = response.data.last_day
        this.tableData = response.data.data
        this.financeAgent = response.data.financeAgent
        this.date = response.data.date
        this.getagentList()
      })
    },
    sure(show) {
      this.$confirm('确认对账信息')
        .then(_ => {
          var date=[];
            date[0] = show.begin_time
            date[1] = show.end_time
          console.log(this.type)
          if (this.type === 1) {
            this.financeSure(show.id,date)
          } else {
            this.agentSure(show.id,date)
          }
        })
        .catch(_ => {})
    },
    agentSure(id,date) {
      agentSure({ 'id': id , 'date': date}).then(response => {
        if (response.code === 200) {
          this.getList()
        }
      })
    },
    financeSure(id,date) {
      financeSure({ 'id': id , 'date': date }).then(response => {
        if (response.code === 200) {
          this.getList()
        }
      })
    },
    back() {
      this.$router.go(-1)
    },
    exportExcel() {
      var id = ''
      if (this.id) {
        id = '&id=' + this.id
      }
      var date = ''
      if (this.date) {
        date = '&date=' + this.date
      }
      console.log(date)

      window.open(process.env.VUE_APP_AGENT_API_PROXY + '/finance/agent/exportReconciliation?token=' + getToken() + id + date)
    }
  }
}
</script>
<style scoped>
</style>
