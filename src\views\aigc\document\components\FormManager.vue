<template>
  <div>
    <el-dialog custom-class="form-manage-dialog" title="表单管理" :visible.sync="dialogVisible" width="60%">
      <div class="header">
        <el-button type="primary" @click="addForm">添加表单项</el-button>
      </div>
      <el-table v-loading="loading" :data="forms" height="50vh" style="width: 100%">
        <el-table-column prop="title" label="表单名称" />
        <el-table-column prop="type" label="表单类型">
          <template slot-scope="{row}">
            <span>{{ formObjects[row.type] || '未知类型' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="220" />
        <el-table-column label="操作" width="180">
          <template slot-scope="{ row }">
            <el-button type="text" @click="editRow(row)">编辑</el-button>
            <el-button type="text" @click="deleteRow(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <edit-form ref="editFormRef" @refresh="fetchData" :typeId="typeId" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import EditForm from './EditForm.vue';
import { formListApi, formDeleteApi } from '@/api/aigc';

export default {
  name: 'FormManager',
  components: { EditForm },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      typeId: 0,
      forms: [],
    };
  },
  computed: {
    ...mapGetters(['formTypes']),
    formObjects() {
      return this.formTypes.reduce((acc, item) => {
        acc[item.value] = item.label;
        return acc;
      }, {});
    }
  },
  methods: {
    // 打开表单管理弹窗
    openDialog(row = null) {
      if (!row) return;
      this.dialogVisible = true;
      this.typeId = row.id; // 设置当前文案类型ID
      this.fetchData(row.id);
    },
    // 获取文案类型数据
    fetchData() {
      this.loading = true;
      formListApi({ type: this.typeId }).then(response => {
        this.forms = response.data;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    // 添加表单项
    addForm() {
      this.$refs.editFormRef.openDialog();
    },
    // 编辑文案类型
    editRow(row) {
      this.$refs.editFormRef.openDialog(row);
    },
    // 删除文案类型
    deleteRow(row) {
      this.$confirm('确定删除该表单项吗？').then(() => {
        formDeleteApi({ id: row.id }).then(() => {
          this.$message.success('删除成功');
          this.fetchData();
        });
      });
    }
  }
};

</script>

<style scoped lang="scss">
.header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}
::v-deep .el-dialog.form-manage-dialog {
  .el-dialog__body {
    padding-top: 0px;
  }
}
</style>