import request from '@/utils/request'

// 获取单休工作日列表
export function getWorkdayApi(data) {
  return request({
    url: '/legal/legalOne',
    method: 'POST',
    data
  })
}
// 获取指定年份的工作日列表
export function getWorkdayByYearApi(data) {
  return request({
    url: '/legal/get_workday_by_dan',
    method: 'POST',
    data
  })
}
// 获取添加单休工作日需要参数
export function getAddWorkdayApi(data) {
  return request({
    url: '/legal/getLegalOneAdd',
    method: 'POST',
    data
  })
}
// 获取修改单休工作日需要参数
export function getEditWorkdayApi(data) {
  return request({
    url: '/legal/getLegalOneEdit',
    method: 'POST',
    data
  })
}
// 添加单休工作日
export function addWorkdayApi(data) {
  return request({
    url: '/legal/legalOneCreate',
    method: 'POST',
    data
  })
}
// 修改单休工作日
export function editWorkdayApi(data) {
  return request({
    url: '/legal/legalOneEdit',
    method: 'POST',
    data
  })
}
// 获取法定工作日列表
export function getLegalWorkdayApi(data) {
  return request({
    url: '/legal/legalList',
    method: 'POST',
    data
  })
}
// 获取修改法定工作日需要参数
export function getEditLegalWorkdayApi(data) {
  return request({
    url: '/legal/getLegalEdit',
    method: 'POST',
    data
  })
}
// 添加法定工作日
export function addLegalWorkdayApi(data) {
  return request({
    url: '/legal/legalCreate',
    method: 'POST',
    data
  })
}
// 修改法定工作日
export function editLegalWorkdayApi(data) {
  return request({
    url: '/legal/legalEdit',
    method: 'POST',
    data
  })
}
// 获取法定节假日列表
export function getHolidayApi(data) {
  return request({
    url: '/legal/legalHolidayList',
    method: 'POST',
    data
  })
}
// 获取修改法定节假日需要参数
export function getEditHolidayApi(data) {
  return request({
    url: '/legal/getLegalHolidayEdit',
    method: 'POST',
    data
  })
}
// 添加法定节假日
export function addHolidayApi(data) {
  return request({
    url: '/legal/legalHolidayCreate',
    method: 'POST',
    data
  })
}
// 修改法定节假日
export function editHolidayApi(data) {
  return request({
    url: '/legal/legalHolidayEdit',
    method: 'POST',
    data
  })
}
