<script>
import { setClueSingle } from '@/api/zhy/clue'

export default {
  name: 'ChangeStatus',
  props: {
    data: {
      type: Object,
      default: () => ({
        status: 0
      })
    }
  },
  computed: {
    _status: {
      get() {
        return this.data.status
      },
      set(val) {
        this.$emit('update:data', {
          ...this.data,
          status: val
        })
      }
    }
  },
  methods: {
    async handleChangeStatus(row, status) {
      try {
        this.$emit('setLoading', row, true)
        const res = await setClueSingle({ id: row.id, status })
        if (res.code === 200) {
          this.$message.success('修改成功')
          this.$emit('refresh')
        } else {
          this.$message.error(res.msg || res.message || '修改失败')
        }
      } catch (error) {
        console.log('🚀 ~ handleChangeStatus ~ error: ', error)
      } finally {
        this.$emit('setLoading', row, false)
      }
    }
  }
}
</script>

<template>
  <el-switch v-model="_status" :active-value="1" :inactive-value="0" @change="(status) => handleChangeStatus(data, status)" />
</template>

<style scoped lang="scss">

</style>
