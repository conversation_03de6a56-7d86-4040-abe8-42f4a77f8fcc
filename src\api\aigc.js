import request from '@/utils/request'

// 获取AIGC分类列表
export function cateListApi(data) {
  return request({
    url: '/aigc/category',
    method: 'POST',
    data
  })
}

// AIGC文案类型 全部不分页
export function cateAllApi() {
  return request({
    url: '/aigc/categoryAll',
    method: 'POST'
  })
}

// AIGC分类详情
export function cateDetailApi(data) {
  return request({
    url: '/aigc/categoryDetail',
    method: 'POST',
    data
  })
}

// AIGC分类 添加、修改
export function cateStoreApi(data) {
  return request({
    url: '/aigc/categoryStore',
    method: 'POST',
    data
  })
}

// AIGC分类 删除
export function cateDeleteApi(data) {
  return request({
    url: '/aigc/categoryDestory',
    method: 'POST',
    data
  })
}

// 文案场景 列表
export function sceneListApi(data) {
  return request({
    url: '/aigc/typeIndex',
    method: 'POST',
    data
  })
}

// 文案场景 详情
export function sceneDetailApi(data) {
  return request({
    url: '/aigc/typeDetail',
    method: 'POST',
    data
  })
}

// 文案场景 添加、修改
export function sceneStoreApi(data) {
  return request({
    url: '/aigc/typeStore',
    method: 'POST',
    data
  })
}

// 文案场景 删除
export function sceneDeleteApi(data) {
  return request({
    url: '/aigc/typeDestory',
    method: 'POST',
    data
  })
}

// 表单列表
export function formListApi(data) {
  return request({
    url: '/aigc/initIndex',
    method: 'POST',
    data
  })
}

// 表单详情
export function formDetailApi(data) {
  return request({
    url: '/aigc/initDetail',
    method: 'POST',
    data
  })
}

// 表单添加、修改
export function formStoreApi(data) {
  return request({
    url: '/aigc/initStore',
    method: 'POST',
    data
  })
}

// 表单删除
export function formDeleteApi(data) {
  return request({
    url: '/aigc/initDestory',
    method: 'POST',
    data
  })
}