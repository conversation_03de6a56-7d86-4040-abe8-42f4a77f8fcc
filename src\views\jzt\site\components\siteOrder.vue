<!-- 站点订单 -->
<template>
  <el-dialog :close-on-click-modal="false" title="订单查询" :visible.sync="dialogVisible" width="80%">
    <el-form :inline="true" :model="listQ" class="demo-form-inline" size="small">
      <el-form-item label="订单类型：">
        <el-select v-model="listQ.type" placeholder="请选择">
          <el-option label="企业订单" :value="1" />
          <el-option label="空间订单" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button :loading="loading" type="primary" icon="el-icon-search" @click="listQ.page = 1;getList()">过滤</el-button>
      </el-form-item>
    </el-form>
    <!-- 表格 -->
    <el-table :data="tableData" style="width: 100%" v-loading="loading">
      <el-table-column prop="order_no" label="订单编号" />
      <el-table-column prop="company_name" label="公司名称" />
      <el-table-column v-if="dataType === 1" prop="package_name" label="套餐名称" />
      <el-table-column v-if="dataType === 2" prop="expansion" label="空间大小" />
      <el-table-column label="订单类型">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.type == 1" effect="dark" size="small">新购</el-tag>
          <el-tag v-if="scope.row.type == 2" effect="dark" size="small">续费</el-tag>
          <el-tag v-if="scope.row.type == 3" effect="dark" size="small">升级</el-tag>
        </template>
      </el-table-column>
      <el-table-column v-if="dataType === 1" prop="create_time" label="添加时间" />
      <el-table-column prop="expiration_at" label="到期日期" />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQ.page"
      :limit.sync="listQ.limit"
      @pagination="getList"
    />
  </el-dialog>
</template>

<script>
import Pagination from '@/components/Pagination'
import { orderListApi } from '@/api/jzt/sites'

export default {
  components: { Pagination },
  data() {
    return {
      dialogVisible: false,
      listQ: { page: 1, limit: 10, type: 1 },
      tableData: [],
      total: 0,
      dataType: 1, // 1 企业 2 空间
      loading: false
    }
  },
  methods: {
    // 打开弹窗
    openDialog(row) {
      this.init()
      this.dialogVisible = true
      this.listQ.company_id = row.company.id
      this.getList()
    },
    init() {
      this.dialogVisible = false
      this.listQ = { page: 1, limit: 10, type: 1 }
      this.tableData = []
      this.total = 0
      this.dataType = 1
    },
    // 关闭弹窗
    closeDialog() {
      this.init()
    },
    // 切换类型
    changeType(type) {
      this.listQ.type = type
      this.getList()
    },
    // 获取列表
    async getList() {
      const that = this
      try {
        this.loading = true
        const res = await orderListApi(that.listQ)
        const { code, data } = res
        if (code === 200) {
          const { data: list, total } = data
          that.tableData = list
          that.total = total
          that.dataType = that.listQ.type
          console.log(res)
        }
      } finally {
        this.loading = false
      }
    }
  }
}
</script>
<style lang='scss' scoped></style>
