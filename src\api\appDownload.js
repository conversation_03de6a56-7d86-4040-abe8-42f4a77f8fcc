import request from '@/utils/request'

// 市场列表
export function getStoreList(data) {
  return request({
    url: '/app_downloads/market/list',
    method: 'POST',
    data: data
  })
}

// 添加市场
export function addStore(data) {
  return request({
    url: '/app_downloads/market/store',
    method: 'POST',
    data: data
  })
}

// 添加市场
export function delStore(data) {
  return request({
    url: '/app_downloads/market/destory',
    method: 'POST',
    data: data
  })
}

// 下载量
export function getCountList(data) {
  return request({
    url: '/app_downloads/list',
    method: 'POST',
    data: data
  })
}

// 录入下载量
export function addCount(data) {
  return request({
    url: '/app_downloads/store',
    method: 'POST',
    data: data
  })
}

// 下载量详情
export function countDetail(data) {
  return request({
    url: '/app_downloads/detail',
    method: 'POST',
    data: data
  })
}
// 下载量详情
export function delCount(data) {
  return request({
    url: '/app_downloads/destory',
    method: 'POST',
    data: data
  })
}

