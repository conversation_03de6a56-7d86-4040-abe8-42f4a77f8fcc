import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/performtype/list',
    method: 'post',
    data
  })
}

export function qltypeindex() {
  return request({
    url: '/performtype/qltypeindex',
    method: 'post'
  })
}

export function storeRole(data) {
  return request({
    url: '/performtype/store',
    method: 'post',
    data
  })
}

export function deleteRole(data) {
  return request({
    url: '/performtype/delete',
    method: 'post',
    data
  })
}
