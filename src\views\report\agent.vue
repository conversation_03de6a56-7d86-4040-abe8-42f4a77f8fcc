<template>
  <div class="container">
    <div style="overflow:hidden;">
      <el-row :gutter="20" class="section-1">
        <el-col :xs="8" :sm="8" :lg="5">
          <SelfCard title="生效代理商总数">
            <count-to :start-val="0" :end-val="agent_data.agent_all" :duration="1000" class="card-panel-num" />
          </SelfCard>
        </el-col>
        <el-col :xs="8" :sm="8" :lg="5">
          <SelfCard title="代理商账户余额合计">
            <div style="display: flex;align-items: center;flex-wrap: wrap;">
              <span class="card-panel-num">￥{{ agent_data.agent_total }}</span>
              <el-button type="primary" @click="funupdate()">查看</el-button>
            </div>
          </SelfCard>
        </el-col>
        <el-col :xs="8" :sm="8" :lg="5">
          <SelfCard title="总生效客户数">
            <count-to :start-val="0" :end-val="agent_data.customecount" :duration="1000" class="card-panel-num" />
          </SelfCard>
        </el-col>
        <el-col :xs="8" :sm="8" :lg="5">
          <SelfCard title="用户总数">
            <div style="display: flex;align-items: end;justify-content: space-between;flex-wrap: wrap;">
              <count-to :start-val="0" :end-val="agent_data.customerallcount" :duration="1000" class="card-panel-num" />
              <p class="desc">企业：
                <count-to :start-val="0" :end-val="agent_data.customercount" :duration="1000" class="desc-num" />
                <span style="padding: 0 10px;">个人：
                  <count-to :start-val="0" :end-val="agent_data.customergrcount" :duration="1000" class="desc-num" />
                </span>
              </p>
            </div>
          </SelfCard>
        </el-col>
        <el-col :xs="8" :sm="8" :lg="5">
          <SelfCard title="在售产品总数">
            <count-to :start-val="0" :end-val="agent_data.product_count" :duration="1000" class="card-panel-num" />
          </SelfCard>
        </el-col>
      </el-row>
    </div>
    <div style="overflow:hidden;">
      <el-row :gutter="20" class="mt20">
        <el-col :xs="24" :sm="24" :lg="12" style="margin-bottom: 15px;">
          <agentchong />
        </el-col>
        <el-col :xs="24" :sm="24" :lg="12" style="margin-bottom: 15px;">
          <agentxiaofei />
        </el-col>
      </el-row>
    </div>
    <div style="overflow:hidden;">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :lg="12" style="margin-bottom: 15px;">
          <agentrecharge />
        </el-col>
        <el-col :xs="24" :sm="24" :lg="12" style="margin-bottom: 15px;">
          <agentprolist />
        </el-col>
      </el-row>
    </div>
    <div style="overflow:hidden;">
      <el-row :gutter="20" style="display: flex; align-items: stretch;">
        <el-col :xs="24" :sm="24" :lg="12" style="margin-bottom: 15px;">
          <regional />
        </el-col>
        <el-col :xs="24" :sm="24" :lg="12" style="margin-bottom: 15px;">
          <productTrend :all-data.sync="productData" :loading.sync="activeLoading" @getActive="getDrawTendency" />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import regional from './agent/agent/components/regional.vue'
import agentrecharge from './agent/agent/components/agentrecharge.vue'
import agentprolist from './agent/agent/components/agentprolist.vue'
import productTrend from './agent/agent/components/productTrend.vue'
import agentchong from './agent/agent/components/agentchong.vue'
import agentxiaofei from './agent/agent/components/agentxiaofei.vue'
import { agentLoginAndRegisterCount, drawTendency, userLoginAndRegisterCount } from '@/api/agent/statistic'
import SelfCard from '@/views/dashboard/admin/components/SelfCard.vue'
import CountTo from 'vue-count-to'

export default {
  name: 'Agent',
  components: { SelfCard, regional, agentrecharge, agentprolist, agentchong, agentxiaofei, productTrend, CountTo },
  data() {
    return {
      activeData: [],
      days: '',
      agent_data: [],
      visit_data: [],
      productData: {},
      activeLoading: false
    }
  },
  created() {
    this.getUserCount()
    // 看代理商的统计数据
    this.getAgentCount()
  },
  mounted() {

  },
  methods: {
    funupdate() {
      this.$router.push({
        name: 'agentAgentList',
        query: {
        }
      })
    },
    getDrawTendency(date, visit, client) {
      this.activeLoading = true
      drawTendency({ date: date, visit: visit, client: client }).then(res => {
        this.activeLoading = false
        this.productData = res.data
        this.activeData = res.data.data
      }).catch(() => {
        this.activeLoading = false
      })
    },
    getAgentCount() {
      agentLoginAndRegisterCount({ days: this.days }).then(res => {
        this.agent_data = res.data
      })
    },
    getUserCount() {
      userLoginAndRegisterCount({ days: this.days }).then(res => {
        this.visit_data = res.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .container {
    height: 100%;
    overflow: auto;
    box-sizing: border-box;

    .section-1 {
      margin-bottom: -20px;
      display: flex;
      flex-wrap: wrap;
      align-items: stretch;

      ::v-deep .el-col-lg-5 {
        width: 20% !important;
        margin-bottom: 20px;

        .self-card {
          height: 100%;
        }
      }

      ::v-deep .self-card {
        .top .left .title{
          font-size: 14px;
          color: #666;
          font-weight: normal;
        }

        .bottom {
          margin-top: 17px;
          margin-bottom: -10px;
        }
      }

      // 数字样式
      .card-panel-num {
        font-size: 24px;
        color: #222;
        margin-bottom: 10px;
        color: #4d80ff; // 深蓝文字

        & + * {
          margin-bottom: 10px;
        }
      }

      // 按钮样式
      .el-button {
        padding: 8px 15px;
        margin-left: 10px;
        background: linear-gradient(45deg, #4d80ff, #6a9dff); // 渐变按钮
        border: none;
      }

      // 新增desc样式
      .desc {
        font-size: 12px;
        color: #666;
        line-height: 1.5;
        text-align: right;
        margin-top: 0;

        .desc-num {
          color: #4d80ff;
          font-weight: bold;
          font-size: 16px;
        }
      }
    }

    // 响应式优化
    @media (max-width: 1200px) {
      .section-1 {
        ::v-deep .el-col {
          .self-card {
            padding: 15px;
          }
        }
      }
    }
  }
</style>
