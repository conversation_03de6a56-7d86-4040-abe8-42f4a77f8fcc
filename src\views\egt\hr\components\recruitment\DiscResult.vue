<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import DiscResultUpdate from './DiscResultUpdate.vue'
import { resultTableColumns } from '../../mixins/resultTable'
import { deleteDISCResultListApi, getDISCResultListApi } from '@/api/egt/recruitment'

export default {
  name: 'DiscResult',
  components: { StatisticsTemplate, DiscResultUpdate },
  data() {
    return {
      config: {
        key: 'discResult',
        tableSettings: {
          api: getDISCResultListApi,
          field: {
            page: 'pageIndex',
            limit: 'pageSize',
            total: 'total'
          },
          columns: [
            {
              label: 'ID',
              prop: 'id',
              width: 60
            },
            {
              label: '性格类型',
              prop: 'type',
              isSlot: true,
              width: 100
            },
            ...resultTableColumns
          ]
        }
      },
      updateVisible: false,
      row: {},
      typeMap: {}
    }
  },
  methods: {
    handleAdd() {
      this.updateVisible = true
      this.row = {}
    },
    handleUpdate(row) {
      this.updateVisible = true
      this.row = row
    },
    async handleDelete(row) {
      if (!row.id) return
      try {
        this.$refs.listWrap.setLoading(row.id, true)
        const res = await deleteDISCResultListApi({ id: row.id })
        if (res.code === 200) {
          this.$message.success('删除成功')
          await this.$refs.listWrap.handleGetData()
        } else {
          this.$message.error(res.msg || res.message || '删除失败')
        }
      } catch (e) {
        console.log(e)
        this.$message.error('删除失败')
      } finally {
        this.$refs.listWrap.setLoading(row.id, false)
      }
    },
    getData(res) {
      this.typeMap = res.data.type
    },
    submitSuccess() {
      this.$refs.listWrap.handleGetData()
    }
  }
}
</script>

<template>
  <div class="list">
    <statistics-template ref="listWrap" :config="config" @get-data="getData">
      <template #topActions>
        <div style="margin-bottom: 10px;text-align: right;width: 100%;">
          <el-button type="primary" @click="handleAdd">添加</el-button>
        </div>
      </template>
      <template #discResult_type="{row}">
        <span>{{ typeMap[row.type] }}</span>
      </template>
      <template #discResult_action="{row}">
        <el-button size="mini" @click="handleUpdate(row)">修改</el-button>
        <el-popconfirm
          title="确定删除吗？"
          @onConfirm="handleDelete(row)"
        >
          <el-button slot="reference" size="mini" type="danger">删除</el-button>
        </el-popconfirm>
      </template>
    </statistics-template>
    <disc-result-update
      :visible.sync="updateVisible"
      :data="row"
      @submit-success="submitSuccess"
    />
  </div>
</template>

<style scoped lang="scss">
.list{
  height: 100%;
}
</style>
