<script>
import UseData from './admin/components/new/UseData.vue'
import CommonlyUsed from '@/views/dashboard/admin/components/new/CommonlyUsed.vue'
import OrderStatistics from '@/views/dashboard/admin/components/new/OrderStatistics.vue'
import LatestTickets from '@/views/dashboard/admin/components/new/LatestTickets.vue'
import PlatformOrder from '@/views/dashboard/admin/components/new/PlatformOrder.vue'
import ActiveTrends from '@/views/dashboard/admin/components/new/ActiveTrends.vue'
import TimeTrends from '@/views/dashboard/admin/components/new/TimeTrends.vue'
import CoreFunc from '@/views/dashboard/admin/components/new/CoreFunc.vue'
import ActiveArea from '@/views/dashboard/admin/components/new/ActiveArea.vue'

export default {
  name: 'DashboardHome',
  components: {
    ActiveArea,
    CoreFunc,
    TimeTrends,
    ActiveTrends,
    PlatformOrder,
    LatestTickets,
    OrderStatistics,
    CommonlyUsed,
    UseData
  }
}
</script>

<template>
  <div class="dashboard">
    <UseData />
    <div class="mt20 section-1 section">
      <div class="commonly-used-wrap">
        <CommonlyUsed />
      </div>
      <div class="order-statistics-wrap">
        <OrderStatistics />
      </div>
    </div>
    <el-row class="mt20 section-2 section" :gutter="20" style="margin-bottom: -20px;">
      <el-col :md="12" :sm="24" style="margin-bottom: 20px;">
        <div class="latest-tickets-wrap h100">
          <LatestTickets />
        </div>
      </el-col>
      <el-col :md="12" :sm="24" style="margin-bottom: 20px;">
        <div class="platform-orders-wrap h100">
          <PlatformOrder />
        </div>
      </el-col>
    </el-row>
    <el-row class="mt20 section-3 section" :gutter="20" style="margin-bottom: -20px;">
      <el-col :md="12" :sm="24" style="margin-bottom: 20px;">
        <div class="active-trends-wrap h100">
          <ActiveTrends />
        </div>
      </el-col>
      <el-col :md="12" :sm="24" style="margin-bottom: 20px;">
        <div class="time-tends-wrap h100">
          <TimeTrends />
        </div>
      </el-col>
    </el-row>
    <div class="mt20 section-4">
      <CoreFunc />
    </div>
    <div class="mt20 section-5">
      <ActiveArea />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.dashboard {
  background: #F0F2F5;
  padding: 20px 30px;
  height: 100%;
  overflow: auto;
  .h100{
    height: 100%;
  }
  .section{
    display: flex;
    justify-content: space-between;
    align-items: stretch;
    flex-wrap: wrap;
    ::v-deep .el-input--medium .el-input__inner{
      height: 32px;
      line-height: 32px;
    }
  }
  .section-1 {
    .commonly-used-wrap {
      width: 56%;
    }
    .order-statistics-wrap {
      width: calc(100% - 56% - 20px);
    }
  }
}
::v-deep .el-table th.is-leaf{
  height: 40px;
  background: #F2F6F9;
}
@media screen and (max-width: 860px) {
  .dashboard{
    .section-1 {
      display: block;
      .commonly-used-wrap,
      .order-statistics-wrap{
        width: 100%;
      }
      .order-statistics-wrap{
        margin-top: 20px;
      }
    }
  }
}
</style>
