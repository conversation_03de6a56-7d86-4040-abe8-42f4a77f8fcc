<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>任务执行类型列表</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="createMenu">新增任务执行类型</el-button>
      </div>
      <el-table
        :data="tableData"
        stripe
        style="width: 100%"
      >
        <el-table-column label="ID" prop="id" width="65" />
        <el-table-column label="标题" prop="name" />
        <el-table-column label="类型" prop="typename" />
        <el-table-column label="状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status" type="success" effect="dark" size="small">启用</el-tag>
            <el-tag v-else type="danger" effect="dark" size="small">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="editDialog(scope.row)">编辑</el-button>
            <el-button type="text" @click="destory(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="query.page" :limit.sync="query.perPage" @pagination="getList" />
    </el-card>
      <!--表单-->
    <el-dialog
      title="任务执行类型管理"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-form ref="ruleForm" :model="formData" :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="名称：" prop="name">
          <el-col :span="12">
            <el-input v-model="formData.name" />
          </el-col>
        </el-form-item>
        <el-form-item label="任务类型："  prop="type">
          <el-select v-model="formData.type" placeholder="请选择" clearable>
            <el-option v-for="app in qltype" :key="app.id" :label="app.name" :value="app.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态开关：" prop="status">
          <el-col :span="20">
            <el-switch
              v-model="formData.status"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
            />
          </el-col>
        </el-form-item>
        <el-form-item label="排序位置：" prop="orderid">
          <el-col :span="10">
            <el-input-number v-model="formData.orderid" :min="0" :max="9999999" :step="1" />
          </el-col>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="saveHandle">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { list, deleteRole, storeRole, qltypeindex } from '../../../api/performtype'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
export default {
  components: { Pagination },
  data() {
    return {
      loading: false,
      total: 0,
      query: { page: 1, perPage: 10 },
      dialogVisible: false,
      formData: {},
      tableData: [],
      qltype: [],
      rules: {
        name: [
          { required: true, message: '缺少名称', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getqltypeList()
  },
  methods: {
    getList() {
      this.loading = true
      list(this.query).then(response => {
        this.loading = false
        this.tableData = response.data.data
        this.total = response.data.total
      }).catch(error => {
        console.log(error)
        this.loading = false
      })
    },
    getqltypeList() {
      qltypeindex().then(response => {
        this.qltype = response.data
      }).catch(error => {
        console.log(error)
      })
    },
    createMenu() {
      this.formData = {
        status: 1,
        orderid: 0
      }
      this.getList()
      this.dialogVisible = true
    },
    editDialog(row) {
      row.orderid=row.id
      this.getList()
      this.formData = row
      this.dialogVisible = true
    },
    saveHandle() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.loading = true
          if (this.formData.id) {
            storeRole(this.formData).then(response => {
              if (response.code === 200) {
                this.dialogVisible = false
                this.loading = false
                this.getList()
              }
            })
          } else {
            storeRole(this.formData).then(response => {
              if (response.code === 200) {
                this.dialogVisible = false
                this.loading = false
                this.getList()
              }
            }).catch(error => {
              console.log(error)
              this.loading = false
            })
          }
        }
      })
    },
    destory(id) {
      this.$alert('删除后不可恢复', '确定删除么', {
        confirmButtonText: '确定',
        callback: action => {
          if (action === 'confirm') {
            deleteRole({ id: id }).then(response => {
              this.$message.success('删除成功')
              this.getList()
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
