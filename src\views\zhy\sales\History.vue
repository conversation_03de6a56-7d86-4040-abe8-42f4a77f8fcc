<script>
export default {
  name: 'History',
  filters: {
    statusFilter(value) {
      if (value === '1') return '是'
      if (value === '0') return '否'
      return '未知'
    }
  },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    maxDisplay: {
      type: Number,
      default: 2
    }
  },
  data() {
    return {
      config: {
        key: 'follow',
        filters: [
          {
            label: '跟进日期',
            prop: 'dateRange',
            type: 'datePicker',
            settings: {
              type: 'daterange',
              valueFormat: 'yyyy-MM-dd',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期'
            }
          },
          {
            label: '金额范围',
            prop: 'amountRange',
            type: 'input',
            settings: {
              type: 'number',
              placeholder: '请输入金额'
            }
          }
        ],
        tableSettings: {
          selection: true,
          index: true,
          columns: [
            {
              label: '跟进金额',
              prop: 'amount',
              sortable: true
            },
            {
              label: '跟进目期',
              prop: 'followDate',
              sortable: true
            },
            {
              label: '客户意向',
              prop: 'intention',
              showOverflowTooltip: true
            },
            {
              label: '跟进情况',
              prop: 'followStatus',
              showOverflowTooltip: true
            },
            {
              label: '创建时间',
              prop: 'createTime',
              sortable: true
            },
            {
              label: '操作',
              prop: 'action',
              fixed: 'right',
              width: '150px',
              isSlot: true
            }
          ]
        }
      },
      // 弹窗相关数据
      dialogVisible: false,
      dialogTitle: '新增跟进记录',
      submitLoading: false,
      form: {
        amount: 0,
        followDate: '',
        intention: '',
        followStatus: ''
      },
      rules: {
        amount: [
          { required: true, message: '请输入跟进金额', trigger: 'blur' }
        ],
        followDate: [
          { required: true, message: '请选择跟进目期', trigger: 'change' }
        ],
        intention: [
          { required: true, message: '请输入客户意向', trigger: 'blur' }
        ],
        followStatus: [
          { required: true, message: '请输入跟进情况', trigger: 'blur' }
        ]
      },
      isExpanded: false
    }
  },
  computed: {
    displayList() {
      return this.isExpanded ? this.list : this.list.slice(0, this.maxDisplay)
    }
  },
  methods: {
    // 获取状态样式类
    getStatusClass(item) {
      if (item.price > 10000) return 'success'
      if (item.price > 5000) return 'warning'
      if (item.price > 1000) return 'info'
      return 'danger'
    },
    // 表格选择回调
    handleSelectionChange(selection) {
      this.selection = selection
    },
    // 新增记录
    handleAdd() {
      this.dialogTitle = '新增跟进记录'
      this.form = {
        amount: 0,
        followDate: '',
        intention: '',
        followStatus: ''
      }
      this.dialogVisible = true
    },
    // 编辑记录
    handleEdit(row) {
      this.dialogTitle = '编辑跟进记录'
      this.form = { ...row }
      this.dialogVisible = true
    },
    // 删除记录
    handleDelete(row) {
      this.$confirm('确认删除该记录?', '提示', {
        type: 'warning'
      }).then(() => {
        // 调用删除接口
        this.$message.success('删除成功')
        this.$emit('delete', row)
      }).catch(() => {})
    },
    // 提交表单
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.submitLoading = true
          // 发送数据到父组件
          this.$emit('submit', this.form)
          // 模拟保存成功
          setTimeout(() => {
            this.$message.success('保存成功')
            this.dialogVisible = false
            this.submitLoading = false
          }, 1000)
        }
      })
    },
    // 切换展开/收起
    toggleExpand() {
      this.isExpanded = !this.isExpanded
    }
  }
}
</script>

<template>
  <div class="timeline-section">
    <ul>
      <li v-for="item in displayList" :key="item.id" :class="getStatusClass(item)">
        <div class="time-badge">{{ item.updated_at }}</div>
        <div class="content-box">
          <p v-if="item.price" class="amount">
            <i class="el-icon-money" />
            <span>成单金额：¥{{ item.price }}</span>
          </p>
          <p v-if="item.intention" class="intention">
            <i class="el-icon-chat-line-round" />
            <span>客户意向：{{ item.intention }}</span>
          </p>
          <p v-if="item.followStatus" class="status">
            <i class="el-icon-document" />
            <span>是否成单：{{ item.status | statusFilter }}</span>
          </p>
          <p v-if="item.followup" class="extra">
            <i class="el-icon-info" />
            <span>跟进情况：{{ item.followup }}</span>
          </p>
        </div>
      </li>
    </ul>
    <div v-if="list.length > maxDisplay" class="expand-btn" @click="toggleExpand">
      <i :class="isExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" />
      <span>{{ isExpanded ? '收起' : `展开更多(${list.length - maxDisplay})` }}</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.timeline-section {
  height: 100%;
  padding: 20px;
  position: relative;

  ul {
    position: relative;
    padding-left: 40px;
    margin: 0;

    &::before {
      position: absolute;
      content: "";
      display: block;
      top: 0;
      bottom: 0;
      margin: auto;
      width: 2px;
      background: #e4e7ed;
      left: 20px;
    }

    li {
      list-style: none;
      position: relative;
      margin-bottom: 30px;
      font-size: 14px;

      &::before {
        position: absolute;
        content: "";
        display: block;
        width: 12px;
        height: 12px;
        top: 14px;
        background: #409eff;
        left: -26px;
        border-radius: 50%;
        border: 2px solid #fff;
        box-shadow: 0 0 0 2px rgba(64,158,255,0.3);
        transition: all 0.3s ease;
      }

      &:hover::before {
        transform: scale(1.2);
      }

      &.success:before {
        background-color: #67c23a;
        box-shadow: 0 0 0 2px rgba(103,194,58,0.3);
      }

      &.info:before {
        background-color: #909399;
        box-shadow: 0 0 0 2px rgba(144,147,153,0.3);
      }

      &.warning:before {
        background-color: #e6a23c;
        box-shadow: 0 0 0 2px rgba(230,162,60,0.3);
      }

      &.danger:before {
        background-color: #f56c6c;
        box-shadow: 0 0 0 2px rgba(245,108,108,0.3);
      }

      .time-badge {
        display: inline-block;
        padding: 4px 8px;
        background: #f2f6fc;
        border-radius: 4px;
        color: #606266;
        font-size: 12px;
        margin-bottom: 8px;
      }

      .content-box {
        background: #fff;
        border-radius: 8px;
        padding: 16px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
        border: 1px solid #ebeef5;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px 0 rgba(0,0,0,0.1);
        }

        p {
          margin: 8px 0;
          display: flex;
          align-items: center;
          line-height: 1.6;

          i {
            margin-right: 8px;
            font-size: 16px;
            color: #909399;
          }

          &.amount {
            color: #409eff;
            font-weight: 500;
            i { color: #409eff; }
          }

          &.intention {
            color: #606266;
            i { color: #e6a23c; }
          }

          &.status {
            color: #303133;
            i { color: #67c23a; }
          }

          &.extra {
            color: #606266;
            font-size: 13px;
            i { color: #909399; }
          }

          span {
            flex: 1;
          }
        }
      }
    }
  }

  .expand-btn {
    text-align: center;
    padding: 10px 0;
    cursor: pointer;
    color: #409eff;
    transition: all 0.3s ease;

    &:hover {
      color: #66b1ff;
    }

    i {
      margin-right: 4px;
      font-size: 14px;
      transition: transform 0.3s ease;
    }

    span {
      font-size: 14px;
    }
  }
}
</style>
