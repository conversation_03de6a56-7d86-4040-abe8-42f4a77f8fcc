<template>
  <div id="taskApproval" class="list-wrap">
    <div class="form">
      <el-form :inline="true" :model="query" class="demo-form-inline" method="get">
        <el-row>
          <el-form-item>
            <el-input v-model="query.username" placeholder="请输入申请人姓名" />
          </el-form-item>
          <el-form-item>
            <el-select v-model="query.status" clearable placeholder="审批状态">
              <el-option v-for="item in statusList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-date-picker
              v-model="query.createtime"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              placeholder="请输入申请时间"
            />
          </el-form-item>
          <el-form-item>
            <el-date-picker
              v-model="query.status_time"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              placeholder="请输入审批时间"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="getList(1)">搜索</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="batchApproval(1)">批量通过</el-button>
            <el-button type="danger" @click="batchApproval(2)">批量驳回</el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <div class="table" shadow="never">
      <el-row style="padding: 20px 0;border-bottom: 1px solid #dfe6ec">
        <el-col v-for="(item, index) in statistics" :key="index" :span="24 / statistics.length">
          <el-statistic :title="item.name" :value="item.value" />
        </el-col>
      </el-row>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%;margin-top: 20px;"
        height="calc(100% - 82px - 20px - 52px)"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" :selectable="selectable" />
        <el-table-column align="center" prop="username" label="申请人" width="100px" />
        <el-table-column align="center" prop="enterprise_name" label="公司名称" width="220px" />
        <el-table-column align="center" prop="position" label="申请岗位/星级" min-width="300px">
          <template #default="{ row }">
            {{ row.position }}/{{ row.gradeValue }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="status" label="审批状态" width="100px">
          <template #default="{ row }">
            <el-tag :type="statusColor(row.status)">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="createtime" label="申请时间" width="200px" />
        <el-table-column align="center" label="个人主页申请内容" width="150px">
          <template #default="{ row }">
            <el-button type="text" @click="showPersonal(row)">点击查看</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="status_time" label="审核时间" width="200px" />
        <el-table-column align="center" fixed="right" label="操作" width="200px">
          <template slot-scope="{row}">
            <el-button
              v-if="row.status === '审核中'"
              type="primary"
              size="small"
              @click="approvalApi(1, row.id)"
            >通过</el-button>
            <el-button
              v-if="row.status === '审核中'"
              type="danger"
              size="small"
              @click="changeDialogVisible({
                name: 'approvalDialogVisiable',
                id: row.id,
                idField: 'approvalId',
                title: '驳回原因',
                titleField: 'approvalTitle',
                data: row,
                dataField: 'approvalData',
                visible: true,
              })"
            >驳回</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="page" style="text-align: center;margin-top: 20px;">
        <el-pagination
          v-show="total > 0"
          background
          :total="total"
          :current-page="page.page"
          :page-sizes="pageSizes"
          :page-size="page.limit"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="getList"
          @size-change="sizeChange"
        />
      </div>
    </div>

    <!-- 审批备注 -->
    <el-dialog
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :title="approvalTitle"
      :visible.sync="approvalDialogVisiable"
      width="500px"
    >
      <el-form v-if="approvalDialogVisiable" ref="approvalForm" :model="approvalForm" :rules="approvalFormRules">
        <el-form-item prop="remarks" label="驳回原因">
          <el-input v-model="approvalForm.remarks" type="textarea" placeholder="请输入驳回原因" />
        </el-form-item>
        <div style="text-align: center; margin-top: 30px">
          <el-button type="primary" @click="approval(2, approvalData)">确定</el-button>
          <el-button @click="closeApprovalDialogDetail">取消</el-button>
        </div>
      </el-form>
    </el-dialog>

    <!-- 个人主页 -->
    <el-dialog
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :title="personalTitle"
      :visible.sync="personalDialogVisiable"
      fullscreen
    >
      <personal v-if="personalDialogVisiable" :id="personalId" :data="personalData" @close="closePersonal" />
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disable */
  import ApprovalDetail from "./components/ApprovalDetail.vue";
  import { getList } from "@/api/task";
  import ElStatistic from "@/components/ElStatistic";
  import Personal from './components/Personal.vue';
  import dialog from '@/mixins/dialog';
  import selfRequest from '@/mixins/selfRequest';
  import form from '@/mixins/form';
  import { getPageMax } from "@/utils/getPageMax";
  import sizeChange from "@/mixins/sizeChange";

  export default {
    name: "TaskApproval",
    components: {
      ApprovalDetail,
      ElStatistic,
      Personal
    },
    data() {
      return {
        query: {
          username: "",
          status: '',
          createtime: '',
          status_time: ''
        },
        loading: false,
        tableData: [],
        approvalTitle: "",
        approvalDialogVisiable: false,
        approvalId: undefined,
        approvalData: {},
        statusList: [
          { id: 0, name: "待审核" }, { id: 1, name: "已通过" }, { id: 2, name: "已驳回" }
        ],
        statistics: [
          {
            name: '待审核个人主页数量',
            value: 0
          },
          {
            name: '已审核个人主页数量',
            value: 0
          }
        ],
        personalDialogVisiable: false,
        personalId: '',
        personalTitle: '',
        personalData: {},
        visiable: false,
        approvalForm: {
          remarks: ''
        },
        approvalFormRules: {
          remarks: [{ required: true, message: '请输入驳回原因', trigger: 'blur' }]
        },
        approvaLoading: false,
        selectRows: []
      }
    },
    mixins: [dialog, selfRequest, form, sizeChange],
    methods: {
      // 过滤已审核
      selectable(row, index) {
        return row.status === '审核中'
      },
      // 获取列表
      getList(page) {
        if (page) this.page.page = page;
        const data = { ...this.query, ...this.page };
        this.loading = true;
        getList(data).then(res => {
          if (res.code === 200 && res.data) {
            if (res.data.data) {
              this.tableData = res.data.data; 	// 列表数据
            }
            this.total = res.data.count; // 总条数
            this.statistics[0].value = res.data.falseNum;
            this.statistics[1].value = res.data.trueNum;

            this.pageSizes = getPageMax(this.total, this.pageSizes);
          }
        }).finally(() => {
          this.loading = false;
        })
      },
      // 关闭弹窗
      closeApprovalDialogDetail() {
        this.changeDialogVisible({
          name: 'approvalDialogVisiable',
          id: '',
          idField: 'approvalId',
          title: '驳回原因',
          titleField: 'approvalTitle',
          data: {},
          dataField: 'approvalData',
          visible: false
        })
      },
      // 表格选中
      handleSelectionChange(rows) {
        this.selectRows = rows;
      },
      // 获取状态
      statusColor(val) {
        let statusMap = {
          '审核中': '',
          '已通过': 'success',
          '已驳回': 'danger'
        }
        return statusMap[val];
      },
      // 个人主页弹窗
      showPersonal(data) {
        this.changeDialogVisible({
          name: 'personalDialogVisiable',
          id: data ? data.id : '',
          idField: 'personalId',
          title: '个人主页',
          titleField: 'personalTitle',
          data: data,
          dataField: 'personalData',
          visible: true
        })
      },
      // 关闭个人主页
      closePersonal() {
        this.changeDialogVisible({
          name: 'personalDialogVisiable',
          id: '',
          idField: 'personalId',
          title: '个人主页',
          titleField: 'personalTitle',
          data: {},
          dataField: 'personalData',
          visible: false
        })
      },
      // 审批
      approvalApi(type, id, batch = false) {
        let typeMap = {
          1: '通过',
          2: '驳回'
        }
        let apiName = '';
        if (batch) {
          if (type === 1) {
            apiName = 'approvalTrue'
          } else {
            apiName = 'approvalFalse'
          }
        } else {
          apiName = 'approval'
        }
        this.showConfirm({
          tips: '确定要' + typeMap[type] + '该申请吗？',
          loadingField: 'approvaLoading',
          apiName,
          data: {
            id,
            status: type,
            remark: this.approvalForm.remarks
          },
          successMsg: '审核成功',
          errMsg: '审核失败',
          success: ({ data }) => {
            this.getList()
            if (type === 2) {
              this.closeApprovalDialogDetail();
            }
          }
        })
      },
      // 驳回意见
      approval() {
        this.validateForm('approvalForm', this.reject)
      },
      reject() {
        this.approvalApi(2, this.approvalData.id);
      },
      batchApproval(type) {
        if (this.selectRows.length === 0) {
          return this.$message.warning('请选择要操作的数据');
        }
        this.approvalApi(type, this.selectRows.map(item => item.id).join(','), true);
      }
    },
    mounted() {
      this.getList(1)
      this.$nextTick(() => {
        // 获取.tab-content的高度
        const tabContent = document.querySelector('.tab-content')
        const tabContentHeight = tabContent.clientHeight
        // 获取.form的高度
        const filter = document.querySelector('.form')
        const filterHeight = filter.clientHeight
        // 计算.table的高度
        const tableHeight = tabContentHeight - filterHeight
        // 设置.table的高度
        const table = document.querySelector('.table')
        table.style.height = tableHeight + 'px'
      })
    }
  };
</script>

<style lang="scss" scoped></style>
