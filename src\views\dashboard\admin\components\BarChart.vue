<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    chartData: {
      type: [Array, Object],
      required: true
    },
    legend: {
      type: Array,
      required: true
    },
    colorList: {
      type: Array,
      required: false,
      default: () => []
    },
    showLegend: {
      type: Boolean,
      required: false,
      default: true
    },
    grid: {
      type: Object,
      required: false,
      default: () => ({
        left: 10,
        right: 10,
        bottom: 20,
        top: 50,
        containLabel: true
      })
    },
    dataType: {
      type: String,
      default: 'normal'
    },
    showZoom: {
      type: Boolean,
      required: false,
      default: true
    },
    rotate: {
      type: Number,
      required: false,
      default: 0
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.setOptions(this.chartData)
    },
    setOptions(chartData = []) {
      const options = {
        dataset: {
          source: chartData
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: this.grid,
        xAxis: {
          type: 'category',
          axisLabel: {
            lineHeight: 30,
            padding: [10, 0, 0, 0],
            rotate: this.rotate,
            interval: 0
          }
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: false
          }
        },
        dataZoom: {
          minSpan: 18,
          show: this.showZoom,
          endValue: 14,
          zoomOnMouseWheel: true
        },
        series: []
      }

      if (this.dataType === 'dataset') {
        options.dataset = {
          source: chartData
        }
      } else {
        options.xAxis.data = this.chartData.x
      }

      options.series = this.legend.map((v, i) => {
        return {
          name: v,
          barWidth: '30%',
          barMaxWidth: 30,
          itemStyle: {
            color: this.colorList[i]
          },
          smooth: true,
          type: 'bar',
          animationDuration: 2800,
          animationEasing: 'cubicInOut',
          data: this.chartData.y
        }
      })

      try {
        this.chart.setOption(options)
        this.chart.on('dataZoom', (params) => {
          const start = params.start
          const end = params.end
          const diff = end - start
          options.dataZoom.start = start
          options.dataZoom.end = end
          // diff小于40，算作‘缩放到一定程度’
          if (diff < 20) {
            options.xAxis.axisLabel.rotate = 0 // 当缩放程度小于40的时候让字体倾斜为0
            options.xAxis.axisLabel.formatter = (data) => {
              console.log(data)
              data = data.replace('-no', '')
              if (data.length > 10) {
                data = data.replace('-', '- \n')
              }
              return data
            }
            this.chart.clear()
            this.chart.setOption(options, false)
          }
          if (diff >= 20) {
            options.xAxis.axisLabel.rotate = this.rotate // 当缩放程度大于40的时候让字体开始倾斜
            this.chart.clear()
            this.chart.setOption(options, false)
          }
        })
      } catch (e) {
        console.log(e)
      }
    }
  }
}
</script>
