import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/sell/list',
    method: 'POST',
    data
  })
}

export function store(data) {
  return request({
    url: '/sell/store',
    method: 'POST',
    data
  })
}

export function selldelete(data) {
  return request({
    url: '/sell/delete',
    method: 'POST',
    data
  })
}

export function getpackageapps(data) {
  return request({
    url: '/sell/getpackageapps',
    method: 'POST',
    data
  })
}
