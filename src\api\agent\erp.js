import request from '@/utils/request'
import platformRequest from '@/utils/requestAPI'

export function websiteIndex(data) {
  return request({
    url: '/user/index',
    method: 'post',
    data
  })
}

export function websiteAdd(data) {
  return request({
    url: '/user/create',
    method: 'post',
    data
  })
}

export function websiteUpdate(data) {
  return request({
    url: '/user/update',
    method: 'post',
    data
  })
}

export function websiteDel(data) {
  return request({
    url: '/user/password',
    method: 'post',
    data
  })
}

export function erpIdc_create(data) {
  return request({
    url: '/user/destroy',
    method: 'post',
    data
  })
}

export function erpIdc_destory(data) {
  return request({
    url: '/user/status',
    method: 'post',
    data
  })
}

export function removeUserProfile(data) {
  return request({
    url: '/user/status',
    method: 'post',
    data
  })
}

export function editPwd(data) {
  return request({
    url: '/user/status',
    method: 'post',
    data
  })
}

export function getProductList(data) {
  return request({
    url: '/user/status',
    method: 'post',
    data
  })
}

export function sms(data) {
  return platformRequest({
    url: '/agent/code',
    method: 'post',
    data
  })
}

