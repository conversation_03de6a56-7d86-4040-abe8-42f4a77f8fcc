<script>
import { addCompanyServiceSettingsApi, getCompanyServiceSettingsUpdateApi, updateCompanyServiceSettingsApi } from '@/api/egt/companyService'
import SelfFormTemp from '@/views/task/components/form/SelfFormTemp.vue'
import update from '@/views/task/mixins/update'

export default {
  name: 'Update',
  components: {
    SelfFormTemp
  },
  mixins: [update],
  data() {
    return {
      formData: {
        id: undefined,
        title: '',
        remarks: '1',
        img: [],
        content: '',
        file: ''
      },
      filters: [
        {
          label: '待办标题',
          type: 'input',
          prop: 'title'
        },
        {
          label: '待办简介',
          type: 'textarea',
          prop: 'remarks'
        },
        {
          label: '待办展示图',
          type: 'upload',
          prop: 'img',
          settings: {
            baseUrl: 'https://hrcloud.obs.cn-north-4.myhuaweicloud.com/',
            action: process.env.VUE_APP_IHR_PROXY + '/Api_Docking/imgupload',
            accept: 'image/*',
            multiple: false,
            disabled: false,
            limit: 1,
            placeholder: '请上传1个2M以内的文件封面'
          }
        },
        {
          label: '待办内容',
          type: 'editor',
          prop: 'content'
        }
      ],
      rules: {
        title: [
          { required: true, message: '请输入文件名称', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    _visible(val) {
      if (val) {
        this.getParams()
      }
    }
  },
  methods: {
    async getParams() {
      const api = this.data.id ? getCompanyServiceSettingsUpdateApi : undefined
      if (!api) return
      try {
        this.loading = true
        const res = await api({ id: this.data.id })
        if (res.code === 200 && res.data) {
          console.log('[ res ] >', res)
          // this.formData = res.data
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false
      }
    },
    handlePreview({ prop }) {
      let url = ''
      if (prop === 'img') {
        url = this.formData.img[0].url
      }
      if (prop === 'document_file') {
        url = this.formData.document_file[0].url
      }
      if (!url) return
      window.open(url)
    },
    handleRemove(file, fileList, prop) {
      this.formData[prop] = fileList
    },
    handleSuccess({ response, prop }) {
      const res = response.response
      if (res.code === 200) {
        const result = [
          {
            uid: response.file.uid || Date.now(),
            url: res.url + res.filepath,
            name: response.file.name || '',
            postUrl: res.filepath
          }
        ]
        this.formData[prop] = result
      } else {
        this.$message.error(res.msg || res.message || '上传失败')
      }
    },
    async submitCallback() {
      const api = this.data.id ? updateCompanyServiceSettingsApi : addCompanyServiceSettingsApi
      if (!api) return
      const postData = JSON.parse(JSON.stringify(this.formData))
      if (this.data.id) {
        if (postData.img && postData.img.length) {
          postData.img = postData.img[0].uid
        }
      } else {
        if (postData.img && postData.img.length) {
          postData.img = postData.img[0].postUrl
        }
      }
      try {
        this.loading = true
        const res = await api(postData)
        if (res.code === 200) {
          this.$emit('submit-success')
          return Promise.resolve({ success: true })
        } else {
          return Promise.reject({ success: false, msg: res.msg || res.message || '操作失败' })
        }
      } catch (error) {
        return Promise.reject({ success: false, msg: '操作失败' })
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<template>
  <el-dialog
    :title="title"
    :visible.sync="_visible"
    width="50%"
  >
    <SelfFormTemp
      ref="formWrap"
      :form-data.sync="formData"
      self-key="print"
      :filters="filters"
      :inline="false"
      label-width="100px"
      :rules="rules"
      @preview="handlePreview"
      @remove="handleRemove"
      @success="handleSuccess"
    />
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel()">取消</el-button>
      <el-button type="primary" @click="handleSubmit()">确定</el-button>
    </div>
  </el-dialog>
</template>
