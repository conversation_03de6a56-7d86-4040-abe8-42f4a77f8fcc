import request from '@/utils/request'

export function fetchList(data) {
  return request({
    url: '/integral/list',
    method: 'post',
    data: data
  })
}

export function store(data) {
  return request({
    url: '/integral/store',
    method: 'post',
    data: data
  })
}

export function remove(data) {
  return request({
    url: '/integral/remove',
    method: 'post',
    data: data
  })
}
export function goodsindex(data) {
  return request({
    url: '/integral/goodsindex',
    method: 'post',
    data: data
  })
}

export function givingstore(data) {
  return request({
    url: '/integral/givingstore',
    method: 'post',
    data: data
  })
}

export function givingindex(data) {
  return request({
    url: '/integral/givingindex',
    method: 'post',
    data: data
  })
}

export function givingremove(data) {
  return request({
    url: '/integral/givingremove',
    method: 'post',
    data: data
  })
}
