<template>
  <div class="list-wrap">
    <div class="filter">
      <el-form :inline="true" :model="listQuery" class="demo-form-inline" size="small">
        <el-form-item label="游戏名称：">
          <el-input v-model="listQuery.title" placeholder="请输入游戏名称" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getList">过滤</el-button>
        </el-form-item>
        <switch-package-type @switchPackageCallback="switchPackageCallback" />
        <el-form-item>
          <el-button icon="el-icon-refresh-right" type="primary" @click="getList">刷新</el-button>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-circle-plus-outline"
            style="margin-left: 40px;"
            @click="addGameInfo('新增')"
          >新增</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table mt20">
      <el-table v-loading="loading" :data="tableData" style="width: 100%" height="calc(100% - 96px)">
        <el-table-column label="ID" type="index" align="center" />
        <el-table-column prop="title" label="游戏名称" width="220px" />
        <el-table-column prop="icon" label="游戏图标" width="120px">
          <template slot-scope="scope">
            <el-image
              v-if="scope.row.icon"
              :src="scope.row.icon"
              class="img-color"
              :preview-src-list="[scope.row.icon]"
              fit="contain"
            >
              <div slot="placeholder" class="image-slot">
                加载中<span class="dot">...</span>
              </div>
            </el-image>
            <span v-else>未设置</span>
          </template>
        </el-table-column>
        <el-table-column prop="game_url" label="游戏链接" />
        <el-table-column prop="status" label="状态" width="120px">
          <template slot-scope="scope">
            {{ scope.row.status === 1 ? "已上线" : "未上线" }}
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="添加时间" width="180px" />
        <el-table-column label="操作" width="120px" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="addGameInfo('编辑', scope.row)">编辑</el-button>
            <el-button type="text" size="mini" @click="delGameInfo(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </div>

    <el-dialog :close-on-click-modal="false" :visible.sync="editDialogVisible" title="新增小游戏" width="660px" destroy-on-close @click="closeDialog">
      <edit-game ref="editGameRef" @closeDialog="closeDialog" />
    </el-dialog>
  </div>
</template>

<script>
import { delGame, getGameList } from '@/api/jzt/marketing'
import EditGame from './game/editGame'
import Pagination from '@/components/Pagination'
import SwitchPackageType from '@/views/jzt/components/SwitchPackageType.vue'

export default {
  name: 'Game',
  components: { SwitchPackageType, Pagination, EditGame },
  data() {
    return {
      listQuery: {
        page: 1,
        limit: 10,
        title: ''
      },
      total: 0,
      tableData: [],
      loading: false,
      editDialogVisible: false
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.tab-content')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      console.log(tabContentHeight, filterHeight)
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  methods: {
    switchPackageCallback() {
      this.getList()
    },
    getList() {
      this.loading = true
      getGameList(this.listQuery).then(response => {
        this.loading = false
        this.tableData = response.data.data
        this.total = response.data.total
      })
    },
    // 新增 编辑
    addGameInfo(type, data) {
      const that = this
      that.editDialogVisible = true
      if (type === '编辑') {
        that.$nextTick(e => {
          that.$refs.editGameRef.initFormData(data)
        })
      }
    },
    // 删除
    delGameInfo(id) {
      this.$confirm('确定要删除该数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(_ => {
        delGame({ id }).then(res => {
          this.$message.success(res.msg)
          this.getList()
        })
      }).catch(_ => {
        console.log('取消')
      })
    },
    // 关闭弹窗
    closeDialog(refresh = false) {
      this.editDialogVisible = false
      if (refresh) {
        this.getList()
      }
    }
  }
}

</script>

<style scoped>
.img-color {
  width: 100px;
  height: 100px;
  border-radius: 5px;
}
</style>

<style lang="scss" scoped>
.list-wrap{
  .filter{
    border-bottom: 1px solid #e5e5e5;
  }
 .table{
    min-height: 200px;
 }
}
</style>
