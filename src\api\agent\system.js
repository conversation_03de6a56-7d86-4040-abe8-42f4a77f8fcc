import request from '@/utils/requestAgent'

export function moduleList(data) {
  return request({
    url: 'system/list',
    method: 'post',
    data: data
  })
}

export function store(data) {
  return request({
    url: 'system/store',
    method: 'post',
    data: data
  })
}
export function update(data) {
  return request({
    url: 'system/update',
    method: 'post',
    data: data
  })
}
export function destroy(data) {
  return request({
    url: 'system/delete',
    method: 'post',
    data: data
  })
}

// 左栏 用户管理 产品开通 未审核通知
export function routerNotice(data) {
  return request({
    url: 'system/router/notice',
    method: 'post',
    data: data
  })
}

export function wordList(data) {
  return request({
    url: 'system/wordlist',
    method: 'post',
    data: data
  })
}

export function wordstore(data) {
  return request({
    url: 'system/wordstore',
    method: 'post',
    data: data
  })
}

export function wordsdestroy(data) {
  return request({
    url: 'system/wordsdestroy',
    method: 'post',
    data: data
  })
}

export function wordsupdate(data) {
  return request({
    url: 'system/wordsupdate',
    method: 'post',
    data: data
  })
}

export function wordloglist(data) {
  return request({
    url: 'system/wordloglist',
    method: 'post',
    data: data
  })
}
