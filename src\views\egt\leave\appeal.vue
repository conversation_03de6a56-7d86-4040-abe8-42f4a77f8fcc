<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import { delAppealApi, getAppealListApi } from '@/api/egt/appeal'
import Update from './appeal/Update.vue'

export default {
  name: 'Appeal',
  components: { Update, StatisticsTemplate },
  data() {
    return {
      config: {
        key: 'appeal',
        tableSettings: {
          api: getAppealListApi,
          field: {
            page: 'pageIndex',
            limit: 'pageSize',
            total: 'total'
          },
          columns: [
            {
              label: 'ID',
              prop: 'id',
              width: 60
            },
            {
              label: '企业名称',
              prop: 'enterprise_name'
            },
            {
              label: '申诉身份证号码',
              prop: 'numberid'
            },
            {
              label: '申诉身份证姓名',
              prop: 'name',
              width: 120
            },
            {
              label: '申诉时间',
              prop: 'create_time',
              width: 180
            },
            {
              label: '状态',
              prop: 'status',
              isSlot: true,
              width: 120
            },
            {
              label: '操作',
              prop: 'action',
              width: 150,
              isSlot: true,
              fixed: 'right',
              align: 'center'
            }
          ]
        }
      },
      row: {},
      updateVisible: false
    }
  },
  methods: {
    getStatus(row) {
      const status = {
        0: { text: '待处理', color: 'warning' },
        1: { text: '已处理', color: 'success' },
        2: { text: '已驳回', color: 'danger' }
      }
      return status[row.status]
    },
    handleUpdate(row) {
      this.updateVisible = true
      this.row = row
    },
    async handleDelete(row) {
      if (!row.id) return
      try {
        this.$refs.statisticsTemplate.setLoading(row, true)
        const res = await delAppealApi({ id: row.id })
        if (res.code === 200) {
          this.$message.success('删除成功')
          await this.$refs.statisticsTemplate.handleGetData()
        } else {
          this.$message.error(res.msg || res.message || '删除失败')
        }
      } catch (error) {
        console.log('[ error ] >', error)
        this.$message.error(error.msg || error.message || '删除失败')
      } finally {
        this.$refs.statisticsTemplate.setLoading(row, false)
      }
    },
    handleSubmitSuccess() {
      this.$refs.statisticsTemplate.handleGetData()
    }
  }
}
</script>

<template>
  <div class="page-wrap">
    <statistics-template ref="statisticsTemplate" :config="config">
      <template v-slot:appeal_status="{row}">
        <div>
          <el-tag :type="getStatus(row).color">{{ getStatus(row).text }}</el-tag>
        </div>
      </template>
      <template v-slot:appeal_action="{row}">
        <el-button size="mini" @click="handleUpdate(row)">编辑</el-button>
        <el-popconfirm
          title="确定删除吗？"
          style="margin-left: 10px;"
          @onConfirm="handleDelete(row)"
        >
          <el-button slot="reference" type="danger" size="mini">删除</el-button>
        </el-popconfirm>
      </template>
    </statistics-template>

    <Update :visible.sync="updateVisible" :data="row" @submit-success="handleSubmitSuccess" />
  </div>
</template>

<style scoped lang="scss">
.page-wrap{
  height: 100%;
}
</style>
