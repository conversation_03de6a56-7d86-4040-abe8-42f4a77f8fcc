<template>
  <div class="app-container">
    <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="120px" class="demo-ruleForm">
      <el-form-item label="请输入原密码：" prop="original_password">
        <el-input v-model="ruleForm.original_password" type="password" />
      </el-form-item>
      <el-form-item label="请输入新密码：" prop="password">
        <el-input v-model="ruleForm.password" type="password" />
      </el-form-item>
      <el-form-item label="请确认新密码：" prop="password_confirmation">
        <el-input v-model="ruleForm.password_confirmation" type="password" />
      </el-form-item>
      <el-form-item>
        <el-row type="flex" justify="center">
          <el-col :span="12">
            <el-button type="primary" size="small" @click="changeProfilePassword">保存</el-button>
            <el-button type="warning" size="small" @click="ruleForm={}">重置</el-button>
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { changeProfile } from '../../../api/user'
export default {
  name: 'ChangePassword',
  data() {
    const validatePassword = (rule, value, callback) => {
      if (value !== this.ruleForm.password) {
        callback(new Error('两次输入密码不一致'))
      }
      if (value.length < 6) {
        callback(new Error('密码不能小于6位'))
      } else {
        callback()
      }
    }
    return {
      ruleForm: {
        type: 1
      },
      rules: {
        original_password: [
          { required: true, message: '原始密码必填', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '新密码必填', trigger: 'blur' }
        ],
        password_confirmation: [
          { required: true, trigger: 'blur', validator: validatePassword }
        ]
      }
    }
  },
  methods: {
    changeProfilePassword() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          changeProfile(this.ruleForm).then(response => {
            this.$message.success('修改成功')
          })
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
