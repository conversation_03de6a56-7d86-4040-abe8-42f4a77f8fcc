import request from '@/utils/request'

export function list() {
  return request({
    url: '/news/category/list',
    method: 'POST'
  })
}

export function topList() {
  return request({
    url: '/news/category/topList',
    method: 'POST'
  })
}

export function store(data) {
  return request({
    url: '/news/category/store',
    method: 'POST',
    data: data
  })
}

export function edit(data) {
  return request({
    url: '/news/category/update',
    method: 'POST',
    data: data
  })
}

export function destory(data) {
  return request({
    url: '/news/category/destory',
    method: 'POST',
    data: data
  })
}
