<template>
  <div class="list-wrap">
    <el-card shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <!--<span>活动产品</span>-->
        <el-button style="float: right; padding: 3px 0" type="text" :loading="commit_loading" @click="commit">保存</el-button>
      </div>
      <el-row>
        <el-col span="7">
          <el-card v-loading="product_loading" style="width: 400px">
            <div slot="header" class="clearfix">
              <span>产品</span>
            </div>
            <el-input
              v-model="filterProduct"
              placeholder="输入关键字进行过滤"
            />
            <el-tree
              ref="product"
              class="filter-tree"
              node-key="id"
              :data="product_list"
              :props="defaultProps"
              :default-checked-keys="default_product_ids"
              default-expand-all
              show-checkbox
              :filter-node-method="filterNode"
            />
          </el-card>
        </el-col>
        <el-col span="7">
          <el-card v-loading="lc_loading" style="width: 400px">
            <div slot="header" class="clearfix">
              <span>龙采代理商</span>
            </div>
            <el-input
              v-model="filterLc"
              placeholder="输入关键字进行过滤"
            />
            <el-tree
              ref="lc"
              class="filter-tree"
              node-key="id"
              :data="lc_list"
              :props="defaultProps"
              :default-checked-keys="default_agent_ids"
              default-expand-all
              show-checkbox
              :filter-node-method="filterNode"
            />
          </el-card>
        </el-col>
        <el-col span="7">
          <el-card v-loading="other_loading" style="width: 400px">
            <div slot="header" class="clearfix">
              <span>其他代理商</span>
            </div>
            <el-input
              v-model="filterOther"
              placeholder="输入关键字进行过滤"
            />
            <el-tree
              ref="other"
              class="filter-tree"
              node-key="id"
              :data="other_list"
              :props="defaultProps"
              :default-checked-keys="default_agent_ids"
              default-expand-all
              show-checkbox
              :filter-node-method="filterNode"
            />
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { detail, lcAgentList, otherAgentList, activityAgentProduct } from '@/api/agent/activity'
import { products } from '@/api/agent/product'

export default {
  name: 'ActivityDetail',
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      id: this.$route.query.id,
      product_loading: false,
      lc_loading: false,
      other_loading: false,
      commit_loading: false,
      filterLc: '',
      filterOther: '',
      filterProduct: '',
      product_list: [{
        id: 1,
        name: '全选',
        children: []
      }],
      lc_list: [
        {
          id: 1,
          name: '全选',
          children: []
        }
      ],
      other_list: [{
        id: 1,
        name: '全选',
        children: []
      }],
      data_lc: [{
        id: 1,
        name: '全选',
        children: []
      }],
      default_agent_ids: [],
      default_product_ids: []
    }
  },
  watch: {
    filterLc(val) {
      this.$refs.lc.filter(val)
    },
    filterOther(val) {
      this.$refs.other.filter(val)
    },
    filterProduct(val) {
      this.$refs.product.filter(val)
    }
  },
  created() {
    this.getProduct()
    this.getLC()
    this.getOther()
    this.getDetail()
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    getDetail() {
      detail({ id: this.id }).then(res => {
        this.default_product_ids = res.data.product_ids
        this.default_agent_ids = res.data.agent_ids
        console.log(res)
      })
    },
    getProduct() {
      this.product_loading = true
      products({ all: true }).then(res => {
        this.product_loading = false
        this.product_list[0].children = res.data.list
      })
    },
    getLC() {
      this.lc_loading = true
      lcAgentList({}).then(res => {
        this.lc_loading = false
        this.lc_list[0].children = res.data
      })
    },
    getOther() {
      this.other_loading = true
      otherAgentList({}).then(res => {
        this.other_loading = false
        this.other_list[0].children = res.data
      })
    },
    commit() {
      this.commit_loading = true
      var product_ids = this.$refs.product.getCheckedKeys()

      var lc_ids = this.$refs.lc.getCheckedKeys()
      var other_ids = this.$refs.other.getCheckedKeys()
      var agent_ids = lc_ids.concat(other_ids)

      if (product_ids.length === 0) {
        return this.$message.error('至少选择一个产品')
      } else if (agent_ids.length === 0) {
        return this.$message.error('至少选择一个代理商')
      } else {
        activityAgentProduct({ activity_id: this.id, product_ids: product_ids, agent_ids: agent_ids }).then(res => {
          this.commit_loading = false
          console.log(res)
          this.$message.success(res.message)
          this.$router.go(-1)
        }).catch(() => {
          this.commit_loading = false
        })
      }
    }

  }
}
</script>

<style scoped>

</style>
