import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/app/list',
    method: 'POST',
    data
  })
}
export function all() {
  return request({
    url: '/app/all',
    method: 'POST'
  })
}

export function parent_apps(data) {
  return request({
    url: '/app/parent_apps',
    method: 'POST',
    data
  })
}

export function children_apps() {
  return request({
    url: '/app/children_apps',
    method: 'POST'
  })
}

export function store(data) {
  return request({
    url: '/app/store',
    method: 'POST',
    data: data
  })
}

export function detail(data) {
  return request({
    url: '/app/detail',
    method: 'POST',
    data: data
  })
}

export function update(data) {
  return request({
    url: '/app/update',
    method: 'POST',
    data: data
  })
}

export function destory(data) {
  return request({
    url: '/app/destory',
    method: 'POST',
    data: data
  })
}

export function miniAppList(data) {
  return request({
    url: '/miniApp/list',
    method: 'POST',
    data: data
  })
}

export function miniAppCreate(data) {
  return request({
    url: '/miniApp/store',
    method: 'POST',
    data: data
  })
}

export function miniAppDefault(data) {
  return request({
    url: '/miniApp/default',
    method: 'POST',
    data: data
  })
}

export function miniAppDestory(data) {
  return request({
    url: '/miniApp/destory',
    method: 'POST',
    data: data
  })
}

export function resetSecret(data) {
  return request({
    url: '/app/resetSecret',
    method: 'POST',
    data: data
  })
}

export function hot() {
  return request({
    url: '/hot/list',
    method: 'POST'
  })
}
export function hot_store(data) {
  return request({
    url: '/hot/store',
    method: 'POST',
    data
  })
}
export function hot_delete(data) {
  return request({
    url: '/hot/delete',
    method: 'POST',
    data
  })
}
//  模块相关
export function modules(data) {
  return request({
    url: '/app/modules',
    method: 'GET',
    params: data
  })
}

export function modules_store(data) {
  return request({
    url: '/app/modules',
    method: 'post',
    data
  })
}

export function modules_del(data) {
  return request({
    url: '/app/modules',
    method: 'delete',
    params: data
  })
}

export function products(data) {
  return request({
    url: '/app/apppro',
    method: 'post',
    params: data
  })
}

export function supplement(data) {
  return request({
    url: '/app/supplement',
    method: 'post',
    data
  })
}

export function supplementUpdate(data) {
  return request({
    url: '/app/supplement/update',
    method: 'post',
    data
  })
}
