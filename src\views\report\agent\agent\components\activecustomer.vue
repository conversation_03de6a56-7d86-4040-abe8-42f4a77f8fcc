<template>
  <el-card class="box-card">
    <div class="header">
      <el-row >
        <el-col :xs="24" :sm="24" :lg="24" >
          <p>各产品生效客户数 <span style="color:#999999;font-size: 14px;padding-left:10px;"><i class="el-icon-warning-outline"></i> 截止目前各产品存量未到期的客户数量</span></p>
        </el-col>
      </el-row>
    </div>
      <div style="height:55px"></div>
    <div  class="view-body1" >
      <el-row style="text-align: center">
        <el-col :xs="6" :sm="6" :lg="6" v-for="(item,index) in lists">
          <p><span class="shuzi">{{ item.count  }}</span></p>
          <p class="proname" >{{ item.name  }}</p>
        </el-col>
      </el-row>
      <div style="height:35px"></div>
      <el-row style="text-align: center">
        <el-col :xs="6" :sm="6" :lg="6" v-for="(item,index) in list1s">
          <p><span class="shuzi">{{ item.count  }}</span></p>
          <p class="proname" >{{ item.name  }}</p>
        </el-col>
      </el-row>
      <div style="height:55px"></div>
    </div>

  </el-card>
</template>

<script>
import { activecustomer } from '@/api/agent/dashboard'
import { mapGetters } from 'vuex'
export default {
  name: 'activecustomer',
  props: {
    allData: {
      type: Object,
      default() {
        return {}
      }
    },
    loading: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  data() {
    return {
      mapLoading: false,
      lists: [],
      list1s: [],
      money: 0,
      num: 0,
      chartLine: null,
      dataSource: [],
      legend: ['今日', '前一日'],
      trend_keys: [],
      trend_data1: [],
      trend_data2: []
    }
  },
  watch: {
    allData(value) {
      this.allData = value

      this.legend = ['今日']
      // 今日
      // this.trend_data1 = this.allData.map_today
      // this.trend_data2 = this.allData.map_last_day// 前一日
    }
  },
  computed: {
    ...mapGetters([
      'type'
    ])
  },
  created() {
    // for (var i = 0; i < 24; i++) {
    //   this.trend_keys.push(i)
    // }
  },
  mounted() {
    this.init()
    this.getActive()
  },
  methods: {
    experience() {
      this.$router.push({
        name: 'ProductListRouter',
        query: {
        }
      })
    },
    getActive() {
      // 产品活跃度排行
      this.$emit('getActive')
    },
    async init() {
      this.getRechargedata()
    },
    async getRechargedata() {
      this.mapLoading = true
      await activecustomer().then(response => {
        // this.mapLoading = false
        this.lists = response.data.list
        this.list1s = response.data.list1
        // this.allData = response.data
        // this.lists = this.allData.data
      }).catch(error => {
        this.mapLoading = false
        console.log(error)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.box-card {
  width: 100%;
  background-color: #ffffff;
  margin-top: 20px;
  .show{
    display: block;
  }
  .hide{
    display: none;
  }
  .header {
     p{
      font-size: 16px;
      font-weight: bold;
    }
    .btn-day{
      flex: 1;
      margin-left: 15px;
      text-align: right;
      span{
        margin: 0 5px;
        padding: 8px 15px;
        font-size: 10px;
        cursor: pointer;
        border:1px solid #eaeaec;
        border-radius: 5px;
      }
      .sel{
        background-color: #0e90d2;
        color: #ffffff;
      }
    }
  }
  .filter-header{
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .right{
      span{
        padding: 10px 25px;
        font-size: 10px;
        cursor: pointer;
        color: #0e90d2;
        border: 1px solid #0e90d2;
      }
      .sel{
        background-color: #0e90d2;
        color: #ffffff;
      }
    }
  }
  .view-body{
    display: flex;
    margin-top: 28px;
    .left{
      flex: 1;
      .dlsbtn{
        border:1px solid #eaeaec;
        border-radius: 10px;
        margin-bottom: 20px;
      }
      .dlsbtn:hover{
        background-color: #f0f2f5;
        border-color: #f0f2f5;
      }
    }
    .right{
      position: relative;
      width: 75%;
      //height: 360px;
      .null{
        position: absolute;
        top: 50px;
        width: 100%;
        line-height: 200px;
        text-align: center;
        color: #8c939d;
        font-size: 30px;
        font-weight: bold;
      }
    }
  }

  .view-body1{
    margin-top: 28px;
    .shuzi{
      color:#0e90d2;font-size: 24px;font-weight: bold
    }
    .proname{
      color:#333333;font-weight: 300;
    }
  }
  .cate{
    float: left;
    width: 227px;
    margin-left: 20px;
    span{
      margin-right: 10px;
      font-size: 16px;
    }
    ::v-deep.el-checkbox__label{
      font-size: 16px;
    }
  }
}
</style>
