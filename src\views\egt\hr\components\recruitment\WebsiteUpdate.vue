<script>
export default {
  name: 'WebsiteUpdate',
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      title: '',
      formData: {
        id: undefined,
        website: '',
        url: '',
        status: 1,
        icon: ''
      }
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.title = this.data.id ? '编辑' : '新增'
          this.formData = { ...this.data }
        }
      },
      immediate: true
    }
  },
  methods: {
    handleCancel() {
      this._visible = false
      //   清空表单
      this.$refs.formRef.resetFields()
    },
    handleSave() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.$message({
            type: 'success',
            message: '保存成功'
          })
          this.handleCancel()
          this.$emit('refresh', this.formData)
        }
      })
    }
  }
}
</script>

<template>
  <el-dialog :close-on-click-modal="false" :visible.sync="_visible" :title="title + '招聘网站'">
    <el-form ref="formRef" :model="formData" label-width="140px">
      <el-form-item label="网站名称">
        <el-input v-model="formData.website" />
      </el-form-item>
      <el-form-item label="网址">
        <el-input v-model="formData.url" />
      </el-form-item>
      <el-form-item label="网站图片">
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
        >
          <img v-if="formData.icon" class="avatar" :src="formData.icon" alt="">
          <i v-else class="el-icon-plus avatar-uploader-icon" />
        </el-upload>
      </el-form-item>
      <el-form-item label="状态">
        <el-radio-group v-model="formData.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="2">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel()">取消</el-button>
      <el-button type="primary" @click="handleSave()">保存</el-button>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
::v-deep .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;

  &:hover {
    border-color: #409EFF;
  }
}
</style>
