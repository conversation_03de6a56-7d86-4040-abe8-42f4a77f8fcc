<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix" style="display: flex; align-items: center;">
        <!--<span>代理商列表</span>-->
        <div style="text-align: right; flex: 1;">
          <el-button style="padding: 0" type="text" @click="handleAdd">添加</el-button>
          <el-button style="padding: 0" type="text" :loading="downloadLoading" @click="exportExcel">导出</el-button>
        </div>
      </div>
      <el-form :inline="true" :model="queryList" class="filter" size="small">
        <el-form-item label="代理商名称">
          <el-input v-model="queryList.name" placeholder="代理商名称" clearable />
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="queryList.type" placeholder="全部" clearable>
            <el-option label="龙采" :value="1" />
            <el-option label="其他" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="负责人名称">
          <el-input v-model="queryList.user" placeholder="负责人名称" clearable />
        </el-form-item>
        <el-form-item label="联系方式">
          <el-input v-model="queryList.phone" placeholder="联系方式" clearable />
        </el-form-item>
        <el-form-item label="代理区域">
          <el-cascader
            v-model="queryList.area"
            :options="provinceAndCityDataPlus"
            clearable
          />
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select v-model="queryList.status" placeholder="全部" clearable>
            <el-option label="审核中" :value="1" />
            <el-option label="已通过" :value="2" />
            <el-option label="已拒绝" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="预存款大于">
          <el-input v-model="queryList.greater" placeholder="输入预存款金额" />
        </el-form-item>
        <el-form-item label="预存款小于">
          <el-input v-model="queryList.less" placeholder="输入预存款金额" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
        </el-form-item>
      </el-form>
      <div class="table mt20">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          stripe
          style="width: 100%"
          height="calc(100% - 96px)"
        >
          <el-table-column
            prop="uuid"
            label="代理商编号"
          />
          <el-table-column
            prop="name"
            label="代理名称"
          />
          <el-table-column
            prop="user"
            label="负责人"
          />
          <el-table-column
            prop="phone"
            label="手机号"
          />
          <el-table-column label="代理区域">
            <template slot-scope="scope">
              {{ getCityByCode(scope.row.area) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="money"
            label="软件账户金额"
          />
          <el-table-column
            prop="shopmoney"
            label="商城预存款金额"
          />
          <el-table-column
            prop="hardmoney"
            label="硬件账户金额"
          />
          <el-table-column label="下属用户">
            <template slot-scope="scope">
              <el-button type="text" icon="el-icon-eye" @click="showAgentCustomers(scope.row)">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column
            prop="lastLogin"
            label="上次登陆"
          />
          <el-table-column label="审核状态">
            <template slot-scope="scope">
              <el-link v-if="scope.row.status === 0" type="primary" :underline="false" @click="checkStatus(scope.row)">未审核</el-link>
              <el-link v-if="scope.row.status === 1" type="primary" :underline="false" @click="checkStatus(scope.row)">审核中</el-link>
              <el-link v-if="scope.row.status === 2" type="success" :underline="false">已通过</el-link>
              <el-link v-if="scope.row.status === 3" type="danger" :underline="false">已拒绝</el-link>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="showDetail(scope.row)">详情</el-button>
              <el-divider direction="vertical" />
              <el-button type="text" @click="editRow(scope.row)">编辑</el-button>
              <el-divider direction="vertical" />
              <el-button v-if="scope.row.enable===1" type="text" style="color:red" @click="enableCloseRow(scope.row)">停用</el-button>
              <el-button v-else type="text" style="color:forestgreen" @click="enableOpenRow(scope.row)">启用</el-button>
              <el-divider direction="vertical" />
              <el-button type="text" style="color: #ff4949" @click="destroyRow(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryList.page"
          :limit.sync="queryList.perPage"
          style="text-align:center;"
          @pagination="getList"
        />
      </div>
    </el-card>
    <!--dialog-->
    <el-dialog
      title="审核操作"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="审核状态" prop="status">
          <el-radio-group v-model="ruleForm.status">
            <el-radio :label="1">审核中</el-radio>
            <el-radio :label="2">通过</el-radio>
            <el-radio :label="3">驳回</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="ruleForm.status === 3" label="驳回原因" prop="remark" style="width: 70%">
          <el-input v-model="ruleForm.remark" type="textarea" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="auditHandle">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { provinceAndCityDataPlus, CodeToText } from 'element-china-area-data'
import { index, destroy, audit, agentUserStatus } from '@/api/agent/agent'
import { agentListExport } from '@/api/agent/downloads'
export default {
  name: 'List',
  components: { Pagination },
  data() {
    return {
      provinceAndCityDataPlus: provinceAndCityDataPlus,
      tableData: [],
      queryList: {
        page: 1,
        perPage: 10
      },
      total: 0,
      ruleForm: {},
      rules: {},
      dialogVisible: false,
      tableLoading: false,
      downloadLoading: false
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.list-wrap>.box-card>.el-card__body')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  methods: {
    handleAdd() {
      this.$router.push({
        name: 'agentAgentAdd'
      })
    },
    getList() {
      this.tableLoading = true
      index(this.queryList).then(response => {
        this.tableLoading = false
        if (response.code === 200) {
          this.tableData = response.data.list
          this.total = response.data.meta.total
        }
      }).catch(() => {
        this.tableLoading = false
      })
    },
    showAgentCustomers(row) {
      this.$router.push({
        name: 'agentAgentSon',
        query: { id: row.id }
      })
    },
    showDetail(row) {
      this.$router.push({
        name: 'agentAgentDetail',
        query: { id: row.id }
      })
    },
    // 编辑
    editRow(row) {
      this.$router.push({
        name: 'agentAgentEdit',
        query: { id: row.id }
      })
    },
    // 删除
    destroyRow(row) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        destroy({ id: row.id }).then(response => {
          if (response.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          }
        })
      })
    },
    // 获取城市根据城市码
    getCityByCode(code) {
      var str = ''
      code.forEach((item) => {
        if (item !== 0) {
          str += ' ' + CodeToText[item]
        }
      })
      return str
    },
    checkStatus(row) {
      this.dialogVisible = true
      this.ruleForm = row
    },
    // 审核代理
    auditHandle() {
      this.dialogVisible = false
      audit(this.ruleForm).then(response => {
        if (response.code === 200) {
          this.$message.success('审核成功')
        }
      })
    },
    // 关闭账号
    enableCloseRow(item) {
      this.$confirm('是否关闭该代理商', '', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        agentUserStatus({ id: item.id, status: 0 }).then(res => {
          if (res.code === 200) {
            this.$message.success('关闭成功')
            this.getList()
          }
        })
      }).catch(() => {})
    },
    // 启用账号
    enableOpenRow(item) {
      this.$confirm('是否开启该代理商', '', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        agentUserStatus({ id: item.id, status: 1 }).then(res => {
          if (res.code === 200) {
            this.$message.success('开启成功')
            this.getList()
          }
        })
      }).catch(() => {})
    },
    async exportExcel() {
      try {
        this.downloadLoading = true
        const params = {
          name: this.queryList.name,
          type: this.queryList.type,
          user: this.queryList.user,
          phone: this.queryList.phone,
          area: this.queryList.area,
          status: this.queryList.status,
          greater: this.queryList.greater,
          less: this.queryList.less
        }
        const res = await agentListExport(params)
        if (res.code === 200) {
          this.$message.success('导出成功，请到下载中心下载')
        } else {
          this.$message.error(res.msg || '导出失败')
        }
      } catch (error) {
        this.$message.error(error.message || '导出失败')
      } finally {
        this.downloadLoading = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap{
  height: 100%;
  .box-card{
    height: 100%;
    border: none;
    ::v-deep .el-card__header{
      padding-top: 0;
    }
    ::v-deep .el-card__body{
      height: calc(100% - 59px);
      overflow: auto;
      padding: 0;
      margin-top: 20px;
    }
    .filter{
      border-bottom: 1px solid #e5e5e5;
    }
    .table{
      min-height: 200px;
    }
  }
}
  @media screen and (max-width: 996px)  {
    .list-wrap{
      .filter{
        max-height: 30vh;
        overflow: auto;
      }
    }
  }
</style>
