<template>
  <div id="aliveTime" style="height: 100%">
    <el-card style="height: 100%">
      <div class="el-row--flex is-justify-space-between is-align-middle">
        <span>分时段用户活跃数</span>
        <el-date-picker
          v-model="date"
          size="small"
          format="yyyy-MM"
          value-format="yyyy-MM"
          type="month"
          placeholder="请选择月份"
          @change="handleGetAliveTimeData"
        />
      </div>
      <div id="active-time">
        <line-chart :show-legend="false" :grid="grid" height="480px" :chart-data="chartData" :legend="legend" :color-list="colorList" :data-type="'normal'" />
      </div>
    </el-card>
  </div>
</template>

<script>
/* eslint-disable */
import LineChart from './LineChart.vue';
import {getAliveTime} from "@/api/dashboard";

export default {
  name: "AliveTime",
  components: { LineChart },
  data() {
    return {
      date: "",
      chartData: {
        x: [],
        y: []
      },
      legend: ["人数"],
      colorList: ["#568ee8"],
      total: 0,
      avarage: 0,
      grid: {
        left: 30,
        right: 30,
        bottom: 20,
        top: 50,
        containLabel: true
      }
    };
  },
  methods: {
    handleGetAliveTimeData(){
      if(!this.date){
        this.date = new Date().getFullYear().toString() + '-' + (new Date().getMonth() + 1).toString().padStart(2, '0');
      }
      getAliveTime({date: this.date}).then(res => {
        if (res.code === 200 && res.data){
          if (res.data.title){
            this.chartData.x = res.data.title.map(v => v[0]);
          }
          if (res.data.arr){
            this.chartData.y = res.data.arr;
          }
        }
      })
    }
  },
  mounted() {
    this.handleGetAliveTimeData();
  }
};
</script>

<style lang="scss" scoped>
.bottom-0{
  margin-bottom: 0;
}
.float-right{
  float: right;
  margin-top: 10px;
  ::v-deep.el-form-item--medium .el-form-item__content{
    display: inline-block;
  }
}
</style>
