<script>
import {
  addOtherTestListApi,
  editOtherTestListApi,
  getAddOtherTestListApi,
  getEditOtherTestListApi
} from '@/api/egt/recruitment'
import { rateMap } from '@/views/egt/hr/utils/rateMap'

const optionRules = {
  required: true,
  message: '请输入选项',
  trigger: 'blur'
}
const rateRules = {
  required: true,
  message: '请填写评分/占比/其他',
  trigger: 'blur'
}
export default {
  name: 'OtherUpdate',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      optionRules,
      rateRules,
      labels: ['A', 'B', 'C', 'D'],
      form: {
        question: '',
        cate: '',
        options: [
          {
            label: 'A',
            id: '',
            value: '',
            rate: ''
          },
          {
            label: 'B',
            id: '',
            value: '',
            rate: ''
          },
          {
            label: 'C',
            id: '',
            value: '',
            rate: ''
          },
          {
            label: 'D',
            id: '',
            value: '',
            rate: ''
          }
        ]
      },
      rules: {
        cate: [
          {
            required: true,
            message: '请选择分类',
            trigger: 'change'
          }
        ],
        question: [
          {
            required: true,
            message: '请输入题目',
            trigger: 'blur'
          }
        ]
      },
      cates: [],
      loading: false,
      submitLoading: false
    }
  },
  computed: {
    rateMap() {
      return rateMap
    },
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    title() {
      return this.data.id ? '编辑' : '新增'
    }
  },
  watch: {
    _visible(val) {
      if (val) {
        this.getParams()
      }
    }
  },
  methods: {
    async getParams() {
      const api = this.data.id ? getEditOtherTestListApi : getAddOtherTestListApi
      if (!api) return
      try {
        this.loading = true
        const res = await api({ id: this.data.id })
        if (res.code === 200 && res.data) {
          // 判断res.data.data是否为数组，是的话，遍历数组，将数组中的对象的key和value分别赋值给label和value
          this.cates = []
          const typeField = this.data.id ? 'category' : 'data'
          if (res.data[typeField] && Array.isArray(res.data[typeField])) {
            this.cates = res.data[typeField].map(item => ({
              label: item.title,
              value: item.id
            }))
          }
          if (res.data.psychology) {
            this.form.question = res.data.psychology.title
            this.form.cate = res.data.psychology.type
          }
          if (this.data.id && res.data.data) {
            // 把res.data.data变为数组
            const data = Object.values(res.data.data)
            if (Array.isArray(data) && data.length) {
              this.form.options = data.map((item, index) => {
                return {
                  label: this.labels[index],
                  id: item.id,
                  value: item.title,
                  rate: item.psychology
                }
              })
            }
          }
        }
      } finally {
        this.loading = false
      }
    },
    handleCancel() {
      this._visible = false
      //   清除表单
      this.$refs.form.resetFields()
    },
    handleSubmit() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          try {
            this.submitLoading = true
            const postData = this.form.options.map((item, index) => {
              return {
                id: this.data.id ? item.id : undefined,
                title: item.value,
                psychology: item.rate
              }
            })
            await this.saveData(postData)
          } catch (e) {
            console.log(e)
          } finally {
            this.submitLoading = false
          }
        }
      })
    },
    // 保存
    async saveData(data) {
      try {
        this.submitLoading = true
        let api = addOtherTestListApi
        const params = new URLSearchParams()

        // 构建类似serialize格式的参数
        data.forEach((item, index) => {
          if (this.data.id) {
            params.append(`title[${item.id}]`, String(item.title)) // 确保值为字符串
            params.append(`psychology[${item.id}]`, String(item.psychology))
          } else {
            params.append(`title[${index}]`, String(item.title)) // 确保值为字符串
            params.append(`psychology[${index}]`, String(item.psychology))
          }
        })

        params.append('psychologyTitle', this.form.question)
        params.append('psychologyType', this.form.cate)

        if (this.data.id) {
          params.append('id', this.data.id)
          api = editOtherTestListApi
        }

        // 生成最终字符串（类似jQuery.serialize()）
        const serialized = params.toString()

        if (!api) return
        const res = await api(serialized) // 发送序列化后的字符串
        if (res.code === 200) {
          this.$message.success(this.data.id ? '编辑成功' : '新增成功')
          this.$emit('submitSuccess', this.form)
          this.handleCancel()
        } else {
          this.$message.error(res.msg || res.message || (this.data.id ? '编辑失败' : '新增失败'))
        }
      } catch (err) {
        this.$message.error(this.data.id ? '编辑失败' : '新增失败')
        console.log('🚀 ~ addOther ~ err: ', err)
      } finally {
        this.submitLoading = false
      }
    }
  }
}
</script>

<template>
  <el-dialog :close-on-click-modal="false" :title="title + '测试题'" :visible.sync="_visible">
    <el-form ref="form" :model="form" label-width="80px" :rules="rules">
      <el-form-item label="题目" prop="question">
        <el-input v-model="form.question" placeholder="请输入题目" />
      </el-form-item>
      <el-form-item label="题目分类" prop="cate">
        <el-select v-model="form.cate" placeholder="请选择分类" :filterable="false" style="width: 100%">
          <el-option v-for="item in cates" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item
        v-for="(option, index) in form.options"
        :key="index"
        :label="'选项' + option.label"
        :prop="'options.' + index + '.value'"
        :rules="optionRules"
      >
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item :rules="optionRules" :prop="'options.' + index + '.value'">
              <el-input v-model="option.value" :placeholder="'请输入选项' + option.label" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item :rules="rateRules" :prop="'options.' + index + '.rate'">
              <el-select v-model="option.rate" placeholder="请选择评分/占比/其他" :filterable="false" style="width: 100%">
                <el-option v-for="(item, key) in rateMap" :key="key" :label="item" :value="key" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="handleCancel()">取 消</el-button>
      <el-button :loading="submitLoading" type="primary" @click="handleSubmit()">确 定</el-button>
    </div>
  </el-dialog>
</template>
