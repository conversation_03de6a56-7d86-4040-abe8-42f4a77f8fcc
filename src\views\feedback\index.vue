<template>
  <div class="app-container" style="height: 100%;">
    <el-card class="card-wrap" style="height: 100%;">
      <div class="list-wrap feedback">
        <div class="filter">
          <el-form :inline="true" :model="query" class="demo-form-inline" method="get"><!---->
            <el-form-item label="反馈类型">
              <el-select v-model="query.type" placeholder="请选择" :clearable="true">
                <el-option v-for="(item,index) in options" :key="index" :label="item" :value="item" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="table">
          <el-table
            v-loading="loading"
            :data="tableData"
            stripe
            style="width: 100%"
            :default-sort="{prop: 'created_at', order: 'descending'}"
            height="calc(100% - 96px)"
          >
            <el-table-column label="序列" align="center" width="80px">
              <template slot-scope="scope">{{ scope.$index + 1 }}</template>
            </el-table-column>
            <el-table-column label="用户" prop="user.name" />
            <el-table-column label="反馈类型" prop="type" />
            <el-table-column label="提交日期" prop="created_at" />
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button type="text" @click="detail(scope.row)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="query.page"
            :limit.sync="query.limit"
            @pagination="getList"
          />
        </div>

        <el-dialog :close-on-click-modal="false" title="查看详情" :visible.sync="dialogFormVisible">
          <el-row>
            <el-col :span="8" style="text-align: center">
              <el-avatar shape="square" :size="60" :src="form.user.logo" @error="errorHandler">
                <img :src="form.user.logo" :alt="form.user.logo">
              </el-avatar>
              <div class="user">
                <p>{{ form.user.name }} </p>
                <small v-if="form.user.is_cerified">：{{ form.user.true_name }}</small>
                <table style="border-collapse: separate;border-spacing: 5px;">
                  <tr>
                    <td width="80" align="right" style="vertical-align: top;">真实姓名</td>
                    <td align="left">{{ form.user.is_cerified ? form.user.true_name : '（ 未实名 ）' }}</td>
                  </tr>
                  <tr>
                    <td width="80" align="right" style="vertical-align: top;">手机号码</td>
                    <td align="left">{{ form.user.phone }}</td>
                  </tr>
                  <tr>
                    <td width="80" align="right" style="vertical-align: top;">省份</td>
                    <td align="left">{{ form.user.province + ' ' + form.user.city }}</td>
                  </tr>
                  <tr>
                    <td width="80" align="right" style="vertical-align: top;">地址</td>
                    <td align="left" style="line-height: 18px;">{{ form.user.address }}</td>
                  </tr>
                </table>
              </div>
            </el-col>
            <el-col :span="16">
              <el-form :model="form">
                <el-form-item label="反馈类型" :label-width="formLabelWidth" style="margin-bottom: 0">
                  {{ form.type }}
                </el-form-item>
                <el-form-item label="反馈时间" :label-width="formLabelWidth" style="margin-bottom: 0">
                  {{ form.created_at }}
                </el-form-item>
                <el-form-item label="反馈内容" :label-width="formLabelWidth">
                  {{ form.content }}
                </el-form-item>
                <el-form-item label="反馈图片" :label-width="formLabelWidth">
                  <template v-if="form.picture && form.picture.length">
                    <el-image
                      v-for="item in form.picture"
                      :key="item"
                      style="width: 100px; height: 100px; margin-right: 10px; margin-bottom: 10px;"
                      :src="item"
                      :preview-src-list="form.picture"
                    />
                  </template>
                  <template v-else>
                    暂无
                  </template>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>

          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogFormVisible = false">取 消</el-button>
            <el-button type="primary" @click="dialogFormVisible = false">确 定</el-button>
          </div>
        </el-dialog>
      </div>
    </el-card>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { fetchList, option } from '@/api/feedback'

export default {
  name: 'Personal',
  components: { Pagination },
  data() {
    return {
      total: 0,
      query: {
        page: 1,
        limit: 10,
        name: null,
        number: null,
        status: null
      },
      options: [],
      tableData: [],
      loading: true,
      form: {
        user: {}
      },
      dialogFormVisible: false,
      formLabelWidth: '120px'
    }
  },
  created() {
    this.getOption()
    this.getList()
  },
  methods: {
    errorHandler() {
      return true
    },
    getOption() {
      option().then(response => {
        this.options = response.data.FEEDBACK_TYPE
      })
    },
    getList() {
      this.loading = true
      fetchList(this.query).then(response => {
        this.total = response.data.total
        this.tableData = response.data.data
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    detail(row) {
      this.form = row
      this.dialogFormVisible = true
    }
  }
}
</script>

<style scoped>
.card-wrap ::v-deep .el-card__body {
  height: 100%;
}
    .feedback {
      height: 100%;
      .filter{
        border-bottom: 1px solid #e8e8e8;
        box-sizing: border-box;
      }
      .table{
        height: calc(100% - 59px - 20px);
        margin-top: 20px;
      }
    }
</style>
