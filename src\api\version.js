import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/version/list',
    method: 'POST',
    data
  })
}

export function detail(data) {
  return request({
    url: '/version/detail',
    method: 'POST',
    data
  })
}

export function store(data) {
  return request({
    url: '/version/store',
    method: 'POST',
    data
  })
}

export function del(data) {
  return request({
    url: '/version/delete',
    method: 'POST',
    data
  })
}
export function phoneModels() {
  return request({
    url: '/version/phone',
    method: 'POST'
  })
}
