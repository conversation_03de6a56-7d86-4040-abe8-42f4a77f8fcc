<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix" style="text-align: right">
        <!--<span>开通列表</span>-->
        <!--<el-button type="text" @click="drawbackList()">到期列表</el-button>-->
        <el-button type="text" style="padding: 0" :loading="downloadLoading" @click="exportExcel">导出</el-button>
      </div>
      <div class="filter">
        <el-form :inline="true" :model="queryList" class="demo-form-inline" size="small">
          <el-form-item label="代理商名称">
            <el-input v-model="queryList.agent" placeholder="代理商名称" />
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="queryList.agent_exact" value="1">精准</el-checkbox>
          </el-form-item>

          <el-form-item label="用户名称">
            <el-input v-model="queryList.user" placeholder="用户名称" />
          </el-form-item>
          <el-form-item label="产品名称">
            <el-select v-model="queryList.product_id" filterable multiple collapse-tags clearable run placeholder="请选择" style="width:400px">
              <el-option
                v-for="item in productsList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="订单类型">
            <el-select v-model="queryList.type" placeholder="全部" clearable>
              <el-option label="购买" :value="1" />
              <el-option label="续费" :value="2" />
              <el-option label="升级" :value="3" />
              <el-option label="体验" :value="0" />
              <el-option label="退单" :value="4" />
              <el-option label="配套产品" :value="6" />
            </el-select>
          </el-form-item>
          <el-form-item label="购买时间">
            <el-date-picker
              v-model="queryList.date"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="dateChange"
            />
          </el-form-item>
          <el-form-item label="到期时间">
            <el-date-picker
              v-model="queryList.end_at"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              clearable
            />
          </el-form-item>
          <el-form-item label="审核状态">
            <el-select v-model="queryList.status" placeholder="全部" clearable>
              <el-option label="审核中" :value="0" />
              <el-option label="已通过" :value="1" />
              <el-option label="已拒绝" :value="-1" />
              <el-option label="已关闭" :value="-2" />
              <el-option label="开通失败" :value="6" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
          </el-form-item>
        </el-form>
      </div>
      <p class="mt20 statistic">
        金额总数: <span>{{ allPrice }}</span>;
        返利总数: <span>{{ allflPrice }}</span>;
        销售金额总数: <span>{{ totalMoney }}</span>;</p>
      <div class="table mt20">
        <div class="table" style="margin-top: 20px">
          <el-table
            :data="tableData"
            stripe
            style="width: 100%"
          >
            <el-table-column
              prop="id"
              label="id"
            />
            <el-table-column
              prop="number"
              label="订单号"
            />
            <el-table-column
              label="产品名称"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.product }}</span>
                <span v-if="scope.row.version">({{ scope.row.version }})</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="type"
              label="交易类型"
            />
            <el-table-column
              prop="agent"
              label="代理名称"
            />
            <el-table-column
              prop="customer"
              label="用户名称"
            />
            <el-table-column
              prop="price"
              label="价格"
            />
            <el-table-column
              prop="flprice"
              label="返利金额"
            />
            <el-table-column label="代理商余额">
              <template slot-scope="scope">{{ scope.row.usable }}</template>
            </el-table-column>
            <el-table-column label="附件">
              <template slot-scope="scope">
                <span>{{ scope.row.order_id }}</span>
                <div style="cursor: pointer" @click="clickContract(scope.row.contract_id)">
                  <el-image v-if="scope.row.contract_id.length > 0" lazy style="width: 50px; height: 50px" :src="scope.row.contract_id" />
                </div>
                <div style="cursor: pointer" @click="clickContract(scope.row.attachment_id)">
                  <el-image v-if="scope.row.attachment_id.length > 0" lazy style="width: 50px; height: 50px" :src="scope.row.attachment_id" />
                </div>
                <div style="cursor: pointer" @click="clickContract(scope.row.attachment_id1)">
                  <el-image v-if="scope.row.attachment_id1.length > 0" lazy style="width: 50px; height: 50px" :src="scope.row.attachment_id1" />
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="domain"
              label="备注"
            />
            <el-table-column
              prop="start_at"
              label="购买时间"
            />
            <el-table-column
              prop="expire_at"
              label="到期时间"
            />
            <el-table-column label="审核状态">
              <template slot-scope="scope">
                <div>
                  <el-link v-if="scope.row.status === 0 " type="primary" :underline="false" @click="showDialog(scope.row)">审核中</el-link>
                  <span v-else-if="scope.row.status === 0">审核中</span>

                  <el-link v-if="scope.row.status === 6 && userType=== 1" type="info" :underline="false" @click="showDialog(scope.row)">开通失败</el-link>
                  <el-link v-if="scope.row.status === 6 && userType!== 1" type="info" :underline="false">开通失败</el-link>

                  <el-link v-if="scope.row.status === 0 && userType === 2" type="primary" :underline="false">审核中</el-link>
                  <el-link v-if="scope.row.status === 1" type="success" :underline="false">已通过</el-link>
                  <el-link v-if="scope.row.status === 5" type="warning" :underline="false">等待开通</el-link>
                  <el-link v-if="scope.row.status === -1" type="danger" :underline="false">已拒绝</el-link>
                  <el-link v-if="scope.row.status === -2" type="danger" :underline="false">已关闭</el-link>
                  <el-link v-if="scope.row.status === 3" type="danger" :underline="false">退款审核中</el-link>
                  <el-link v-if="scope.row.status === 4" type="danger" :underline="false">已退款</el-link>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              show-overflow-tooltip
              prop="remark"
              label="审核内容"
            />
            <el-table-column v-if="userType === 1" label="操作">
              <template slot-scope="scope">
                <el-link v-if="scope.row.type === '体验' && scope.row.status === 1" type="primary" :underline="false" @click="financeFollowUp=scope.row;dialogFollowUpVisible=true">跟进</el-link>
                <el-link v-if="[165].includes(scope.row.product_id) && scope.row.status === 1" style="color: #30B08F" type="primary" :underline="false" @click="upProductToOtherProduct(scope.row)">变动</el-link>
              </template>
            </el-table-column>
            <el-table-column v-if="userType === 2" label="操作">
              <template slot-scope="scope">
                <div v-if="scope.row.type === '赠送'" />
                <div v-else>
                  <el-link v-if="scope.row.status === 0 && scope.row.order_id === null && scope.row.type !== '赠送'" type="primary" :underline="false" @click="dialogCancel(scope.row.id)">取消订单</el-link>
                  <el-link v-if="scope.row.status === 0 && scope.row.product_id === 227 " type="primary" :underline="false" @click="dialogCancel(scope.row.id)">取消订单</el-link>
                  <el-link v-if="scope.row.status === -1" type="warning" :underline="false" @click="dialogDestory(scope.row.id)">删除订单</el-link>
                  <el-link v-if="scope.row.status === 1 && (scope.row.type==='购买' || scope.row.type==='续费' || scope.row.type==='升级' ) && scope.row.order_id===null " type="danger" :underline="false" @click="dialogDrawback(scope.row)">申请退款</el-link>
                  <el-link v-if="scope.row.status === 3" type="primary" :underline="false" @click="dialogCancelDrawback(scope.row.id)">取消退款</el-link>
                  <el-link v-if="scope.row.status === 5" type="warning" :underline="false">等待开通</el-link>
                  <el-link v-if="scope.row.status === 6" type="info" :underline="false">开通失败</el-link>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryList.page"
            :limit.sync="queryList.perPage"
            style="text-align:center;"
            @pagination="getList"
          />
        </div>
      </div>
    </el-card>

    <el-dialog
      title="审核"
      :visible.sync="dialogFormVisible"
      width="600px"
      top="10vh"
    >
      <el-form ref="form" :model="form" label-width="120px" :rules="rules">
        <el-form-item label="三月内订单">
          <ul style="max-height: 300px;min-height: 50px;overflow:auto;padding: 0;margin: 0">
            <li v-for="item in history_list" :key="item.id" style="list-style: none">
              <div style="height: 23px">
                <span v-if="item.version">{{ item.name }}({{ item.version }})</span>
                <span v-else>{{ item.name }}</span>
                <span style="color: mediumpurple">&nbsp;￥{{ item.price }}</span>
                <span>&nbsp;{{ item.created_at }}&nbsp;</span>
                <span v-if="item.status===0" style="color: deepskyblue">审核中</span>
                <span v-if="item.status===1" style="color: forestgreen">已通过</span>
                <span v-if="item.status===-1" style="color: red">已拒绝</span>
              </div>
            </li>
          </ul>
        </el-form-item>
        <el-form-item label="审核状态" style="width: 70%">
          <el-select v-model="form.status" placeholder="请选择">
            <el-option label="审核通过" :value="1" />
            <el-option label="审核驳回" :value="-1" />
          </el-select>
        </el-form-item>
        <el-form-item label="审核内容" prop="content" style="width: 70%">
          <el-input
            v-model="form.content"
            type="textarea"
            placeholder="请输入内容"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="dialogConfirm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="退款申请"
      :visible.sync="dialogDrawbackVisible"
      width="35%"
      top="10vh"
    >
      <el-form ref="form" :model="drawbackForm" label-width="120px" :rules="drawbackRules">
        <el-form-item label="用户名称">
          <span>{{ drawbackForm.customer }}</span>
        </el-form-item>
        <el-form-item label="产品名称" prop="product">
          <span>{{ drawbackForm.product }}</span>
        </el-form-item>
        <el-form-item label="申请原因" prop="content" style="width: 70%">
          <el-input
            v-model="drawbackForm.content"
            type="textarea"
            placeholder="请输入内容"
            maxlength="30"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="提示" prop="product" style="color:#f00;">
          <span> 资海云所有软件产品均为按天计费，使用时长以平台购买时间为准，退款审核的时间为1-3工作日，审核通过后，产品结束计费</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogDrawbackVisible = false">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="drawback_create">确 定</el-button>
      </div>
    </el-dialog>
    <!--体验订单跟进-->
    <FollowUpDialog :finance="financeFollowUp" :dialog-visible="dialogFollowUpVisible" @close="dialogFollowUpVisible=false" />
  </div>
</template>

<script>
import { productspx, openList, beforeOpenList, openListDestory, drawback_create, drawback_cancel, productUpToOtherProduct } from '@/api/agent/product'
import { audit, destory, retryProduct } from '@/api/agent/finance'
import { mapGetters } from 'vuex'
import Pagination from '@/components/Pagination'
import moment from 'moment'
import FollowUpDialog from './components/FollowUpDialog'
import { download } from '../utils/download'
import { financeListExport } from '@/api/agent/downloads'
export default {
  name: 'List',
  components: { Pagination, FollowUpDialog },
  data() {
    return {
      dialogFormVisible: false,
      dialogLoading: false,
      dialogDrawbackVisible: false,
      dialogFollowUpVisible: false,
      tableData: [],
      productsList: [],
      allPrice: 0,
      allflPrice: 0,
      queryList: {
        page: 1,
        perPage: 10
      },
      form: {},
      drawbackForm: { id: '', content: '' },
      total: 0,
      rules: {
        content: [
          { required: true, message: '请输入审核内容', trigger: 'blur' }
        ]
      },
      drawbackRules: {
        content: [
          { required: true, message: '请输入退款内容', trigger: 'blur' }
        ]
      },
      history_list: [],
      financeFollowUp: {},
      downloadLoading: false,
    }
  },
  computed: {
    // 别名处理防止冲突
    ...mapGetters({
      userType: 'agentUserType'
    }),
    totalMoney() {
      return (Number(this.allPrice) * 1000 - Number(this.allflPrice) * 1000) / 1000
    }
  },
  created() {
    this.getList()
    this.getProducts()
  },
  methods: {
    getList() {
      this.tableLoading = true
      openList(this.queryList).then(response => {
        if (response.code === 200 && response.data) {
          this.tableData = response.data.list
          this.total = response.data.meta.total
          this.allPrice = response.data.allPrice
          this.allflPrice = response.data.allflPrice
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    // 获取产品列表
    getProducts() {
      productspx({ all: true }).then(response => {
        if (response.code === 200 && response.data) {
          this.productsList = response.data
        }
      })
    },
    pushToStatement() {
      this.$router.push({
        name: 'FinanceAgentStatementRouter'
      })
    },
    drawbackList() {
      this.$router.push({
        name: 'ProductExpireEndRouter',
        query: {
        }
      })
    },
    keyorderlist() {
      this.$router.push({
        name: 'ProductkeyListRouter',
        query: {
        }
      })
    },
    experience() {
      this.$router.push({
        name: 'ProductExperienceRouter',
        query: {
        }
      })
    },
    drawbacklist() {
      this.$router.push({
        name: 'ProductDrawbackListRouter',
        query: {
        }
      })
    },
    platform() {
      this.$router.push({
        name: 'ProductPlatformRouter',
        query: {
        }
      })
    },
    supplement() {
      this.$router.push({
        name: 'FinanceSupplementRouter',
        query: {
        }
      })
    },
    getBeforeList() {
      this.dialogLoading = true
      beforeOpenList(this.form).then(response => {
        console.log(response.data)
        this.history_list = response.data
        this.dialogLoading = false
      }).catch(() => {
        this.dialogLoading = false
      })
    },
    showDialog(row) {
      if (row.type === '赠送') {
        console.log('赠送')
      } else {
        if (row.status === 6) {
          this.$confirm('如果是域名订单请查看域名是否已续费成功?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            confirmButtonClass: 'confirmbtn',
            type: 'warning'
          }).then(() => {
            this.$confirm('你真的已经查看了吗?', '提示', {
              confirmButtonText: '查看了',
              cancelButtonText: '取消',
              confirmButtonClass: 'confirmbtn',
              type: 'warning'
            }).then(() => {
              this.$confirm('数据看了吗，思普看了吗?', '提示', {
                confirmButtonText: '看了',
                cancelButtonText: '取消',
                confirmButtonClass: 'confirmbtn',
                type: 'warning'
              }).then(() => {
                this.form = {
                  id: row.id,
                  order_status: row.status
                }
                this.getBeforeList()
                this.dialogFormVisible = true
              }).catch(error => {
                console.log(error)
              })
            }).catch(error => {
              console.log(error)
            })
          }).catch(error => {
            console.log(error)
          })
        } else {
          this.form = {
            id: row.id,
            order_status: row.status
          }
          this.getBeforeList()
          this.dialogFormVisible = true
        }
      }
    },
    dialogConfirm() {
      this.dialogLoading = true
      if (this.form.status === 1 && !this.form.content) {
        this.form.content = '审核通过'
      }
      if (this.form.order_status === 6) {
        retryProduct(this.form).then(response => {
          if (response.code === 200) {
            this.dialogLoading = false
            this.$message.success('审核成功')
            this.dialogFormVisible = false
            this.getList()
          }
        }).catch(() => {
          this.dialogLoading = false
        })
      } else {
        audit(this.form).then(response => {
          if (response.code === 200) {
            this.dialogLoading = false
            this.$message.success('审核成功')
            this.dialogFormVisible = false
            this.getList()
          }
        }).catch(() => {
          this.dialogLoading = false
        })
      }
    },
    // 取消订单
    dialogCancel(id) {
      this.$confirm('确定要取消订单嘛?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        destory({ id: id }).then(response => {
          this.$message.success('操作成功')
          this.getList()
        }).catch(error => {
          console.log(error)
        })
      }).catch(error => {
        console.log(error)
      })
    },
    // 删除订单
    dialogDestory(id) {
      this.$confirm('确定要删除订单嘛?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        openListDestory({ id: id }).then(response => {
          this.$message.success('操作成功')
          this.getList()
        }).catch(error => {
          console.log(error)
        })
      }).catch(error => {
        console.log(error)
      })
    },
    // 打开图片
    clickContract(url) {
      window.open(url)
    },
    // 退单
    dialogDrawback(item) {
      this.dialogDrawbackVisible = true
      this.drawbackForm.id = item.id
      this.drawbackForm.customer = item.customer
      this.drawbackForm.product = item.product
      this.drawbackForm.content = ''
    },
    // 申请退单
    drawback_create() {
      this.$confirm('确定要申请退订单嘛?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.dialogLoading = true
        drawback_create(this.drawbackForm).then(response => {
          this.dialogLoading = false
          this.dialogDrawbackVisible = false
          this.$message.success(response['message'])
          this.getList()
        }).catch(error => {
          console.log(error)
          this.dialogLoading = false
        })
      }).catch(error => {
        console.log(error)
      })
    },
    // 取消退单
    dialogCancelDrawback(id) {
      this.$confirm('确定要取消退单嘛?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.dialogLoading = true
        drawback_cancel({ id: id }).then(response => {
          this.dialogLoading = false
          this.dialogDrawbackVisible = false
          this.$message.success(response['message'])
          this.getList()
        }).catch(error => {
          console.log(error)
          this.dialogLoading = false
        })
      }).catch(error => {
        console.log(error)
      })
    },
    // 变动产品为另一个产品
    upProductToOtherProduct(item) {
      this.$confirm('确定要变动订单嘛?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.dialogLoading = true
        productUpToOtherProduct({ id: item.id }).then(response => {
          this.$message.success(response['message'])
          this.getList()
        }).catch(error => {
          console.log(error)
        })
      }).catch(error => {
        console.log(error)
      })
    },
    async exportExcel() {
      try {
        this.downloadLoading = true
        const params = {
          number: this.queryList.number,
          type: this.queryList.type,
          status: this.queryList.status,
          product: this.queryList.product,
          agent: this.queryList.agent,
          user: this.queryList.user,
          category_id: this.queryList.category_id,
          date: this.queryList.date,
          end_at: this.queryList.end_at,
          product_id: this.queryList.product_id,
          invoke: this.queryList.invoke,
          agent_exact: this.queryList.agent_exact,
        }
        const res = await financeListExport(params)
        if (res.code === 200) {
          this.$message.success('导出成功，请到下载中心下载')
        } else {
          this.$message.error(res.msg || '导出失败')
        }
      } catch (error) {
        this.$message.error(error.message || '导出失败')
      } finally {
        this.downloadLoading = false
      }
    },
    dateChange() {
      if (this.queryList.date) {
        this.queryList.date[0] = moment(this.queryList.date[0]).format('YYYY-MM-DD H:m:s')
        this.queryList.date[1] = moment(this.queryList.date[1]).format('YYYY-MM-DD H:m:s')
      }
    }
  }
}
</script>

<style lang="scss" >
.el-message-box__message{
  p{
    color:#f00!important;
    font-size: 16px!important;
  }
}
.confirmbtn{
    color:#FFFFFF;
    background-color:#f00!important;
    border-color:#f00!important;
  }

</style>
<style scoped lang="scss">
.list-wrap {
  height: 100%;
  .box-card {
    height: 100%;
    ::v-deep .el-card__header {
      padding-top: 0;
    }
    ::v-deep .el-card__body {
      padding: 0;
      margin-top: 20px;
      height: calc(100% - 58px);
      overflow: auto;
    }
  }
  .filter {
    border-bottom: 1px solid #E5E5E5;
  }
  .statistic{
    margin-top: 20px;

    /* 方案2：强调数字风格 */
     font-size: 14px;
     color: #333;
     span {
       color: #409EFF;
       font-weight: 500;
       font-size: 16px;
     }

    /* 通用增强效果 */
    transition: all 0.3s;
    font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
  }

  .table {
    margin-top: 20px;
    min-height: 200px;
  }
}
</style>
