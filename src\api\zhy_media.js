import request from '@/utils/request'

// 获取媒体投放列表
export function mediaListApi(data) {
  return request({
    url: '/mediums/list',
    method: 'POST',
    data: data
  })
}

// 获取媒体投放添加、修改
export function mediaAddApi(data) {
  return request({
    url: '/mediums/store',
    method: 'POST',
    data: data
  })
}

// 删除媒体投放
export function mediaDelApi(id) {
  return request({
    url: '/mediums/destory/',
    method: 'POST',
    data: { id }
  })
}

// 获取媒体投放分类 带分页
export function mediaTypeApi(data) {
  return request({
    url: '/mediumtypes/list',
    method: 'POST',
    data: data
  })
}

// 获取媒体投放分类 不分页
export function mediaTypeAllApi(data) {
  return request({
    url: '/mediumtypes/nonePageList',
    method: 'POST',
    data: data
  })
}

// 获取媒体投放分类添加、修改
export function mediaTypeAddApi(data) {
  return request({
    url: '/mediumtypes/store',
    method: 'POST',
    data: data
  })
}

// 删除媒体投放分类
export function mediaTypeDelApi(id) {
  return request({
    url: '/mediumtypes/destory',
    method: 'POST',
    data: { id }
  })
}