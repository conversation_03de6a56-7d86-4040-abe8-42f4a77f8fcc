import request from '@/utils/requestJzt'

export function bases(data) {
  return request({
    url: '/zhyman/liblst',
    method: 'post',
    data
  })
}

export function setCase(data) {
  return request({
    url: '/zhyman/setCase',
    method: 'post',
    data
  })
}

export function colorList(data) {
  return request({
    url: '/zhyman/color_list',
    method: 'post',
    data
  })
}

export function editCase(data) {
  return request({
    url: '/zhyman/libupdate',
    method: 'post',
    data
  })
}

export function setCaseRecommend(data) {
  return request({
    url: '/zhyman/recommendCase',
    method: 'post',
    data
  })
}

export function hideDomain(data) {
  return request({
    url: '/zhyman/noshowDomain',
    method: 'post',
    data
  })
}

export function getCaseMessage(data) {
  return request({
    url: '/zhyman/message',
    method: 'post',
    data
  })
}

export function delCase(data) {
  return request({
    url: '/zhyman/casedel',
    method: 'post',
    data
  })
}
