<template>
  <iframe
    id="iframepage"
    name="iframepage"
    frameBorder="0"
    scrolling="no"
    width="100%"
    height="auto"
    onLoad="this.height=800"
    :src="url"
  />
</template>
<script>
import { yidongyun_iframe } from "@/api/agent/product";
export default {
  name: '/yidongyun',
  data() {
    return {
      url: 'https://ecloud.10086.cn/api/query/ecm-compute-static/vm/list?poolId=CIDC-RP-25&productType=vm&isDistribution=true',
      height: 0,
      id: this.$route.query.id
    }
  },
  created() {
  },
  mounted() {
    this.sendDataToIframe()
  },
  methods: {
    sendDataToIframe() {
      let access_token='';
      yidongyun_iframe({ customer_id: this.id}).then(res => {
        const token = res.data.access_token
        window.addEventListener('message', function(e) {
          let res = e.data
          console.log(res)
          try {
            if (typeof JSON.parse(e.data) === 'object') {
              res = JSON.parse(e.data)
            }
          } catch (err) {}

          switch (res.action) {
            case 'consoleReady':
              window.sendMessage('consoleInit', { token })
              break
          }
        }, false)
      })
      function json2str(obj) {
        return JSON.stringify(obj, function(key, val) {
          if (typeof val === 'function') {
            val = val.toString()
          }
          return val
        })
      }
    window.sendMessage = function(action, data) {
        const iframe = document.getElementById('iframepage');
        iframe.contentWindow.postMessage(json2str({ action: action, data: data }), '*')
      }
    }
  }
}
</script>

<style scoped>
</style>
