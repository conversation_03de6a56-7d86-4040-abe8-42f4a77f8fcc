<script>
import SelfCard from '@/views/dashboard/admin/components/SelfCard.vue'
import { getCoreFunction } from '@/api/dashboard'
import BarEchart from '@/views/dashboard/admin/components/new/BarEchart.vue'

export default {
  name: 'CoreFunc',
  components: { BarEchart, SelfCard },
  data() {
    return {
      date: '',
      chartData: {
        x: [],
        y: []
      },
      legend: ['访问量'],
      colorList: ['#FFA63E'],
      total: 0,
      avarage: 0,
      grid: {
        left: 30,
        right: 100,
        bottom: 60,
        top: 30,
        containLabel: true
      }
    }
  },
  mounted() {
    this.handleGetCoreFunction()
  },
  methods: {
    handleGetCoreFunction() {
      if (!this.date) {
        this.date = new Date().getFullYear().toString() + '-' + (new Date().getMonth() + 1).toString().padStart(2, '0')
      }
      getCoreFunction({ date: this.date }).then(res => {
        if (res.code === 200 && res.data) {
          if (res.data.title) {
            this.chartData.x = res.data.title
          }
          if (res.data.count) {
            this.chartData.y = res.data.count
          }
        }
      })
    }
  }
}
</script>

<template>
  <SelfCard title="核心功能使用次数（访问量）">
    <bar-echart :show-legend="false" :grid="grid" height="500px" :chart-data="chartData" :legend="legend" :color-list="colorList" :data-type="'normal'" :rotate="-45" />
  </SelfCard>
</template>

<style scoped lang="scss">

</style>
