<!-- 媒体投放列表 -->
<template>
  <div style="height: 100%;">
    <el-table
      :data="tableData"
      style="width: 100%"
      row-key="id"
      highlight-current-row
      height="calc(100% - 96px)"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column label="ID" prop="id" width="80" />
      <el-table-column label="图标" prop="icon" width="180">
        <template slot-scope="scope">
          <el-image
            v-if="scope.row.icon"
            style="width: 40px; height: 40px"
            :src="scope.row.icon"
            fit="scale-down"
          />
          <span v-else>未上传</span>
        </template>
      </el-table-column>
      <el-table-column label="上级分类" prop="category" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.category ? scope.row.category : '顶级' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="title" label="名称" />
      <el-table-column label="操作" width="180">
        <template slot="header">
          <el-button type="primary" size="small" @click="addDialog">新增分类</el-button>
        </template>
        <template slot-scope="scope">
          <el-link :underline="false" type="primary" @click="editDialog(scope.row)">编辑</el-link>
          <el-popconfirm title="确定删除当前记录吗？" @onConfirm="deleteHandle(scope.row)">
            <el-link slot="reference" :underline="false" type="danger">删除</el-link>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      style="margin-top: 0;"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
    <!-- 新增 修改 -->
    <cate-edit-dialog ref="cateEditDialogRef" :edit-api="editApi" @getList="getList" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import CateEditDialog from './CateEditDialog'
import { aiTypeApi, aiTypeAddApi, aiTypeDelApi } from '@/api/zhy_ai'

export default {
  components: { Pagination, CateEditDialog },
  data() {
    return {
      listQuery: {
        page: 1,
        limit: 10
      },
      total: 0,
      tableData: [],
      editApi: aiTypeAddApi

    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    // 获取列表
    async getList() {
      const that = this
      const result = await aiTypeApi(that.listQuery)
      const { code, data } = result
      if (code === 200) {
        const { total, data: list } = data
        that.total = total
        that.tableData = list
      }
    },
    // 添加
    addDialog() {
      this.$refs.cateEditDialogRef.openDialog(null)
    },
    // 编辑
    editDialog(row) {
      this.$refs.cateEditDialogRef.openDialog(row)
    },
    // 删除电商平台
    async deleteHandle(row) {
      const that = this
      const result = await aiTypeDelApi(row.id)
      const { code } = result
      if (code === 200) {
        that.$message.success('删除成功')
        that.getList()
      }
    }
  }
}
</script>

<style scoped lang="scss">
  .list-wrap {
    height: 100%;

    ::v-deep .card-wrap {
      height: 100%;
      border: none;

      .el-card__body {
        height: 100%;
      }

      .box-card {
        height: 100%;
        border: none;

        .el-card__body {
          height: calc(100% - 59px);
          overflow: auto;
        }
      }
    }
  }
</style>
