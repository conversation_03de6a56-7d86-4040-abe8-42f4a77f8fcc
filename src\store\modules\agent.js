const state = {
  userType: [],
  roleType: []
}
const mutations = {
  SET_USER_TYPE: (state, userType) => {
    state.userType = userType
  },
  SET_ROLE_TYPE: (state, roleType) => {
    state.roleType = roleType
  }
}
const actions = {
  setUserType({ commit }, userType) {
    commit('SET_USER_TYPE', userType)
  },
  setRoleType({ commit }, roleType) {
    commit('SET_ROLE_TYPE', roleType)
  }
}
export default {
  namespaced: true,
  state,
  mutations,
  actions
}
