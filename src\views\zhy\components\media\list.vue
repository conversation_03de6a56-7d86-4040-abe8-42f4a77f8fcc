<!-- 媒体投放列表 -->
<template>
  <div class="">
    <el-table
      :data="tableData"
      style="width: 100%"
      row-key="id"
      highlight-current-row
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      height="calc(100vh - 380px)"
    >
      <el-table-column label="ID" prop="id" width="80" />
      <el-table-column label="分类" prop="category" width="180" />
      <el-table-column prop="title" label="名称" />
      <el-table-column prop="url" label="链接" />
      <el-table-column label="操作" width="180">
        <template slot="header">
          <el-button type="primary" size="small" @click="addDialog">新增平台</el-button>
        </template>
        <template slot-scope="scope">
          <el-link :underline="false" type="primary" @click="editDialog(scope.row)">编辑</el-link>
          <el-popconfirm title="确定删除当前记录吗？" @onConfirm="deleteHandle(scope.row)">
            <el-link slot="reference" :underline="false" type="danger">删除</el-link>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      style="margin-top: 0;"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
    <!-- 新增 修改 -->
    <edit-dialog ref="editDialogRef" :edit-api="editApi" @getList="getList" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import EditDialog from './EditDialog'
import { mediaListApi, mediaAddApi, mediaDelApi } from '@/api/zhy_media'

export default {
  components: { Pagination, EditDialog },
  data() {
    return {
      listQuery: {
        page: 1,
        limit: 10
      },
      total: 0,
      tableData: [],
      editApi: mediaAddApi

    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    // 获取列表
    async getList() {
      const that = this
      const result = await mediaListApi(that.listQuery)
      const { code, data } = result
      if (code === 200) {
        const { total, data: list } = data
        that.total = total
        that.tableData = list
      }
    },
    // 添加
    addDialog() {
      this.$refs.editDialogRef.openDialog(null)
    },
    // 编辑
    editDialog(row) {
      this.$refs.editDialogRef.openDialog(row)
    },
    // 删除电商平台
    async deleteHandle(row) {
      const that = this
      const result = await mediaDelApi(row.id)
      const { code } = result
      if (code === 200) {
        that.$message.success('删除成功')
        that.getList()
      }
    }
  }
}
</script>
<style lang='scss' scoped></style>
