<script>
import SelfFormTemp from '@/views/task/components/form/SelfFormTemp.vue'
import update from '@/views/task/mixins/update'

export default {
  name: 'LibraryUpdate',
  components: { SelfFormTemp },
  mixins: [update],
  data() {
    return {
      formData: {},
      rules: {},
      filters: [
        {
          label: '权限名称',
          prop: 'name',
          type: 'input',
          value: ''
        },
        {
          label: '路径',
          prop: 'path',
          type: 'input'
        },
        {
          label: '简单描述',
          prop: 'desc',
          type: 'textarea'
        },
        {
          label: '所属产品',
          prop: 'product_id',
          type: 'select',
          options: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '产品1',
              value: '1'
            }
          ]
        },
        {
          label: '所属栏目',
          prop: 'cate_id',
          type: 'select',
          options: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '栏目1',
              value: '1'
            }
          ]
        }
      ]
    }
  }
}
</script>

<template>
  <el-dialog :close-on-click-modal="false" :title="title + '操作手册'" :visible.sync="_visible">
    <SelfFormTemp ref="formWrap" :form-data.sync="formData" :inline="false" :rules="rules" :filters="filters" label-width="100px" />
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
