import request from '@/utils/request'

// 代办业务设置列表
export function getCompanyServiceSettingsListApi(data) {
  return request({
    url: '/agency/agencyList',
    method: 'POST',
    data
  })
}
// 获取修改代办设置需要参数
export function getCompanyServiceSettingsUpdateApi(data) {
  return request({
    url: '/agency/getAgencyEdit',
    method: 'POST',
    data
  })
}
// 添加企业服务代办设置
export function addCompanyServiceSettingsApi(data) {
  return request({
    url: '/agency/agencyCreate',
    method: 'POST',
    data
  })
}
// 修改企业服务代办设置
export function updateCompanyServiceSettingsApi(data) {
  return request({
    url: '/agency/agencyEdit',
    method: 'POST',
    data
  })
}
// 企业服务代办列表
export function getCompanyServiceListApi(data) {
  return request({
    url: '/agency/agencyIndex',
    method: 'POST',
    data
  })
}
