<template>
  <div class="follow-record">
    <statistics-template :config="config" @selection-change="handleSelectionChange">
      <template #topActions>
        <el-button type="primary" @click="handleAdd">新增记录</el-button>
      </template>

      <!-- 自定义操作列 -->
      <template #follow_action="{ row }">
        <el-button type="text" @click="handleEdit(row)">编辑</el-button>
        <el-button type="text" class="delete-btn" @click="handleDelete(row)">删除</el-button>
      </template>
    </statistics-template>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="50%">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="跟进金额" prop="amount">
          <el-input-number v-model="form.amount" :min="0" :precision="2" :step="100" />
        </el-form-item>

        <el-form-item label="跟进目期" prop="followDate">
          <el-date-picker
            v-model="form.followDate"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>

        <el-form-item label="客户意向" prop="intention">
          <el-input
            type="textarea"
            v-model="form.intention"
            :rows="3"
            placeholder="请输入客户意向"
          />
        </el-form-item>

        <el-form-item label="跟进情况" prop="followStatus">
          <el-input
            type="textarea"
            v-model="form.followStatus"
            :rows="3"
            placeholder="请输入跟进情况"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'

export default {
  name: 'FollowRecord',
  components: { StatisticsTemplate },
  data() {
    return {
      config: {
        key: 'follow',
        filters: [
          {
            label: '跟进日期',
            prop: 'dateRange',
            type: 'datePicker',
            settings: {
              type: 'daterange',
              valueFormat: 'yyyy-MM-dd',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期'
            }
          },
          {
            label: '金额范围',
            prop: 'amountRange',
            type: 'input',
            settings: {
              type: 'number',
              placeholder: '请输入金额'
            }
          }
        ],
        tableSettings: {
          selection: true,
          index: true,
          columns: [
            {
              label: '跟进金额',
              prop: 'amount',
              sortable: true
            },
            {
              label: '跟进目期',
              prop: 'followDate',
              sortable: true
            },
            {
              label: '客户意向',
              prop: 'intention',
              showOverflowTooltip: true
            },
            {
              label: '跟进情况',
              prop: 'followStatus',
              showOverflowTooltip: true
            },
            {
              label: '创建时间',
              prop: 'createTime',
              sortable: true
            },
            {
              label: '操作',
              prop: 'action',
              fixed: 'right',
              width: '150px',
              isSlot: true
            }
          ]
        }
      },
      // 弹窗相关数据
      dialogVisible: false,
      dialogTitle: '新增跟进记录',
      submitLoading: false,
      form: {
        amount: 0,
        followDate: '',
        intention: '',
        followStatus: ''
      },
      rules: {
        amount: [
          { required: true, message: '请输入跟进金额', trigger: 'blur' }
        ],
        followDate: [
          { required: true, message: '请选择跟进目期', trigger: 'change' }
        ],
        intention: [
          { required: true, message: '请输入客户意向', trigger: 'blur' }
        ],
        followStatus: [
          { required: true, message: '请输入跟进情况', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 表格选择回调
    handleSelectionChange(selection) {
      this.selection = selection
    },
    // 新增记录
    handleAdd() {
      this.dialogTitle = '新增跟进记录'
      this.form = {
        amount: 0,
        followDate: '',
        intention: '',
        followStatus: ''
      }
      this.dialogVisible = true
    },
    // 编辑记录
    handleEdit(row) {
      this.dialogTitle = '编辑跟进记录'
      this.form = { ...row }
      this.dialogVisible = true
    },
    // 删除记录
    handleDelete(row) {
      this.$confirm('确认删除该记录?', '提示', {
        type: 'warning'
      }).then(() => {
        // 调用删除接口
        this.$message.success('删除成功')
      }).catch(() => {})
    },
    // 提交表单
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.submitLoading = true
          // 调用保存接口
          setTimeout(() => {
            this.$message.success('保存成功')
            this.dialogVisible = false
            this.submitLoading = false
          }, 1000)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.follow-record {
  height: 100%;

  .dialog-footer {
    text-align: right;
  }

  .delete-btn {
    color: #f56c6c;
  }
}
</style>