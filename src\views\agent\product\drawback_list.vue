<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <!--<div slot="header" class="clearfix">
        &lt;!&ndash;<span>退款列表</span>&ndash;&gt;
        <el-button style="float: right; padding: 0;" type="text" @click="drawbackKeylist()">密钥产品退款列表</el-button>
      </div>-->
      <div v-loading="loading" style="height: 100%">
        <div class="filter">
          <el-form :inline="true" :model="queryList" class="demo-form-inline" size="small">
            <el-form-item label="产品名称">
              <el-input v-model="queryList.product" placeholder="请输入产品名称" />
            </el-form-item>
            <el-form-item v-if="userType === 1" label="代理商名称">
              <el-input v-model="queryList.agent" placeholder="代理商名称" />
            </el-form-item>
            <el-form-item label="用户名称">
              <el-input v-model="queryList.user" placeholder="用户名称" />
            </el-form-item>
            <el-form-item label="购买时间">
              <el-date-picker
                v-model="queryList.start_at"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
                clearable
              />
            </el-form-item>
            <el-form-item label="到期时间">
              <el-date-picker
                v-model="queryList.end_at"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
                clearable
              />
            </el-form-item>
            <el-form-item label="审核状态">
              <el-select v-model="queryList.status" placeholder="全部" clearable>
                <el-option label="审核中" :value="0" />
                <el-option label="已通过" :value="1" />
                <el-option label="已拒绝" :value="-1" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="table mt20">
          <el-table
            :data="tableData"
            stripe
            style="width: 100%"
            height="calc(100% - 96px)"
          >
            <el-table-column
              prop="product"
              label="产品名称"
            />
            <el-table-column
              v-if="userType === 1"
              prop="agent"
              label="代理名称"
            />
            <el-table-column
              prop="customer"
              label="用户名称"
            />
            <el-table-column
              prop="finance_price"
              label="订单金额"
              width="110"
            />
            <el-table-column
              prop="price"
              label="退款金额"
              width="110"
            />
            <el-table-column
              prop="created_at"
              label="申请退款时间"
            />
            <el-table-column
              prop="updated_at"
              label="处理退款时间"
            />
            <el-table-column
              prop="content"
              label="退款原因"
            />
            <el-table-column label="客服备注">
              <template slot-scope="scope">
                <div v-if="scope.row.remark && scope.row.remark.substr(0,4)==='http' " style="cursor: pointer">
                  <el-image lazy style="width: 50px; height: 50px" :src="scope.row.remark" :preview-src-list="[scope.row.remark]" />
                </div>
                <span v-else-if="scope.row.status===-1">{{ scope.row.remark }}</span>
              </template>
            </el-table-column>
            <el-table-column label="审核状态">
              <template slot-scope="scope">
                <el-link v-if="scope.row.status === 0" type="primary" :underline="false" @click="showDialog(scope.row)">审核中</el-link>
                <el-link v-if="scope.row.status === 1" type="primary" :underline="false">已通过：财务</el-link>
                <el-link v-if="scope.row.status === -1" type="danger" :underline="false">已拒绝</el-link>
                <el-link v-if="scope.row.status === 2" type="warning" :underline="false" @click="showDialog(scope.row)">已通过：客服</el-link>
              </template>
            </el-table-column>
            <el-table-column
              show-overflow-tooltip
              prop="auth"
              label="审核内容"
            />
            <el-table-column v-if="userType === 2" label="操作">
              <template slot-scope="scope">
                <el-link v-if="scope.row.status === -1" type="danger" :underline="false" @click="drawback_destory(scope.row.drawback_id)">删除</el-link>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryList.page"
            :limit.sync="queryList.perPage"
            style="text-align:center;"
            @pagination="getList"
          />
        </div>
      </div>
    </el-card>
    <el-dialog
      title="退款申请"
      :visible.sync="dialogFormVisible"
      width="35%"
      top="10vh"
    >
      <el-form ref="form" :model="form" label-width="120px" :rules="rules">
        <el-form-item label="用户名称">
          <span>{{ form.customer }}</span>
        </el-form-item>
        <el-form-item label="产品名称" prop="product">
          <span>{{ form.product }}</span>
        </el-form-item>
        <el-form-item label="退款原因" prop="product">
          <span>{{ form.content }}</span>
        </el-form-item>
        <el-form-item label="审核" prop="content" style="width: 70%">
          <el-select v-model="drawbackForm.status">
            <el-option label="审核通过" :value="1" />
            <el-option label="审核拒绝" :value="-1" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="drawbackForm.status === 1 && form.status === 0" label="客服备注" prop="content" style="width: 70%">
          <upload-img
            :url.sync="drawbackForm.content"
            accept="image/png, image/jpeg"
          />
        </el-form-item>
        <el-form-item v-if="drawbackForm.status === -1 && form.status === 0" label="拒绝原因" prop="content" style="width: 70%">
          <el-input
            v-model="drawbackForm.content"
            type="textarea"
            placeholder="请输入原因"
            maxlength="30"
            show-word-limit
          />
        </el-form-item>
        <el-form-item v-if="form.status === 2" label="审核内容" prop="content" style="width: 70%">
          <el-input
            v-model="drawbackForm.content"
            type="textarea"
            placeholder="请输入内容"
            maxlength="30"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="auth">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { drawback_list, auth_server, auth_finance, drawback_destory } from '@/api/agent/product'
import { mapGetters } from 'vuex'
import Pagination from '@/components/Pagination'
import UploadImg from '@/components/Upload/uploadImg'
export default {
  name: 'List',
  components: { UploadImg, Pagination },
  data() {
    return {
      role: '',
      dialogFormVisible: false,
      loading: false,
      dialogLoading: false,
      tableData: [],
      queryList: {
        page: 1,
        perPage: 10
      },
      form: {},
      drawbackForm: { id: '', content: '', remark: '', auth: '', status: 1 },
      total: 0,
      rules: {
        content: [
          { required: true, message: '请输入审核内容', trigger: 'blur' }
        ]
      },
      drawbackRules: {
        content: [
          { required: true, message: '请输入退款内容', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    // 别名处理防止冲突
    ...mapGetters({
      userType: 'agentUserType',
      roles: 'agentRoleType'
    })
  },
  created() {
    this.role = this.roles[0]
    this.getList()
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.list-wrap > .el-card > .el-card__body')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  methods: {
    getList() {
      this.loading = true
      drawback_list(this.queryList).then(response => {
        if (response.code && response.data) {
          this.tableData = response.data.list
          this.total = response.data.meta.total
        }
      }).finally(() => {
        this.loading = false
      })
    },
    drawbackKeylist() {
      this.$router.push({
        name: 'agentProductRefund',
        query: {
        }
      })
    },
    showDialog(row) {
      this.form = row
      this.drawbackForm = {}
      this.drawbackForm.id = row.id
      const showList = [0, 2]
      if (showList.includes(row.status)) {
        this.dialogFormVisible = true
      }
    },
    // 审核
    auth() {
      this.dialogLoading = true
      if (this.form.status === 0) {
        this.drawbackForm.remark = this.drawbackForm.content
        auth_server(this.drawbackForm).then(response => {
          this.dialogLoading = false
          this.dialogFormVisible = false
          this.$message.success(response['message'])
          this.getList()
        }).catch(error => {
          console.log(error)
          this.dialogLoading = false
        })
      } else if (this.form.status === 2) {
        this.drawbackForm.auth = this.drawbackForm.content
        auth_finance(this.drawbackForm).then(response => {
          this.dialogLoading = false
          this.dialogFormVisible = false
          this.$message.success(response['message'])
          this.getList()
        }).catch(error => {
          console.log(error)
          this.dialogLoading = false
        })
      }
    },
    // 删除 被拒绝的退款记录
    drawback_destory(id) {
      this.$confirm('确定要删除嘛?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.dialogLoading = true
        drawback_destory({ id: id }).then(response => {
          this.$message.success(response['message'])
          this.getList()
        }).catch(error => {
          console.log(error)
        })
      }).catch(error => {
        console.log(error)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap {
  height: 100%;
  .box-card {
    height: 100%;
    ::v-deep .el-card__header {
      padding-top: 0;
    }
    ::v-deep .el-card__body {
      padding: 0;
      height: 100%;
      overflow: auto;
    }
  }
  .filter {
    border-bottom: 1px solid #E5E5E5;
  }
  .table {
    margin-top: 20px;
    min-height: 200px;
  }
}
</style>
