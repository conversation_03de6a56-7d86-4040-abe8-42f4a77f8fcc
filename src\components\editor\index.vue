<template>
  <div ref="editor" :style="{ height:height}" />
</template>

<script>
import E from 'wangeditor'
export default {
  name: 'Editor',
  props: {
    content: {
      type: String,
      default: () => ''
    },
    height: {
      type: String,
      default: () => '350px'
    },
    path: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      editor: {},
      isChange: false
    }
  },
  watch: {
    content() {
      if (!this.isChange) {
        this.editor.txt.html(this.content)
      }
      this.isChange = false
    }
  },
  mounted() {
    this.editor = new E(this.$refs.editor)
    this.editor.config.menus = [
      'fontSize',
      'italic',
      'underline',
      'strikeThrough',
      'link',
      'list',
      'justify',
      'image',
      'table'
      // 'showFullScreen'
    ]
    this.editor.config.showFullScreen = true
    this.editor.config.onchange = (html) => {
      this.isChange = true
      this.$emit('update:content', html)
    }

    this.editor.config.uploadImgServer = process.env.VUE_APP_BASE_API + '/upload'
    this.editor.config.withCredentials = false
    this.editor.config.uploadFileName = 'file'
    this.editor.config.zIndex = 100
    this.editor.config.uploadImgHeaders = {
      'Authorization': 'Bearer ' + this.$store.getters.token
    }
    this.editor.config.uploadImgParams = {
      path: this.path
    }
    this.editor.config.uploadImgHooks = {
      customInsert: function(insertImg, result, editor) {
        // 图片上传并返回结果，自定义插入图片的事件（而不是编辑器自动插入图片！！！）
        if (result.code === 200) {
          var url = result.data.url
          insertImg(url)
        } else {
          alert(result.message)
        }
      }
    }
    this.editor.create()

    if (this.content) {
      this.editor.txt.html(this.content)
    }
  }
}
</script>

