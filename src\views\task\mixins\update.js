export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    },
    dialogTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      submitLoading: false
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    title() {
      return this.data.id ? '编辑' : '新增'
    }
  },
  watch: {
    data: {
      handler(val) {
        if (!val) return
        if (!this.filters) return
        const uploadField = this.filters.filter(v => v.type === 'upload')
        const copyData = JSON.parse(JSON.stringify(val))
        if (uploadField.length) {
          uploadField.forEach(field => {
            const key = field.prop
            if (copyData[key]) {
              // 判断copyData[key]是否是字符串
              if (typeof copyData[key] === 'string') {
                copyData[key] = [copyData[key]]
              }
              // 判断copyData[key]是否是数组
              if (Array.isArray(copyData[key])) {
                copyData[key] = copyData[key].map(v => ({
                  uid: v.fileId || v,
                  name: v.fileName || v.split('/').pop().split('\\').pop(),
                  url: field.settings.baseUrl ? field.settings.baseUrl + (v.fileUrl || v) : (v.fileUrl || v)
                }))
              }
            } else {
              copyData[key] = []
            }
          })
        }
        // 判断copyData中是否有this.filters中没有的属性，如果没有则加上
        this.filters.forEach(field => {
          const key = field.prop
          if (!copyData[key]) {
            copyData[key] = undefined
          }
        })
        this.formData = copyData
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleSubmit(refName = '') {
      let ref
      if (refName && typeof refName === 'string') {
        ref = this.$refs[refName]
      } else {
        ref = this.$refs.formWrap.$refs.filterRef
      }
      ref.validate(async(valid) => {
        if (valid) {
          this.$emit('submit', this.formData)
          if (this.submitCallback) {
            const { success, msg } = await this.submitCallback()
            if (success) {
              this.$message.success('操作成功')
              this.handleCancel(refName)
            } else {
              this.submitLoading = false
              this.$message.error(msg)
            }
          }
        }
      })
    },
    handleCancel(refName = '') {
      let ref
      if (refName && typeof refName === 'string') {
        ref = this.$refs[refName]
      } else {
        ref = this.$refs.formWrap.$refs.filterRef
      }
      try {
        ref.resetFields()
      } catch (e) {
        for (const formDataKey in this.formData) {
          // eslint-disable-next-line no-unused-vars
          this.formData[formDataKey] = ''
        }
      }
      this._visible = false
      this.$emit('cancel')
    }
  }
}
