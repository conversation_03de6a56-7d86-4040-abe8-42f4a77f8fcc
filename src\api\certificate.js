import request from '@/utils/request'

export function personal_list(data) {
  return request({
    url: '/certificate/personal',
    method: 'POST',
    data: data
  })
}

export function personal_detail(data) {
  return request({
    url: '/certificate/personal/detail',
    method: 'POST',
    data: data
  })
}


export function personal_check(data) {
  return request({
    url: '/certificate/personal/check',
    method: 'POST',
    data: data
  })
}


export function company_list(data) {
  return request({
    url: '/certificate/company',
    method: 'POST',
    data: data
  })
}

export function company_detail(data) {
  return request({
    url: '/certificate/company/detail',
    method: 'POST',
    data: data
  })
}

export function company_check(data) {
  return request({
    url: '/certificate/company/check',
    method: 'POST',
    data: data
  })
}
