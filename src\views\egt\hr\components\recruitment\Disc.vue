<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import DiscUpdate from './DiscUpdate.vue'
import { deleteDISCListApi, getDISCListApi } from '@/api/egt/recruitment'

export default {
  name: 'Disc',
  components: {
    StatisticsTemplate,
    DiscUpdate
  },
  data() {
    return {
      config: {
        key: 'disc',
        tableSettings: {
          api: getDISCListApi,
          columns: [
            {
              label: 'ID',
              prop: 'id',
              width: 60
            },
            {
              label: '选项',
              prop: 'option',
              isSlot: true
            },
            {
              label: '操作',
              prop: 'action',
              isSlot: true,
              align: 'center',
              width: '200'
            }
          ],
          field: {
            page: 'pageIndex',
            limit: 'pageSize',
            total: 'total'
          }
        }
      },
      updateVisible: false,
      row: {}
    }
  },
  methods: {
    handleAdd() {
      this.row = {}
      this.updateVisible = true
    },
    handleUpdate(row) {
      this.row = row
      this.updateVisible = true
    },
    async handleDelete(row) {
      if (!row.id) return
      try {
        this.$refs.filterListTableWrap.setLoading(row.id, true)
        const res = await deleteDISCListApi({ id: row.id })
        if (res.code === 200) {
          this.$message.success('删除成功')
          await this.$refs.filterListTableWrap.handleGetData()
        } else {
          this.$message.error(res.msg)
        }
      } catch (e) {
        console.log(e)
        this.$message.error('删除失败')
      } finally {
        this.$refs.filterListTableWrap.setLoading(row.id, false)
      }
    },
    handleSubmit() {
      this.$refs.filterListTableWrap.handleGetData()
    }
  }
}
</script>

<template>
  <div class="list">
    <statistics-template ref="filterListTableWrap" :config="config">
      <template #topActions>
        <div style="margin-bottom: 10px;text-align: right;width: 100%;">
          <el-button type="primary" @click="handleAdd">添加</el-button>
        </div>
      </template>
      <template #disc_option="{row}">
        <div v-html="row.option" />
      </template>
      <template #disc_action="{row}">
        <el-button size="mini" @click="handleUpdate(row)">编辑</el-button>
        <el-popconfirm
          title="确定删除吗？"
          @onConfirm="handleDelete(row)"
        >
          <el-button slot="reference" size="mini" type="danger" :loading="row.loading">删除</el-button>
        </el-popconfirm>
      </template>
    </statistics-template>
    <disc-update :visible.sync="updateVisible" :data="row" @submit="handleSubmit" />
  </div>
</template>

<style scoped lang="scss">
.list{
  height: 100%;
}
</style>
