# just a flag
ENV = 'development'

# base api
VUE_APP_BASE_API = 'https://apidev.china9.cn/manager'

# vue-cli uses the VUE_CLI_BABEL_TRANSPILE_MODULES environment variable,
# to control whether the babel-plugin-dynamic-import-node plugin is enabled.
# It only does one thing by converting all import() to require().
# This configuration can significantly increase the speed of hot updates,
# when you have a large number of pages.
# Detail:  https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/index.js

VUE_CLI_BABEL_TRANSPILE_MODULES = true

VUE_APP_OPEN_API = 'https://open.china9.cn'
VUE_APP_OPEN_API_PROXY = '/openApi'

VUE_APP_TASK_API = 'https://flexible.china9.cn/api/'
VUE_APP_TASK_API_PROXY = '/taskApi'

VUE_APP_JZT_API = 'https://zhjzt.china9.cn/api/'
VUE_APP_JZT_API_PROXY = '/jztApi'

VUE_APP_JZT2_API = 'https://jzt2.china9.cn/api/'
VUE_APP_JZT2_API_PROXY = '/jzt2Api'

VUE_APP_API_API = 'https://apidev.china9.cn/'
VUE_APP_API_API_PROXY = '/apiApi'

VUE_APP_AGENT_API = 'https://scrm-api.china9.cn/api/'
VUE_APP_AGENT_API_PROXY = '/agentApi'

VUE_APP_TASK_MANAGER_API = 'https://flexible.china9.cn/manager/'
VUE_APP_TASK_MANAGER_PROXY = '/taskManagerApi'

VUE_APP_IHR_API = 'https://ihr.china9.cn/baseapi'
VUE_APP_IHR_PROXY = '/ihrApi'
