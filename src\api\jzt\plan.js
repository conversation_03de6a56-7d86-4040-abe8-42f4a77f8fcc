import request from '@/utils/requestJzt'

// 获取节日列表
export function List(data) {
  return request({
    url: '/zhymans/dayList',
    method: 'post',
    data
  })
}
// 添加节日
export function Add(data) {
  return request({
    url: '/zhymans/addday',
    method: 'post',
    data
  })
}
// 修改节日
export function Edit(data) {
  return request({
    url: '/zhymans/editday',
    method: 'post',
    data
  })
}
// 删除节日
export function Delete(data) {
  return request({
    url: '/zhymans/delday',
    method: 'post',
    data
  })
}

// 获取节日分类
export function Category() {
  return request({
    url: '/zhymans/daytypeList',
    method: 'post'
  })
}
// 添加节日分类
export function AddCategory(data) {
  return request({
    url: '/zhymans/adddaytype',
    method: 'post',
    data
  })
}
// 修改节日分类
export function EditCategory(data) {
  return request({
    url: '/zhymans/editdaytype',
    method: 'post',
    data
  })
}
// 删除节日分类
export function DeleteCategory(data) {
  return request({
    url: '/zhymans/deldaytype',
    method: 'post',
    data
  })
}
