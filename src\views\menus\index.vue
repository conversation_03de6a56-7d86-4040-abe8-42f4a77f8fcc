<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>菜单管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="createMenu">新增菜单</el-button>
      </div>
      <el-table
        :data="tableData"
        style="width: 100%"
        row-key="id"
        highlight-current-row
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        :row-class-name="tableRowClassName"
        height="600px"
      >
        <el-table-column
          label="ID"
          width="65"
        />
        <el-table-column label="图标" width="65">
          <template slot-scope="scope">
            <svg-icon v-if="scope.row.icon" :icon-class="scope.row.icon" />
          </template>
        </el-table-column>
        <el-table-column
          prop="title"
          label="名称"
          width="180"
        />
        <el-table-column
          prop="path"
          label="路径"
        />
        <el-table-column label="状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status" type="success" effect="dark" size="small">启用</el-tag>
            <el-tag v-else type="danger" effect="dark" size="small">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-link :underline="false" type="primary" @click="editDialog(scope.row)">编辑</el-link>
            <el-popconfirm
              title="确定删除当前记录吗？"
              @onConfirm="deleteHandle(scope.row)"
            >
              <el-link slot="reference" :underline="false" type="danger">删除</el-link>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getTableMenus" />
    </el-card>
    <!--表单-->
    <el-dialog
      title="菜单管理"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-form ref="ruleForm" :model="formData" :rules="rules" label-width="180px" class="demo-ruleForm">
        <el-form-item label="父级菜单：">
          <el-col :span="20">
            <el-cascader
              v-model="formData.pid"
              placeholder="请选择"
              :options="rootMenus"
              :props="{
                label: 'title',
                value: 'id',
                checkStrictly: true,
                expandTrigger: 'hover',
                emitPath:false
              }"
              clearable
            />
          </el-col>
        </el-form-item>
        <el-form-item label="中文名称：" prop="title">
          <el-col :span="12">
            <el-input v-model="formData.title" />
          </el-col>
        </el-form-item>
        <el-form-item label="英文名称：" prop="name">
          <el-col :span="12">
            <el-input v-model="formData.name" />
          </el-col>
        </el-form-item>
        <el-form-item label="图标代码：" prop="icon">
          <el-col :span="12">
            <el-input v-model="formData.icon" />
          </el-col>
        </el-form-item>
        <el-form-item label="显示URL：" prop="path">
          <el-col :span="20">
            <el-input v-model="formData.path" />
          </el-col>
        </el-form-item>
        <el-form-item label="跳转URL：" prop="redirect">
          <el-col :span="20">
            <el-input v-model="formData.redirect" />
          </el-col>
        </el-form-item>
        <el-form-item label="组件路径：" prop="component">
          <el-col :span="20">
            <el-input v-model="formData.component" />
          </el-col>
        </el-form-item>
        <!--是否显示在左侧导航-->
        <el-form-item label="是否显示在左侧导航：" prop="showInLeft">
          <el-col :span="20">
            <el-switch
              v-model="formData.showInLeft"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
            />
          </el-col>
        </el-form-item>
        <el-form-item label="是否隐藏：" prop="hidden">
          <el-col :span="20">
            <el-switch
              v-model="formData.hidden"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
            />
          </el-col>
        </el-form-item>
        <el-form-item label="状态开关：" prop="status">
          <el-col :span="20">
            <el-switch
              v-model="formData.status"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
            />
          </el-col>
        </el-form-item>
        <el-form-item label="排序位置：" prop="level">
          <el-col :span="10">
            <el-input-number v-model="formData.level" :min="0" :max="9999999" :step="1" />
          </el-col>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="saveHandle">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getTreeTableMenus, getRootMenus, createMenu, updateMenu, deleteMenu } from '../../api/menu'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
export default {
  components: { Pagination },
  data() {
    return {
      loading: false,
      listQuery: {
        page: 1,
        limit: 10
      },
      total: 0,
      rootMenus: [],
      dialogVisible: false,
      formData: {},
      tableData: [],
      rules: {
        name: [
          { required: true, message: '缺少菜单英文名称', trigger: 'blur' }
        ],
        title: [
          { required: true, message: '缺少菜单中文名称', trigger: 'blur' }
        ],
        path: [
          { required: true, message: '显示URL必填', trigger: 'blur' }
        ],
        component: [
          { required: true, message: '组件路径必填', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getTableMenus()
  },
  methods: {
    getTableMenus() {
      getTreeTableMenus(this.listQuery).then(response => {
        this.total = response.data.total
        this.tableData = response.data.data
      })
    },
    getMenuList() {
      getRootMenus().then(response => {
        this.rootMenus = response.data
        this.rootMenus.unshift({
          id: 0,
          title: '顶级菜单'
        })
      })
    },
    createMenu() {
      this.formData = {
        pid: 0,
        status: 1,
        level: 0,
        hidden: 0
      }
      this.getMenuList()
      this.dialogVisible = true
    },
    editDialog(row) {
      this.getMenuList()
      this.formData = row
      this.dialogVisible = true
    },
    saveHandle() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.loading = true
          if (this.formData.id) {
            updateMenu(this.formData).then(response => {
              if (response.code === 200) {
                this.dialogVisible = false
                this.loading = false
                this.getTableMenus()
              }
            })
          } else {
            createMenu(this.formData).then(response => {
              if (response.code === 200) {
                this.dialogVisible = false
                this.loading = false
                this.getTableMenus()
              }
            }).catch(error => {
              console.log(error)
              this.loading = false
            })
          }
        }
      })
    },
    deleteHandle(row) {
      deleteMenu(row).then(response => {
        if (response.code === 200) {
          this.getTableMenus()
        }
      })
    },
    tableRowClassName({ row, index }) {
      if (row.pid === 0) {
        return 'success-row'
      }
      return ''
    }
  }
}
</script>

<style>
  .el-table .success-row {
    background: #f0f9eb;;
  }
</style>
