<template>
  <div class="scene-wrap">
    <div class="header">
      <div class="header-left">
        <el-select v-model="listQ.category" placeholder="请选择文案类型" class="select-category" clearable @change="refreshData">
          <el-option
            v-for="cate in aigcCategories"
            :key="cate.id"
            :label="cate.title"
            :value="cate.id"
          />
        </el-select>
        <el-input style="width: 300px;margin-left: 10px; margin-right: 10px;" v-model="listQ.title" placeholder="请输入场景名称" class="input-title" clearable @change="refreshData" />
        <el-button type="primary" @click="refreshData">查询</el-button>
      </div>
      <el-button type="primary" @click="addScene">新增场景</el-button>
    </div>
    <div class="list-wrapper">
      <div class="table-box">
        <el-table
          v-loading="loading"
          :data="scenes"
          height="100%"
          style="width: 100%"
        >
          <el-table-column prop="title" label="场景名称" />
          <el-table-column prop="categoryTitle" label="文案类型" />
          <el-table-column prop="created_at" label="创建时间" width="220" />
          <el-table-column label="操作" width="180">
            <template slot-scope="{ row }">
              <el-button type="text" @click="editRow(row)">编辑</el-button>
              <el-button type="text" @click="deleteRow(row)">删除</el-button>
              <el-button type="text" @click="formManagement(row)">表单管理</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-box">
        <el-pagination
          v-if="listTotal > 0"
          background
          layout="total, prev, pager, next, jumper"
          :page-size="listQ.size"
          :current-page="listQ.page"
          :total="listTotal"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
    <edit-scene ref="editSceneRef" @refresh="refreshData" />
    <form-manager ref="formManagerRef" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import EditScene from './components/editScene.vue';
import FormManager from './components/FormManager.vue';
import { sceneListApi, sceneDeleteApi } from "@/api/aigc";

export default {
  name: 'AiDocumentScene',
  components: { EditScene, FormManager },
  data() {
    return {
      loading: false,
      scenes: [],
      listTotal: 0,
      listQ: { page: 1, size: 10 },
    };
  },
  computed: {
    ...mapGetters(['aigcCategories']),
  },
  created() {
    this.fetchData();
    this.$store.dispatch('aigc/fetchCategories');
  },
  methods: {
    // 加载数据
    fetchData() {
      this.loading = true;
      sceneListApi(this.listQ)
        .then((res) => {
          console.log("场景列表", res);
          let { data, total } = res.data;
          if (res.code == 200) {
            if (total > 0 && data.length == 0) {
              this.listQ.page = this.listQ.page - 1;
              this.fetchData();
              return;
            }
            this.scenes = data;
            this.listTotal = total;
          }
        })
        .catch(() => {
          console.error("加载场景数据失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 刷新数据
    refreshData() {
      this.listQ.page = 1;
      this.fetchData();
    },
    handleSizeChange(size) {
      this.listQ.size = size;
      this.fetchData();
    },
    handleCurrentChange(page) {
      this.listQ.page = page;
      this.fetchData();
    },
    // 添加场景
    addScene() {
      this.$refs.editSceneRef.openDialog();
    },
    // 编辑场景
    editRow(row) {
      this.$refs.editSceneRef.openDialog(row);
    },
    // 删除场景
    deleteRow(row) {
      this.$confirm('确定删除该场景吗？').then(() => {
        sceneDeleteApi({ id: row.id }).then(() => {
          this.$message.success('删除成功');
          this.fetchData();
        });
      });
    },
    // 表单管理
    formManagement(row) {
      this.$refs.formManagerRef.openDialog(row);
    }
  },
}
</script>

<style scoped lang="scss">
.scene-wrap {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    .header-left {
      display: flex;
      align-items: center;
    }
  }
  .list-wrapper {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .table-box {
      flex: 1;
      overflow: hidden;
    }
    .pagination-box {
      text-align: center;
      padding: 10px;
    }
  }
}
</style>