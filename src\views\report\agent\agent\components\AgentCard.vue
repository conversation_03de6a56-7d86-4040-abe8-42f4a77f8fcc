<template>
  <SelfCard :title="title">
    <template v-slot:right>
      <div style="display: flex; align-items: center;">
        <el-date-picker
          v-model="value2"
          value-format="yyyy-MM"
          type="monthrange"
          align="left"
          unlink-panels
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          :picker-options="pickerOptions"
          @change="changeDay()"
        />
        <div class="btn-day">
          <el-radio-group v-model="indexOfType">
            <el-radio-button v-for="charge in chargeRadio" :key="charge.value" :label="charge.value">{{ charge.label
            }}</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </template>
    <div :class="[indexOfType === 0 ? 'show' : 'hide', 'content']">
      <div class="view-body">
        <div class="top">
          <el-row :gutter="20">
            <el-col v-for="(item, index) in statistics" :key="index" :xs="8" :sm="8" :lg="8">
              <el-row class="dlsbtn" :style="{backgroundColor: color.card}">
                <el-col :xs="16" :sm="16" :lg="16">
                  <div>
                    <p class="item-title">{{ item.title }}</p>
                  </div>
                  <div>
                    <count-to :start-val="0" :end-val="item.value" :duration="1000" class="item-num" />
                  </div>
                </el-col>
                <el-col :xs="8" :sm="8" :lg="8">
                  <div class="demo-image">
                    <div class="block">
                      <el-image style="width: 40px; height: 40px" :src="item.img" fit="fill" />
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
        <div v-loading="mapLoading" class="bottom">
          <slot v-if="$slots.chart" name="chart" />
          <div v-else :id="chartId" style="width: 100%;height: 334px;" />
        </div>
      </div>
    </div>
    <div :class="[indexOfType === 1 ? 'show' : 'hide', 'content']">
      <div class="view-body1">
        <table v-if="!$slots.table" border="1">
          <tr>
            <th>排名</th>
            <th>代理区域</th>
            <th style="text-align: left">代理商名称</th>
            <th>充值金额</th>
          </tr>
          <tr v-for="(item, index) in tableData" :key="index">
            <td>{{ index + ((searchForm.page - 1) * 5) + 1 }}</td>
            <td>{{ item.area }}</td>
            <td style="text-align: left">{{ item.name }}</td>
            <td>{{ item.actualprice }}</td>
          </tr>
        </table>
        <slot v-else name="table" />
        <pagination
          v-show="searchForm.total > 0"
          :total="searchForm.total"
          :page.sync="searchForm.page"
          :limit.sync="searchForm.perPage"
          :auto-scroll="false"
          layout="total, prev, pager, next, jumper"
          style="text-align:center;"
          @pagination="((page, limit) => {
            this.$emit('getTable', page, limit)
          })"
        />
        <div v-if="tableData.length === 0" class="null">暂无数据</div>
      </div>
    </div>
  </SelfCard>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import CountTo from 'vue-count-to'
import { mapGetters } from 'vuex'
import * as echarts from 'echarts'
import SelfCard from '@/views/dashboard/admin/components/SelfCard.vue'

export default {
  name: 'AgentCard',
  components: { Pagination, SelfCard, CountTo },
  props: {
    title: {
      type: String,
      default: ''
    },
    chargeRadio: {
      type: Array,
      default: () => []
    },
    statistics: {
      type: Array,
      default: () => []
    },
    chartData: {
      type: Object,
      default: () => ({
        x: [],
        y: []
      })
    },
    color: {
      type: Object,
      default: () => ({
        card: '',
        chart: ''
      })
    },
    tableData: {
      type: Array,
      default: () => ([])
    },
    form: {
      type: Object,
      default: () => ({
        status: '',
        page: 1,
        perPage: 5,
        total: 0,
        is_buy: false
      })
    },
    chartId: {
      type: String,
      default: ''
    },
    date: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      mapLoading: false,
      alltotalprice: 0,
      alltotalrjprice: 0,
      alltotalyjprice: 0,
      indexOfDate: 0,
      indexOfType: 0,
      indexOfCate: 0,
      chartLine: null,
      dataSource: [],
      trend_keys: [],
      trend_data1: [],
      trend_data2: []
    }
  },
  computed: {
    ...mapGetters([
      'type'
    ]),
    value2: {
      get() {
        return this.date
      },
      set(val) {
        this.$emit('update:date', val)
      }
    },
    searchForm: {
      get() {
        return this.form
      },
      set(val) {
        this.$emit('update:form', val)
      }
    }
  },
  watch: {
    chartData: {
      handler() {
        this.setOptions()
      },
      deep: true,
      immediate: true
    },
    indexOfType: {
      handler() {
        this.init()
      },
      immediate: true
    }
  },
  mounted() {
    this.init()
    this.getActive()
  },
  methods: {
    selectType(index) {
      this.indexOfType = index
      this.init()
    },
    changeDay() {
      this.init()
    },
    getActive() {
      // 产品活跃度排行
      this.$emit('getActive')
    },
    async init() {
      this.$emit('getTable')
    },
    setOptions() {
      this.$nextTick(() => {
        const ele = document.getElementById(this.chartId)
        if (!ele) return
        this.chartLine = echarts.init(ele)
        this.chartLine.setOption({
          legend: {
            show: false
          },
          tooltip: {
            trigger: 'axis',
            showContent: true,
            formatter: function(params) {
              return params[0].axisValue + '<br/>' + params[0].seriesName + params[0].data[1] + '万元'
            }
          },
          dataset: {
            source: [
              this.chartData.x,
              this.chartData.y
            ]
          },
          xAxis: { type: 'category', boundaryGap: false },
          grid: { top: '10%', left: '5%', right: '5%', bottom: '10%' },
          yAxis: [
            {
              type: 'value',
              name: '万元'
            }
          ],
          series: [
            {
              type: 'line', smooth: true, seriesLayoutBy: 'row',
              name: '代理商充值总额',
              itemStyle: {
                normal: {
                  color: 'transparent', // 折线点的颜色
                  lineStyle: {
                    color: this.color.chart// 折线的颜色
                  }
                }
              },
              areaStyle: { // 区域颜色
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: this.color.chart + '80'
                }, {
                  offset: 1,
                  color: '#fff'
                }])
              }
            }
          ]
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .show {
    display: block;
  }

  .hide {
    display: none;
  }

  .content{
    margin-top: 42px;
    height: 444px;
  }

  .btn-day {
    flex: 1;
    margin-left: 15px;
    text-align: right;

    span {
      margin: 0 5px;
      padding: 8px 15px;
      font-size: 10px;
      cursor: pointer;
      border: 1px solid #eaeaec;
      border-radius: 5px;
    }

    .sel {
      background-color: #0e90d2;
      color: #ffffff;
    }
  }

  .filter-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .right {
      span {
        padding: 10px 25px;
        font-size: 10px;
        cursor: pointer;
        color: #0e90d2;
        border: 1px solid #0e90d2;
      }

      .sel {
        background-color: #0e90d2;
        color: #ffffff;
      }
    }
  }

  .view-body {
    .top {
      display: block;

      .el-row {
        display: flex;
        align-items: stretch;

        .el-col {
          .dlsbtn {
            border-radius: 8px;
            padding: 16px;
            transition: all 0.3s;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            }

            p.item-title{
              font-size: 14px;
              color: #666;
            }

            .item-num{
              font-size: 22px;
              color: #222;
              margin-top: 10px;
              display: block;
            }

            ::v-deep .el-col {
              padding: 0;

              &:last-child {
                display: flex;
                align-items: center;
                justify-content: center;
              }
            }

            p {
              margin: 0;
            }
          }
        }
      }
    }
  }

  .view-body1 {
    margin-top: 28px;
    position: relative;
    min-height: 334px;

    table {
      display: inline-block;
      width: 100%;
      //height: 360px;
      border: 0 solid transparent;

      tr {
        height: 40px;

        th {
          width: 191px;
        }

        th:nth-child(1) {
          width: 50px;
        }

        th:nth-child(3) {
          width: 335px;
        }

        td {
          font-size: 14px;
          text-align: center;
        }
      }

      tr:nth-child(1) {
        background-color: #eff4f7;

        th {
          color: #3c3c3c;
          font-weight: 100;
          font-size: 14px;
        }
      }

      tr:nth-child(2n) {
        background-color: #f9f9f9;
      }
    }

    .pagination-container {
      width: 100%;
      position: absolute;
      bottom: 0px;
      margin: 0px;
      padding: 22px 0;
    }

    .null {
      width: 100%;
      min-height: 334px;
      line-height: 200px;
      text-align: center;
      color: #8c939d;
      font-size: 20px;
      font-weight: bold;
    }
  }

  .cate {
    float: left;
    width: 227px;
    margin-left: 20px;

    span {
      margin-right: 10px;
      font-size: 16px;
    }

    ::v-deep.el-checkbox__label {
      font-size: 16px;
    }
  }
</style>
