import request from '@/utils/request'

// 获取申诉记录列表
export function getAppealListApi(data) {
  return request({
    url: '/otherapi/identityappeal',
    method: 'POST',
    data
  })
}
// 获取修改申诉记录需要参数
export function getAppealEditApi(data) {
  return request({
    url: '/otherapi/getIdentityappealEdit',
    method: 'POST',
    data
  })
}
// 修改申诉记录
export function editAppealApi(data) {
  return request({
    url: '/otherapi/identityappealEdit',
    method: 'POST',
    data
  })
}
// 删除申诉记录
export function delAppealApi(data) {
  return request({
    url: '/otherapi/identityappealDelete',
    method: 'POST',
    data
  })
}
