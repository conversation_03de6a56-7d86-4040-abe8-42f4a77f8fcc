import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/oauth/token',
    method: 'post',
    data
  })
}

export function getInfo() {
  return request({
    url: '/user/info',
    method: 'get'
  })
}

export function logout() {
  return request({
    url: '/user/logout',
    method: 'post'
  })
}

export function getMenus() {
  return request({
    url: '/menus/list',
    method: 'POST'
  })
  /* return Promise.resolve({
    code: 200,
    message: 'ok',
    data: [
      {
        id: 10085,
        path: '/zhy',
        component: 'Layout',
        redirect: '/app/products',
        name: 'zhy',
        meta: {
          title: '资海云',
          icon: 'lock',
          noCache: true
        },
        showInLeft: true,
        hidden: false,
        children: [
          {
            id: 5,
            path: '/app/products',
            component: 'SecondLayout',
            redirect: '/app/list',
            name: 'ZhyProducts',
            meta: {
              title: '产品管理',
              icon: 'app',
              noCache: true
            },
            hidden: false,
            showInLeft: true,
            children: [
              {
                id: 6,
                path: '/app/list',
                component: 'app/list',
                name: 'app-list',
                meta: {
                  title: '产品列表',
                  noCache: true,
                  activeMenu: '/app/products'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 22,
                path: '/orders/list',
                component: 'orders/orders',
                name: 'OrderList',
                meta: {
                  title: '订单列表',
                  noCache: true,
                  activeMenu: '/app/products'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 37,
                path: '/invoice/list',
                component: 'invoice/list',
                name: 'Invoice',
                meta: {
                  title: '发票审核',
                  noCache: true,
                  activeMenu: '/app/products'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10070,
                path: '/withdrawal/list',
                component: 'withdrawal/list',
                name: 'Withdrawal',
                meta: {
                  title: '提现审核',
                  noCache: true,
                  activeMenu: '/app/products'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 8,
                path: '/apps/edit',
                component: 'app/edit',
                name: 'app-edit',
                meta: {
                  title: '编辑产品',
                  noCache: true,
                  activeMenu: '/app/products'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10123,
                path: '/supplement',
                component: 'app/supplement',
                name: 'supplement',
                meta: {
                  title: '配套产品',
                  noCache: true,
                  activeMenu: '/app/products'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 15,
                path: '/packages',
                component: 'app/packages',
                name: 'Packages',
                meta: {
                  title: '套餐管理',
                  noCache: true,
                  activeMenu: '/app/products'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 16,
                path: '/appMiniList',
                component: 'app/appMiniList',
                name: 'appMiniList',
                meta: {
                  title: '小程序管理',
                  noCache: true,
                  activeMenu: '/app/products'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10054,
                path: '/app/modules',
                component: 'app/modules/index',
                name: 'AppModules',
                meta: {
                  title: '模块管理',
                  noCache: true,
                  activeMenu: '/app/products'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 7,
                path: '/apps/add',
                component: 'app/add',
                name: 'app-add',
                meta: {
                  title: '新增产品',
                  noCache: true,
                  activeMenu: '/app/products'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 33,
                path: '/app/hot',
                component: 'app/hot',
                name: 'HotApp',
                meta: {
                  title: '热门产品',
                  noCache: true,
                  activeMenu: '/app/products'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 36,
                path: '/app/category',
                component: 'app/category',
                name: 'AppCategory',
                meta: {
                  title: '产品分类',
                  noCache: true,
                  activeMenu: '/app/products'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 23,
                path: '/orders/detail',
                component: 'orders/order_detail',
                name: 'OrderDetail',
                meta: {
                  title: '订单详情',
                  noCache: true,
                  activeMenu: '/app/products'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 38,
                path: '/invoice/detail',
                component: 'invoice/detail',
                name: 'InvoiceDetail',
                meta: {
                  title: '审核详情',
                  noCache: true,
                  activeMenu: '/app/products'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10071,
                path: '/withdrawal/detail',
                component: 'withdrawal/detail',
                name: 'WithdrawalDetail',
                meta: {
                  title: '提现详情',
                  noCache: true,
                  activeMenu: '/app/products'
                },
                showInLeft: false,
                hidden: true
              }
            ]
          },
          {
            id: 17,
            path: '/news',
            component: 'SecondLayout',
            redirect: '/news/list',
            name: 'news',
            meta: {
              title: '新闻管理',
              icon: 'documentation',
              noCache: true
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 18,
                path: '/news/list',
                component: 'news/list',
                name: 'newslist',
                meta: {
                  title: '新闻列表',
                  noCache: true,
                  activeMenu: '/news'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 19,
                path: '/news/cateList',
                component: 'news/cateList',
                name: 'NewsCateList',
                meta: {
                  title: '新闻分类',
                  noCache: true,
                  activeMenu: '/news'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 20,
                path: '/news/edit',
                component: 'news/edit',
                name: 'news-edit',
                meta: {
                  title: '编辑新闻',
                  noCache: true,
                  activeMenu: '/news'
                },
                showInLeft: false,
                hidden: true
              }
            ]
          },
          {
            id: 29,
            path: '/workorders',
            component: 'SecondLayout',
            redirect: '/workorder/list',
            name: 'WorkOrder',
            meta: {
              title: '工单系统',
              icon: 'workorder',
              noCache: true
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 30,
                path: '/workorder/list',
                component: 'workorder/list',
                name: 'WorkOrders',
                meta: {
                  title: '工单列表',
                  noCache: true,
                  activeMenu: '/workorders'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 31,
                path: '/workorder/cate',
                component: 'workorder/cate',
                name: 'WorkOrderCate',
                meta: {
                  title: '问题分类',
                  noCache: true,
                  activeMenu: '/workorders'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 32,
                path: '/workorder/detail',
                component: 'workorder/detail',
                name: 'WorkOrderDetail',
                meta: {
                  title: '工单详情',
                  noCache: true,
                  activeMenu: '/workorders'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 35,
                path: '/feedback/index',
                component: 'feedback/index',
                name: 'FeedbackIndex',
                meta: {
                  title: '反馈管理',
                  noCache: true,
                  activeMenu: '/workorders'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10202,
                path: '/workorder/appeal',
                component: 'workorder/appeal',
                name: 'workorderAppeal',
                meta: {
                  title: '员工信息留言申诉',
                  noCache: true,
                  activeMenu: '/workorders'
                },
                showInLeft: false,
                hidden: false
              }
            ]
          },
          {
            id: 10072,
            path: '/task',
            component: 'SecondLayout',
            redirect: '/task/taskApproval',
            name: 'task',
            meta: {
              title: '抢单平台',
              icon: 'task',
              noCache: true
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10073,
                path: '/task/taskApproval',
                component: 'task/TaskApproval',
                name: 'taskApproval',
                meta: {
                  title: '工种审批',
                  noCache: true,
                  activeMenu: '/task'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10077,
                path: '/task/taskUser',
                component: 'task/TaskUser',
                name: 'taskUser',
                meta: {
                  title: '抢单平台',
                  noCache: true,
                  activeMenu: '/task'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10130,
                path: '/task/statistics',
                component: 'task/statistics',
                name: 'task-statistics',
                meta: {
                  title: '数据统计',
                  noCache: true,
                  activeMenu: '/task'
                },
                showInLeft: false,
                hidden: false
              }
            ]
          },
          {
            id: 10201,
            path: '/zhyMenu',
            component: 'SecondLayout',
            redirect: '/zhyMenu/list',
            name: 'zhyMenu',
            meta: {
              title: '菜单管理',
              noCache: true,
              icon: 'lock'
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10086,
                path: '/zhyMenu/list',
                component: 'zhy/menu',
                name: 'zhyMenuList',
                meta: {
                  title: '资海云菜单',
                  noCache: true,
                  icon: 'lock',
                  activeMenu: '/zhyMenu'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10088,
                path: '/zhy/e-commerce',
                component: 'zhy/e-commerce',
                name: 'e-commerce',
                meta: {
                  title: '全域电商',
                  noCache: true,
                  activeMenu: '/zhyMenu'
                },
                hidden: false
              },
              {
                id: 10089,
                path: '/zhy/media',
                component: 'zhy/media',
                name: 'zhy-media',
                meta: {
                  title: '媒体投放',
                  noCache: true,
                  activeMenu: '/zhyMenu'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10124,
                path: '/zhy/government',
                component: 'zhy/government',
                name: 'government',
                meta: {
                  title: '政府政策',
                  noCache: true,
                  activeMenu: '/zhyMenu'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10125,
                path: '/zhy/ai',
                component: 'zhy/ai',
                name: 'zhy-ai',
                meta: {
                  title: 'AI工具',
                  noCache: true,
                  activeMenu: '/zhyMenu'
                },
                showInLeft: false,
                hidden: false
              }
            ]
          },
          {
            id: 10058,
            path: '/developer',
            component: 'SecondLayout',
            redirect: '/developer/list',
            name: 'developer',
            meta: {
              title: '开放平台',
              icon: 'permission',
              noCache: true
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10060,
                path: '/developer/list',
                component: 'developer/list',
                name: 'developerList',
                meta: {
                  title: '开发者认证',
                  noCache: true,
                  activeMenu: '/developer'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10059,
                path: '/developer/edit',
                component: 'developer/edit',
                name: 'developerEdit',
                meta: {
                  title: '认证详情',
                  noCache: true,
                  activeMenu: '/developer'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10062,
                path: '/developer-doc/edit',
                component: 'developer-doc/edit',
                name: 'edit',
                meta: {
                  title: '编辑文章',
                  noCache: true,
                  activeMenu: '/developer'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10063,
                path: '/developer-doc/cateList',
                component: 'developer-doc/cateList',
                name: 'category',
                meta: {
                  title: '文章分类',
                  noCache: true,
                  activeMenu: '/developer'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10061,
                path: '/developer-doc/list',
                component: 'developer-doc/list',
                name: 'DevelopmentDocumentation',
                meta: {
                  title: '开发者文档',
                  noCache: true,
                  activeMenu: '/developer'
                },
                showInLeft: false,
                hidden: false
              }
            ]
          },
          {
            id: 10080,
            path: '/product',
            component: 'SecondLayout',
            redirect: '/product/auth',
            name: 'product',
            meta: {
              title: '企业定制生产系统',
              icon: 'lock',
              noCache: true
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10081,
                path: '/product/auth',
                component: 'product/Auth',
                name: 'productAuth',
                meta: {
                  title: '企业定制生产系统',
                  noCache: true,
                  activeMenu: '/product'
                },
                showInLeft: false,
                hidden: false
              }
            ]
          },
          {
            id: 10045,
            path: '/shop',
            component: 'SecondLayout',
            redirect: '/shop/list',
            name: 'shop',
            meta: {
              title: '福利商城',
              icon: 'shop',
              noCache: true
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10046,
                path: '/shop/category',
                component: 'shop/category',
                name: 'shop-category',
                meta: {
                  title: '分类管理',
                  noCache: true,
                  activeMenu: '/shop'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10047,
                path: '/shop/list',
                component: 'shop/list',
                name: 'shop-list',
                meta: {
                  title: '商品列表',
                  noCache: true,
                  activeMenu: '/shop'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10048,
                path: '/shop/orders',
                component: 'shop/orders',
                name: 'shop-orders',
                meta: {
                  title: '订单列表',
                  noCache: true,
                  activeMenu: '/shop'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10049,
                path: '/shop/good/add',
                component: 'shop/add',
                name: 'goods-add',
                meta: {
                  title: '添加商品',
                  noCache: true,
                  activeMenu: '/shop'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10050,
                path: '/shop/good/edit',
                component: 'shop/edit',
                name: 'goods-edit',
                meta: {
                  title: '编辑商品',
                  noCache: true,
                  activeMenu: '/shop'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10051,
                path: '/shop/order/detail',
                component: 'shop/order-detail',
                name: 'order-detail',
                meta: {
                  title: '订单详情',
                  noCache: true,
                  activeMenu: '/shop'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10052,
                path: '/shop/wallet_orders',
                component: 'shop/wallet-orders',
                name: 'pay-orders',
                meta: {
                  title: '充值订单',
                  noCache: true,
                  activeMenu: '/shop'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10053,
                path: '/shop/wallet/config',
                component: 'shop/wallet-config',
                name: 'wallet-config',
                meta: {
                  title: '充值配置',
                  noCache: true,
                  activeMenu: '/shop'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10056,
                path: '/shop/evaluation',
                component: 'shop/evaluation',
                name: 'evaluation',
                meta: {
                  title: '评价管理',
                  noCache: true,
                  activeMenu: '/shop'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10055,
                path: '/shop/advertising',
                component: 'shop/advertising',
                name: 'banner',
                meta: {
                  title: 'banner图',
                  noCache: true,
                  activeMenu: '/shop'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10057,
                path: '/shop/collects',
                component: 'shop/collects',
                name: 'collects',
                meta: {
                  title: '福利征集',
                  noCache: true,
                  activeMenu: '/shop'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10064,
                path: '/shop/integral/giving',
                component: 'integral/giving',
                name: 'IntegralGiving',
                meta: {
                  title: '发放积分',
                  noCache: true,
                  activeMenu: '/shop'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10065,
                path: '/shop/shopshow',
                component: 'shop/shopshow',
                name: 'shop-shopshow',
                meta: {
                  title: '商品销售统计',
                  noCache: true,
                  activeMenu: '/shop'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10044,
                path: '/integral',
                component: 'integral/index',
                name: 'IntegralIndex',
                meta: {
                  title: '任务规则',
                  noCache: true,
                  activeMenu: '/shop'
                },
                showInLeft: false,
                hidden: false
              }
            ]
          },
          {
            id: 10039,
            path: '/version',
            component: 'SecondLayout',
            redirect: '/version/list',
            name: 'Version',
            meta: {
              title: '版本控制',
              icon: 'version',
              noCache: true
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10040,
                path: '/version/list',
                component: 'version/list',
                name: 'VersionManager',
                meta: {
                  title: '版本管理',
                  noCache: true,
                  activeMenu: '/version'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10041,
                path: '/version/add',
                component: 'version/add',
                name: 'add-version',
                meta: {
                  title: '新增版本',
                  noCache: true,
                  activeMenu: '/version'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10042,
                path: '/version/edit',
                component: 'version/edit',
                name: 'edit-version',
                meta: {
                  title: '编辑版本',
                  noCache: true,
                  activeMenu: '/version'
                },
                showInLeft: false,
                hidden: true
              }
            ]
          },
          //    销售线索
          {
            id: 10216,
            path: '/zhy/sales',
            component: 'SecondLayout',
            redirect: '/zhy/sales/clue',
            name: 'zhy-sales',
            meta: {
              title: '销售线索',
              icon: 'lock',
              noCache: true
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10217,
                path: '/zhy/sales/clue',
                component: 'zhy/sales/clue',
                name: 'zhy-sales-clue',
                meta: {
                  title: '销售线索收集',
                  noCache: true,
                  activeMenu: '/zhy/sales'
                },
                showInLeft: false,
                hidden: false
              }
            ]
          }
        ]
      },
      {
        id: 10217,
        path: '/egt',
        component: 'Layout',
        redirect: '/egt/hr',
        name: 'egt',
        meta: {
          title: '易管通',
          icon: 'lock',
          noCache: true
        },
        showInLeft: true,
        hidden: false,
        children: [
          {
            id: 10218,
            path: '/egt/hr',
            component: 'SecondLayout',
            redirect: '/egt/hr/workday',
            name: 'egt-hr',
            meta: {
              title: '人资管理',
              noCache: true,
              icon: 'lock',
              activeMenu: '/egt'
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10219,
                path: '/egt/hr/workday',
                component: 'egt/hr/workday',
                name: 'egt-hr-workday',
                meta: {
                  title: '工作日',
                  noCache: true,
                  activeMenu: '/egt/hr'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10220,
                path: '/egt/hr/recruitment',
                component: 'egt/hr/recruitment',
                name: 'egt-hr-recruitment',
                meta: {
                  title: '招聘管理',
                  noCache: true,
                  activeMenu: '/egt/hr'
                },
                showInLeft: false,
                hidden: false
              },
              //    人才学习数据统计
              {
                id: 10227,
                path: '/egt/hr/study',
                component: 'egt/hr/study',
                name: 'egt-hr-study',
                meta: {
                  title: '人才学习数据统计',
                  noCache: true,
                  activeMenu: '/egt/hr'
                },
                showInLeft: false,
                hidden: false
              },
              // 考勤界面设置
              {
                id: 10228,
                path: '/egt/hr/attendance',
                component: 'egt/hr/attendance',
                name: 'egt-hr-attendance',
                meta: {
                  title: '考勤界面设置',
                  noCache: true,
                  activeMenu: '/egt/hr'
                },
                showInLeft: false,
                hidden: false
              }
            ]
          },
          {
            id: 10221,
            path: '/egt/administrative',
            component: 'SecondLayout',
            redirect: '/egt/administrative/print',
            name: 'egt-administrative',
            meta: {
              title: '行政管理',
              noCache: true,
              icon: 'lock',
              activeMenu: '/egt'
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10222,
                path: '/egt/administrative/print',
                component: 'egt/administrative/print',
                name: 'egt-administrative-print',
                meta: {
                  title: '文件打印',
                  noCache: true,
                  activeMenu: '/egt/administrative'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10223,
                path: '/egt/administrative/printCate',
                component: 'egt/administrative/printCate',
                name: 'egt-administrative-printCate',
                meta: {
                  title: '文件类型',
                  noCache: true,
                  activeMenu: '/egt/administrative'
                },
                showInLeft: false,
                hidden: true
              },
              //    企业服务待办
              {
                id: 10227,
                path: '/egt/administrative/companyService',
                component: 'egt/administrative/companyService',
                name: 'egt-administrative-companyService',
                meta: {
                  title: '企业服务待办',
                  noCache: true,
                  activeMenu: '/egt/administrative'
                },
                showInLeft: false,
                hidden: false
              }
            ]
          },
          {
            id: 10224,
            path: '/egt/legal',
            component: 'SecondLayout',
            redirect: '/egt/legal/contract',
            name: 'egt-legal',
            meta: {
              title: '法务管理',
              noCache: true,
              icon: 'lock',
              activeMenu: '/egt'
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10225,
                path: '/egt/legal/contract',
                component: 'egt/legal/contract',
                name: 'egt-legal-contract',
                meta: {
                  title: '合同范本',
                  noCache: true,
                  activeMenu: '/egt/legal'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10226,
                path: '/egt/legal/contractCate',
                component: 'egt/legal/contractCate',
                name: 'egt-legal-contractCate',
                meta: {
                  title: '合同分类',
                  noCache: true,
                  activeMenu: '/egt/legal'
                },
                showInLeft: false,
                hidden: true
              }
            ]
          }
        ]
      },
      {
        id: 10201,
        path: '/jzt',
        component: 'Layout',
        redirect: '/jzt/site',
        name: 'jzt',
        meta: {
          title: '建站通',
          icon: 'lock',
          noCache: true
        },
        showInLeft: true,
        hidden: false,
        children: [
          {
            id: 10202,
            path: '/jzt/site',
            component: 'SecondLayout',
            redirect: '/jzt/site/list',
            name: 'jzt-site',
            meta: {
              title: '站点管理',
              noCache: true,
              icon: 'lock',
              activeMenu: '/jzt'
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10203,
                path: '/jzt/site/list',
                component: 'jzt/site/list',
                name: 'jzt-site-list',
                meta: {
                  title: '站点列表',
                  noCache: true,
                  activeMenu: '/jzt/site'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10204,
                path: '/jzt/site/material',
                component: 'jzt/site/material',
                name: 'jzt-site-material',
                meta: {
                  title: '素材库操作记录',
                  noCache: true,
                  activeMenu: '/jzt/site'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10205,
                path: '/jzt/site/publish',
                component: 'jzt/site/publish',
                name: 'jzt-site-publish',
                meta: {
                  title: '站点发布记录',
                  noCache: true,
                  activeMenu: '/jzt/site'
                },
                showInLeft: false,
                hidden: false
              }
            ]
          },
          {
            id: 10206,
            path: '/jzt/cases',
            component: 'SecondLayout',
            redirect: '/jzt/cases/base',
            name: 'jzt-cases',
            meta: {
              title: '案例库管理',
              noCache: true,
              icon: 'lock',
              activeMenu: '/jzt'
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10207,
                path: '/jzt/cases/base',
                component: 'jzt/cases/base',
                name: 'jzt-cases-base',
                meta: {
                  title: '案例库管理',
                  noCache: true,
                  icon: 'lock',
                  activeMenu: '/jzt/cases/base'
                },
                showInLeft: true,
                hidden: false
              }
            ]
          },
          {
            id: 10208,
            path: '/jzt/limit-word',
            component: 'SecondLayout',
            redirect: '/jzt/limit-word/index',
            name: 'jzt-limit-word',
            meta: {
              title: '极限词管理',
              noCache: true,
              icon: 'lock',
              activeMenu: '/jzt'
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10209,
                path: '/jzt/limit-word/index',
                component: 'jzt/limit-word/index',
                name: 'jzt-limit-word-index',
                meta: {
                  title: '极限词列表',
                  noCache: true,
                  icon: 'lock',
                  activeMenu: '/jzt/limit-word'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10210,
                path: '/jzt/limit-word/log',
                component: 'jzt/limit-word/log',
                name: 'jzt-limit-word-log',
                meta: {
                  title: '极限词日志',
                  noCache: true,
                  icon: 'lock',
                  activeMenu: '/jzt/limit-word'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10210,
                path: '/jzt/limit-word/site',
                component: 'jzt/limit-word/site',
                name: 'jzt-limit-word-site',
                meta: {
                  title: '站点白名单',
                  noCache: true,
                  icon: 'lock',
                  activeMenu: '/jzt/limit-word'
                },
                showInLeft: false,
                hidden: false
              }
            ]
          },
          {
            id: 10211,
            path: '/jzt/marketing',
            component: 'SecondLayout',
            redirect: '/jzt/marketing/game',
            name: 'jzt-marketing',
            meta: {
              title: '营销管理',
              noCache: true,
              icon: 'lock',
              activeMenu: '/jzt'
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10212,
                path: '/jzt/marketing/game',
                component: 'jzt/marketing/game',
                name: 'jzt-marketing-game',
                meta: {
                  title: '小游戏',
                  noCache: true,
                  icon: 'lock',
                  activeMenu: '/jzt/marketing'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10213,
                path: '/jzt/inspiration/index',
                component: 'jzt/inspiration/index',
                name: 'jzt-inspiration-index',
                meta: {
                  title: '灵感创意',
                  noCache: true,
                  icon: 'lock',
                  activeMenu: '/jzt/marketing'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10214,
                path: '/jzt/inspiration/cate',
                component: 'jzt/inspiration/cate',
                name: 'jzt-inspiration-cate',
                meta: {
                  title: '灵感创意分类',
                  noCache: true,
                  icon: 'lock',
                  activeMenu: '/jzt/marketing'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10215,
                path: '/jzt/plan/index',
                component: 'jzt/plan/index',
                name: 'jzt-plan-index',
                meta: {
                  title: '节日祝福',
                  noCache: true,
                  icon: 'lock',
                  activeMenu: '/jzt/marketing'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10216,
                path: '/jzt/plan/cate',
                component: 'jzt/plan/cate',
                name: 'jzt-plan-cate',
                meta: {
                  title: '节日祝福分类',
                  noCache: true,
                  icon: 'lock',
                  activeMenu: '/jzt/marketing'
                },
                showInLeft: false,
                hidden: true
              }
            ]
          }
        ]
      },
      {
        id: 10055,
        path: '/businessCard',
        component: 'Layout',
        redirect: '/businessCard/user/list',
        name: 'businessCard',
        meta: {
          title: '名片管理',
          icon: 'lock',
          noCache: true
        },
        showInLeft: true,
        hidden: false,
        children: [
          {
            id: 10056,
            path: '/businessCard/user',
            redirect: '/businessCard/user/list',
            component: 'SecondLayout',
            name: 'businessCardList',
            meta: {
              title: '名片列表',
              noCache: true,
              activeMenu: '/businessCard'
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10057,
                path: '/businessCard/user/list',
                component: 'businessCard/user/list',
                name: 'businessCardListList',
                meta: {
                  title: '名片列表',
                  noCache: true,
                  activeMenu: '/businessCard/user'
                },
                showInLeft: false,
                hidden: false
              }
            ]
          },
          {
            id: 10058,
            path: '/businessCard/order',
            redirect: '/businessCard/order/list',
            component: 'SecondLayout',
            name: 'businessCardOrder',
            meta: {
              title: '订单列表',
              noCache: true,
              activeMenu: '/businessCard'
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10059,
                path: '/businessCard/order/list',
                component: 'businessCard/order/list',
                name: 'businessCardOrderList',
                meta: {
                  title: '订单列表',
                  noCache: true,
                  activeMenu: '/businessCard/order'
                },
                showInLeft: false,
                hidden: false
              }
            ]
          },
          {
            id: 10060,
            path: '/businessCard/data',
            redirect: '/businessCard/data/invited',
            component: 'SecondLayout',
            name: 'businessCardData',
            meta: {
              title: '数据管理',
              noCache: true,
              activeMenu: '/businessCard'
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10061,
                path: '/businessCard/data/invited',
                component: 'businessCard/data/invited',
                name: 'businessCardInvited',
                meta: {
                  title: '邀请统计',
                  noCache: true,
                  activeMenu: '/businessCard/data'
                },
                showInLeft: false,
                hidden: false
              }
            ]
          }
        ]
      },
      {
        id: 10062,
        path: '/agent',
        component: 'Layout',
        redirect: '/agent/certification',
        name: 'agent',
        meta: {
          title: '代理商系统',
          icon: 'lock',
          noCache: true
        },
        showInLeft: true,
        hidden: false,
        children: [
          {
            id: 10063,
            path: '/agent/certification',
            redirect: '/agent/certification/list',
            component: 'SecondLayout',
            name: 'agentCertification',
            meta: {
              title: '认证管理',
              noCache: true,
              activeMenu: '/agent'
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10064,
                path: '/agent/certification/list',
                component: 'agent/certification/list',
                name: 'agentCertificationList',
                meta: {
                  title: '认证列表',
                  noCache: true,
                  activeMenu: '/agent/certification'
                },
                showInLeft: false,
                hidden: false
              }
            ]
          },
          {
            id: 10065,
            path: '/agent/agent',
            redirect: '/agent/agent/list',
            component: 'SecondLayout',
            name: 'agentAgent',
            meta: {
              title: '代理管理',
              noCache: true,
              activeMenu: '/agent'
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10066,
                path: '/agent/agent/list',
                component: 'agent/agent/list',
                name: 'agentAgentList',
                meta: {
                  title: '代理商列表',
                  noCache: true,
                  activeMenu: '/agent/agent'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10067,
                path: '/agent/agent/detail',
                component: 'agent/agent/detail',
                name: 'agentAgentDetail',
                meta: {
                  title: '代理商详情',
                  noCache: true,
                  activeMenu: '/agent/agent'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10068,
                path: '/agent/agent/edit',
                component: 'agent/agent/edit',
                name: 'agentAgentEdit',
                meta: {
                  title: '代理商编辑',
                  noCache: true,
                  activeMenu: '/agent/agent'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10068,
                path: '/agent/agent/add',
                component: 'agent/agent/add',
                name: 'agentAgentAdd',
                meta: {
                  title: '添加代理商',
                  noCache: true,
                  activeMenu: '/agent/agent'
                },
                showInLeft: false,
                hidden: true
              },
              // 下属列表
              {
                id: 10068,
                path: '/agent/agent/son',
                component: 'agent/agent/customers',
                name: 'agentAgentSon',
                meta: {
                  title: '代理商客户',
                  noCache: true,
                  activeMenu: '/agent/agent'
                },
                showInLeft: false,
                hidden: true
              }
            ]
          },
          {
            id: 10069,
            path: '/agent/customers',
            redirect: '/agent/customers/list',
            component: 'SecondLayout',
            name: 'agentCustomers',
            meta: {
              title: '用户管理',
              noCache: true,
              activeMenu: '/agent'
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10070,
                path: '/agent/customers/list',
                component: 'agent/customers/list',
                name: 'agentCustomersList',
                meta: {
                  title: '客户列表',
                  noCache: true,
                  activeMenu: '/agent/customers'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10071,
                path: '/agent/customers/auth',
                component: 'agent/customers/auth',
                name: 'agentCustomersAuth',
                meta: {
                  title: '用户认证信息',
                  noCache: true,
                  activeMenu: '/agent/customers'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10072,
                path: '/agent/customers/edit',
                component: 'agent/customers/edit',
                name: 'agentCustomersEdit',
                meta: {
                  title: '编辑客户',
                  noCache: true,
                  activeMenu: '/agent/customers'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10073,
                path: '/agent/customers/detail',
                component: 'agent/customers/detail',
                name: 'agentCustomersDetail',
                meta: {
                  title: '客户详情',
                  noCache: true,
                  activeMenu: '/agent/customers'
                },
                showInLeft: false,
                hidden: true
              },
              //    建站通用户
              {
                id: 10074,
                path: '/agent/customers/jztList',
                component: 'agent/customers/jianZhanTong/list',
                name: 'agentCustomersJztList',
                meta: {
                  title: '建站通用户',
                  noCache: true,
                  activeMenu: '/agent/customers'
                },
                showInLeft: false,
                hidden: false
              },
              //  建站通用户详情
              {
                id: 10074,
                path: '/agent/customers/jztDetail',
                component: 'agent/customers/jianZhanTong/iframe',
                name: 'agentCustomersJztDetail',
                meta: {
                  title: '建站通用户详情',
                  noCache: true,
                  activeMenu: '/agent/customers'
                },
                showInLeft: false,
                hidden: true
              },
              //    转移用户
              {
                id: 10074,
                path: '/agent/customers/transfer',
                component: 'agent/customers/distract',
                name: 'agentCustomersTransfer',
                meta: {
                  title: '转移用户',
                  noCache: true,
                  activeMenu: '/agent/customers'
                },
                showInLeft: false,
                hidden: false
              },
              //    企业管理
              {
                id: 10074,
                path: '/agent/customers/company',
                component: 'agent/customers/adminManager',
                name: 'agentCustomersCompany',
                meta: {
                  title: '企业管理',
                  noCache: true,
                  activeMenu: '/agent/customers'
                },
                showInLeft: false,
                hidden: true
              },
              // 域名模板详情
              {
                id: 10074,
                path: '/agent/customers/domainDetail',
                component: 'agent/customers/showinfo',
                name: 'agentCustomersDomainDetail',
                meta: {
                  title: '域名模板详情',
                  noCache: true,
                  activeMenu: '/agent/customers'
                },
                showInLeft: false,
                hidden: true
              },
              // 域名持有者
              {
                id: 10074,
                path: '/agent/customers/domainHolder',
                component: 'agent/customers/domainTelement',
                name: 'agentCustomersDomainHolder',
                meta: {
                  title: '域名持有者',
                  noCache: true,
                  activeMenu: '/agent/customers'
                },
                showInLeft: false,
                hidden: true
              },
              // 域名持有者认证
              {
                id: 10074,
                path: '/agent/customers/domainHolderAuth',
                component: 'agent/customers/domainTelementAuth',
                name: 'agentCustomersDomainHolderAuth',
                meta: {
                  title: '域名持有者认证',
                  noCache: true,
                  activeMenu: '/agent/customers'
                },
                showInLeft: false,
                hidden: true
              }
            ]
          },
          {
            id: 10074,
            path: '/agent/website',
            redirect: '/agent/website/domain',
            component: 'SecondLayout',
            name: 'agentWebsite',
            meta: {
              title: '网站管理',
              noCache: true,
              activeMenu: '/agent'
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10075,
                path: '/agent/website/domain',
                component: 'agent/website/domain',
                name: 'agentWebsiteDomain',
                meta: {
                  title: '域名列表',
                  noCache: true,
                  activeMenu: '/agent/website'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10076,
                path: '/agent/product/renewal',
                component: 'agent/product/renewal',
                name: 'agentProductRenewal',
                meta: {
                  title: '产品续费',
                  noCache: true,
                  activeMenu: '/agent/website'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10077,
                path: '/agent/website/space',
                component: 'agent/website/space',
                name: 'agentWebsiteSpace',
                meta: {
                  title: '空间列表',
                  noCache: true,
                  activeMenu: '/agent/website'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10078,
                path: '/agent/website/spaceDetail',
                component: 'agent/website/spaceDetail',
                name: 'agentWebsiteSpaceDetail',
                meta: {
                  title: '空间详情',
                  noCache: true,
                  activeMenu: '/agent/website'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10079,
                path: '/agent/product/update',
                component: 'agent/product/update',
                name: 'agentProductUpdate',
                meta: {
                  title: '产品更新',
                  noCache: true,
                  activeMenu: '/agent/website'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10080,
                path: '/agent/yidongyun/list',
                component: 'agent/yidongyun/list',
                name: 'agentYidongyunList',
                meta: {
                  title: '移动云列表',
                  noCache: true,
                  activeMenu: '/agent/website'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10081,
                path: '/agent/yidongyun/iframe',
                component: 'agent/yidongyun/iframe',
                name: 'agentYidongyunManageIframe',
                meta: {
                  title: '移动云管理',
                  noCache: true,
                  activeMenu: '/agent/website'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10082,
                path: '/agent/yidongyun/eosiframe',
                component: 'agent/yidongyun/eosiframe',
                name: 'agentYidongyunEosIframe',
                meta: {
                  title: '移动云管理',
                  noCache: true,
                  activeMenu: '/agent/website'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10083,
                path: '/agent/yidongyun/detail',
                component: 'agent/yidongyun/detail',
                name: 'agentYidongyunDetail',
                meta: {
                  title: '移动云详情',
                  noCache: true,
                  activeMenu: '/agent/website'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10084,
                path: '/agent/yidongyun/icpshow',
                component: 'agent/yidongyun/icpshow',
                name: 'agentYidongyunIcpshow',
                meta: {
                  title: '移动云备案',
                  noCache: true,
                  activeMenu: '/agent/website'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10085,
                path: '/agent/yidongyun/monthlyDetails',
                component: 'agent/yidongyun/estimatedbill',
                name: 'agentYidongyunMonthlyDetails',
                meta: {
                  title: '月度详单',
                  noCache: true,
                  activeMenu: '/agent/website'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10086,
                path: '/agent/yidongyun/monthlyBilling',
                component: 'agent/yidongyun/monthlystatement',
                name: 'agentYidongyunMonthlyBilling',
                meta: {
                  title: '月度账单',
                  noCache: true,
                  activeMenu: '/agent/website'
                },
                showInLeft: false,
                hidden: true
              }
            ]
          },
          {
            id: 10087,
            path: '/agent/finance',
            redirect: '/agent/product/list',
            component: 'SecondLayout',
            name: 'agentFinance',
            meta: {
              title: '财务管理',
              noCache: true,
              activeMenu: '/agent'
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10088,
                path: '/agent/product/list',
                component: 'agent/product/list',
                name: 'agentProductList',
                meta: {
                  title: '开通列表',
                  noCache: true,
                  activeMenu: '/agent/finance'
                },
                showInLeft: false,
                hidden: false
              },
              /!* {
                id: 10089,
                path: "/agent/finance/companyStatements",
                component: "agent/finance/reconciliation",
                name: "agentFinanceCompanyStatements",
                meta: {
                  title: "公司对账单",
                  noCache: true,
                  activeMenu: "/agent/finance",
                },
                showInLeft: false,
                hidden: false,
              }, *!/
              /!* {
                id: 10090,
                path: "/agent/product/platform",
                component: "agent/product/platform",
                name: "agentProductPlatform",
                meta: {
                  title: "平台订单",
                  noCache: true,
                  activeMenu: "/agent/finance",
                },
                showInLeft: false,
                hidden: false,
              }, *!/
              {
                id: 10100,
                path: '/agent/product/experience',
                component: 'agent/product/experience',
                name: 'agentProductExperience',
                meta: {
                  title: '体验产品',
                  noCache: true,
                  activeMenu: '/agent/finance'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10091,
                path: '/agent/product/refundReview',
                component: 'agent/product/drawback_list',
                name: 'agentProductRefundReview',
                meta: {
                  title: '退款审核',
                  noCache: true,
                  activeMenu: '/agent/finance'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10092,
                path: '/agent/product/keyProductRefunds',
                component: 'agent/product/drawback_keylist',
                name: 'agentProductRefund',
                meta: {
                  title: '秘钥产品退款列表',
                  noCache: true,
                  activeMenu: '/agent/finance'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10093,
                path: '/agent/finance/supplement',
                component: 'agent/finance/supplement',
                name: 'agentFinanceSupplement',
                meta: {
                  title: '补开产品',
                  noCache: true,
                  activeMenu: '/agent/finance'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10094,
                path: '/agent/finance/reopen',
                component: 'agent/finance/supplementOpen',
                name: 'agentFinanceReopen',
                meta: {
                  title: '补开产品',
                  noCache: true,
                  activeMenu: '/agent/finance'
                },
                showInLeft: false,
                hidden: true
              },
              /!* {
                id: 10095,
                path: "/agent/product/keyOrderList",
                component: "agent/product/keyorderlist",
                name: "agentProductKeyOrderList",
                meta: {
                  title: "秘钥产品开通列表",
                  noCache: true,
                  activeMenu: "/agent/finance",
                },
                showInLeft: false,
                hidden: false,
              }, *!/
              {
                id: 10096,
                path: '/agent/product/expirationList',
                component: 'agent/product/expire_end',
                name: 'agentProductExpirationList',
                meta: {
                  title: '到期列表',
                  noCache: true,
                  activeMenu: '/agent/finance'
                },
                showInLeft: false,
                hidden: false
              }
            ]
          },
          {
            id: 10097,
            path: '/agent/system/product',
            redirect: '/agent/system/product/defaultPrice',
            component: 'SecondLayout',
            name: 'agentSystemProduct',
            meta: {
              title: '系统产品',
              noCache: true,
              activeMenu: '/agent'
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10098,
                path: '/agent/system/product/defaultPrice',
                component: 'agent/system/product/defaultPrice',
                name: 'agentSystemProductDefaultPrice',
                meta: {
                  title: '代理商默认价目',
                  noCache: true,
                  activeMenu: '/agent/system/product'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10099,
                path: '/agent/system/product/ydproduct',
                component: 'agent/system/product/ydproduct',
                name: 'agentSystemProductYdProduct',
                meta: {
                  title: '移动云产品',
                  noCache: true,
                  activeMenu: '/agent/system/product'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10101,
                path: '/agent/system/product/droop',
                component: 'agent/system/product/product_droop',
                name: 'agentSystemProductDroop',
                meta: {
                  title: '垂类产品',
                  noCache: true,
                  activeMenu: '/agent/system/product'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10102,
                path: '/agent/system/product/year',
                component: 'agent/system/product/yearPrice',
                name: 'agentSystemProductYear',
                meta: {
                  title: '年限价格',
                  noCache: true,
                  activeMenu: '/agent/system/product'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10103,
                path: '/agent/system/product/supplement',
                component: 'agent/system/product/supplement',
                name: 'agentSystemProductSupplement',
                meta: {
                  title: '配套产品',
                  noCache: true,
                  activeMenu: '/agent/system/product'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10104,
                path: '/agent/system/product/contain',
                component: 'agent/system/product/containProducts',
                name: 'agentSystemProductContain',
                meta: {
                  title: '包含产品',
                  noCache: true,
                  activeMenu: '/agent/system/product'
                },
                showInLeft: false,
                hidden: true
              },
              {
                id: 10105,
                path: '/agent/system/product/cate',
                component: 'agent/system/product/productCategory.vue',
                name: 'agentSystemProductCate',
                meta: {
                  title: '产品种类',
                  noCache: true,
                  activeMenu: '/agent/system/product'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10106,
                path: '/agent/system/product/activity',
                component: 'agent/system/product/activityProduct',
                name: 'agentSystemProductActivity',
                meta: {
                  title: '优惠活动',
                  noCache: true,
                  activeMenu: '/agent/system/product'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10107,
                path: '/agent/system/product/activityDetail',
                component: 'agent/system/product/activityDetail',
                name: 'agentSystemProductActivityDetail',
                meta: {
                  title: '活动产品',
                  noCache: true,
                  activeMenu: '/agent/system/product'
                },
                showInLeft: false,
                hidden: true
              }
            ]
          },
          // 下载中心
          {
            id: 10097,
            path: '/agent/system/download',
            redirect: '/agent/system/download/list',
            component: 'SecondLayout',
            name: 'agentSystemDownload',
            meta: {
              title: '下载中心',
              noCache: true,
              activeMenu: '/agent'
            },
            showInLeft: true,
            hidden: false,
            children: [
              {
                id: 10098,
                path: '/agent/system/download/list',
                component: 'agent/system/downloads/downloads',
                name: 'agentSystemDownloadList',
                meta: {
                  title: '下载列表',
                  noCache: true,
                  activeMenu: '/agent/system/download'
                },
                showInLeft: false,
                hidden: false
              }
            ]
          }
        ]
      },
      {
        id: 10108,
        path: '/report',
        redirect: '/report/report',
        component: 'Layout',
        name: 'agentReport',
        meta: {
          title: '运营报表',
          icon: 'lock',
          noCache: true,
          activeMenu: '/agent'
        },
        showInLeft: true,
        hidden: false,
        children: [
          {
            id: 10109,
            path: '/report/report',
            component: 'SecondLayout',
            redirect: '/report/report/task',
            name: 'agentReportReport',
            meta: {
              title: '运营报表',
              noCache: true,
              activeMenu: '/report'
            },
            showInLeft: false,
            hidden: false,
            children: [
              {
                id: 10109,
                path: '/report/report/task',
                component: 'report/task',
                name: 'agentReportTask',
                meta: {
                  title: '抢单平台总数据',
                  noCache: true,
                  activeMenu: '/report'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10110,
                path: '/report/report/user',
                component: 'dashboard/index',
                name: 'agentReportUser',
                meta: {
                  title: '用户总数据',
                  noCache: true,
                  activeMenu: '/report'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10111,
                path: '/report/report/attendance',
                component: 'report/attendance',
                name: 'agentReportAttendance',
                meta: {
                  title: '考勤数据',
                  noCache: true,
                  activeMenu: '/report'
                },
                showInLeft: false,
                hidden: false
              },
              {
                id: 10112,
                path: '/report/report/agent',
                component: 'report/agent',
                name: 'agentReportAgent',
                meta: {
                  title: '代理商总数据',
                  noCache: true,
                  activeMenu: '/report'
                },
                showInLeft: false,
                hidden: false
              },
              //    站点、素材总数据
              {
                id: 10113,
                path: '/report/report/site',
                component: 'report/site',
                name: 'agentReportSite',
                meta: {
                  title: '站点、素材总数据',
                  noCache: true,
                  activeMenu: '/report'
                },
                showInLeft: false,
                hidden: false
              }
            ]
          }
        ]
      },
      {
        'id': 1,
        'path': '/premisson',
        'component': 'Layout',
        'redirect': '/menus',
        'name': 'Pressmion',
        'meta': {
          'title': '权限管理',
          'icon': 'lock',
          'noCache': true
        },
        'showInLeft': true,
        'hidden': false,
        'children': [
          {
            'id': 2,
            'path': '/menus',
            'component': 'menus/index',
            'name': 'Menus',
            'meta': {
              'title': '菜单管理',
              'noCache': true
            },
            'showInLeft': true,
            'hidden': false
          },
          {
            'id': 3,
            'path': '/roles',
            'component': 'permission/role',
            'name': 'Roles',
            'meta': {
              'title': '角色管理',
              'noCache': true
            },
            'showInLeft': true,
            'hidden': false
          },
          {
            'id': 4,
            'path': '/users',
            'component': 'permission/user',
            'name': 'Users',
            'meta': {
              'title': '用户管理',
              'noCache': true
            },
            'showInLeft': true,
            'hidden': false
          }
        ]
      },
      {
        'id': 5,
        'path': '/system',
        'component': 'Layout',
        'redirect': '/system/operatorSManual',
        'name': 'System',
        'meta': {
          'title': '系统设置',
          'icon': 'lock',
          'noCache': true
        },
        'showInLeft': true,
        'hidden': false,
        'children': [
          {
            'id': 6,
            'path': '/system/operatorSManual',
            'component': 'system/operatorSManual',
            'name': 'OperatorSManual',
            'meta': {
              'title': '操作手册',
              'noCache': true
            },
            'showInLeft': true,
            'hidden': false
          },
          {
            'id': 7,
            'path': '/system/industryLibrary',
            'component': 'system/industryLibrary',
            'name': 'IndustryLibrary',
            'meta': {
              'title': '行业库',
              'noCache': true
            },
            'showInLeft': true,
            'hidden': false
          }
        ]
      }
    ]
  }) */
}

export function getUsers(data) {
  return request({
    url: '/user/list',
    method: 'POST',
    data: data
  })
}
export function membersList(data) {
  return request({
    url: '/user/employee',
    method: 'POST',
    data: data
  })
}
export function getCompay(data) {
  return request({
    url: '/user/companylist',
    method: 'POST',
    data: data
  })
}
export function updateUser(data) {
  return request({
    url: '/user/update',
    method: 'POST',
    data: data
  })
}

export function storeUser(data) {
  return request({
    url: '/user/store',
    method: 'POST',
    data: data
  })
}

export function resetPassword(data) {
  return request({
    url: '/user/resetPassword',
    method: 'POST',
    data: data
  })
}

export function changeProfile(data) {
  return request({
    url: '/user/changeProfile',
    method: 'POST',
    data: data
  })
}

export function deleteUser(data) {
  return request({
    url: '/user/delete',
    method: 'POST',
    data: data
  })
}
