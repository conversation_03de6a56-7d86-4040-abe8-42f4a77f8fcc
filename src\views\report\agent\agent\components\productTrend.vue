<template>
  <SelfCard title="用户操作趋势图" class="box-card">
    <template v-slot:right>
      <el-radio-group v-model="indexOfType" style="margin-left: 10px" @change="selectType">
        <el-radio-button v-for="charge in radios" :key="charge.value" :label="charge.value">
          {{ charge.label }}
        </el-radio-button>
      </el-radio-group>
    </template>
    <div class="cate">
      <el-radio-group v-model="indexOfDate" @change="changeDay">
        <el-radio :label="0">前一日</el-radio>
        <el-radio :label="1">上周同期</el-radio>
      </el-radio-group>
    </div>
    <div>
      <div id="openLine" style="width: 100%;height: 310px;" />
    </div>
  </SelfCard>
</template>

<script>
import { mapGetters } from 'vuex'
import * as echarts from 'echarts'
import moment from 'moment'
import SelfCard from '@/views/dashboard/admin/components/SelfCard.vue'
export default {
  name: 'ProductTrend',
  components: {
    SelfCard
  },
  props: {
    allData: {
      type: Object,
      default() {
        return {}
      }
    },
    loading: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  data() {
    return {
      indexOfDate: 0,
      indexOfType: 0,
      indexOfCate: 0,
      mapLoading: false,
      openLine: null,
      dataSource: [],
      legend: ['今日', '前一日'],
      trend_keys: [],
      trend_data1: [],
      trend_data2: [],
      radios: [
        {
          label: '浏览量(PV)',
          value: 0
        },
        {
          label: '访客数(UV)',
          value: 1
        },
        {
          label: 'ip访问量',
          value: 2
        }
      ]
    }
  },
  watch: {
    allData(value) {
      this.allData = value

      this.legend = ['今日', this.indexOfDate === 0 ? '前一日' : '上周同期']
      // 今日
      this.trend_data1 = this.allData.map_today
      this.trend_data2 = this.allData.map_last_day// 前一日
      this.setOptions(this.trend_data1, this.trend_data2)
    }
  },
  computed: {
    ...mapGetters([
      'type'
    ])
  },
  created() {
    for (var i = 0; i < 24; i++) {
      this.trend_keys.push(i)
    }
  },
  mounted() {
    this.init()

    this.getActive()
  },
  methods: {
    selectType(index) {
      this.indexOfType = index
      this.getActive()
    },
    selectCate(index) {
      this.indexOfCate = index
      this.getActive()
    },
    changeDay() {
      this.getActive()
    },
    getActive() {
      var date = new Date()
      if (this.indexOfDate === 0) {
        date.setTime(date.getTime() - 24 * 60 * 60 * 1000)
        date = moment(date).format('YYYY-MM-DD')
      } else {
        date.setTime(date.getTime() - 7 * 24 * 60 * 60 * 1000)
        date = moment(date).format('YYYY-MM-DD')
      }

      var visit = ['pv', 'uv', 'ip'][this.indexOfType]

      var client = ['cloud', 'agent'][this.indexOfCate]

      // 产品活跃度排行
      this.$emit('getActive', date, visit, client)
    },
    async init() {
      this.initopenLine()
    },
    initopenLine() {
      this.mapLoading = true
      this.openLine = echarts.init(document.getElementById('openLine'))
      this.setOptions()
    },
    setOptions() {
      this.openLine.setOption({
        tooltip: {
          trigger: 'axis',
          showContent: true
        },
        legend: {
          x: 'center',
          y: 'bottom',
          data: this.legend
        },
        dataset: {
          source: [
            this.trend_keys,
            this.trend_data1,
            this.trend_data2
          ]
        },
        xAxis: { type: 'category' },
        yAxis: { gridIndex: 0 },
        grid: { top: '5%' },
        series: [
          {
            type: 'line', smooth: true, seriesLayoutBy: 'row',
            name: this.legend[0],
            itemStyle: {
              normal: {
                color: '#4d80ff', // 折线点的颜色
                lineStyle: {
                  color: '#4d80ff'// 折线的颜色
                }
              }
            },
            areaStyle: { // 区域颜色
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: '#4d80ff'
              }, {
                offset: 1,
                color: '#f0f5ff'
              }])
            }
          },
          {
            type: 'line', smooth: true, seriesLayoutBy: 'row',
            name: this.legend[1],
            itemStyle: {
              normal: {
                color: '#a66ef6', // 折线点的颜色
                lineStyle: {
                  color: '#a66ef6'// 折线的颜色
                }
              }
            },
            areaStyle: { // 区域颜色
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: '#a66ef6'
              }, {
                offset: 1,
                color: '#f9f0ff'
              }])
            }
          }
        ]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .box-card {
    height: 100%;

    .cate {
      padding-left: 20px;
      box-sizing: border-box;

      span {
        margin-right: 10px;
        font-size: 16px;
      }

      ::v-deep.el-checkbox__label {
        font-size: 16px;
      }
    }

    #openLine {
      margin-top: 20px;
    }
  }
</style>
