<script>
import { mapGetters, mapMutations } from 'vuex'

export default {
  name: 'SwitchPackageType',
  computed: {
    ...mapGetters(['jztPackageType', 'jztPackageTypeList'])
  },
  methods: {
    ...mapMutations({
      'setJztPackageType': 'jzt/SET_PACKAGE_TYPE'
    }),
    // 切换套餐
    async switchPackage() {
      if (this.jztPackageType === '1.1') {
        this.setJztPackageType('2.0')
      } else {
        this.setJztPackageType('1.1')
      }
      this.$emit('switchPackageCallback')
    }
  }
}
</script>

<template>
  <el-form-item v-if="jztPackageTypeList.length" label="当前站点：" style="float: right">
    <span style="margin-right: 10px;color: #4d80ff;">建站通{{ jztPackageType }}</span>
    <el-button type="primary" icon="el-icon-refresh" @click="switchPackage()">切换</el-button>
  </el-form-item>
</template>

<style scoped lang="scss">

</style>
