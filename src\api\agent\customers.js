import request from '@/utils/request'

export function index(data) {
  return request({
    url: '/customers/list',
    method: 'post',
    data: data
  })
}

export function store(data) {
  return request({
    url: '/customers/store',
    method: 'post',
    data: data
  })
}

export function auth(data) {
  return request({
    url: '/customers/auth',
    method: 'post',
    data: data
  })
}

export function update(data) {
  return request({
    url: '/customers/update',
    method: 'post',
    data: data
  })
}

export function destroy(data) {
  return request({
    url: '/customers/delete',
    method: 'post',
    data: data
  })
}

export function detail(data) {
  return request({
    url: '/customers/detail',
    method: 'post',
    data: data
  })
}

export function audit(data) {
  return request({
    url: '/customers/audit',
    method: 'post',
    data: data
  })
}

export function companyIndex(data) {
  return request({
    url: '/customers/companyIndex',
    method: 'post',
    data: data
  })
}
export function catchCustomer(data) {
  return request({
    url: '/customers/catchCustomer',
    method: 'post',
    data: data
  })
}

export function distract(data) {
  return request({
    url: '/customers/distract',
    method: 'post',
    data: data
  })
}

export function distractList(data) {
  return request({
    url: '/customers/distract/list',
    method: 'post',
    data: data
  })
}

export function distractAudit(data) {
  return request({
    url: '/customers/distract/audit',
    method: 'post',
    data: data
  })
}

export function distractCancel(data) {
  return request({
    url: '/customers/distract/cancel',
    method: 'post',
    data: data
  })
}

export function agentNameList(data) {
  return request({
    url: '/agent/nameList',
    method: 'post',
    data: data
  })
}

export function options(data) {
  return request({
    url: '/option',
    method: 'post',
    data: data
  })
}

export function agentCustomerList(data) {
  return request({
    url: '/customers/agentCustomer/list',
    method: 'post',
    data: data
  })
}

export function agentCustomerUpdate(data) {
  return request({
    url: '/customers/agentCustomer/update',
    method: 'post',
    data: data
  })
}

export function productList(data) {
  return request({
    url: '/customers/productList',
    method: 'post',
    data: data
  })
}

export function orderList(data) {
  return request({
    url: '/customers/orderList',
    method: 'post',
    data: data
  })
}
export function manager(data) {
  return request({
    url: '/customers/manager_list',
    method: 'post',
    data: data
  })
}

export function usermanager(data) {
  return request({
    url: '/customers/usermanager',
    method: 'post',
    data: data
  })
}

export function manager_change(data) {
  return request({
    url: '/customers/manager_change',
    method: 'post',
    data: data
  })
}

export function manager_EditPwd(data) {
  return request({
    url: '/customers/changeManagerEdit',
    method: 'post',
    data: data
  })
}

export function manager_EditPhone(data) {
  return request({
    url: '/customers/changePhoneEdit',
    method: 'post',
    data: data
  })
}

export function updateUserType(data) {
  return request({
    url: '/customers/updateUserType',
    method: 'post',
    data: data
  })
}

export function huaweiImg(data) {
  return request({
    url: '/customers/huaweiidentify',
    method: 'post',
    data: data
  })
}

export function siteshow(data) {
  return request({
    url: '/customers/siteshow',
    method: 'post',
    data: data
  })
}
