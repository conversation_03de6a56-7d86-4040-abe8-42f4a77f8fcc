<script>
import {
  addHoliday<PERSON><PERSON>,
  addLegal<PERSON>orkday<PERSON><PERSON>,
  addWorkday<PERSON>pi, editHolidayApi, editLegalWorkdayApi,
  editWorkdayApi,
  getAddWorkdayApi, getEditHolidayApi, getEditLegalWorkdayApi,
  getEditWorkdayApi,
  getWorkdayByYearApi
} from '@/api/egt/workday'

export default {
  name: 'Update',
  props: {
    id: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    showYear: {
      type: Boolean,
      default: false
    },
    label: {
      type: String,
      default: '工作日'
    },
    activeData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      formData: {
        id: '',
        year: '',
        dates: []
      },
      canSelectYears: [],
      loading: false,
      saveLoading: false,
      rules: {
        year: [
          { required: true, message: '请选择年度', trigger: 'blur' }
        ],
        dates: [
          { required: true, message: '请选择日期', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    title() {
      return (this.data.id ? '编辑' : '新增') + this.activeData.label
    },
    defaultValue() {
      return this.formData.year ? new Date(this.formData.year) : null
    },
    pickerOptions: {
      get() {
        const that = this
        return {
          disabledDate(time) {
            if (that.canSelectYears.length === 0) return false
            return new Date(that.canSelectYears[0]).getTime() >= time.getTime() || new Date(that.canSelectYears[that.canSelectYears.length - 1]).getTime() <= time.getTime()
          }
        }
      },
      set(val) {
        return val
      }
    },
    getParamsMap() {
      return {
        '1': {
          'add': {
            getParams: {
              api: getAddWorkdayApi,
              callback: this.setAddData1
            },
            save: {
              api: addWorkdayApi,
              fields: [{ 'year': 'year' }, { 'paichu': 'dates' }],
              callback: this.saveData1
            }
          },
          'update': {
            getParams: {
              api: getEditWorkdayApi,
              callback: this.setUpdateData1
            },
            save: {
              api: editWorkdayApi,
              fields: [{ 'year': 'year' }, { 'legal': 'dates' }],
              callback: this.updateData1
            }
          }
        },
        '2': {
          'add': {
            getParams: {
              // api: getAddWorkdayApi
            },
            save: {
              api: addLegalWorkdayApi,
              fields: [{ 'paichu': 'dates' }],
              callback: this.saveData1
            }
          },
          'update': {
            getParams: {
              api: getEditLegalWorkdayApi,
              callback: this.setUpdateData2
            },
            save: {
              api: editLegalWorkdayApi,
              fields: [{ 'year': 'year' }, { 'legal': 'dates' }],
              callback: this.updateData1
            }
          }
        },
        '3': {
          'add': {
            getParams: {
              // api: getAddWorkdayApi
            },
            save: {
              api: addHolidayApi,
              fields: [{ 'holiday': 'dates' }],
              callback: this.saveData1
            }
          },
          'update': {
            getParams: {
              api: getEditHolidayApi,
              callback: this.setUpdateData1
            },
            save: {
              api: editHolidayApi,
              fields: [{ 'year': 'year' }, { 'holiday': 'dates' }],
              callback: this.updateData1
            }
          }
        }
      }
    }
  },
  watch: {
    _visible(val) {
      if (val) {
        this.handleGetParams()
      } else {
        this.formData = {
          id: '',
          year: '',
          dates: []
        }
      }
    }
  },
  methods: {
    handleCancel() {
      this._visible = false
      //   清空表单
      this.$refs.formRef.resetFields()
    },
    handleSave() {
      this.$refs.formRef.validate(async valid => {
        if (valid) {
          const { success, msg, message } = await this.saveData()
          if (!success) {
            this.$message({
              type: 'error',
              message: msg || message || '保存失败'
            })
            return
          }
          this.$message({
            type: 'success',
            message: '保存成功'
          })
          this.handleCancel()
          this.$emit('refresh', this.formData)
        }
      })
    },
    async saveData() {
      try {
        if (this.getParamsMap[this.id]) {
          this.saveLoading = true
          const type = this.data.id ? 'update' : 'add'
          const dates = this.formData.dates.join(',')
          const data = {
            id: this.formData.id,
            year: this.formData.year,
            dates
          }
          const postData = {}
          this.getParamsMap[this.id][type]['save']['fields'].forEach(item => {
            postData[Object.keys(item)[0]] = data[Object.values(item)[0]]
          })
          // return
          const res = await this.getParamsMap[this.id][type]['save']['api'](postData)
          if (this.getParamsMap[this.id][type]['save']['callback'](res)) {
            const { success, msg } = this.getParamsMap[this.id][type]['save']['callback'](res)
            return {
              success,
              msg
            }
          } else {
            return {
              success: false,
              msg: '操作失败'
            }
          }
        }
      } catch (err) {
        console.log(err)
        return {
          success: false,
          msg: '操作失败'
        }
      } finally {
        this.saveLoading = false
      }
    },
    saveData1(res) {
      if (res.code === 200) {
        return {
          success: true,
          msg: '保存成功'
        }
      } else {
        return {
          success: false,
          msg: res.msg || res.message || '保存失败'
        }
      }
    },
    updateData1(res) {
      if (res.code === 200) {
        return {
          success: true,
          msg: '修改成功'
        }
      } else {
        return {
          success: false,
          msg: res.msg || '修改失败'
        }
      }
    },
    async handleChangeYear(val) {
      if (this.data.id) {
        try {
          this.loading = true
          const res = await getWorkdayByYearApi({ year: val })
          if (res.code === 200 && res.data && res.data.data) {
            this.formData.dates = res.data.data
          }
        } catch (err) {
          console.log(err)
        } finally {
          this.loading = false
        }
      }
    },
    async handleGetParams() {
      try {
        if (this.getParamsMap[this.id]) {
          this.loading = true
          const type = this.data.id ? 'update' : 'add'
          if (this.getParamsMap[this.id][type]['getParams']['api']) {
            const res = await this.getParamsMap[this.id][type]['getParams']['api']({ year: this.data.year })
            if (this.getParamsMap[this.id][type]['getParams']['callback']) {
              this.getParamsMap[this.id][type]['getParams']['callback'](res)
            } else {
              this.formData = this.data
            }
          }
        }
      } catch (err) {
        console.log(err)
      } finally {
        this.loading = false
      }
    },
    setAddData1(res) {
      if (res.code === 200 && res.data) {
        this.formData.year = res.data.ckYear
        this.canSelectYears = res.data.year
        this.formData.dates = []
      }
    },
    setUpdateData1(res) {
      this.formData = { ...this.data }
      if (res.code === 200 && res.data) {
        this.canSelectYears = res.data.year
        this.formData = JSON.parse(JSON.stringify(this.data))
      }
    },
    setUpdateData2(res) {
      this.formData = { ...this.data }
      if (res.code === 200 && res.data) {
        this.canSelectYears = res.data.year
        this.formData = JSON.parse(JSON.stringify(this.data))
      }
    }
  }
}
</script>

<template>
  <el-dialog :close-on-click-modal="false" :visible.sync="_visible" :title="title">
    <el-alert
      v-if="activeData.tips"
      :title="activeData.tips"
      type="warning"
      style="margin-bottom: 20px;"
    />
    <el-form v-if="_visible" ref="formRef" :model="formData" :rules="rules" label-width="140px">
      <el-form-item v-if="showYear" label="年">
        <el-date-picker
          v-model="formData.year"
          type="year"
          placeholder="选择年"
          format="yyyy"
          value-format="yyyy"
          :picker-options="pickerOptions"
          @change="handleChangeYear"
        />
      </el-form-item>
      <el-form-item v-loading="loading" :label="formData.year + '年' + label">
        <el-date-picker
          v-model="formData.dates"
          type="dates"
          placeholder="选择一个或多个日期"
          style="width: 100%"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          :default-value="defaultValue"
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button :loading="saveLoading" @click="handleCancel()">取消</el-button>
      <el-button :loading="saveLoading" type="primary" @click="handleSave()">保存</el-button>
    </span>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
