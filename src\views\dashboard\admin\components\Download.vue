<template>
  <div id="download">
    <el-card shadow="never" class="border-transparent">
      <div class="el-row--flex is-justify-space-between is-align-middle">
        <span class="title">各手机市场累计下载量</span>
      </div>
      <div id="download">
        <bar-chart :show-legend="false" :grid="grid" height="400px" :chart-data="chartData" :legend="legend" :color-list="colorList" :data-type="'normal'" :show-zoom="showZoom" />
      </div>
    </el-card>
  </div>
</template>

<script>
/* eslint-disable */
import BarChart from './BarChart.vue';
import {getDownloadData} from "@/api/dashboard";

export default {
  name: "download",
  components: { BarChart },
  props: ['date'],
  watch: {
    date: {
      handler(v){
        this.handleGetDownload(v);
      },
      immediate: true
    }
  },
  data() {
    return {
      chartData: {
        x: [],
        y: []
      },
      legend: ["下载量"],
      colorList: ["#fb726c"],
      total: 0,
      avarage: 0,
      grid: {
        left: 30,
        right: 30,
        bottom: 20,
        top: 30,
        containLabel: true
      },
      showZoom: false
    };
  },
  methods: {
    handleGetDownload(date = this.date){
      if(!date){
        date = new Date().getFullYear().toString() + '-' + (new Date().getMonth() + 1).toString().padStart(2, '0');
      }
      this.chartData = {
        x: [],
        y: []
      }
      getDownloadData({date: this.date}).then(res => {
        if (res.code === 200 && res.data){
          this.chartData.x = res.data.list;
          this.chartData.y = res.data.count;
          if (this.chartData.x.length > 6){
            this.showZoom = true;
          }
        }
      })
    }
  },
  mounted() {
  }
};
</script>

<style lang="scss" scoped>
.bottom-0{
  margin-bottom: 0;
}
.float-right{
  float: right;
  margin-top: 10px;
  ::v-deep.el-form-item--medium .el-form-item__content{
    display: inline-block;
  }
}
.border-transparent{
  border: none;
}
.title{
  font-size: 20px;
  color: #000;
  margin: 0;
  font-weight: bold;
}
</style>
