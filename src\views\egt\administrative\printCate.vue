<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import PrintCateUpdate from './components/print/PrintCateUpdate.vue'
import { deletePrintCateApi, getPrintTypeList } from '@/api/egt/print'

export default {
  name: 'PrintCate',
  components: {
    StatisticsTemplate,
    PrintCateUpdate
  },
  data() {
    return {
      config: {
        key: 'printCate',
        tableSettings: {
          api: getPrintTypeList,
          field: {
            page: 'pageIndex',
            limit: 'pageSize',
            total: 'total'
          },
          columns: [
            {
              label: 'ID',
              prop: 'id',
              width: 60
            },
            {
              prop: 'title',
              label: '分类名称'
            },
            {
              prop: 'action',
              lable: '操作',
              align: 'center',
              isSlot: true,
              width: '200px',
              fixed: 'right'
            }
          ]
        }
      },
      updateVisible: false,
      row: {}
    }
  },
  methods: {
    handleAdd() {
      this.updateVisible = true
      this.row = {}
    },
    handleUpdate(row) {
      this.updateVisible = true
      this.row = row
    },
    async handleDelete(row) {
      if (!row.id) return
      try {
        this.$refs.listWrapRef.setLoading(true)
        const res = await deletePrintCateApi({ id: row.id })
        if (res.code === 200) {
          this.$message.success('删除成功')
          this.$refs.listWrapRef.handleGetData()
        } else {
          this.$message.error(res.msg || res.message || '删除失败')
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.$refs.listWrapRef.setLoading(false)
      }
    },
    handleSubmitSuccess() {
      this.$refs.listWrapRef.handleGetData()
    }
  }
}
</script>

<template>
  <div class="page-wrap">
    <StatisticsTemplate ref="listWrapRef" :config="config" self-key="printCate">
      <template #topActions>
        <div style="display: flex; justify-content: space-between;">
          <!-- 返回 -->
          <el-button icon="el-icon-arrow-left" type="text" @click="$router.back()">返回</el-button>
          <el-button type="primary" @click="handleAdd">添加</el-button>
        </div>
      </template>
      <template #printCate_action="{row}">
        <el-button size="mini" @click="handleUpdate(row)">编辑</el-button>
        <el-popconfirm
          title="确定删除吗？"
          @onConfirm="handleDelete(row)"
        >
          <el-button slot="reference" type="danger" size="mini">删除</el-button>
        </el-popconfirm>
      </template>
    </StatisticsTemplate>
    <PrintCateUpdate
      :visible.sync="updateVisible"
      :data="row"
      @submit-success="handleSubmitSuccess"
    />
  </div>
</template>

<style scoped lang="scss">
  .page-wrap {
    height: calc(100% - 20px);
 }
</style>
