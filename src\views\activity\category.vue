<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>活动分类</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="createView">添加分类</el-button>
      </div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="65"
        />
        <el-table-column
          prop="name"
          label="分类名称"
        />
        <el-table-column label="状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status" type="success" effect="dark" size="small">启用</el-tag>
            <el-tag v-else type="danger" effect="dark" size="small">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-link :underline="false" type="primary" @click="editDialog(scope.row)">编辑</el-link>
            <el-popconfirm
              title="确定删除当前记录吗？"
              @onConfirm="deleteHandle(scope.row)"
            >
              <el-link slot="reference" :underline="false" type="danger">删除</el-link>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!--  表单  -->
    <el-dialog
      title="活动分类管理"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="分类名称：" prop="name">
          <el-col :span="20">
            <el-input v-model="ruleForm.name" />
          </el-col>
        </el-form-item>
        <el-form-item label="分类排序：" prop="level">
          <el-col :span="10">
            <el-input-number v-model="ruleForm.level" :min="0" :max="9999999" :step="1" />
          </el-col>
        </el-form-item>
        <el-form-item label="状态开关：" prop="status">
          <el-col :span="20">
            <el-switch
              v-model="ruleForm.status"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
            />
          </el-col>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="saveHandle">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { category_list, category_store, category_delete } from '../../api/activity_category'
export default {
  name: 'Category',
  data() {
    return {
      tableLoading: false,
      loading: false,
      dialogVisible: false,
      tableData: [],
      ruleForm: {},
      rules: {
        name: [
          { required: true, message: '分类名称必填', trigger: 'blur' }
        ],
        level: [
          { required: true, message: '分类顺序必填', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getCategoryList()
  },
  methods: {
    getCategoryList() {
      this.tableLoading = true
      category_list().then(response => {
        this.tableData = response.data
        this.tableLoading = false
      })
    },
    createView() {
      this.ruleForm = {
        status: 1,
        level: 0
      }
      this.dialogVisible = true
    },
    editDialog(row) {
      this.ruleForm = row
      this.dialogVisible = true
    },
    saveHandle() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.loading = true
          category_store(this.ruleForm).then(response => {
            this.dialogVisible = false
            this.loading = false
            this.getCategoryList()
          }).catch(() => {
            this.loading = false
          })
        }
      })
    },
    deleteHandle(row) {
      category_delete(row).then(response => {
        this.$message.success('删除成功')
        this.getCategoryList()
      })
    }
  }
}
</script>

<style scoped>

</style>
