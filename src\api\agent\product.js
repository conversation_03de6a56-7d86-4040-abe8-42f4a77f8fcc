import request from '@/utils/request'

export function products(data) {
  return request({
    url: '/product/list',
    method: 'post',
    data: data
  })
}
export function productspx(data) {
  return request({
    url: '/dashboard/indexpx',
    method: 'post',
    data: data
  })
}

export function category(data) {
  return request({
    url: '/product/category',
    method: 'post',
    data: data
  })
}

export function store(data) {
  return request({
    url: '/product/store',
    method: 'post',
    data: data
  })
}
export function update(data) {
  return request({
    url: '/product/update',
    method: 'post',
    data: data
  })
}
export function destroy(data) {
  return request({
    url: '/product/delete',
    method: 'post',
    data: data
  })
}
export function getHistory(data) {
  return request({
    url: '/product/history',
    method: 'post',
    data: data
  })
}
export function agent(data) {
  return request({
    url: '/product/agent',
    method: 'post',
    data: data
  })
}
export function setAgentPrice(data) {
  return request({
    url: '/product/setAgentPrice',
    method: 'post',
    data: data
  })
}
export function getAgentPriceApply(data) {
  return request({
    url: '/product/getAgentPriceApply',
    method: 'post',
    data: data
  })
}
export function updateAgentPriceApply(data) {
  return request({
    url: '/product/updateAgentPriceApply',
    method: 'post',
    data: data
  })
}

export function agentPrice(data) {
  return request({
    url: '/product/agentPrice',
    method: 'post',
    data: data
  })
}

export function openProduct(data) {
  return request({
    url: '/product/open',
    method: 'post',
    data: data
  })
}
export function openList(data) {
  return request({
    url: '/product/openList',
    method: 'post',
    data: data
  })
}

export function openListDestory(data) {
  return request({
    url: '/product/openList/destory',
    method: 'post',
    data: data
  })
}

export function beforeOpenList(data) {
  return request({
    url: '/product/beforeOpenList',
    method: 'post',
    data: data
  })
}
export function beforeDomainList(data) {
  return request({
    url: '/product/beforeDomainList',
    method: 'post',
    data: data
  })
}
export function userPrice(data) {
  return request({
    url: '/product/userPrice',
    method: 'post',
    data: data
  })
}
export function updateUserPrice(data) {
  return request({
    url: '/product/updateUserPrice',
    method: 'post',
    data: data
  })
}

export function drawback_list(data) {
  return request({
    url: '/product/drawback/list',
    method: 'post',
    data: data
  })
}

export function drawback_create(data) {
  return request({
    url: '/product/drawback/create',
    method: 'post',
    data: data
  })
}

export function drawback_cancel(data) {
  return request({
    url: '/product/drawback/cancel',
    method: 'post',
    data: data
  })
}

export function drawback_destory(data) {
  return request({
    url: '/product/drawback/destory',
    method: 'post',
    data: data
  })
}

export function auth_server(data) {
  return request({
    url: '/product/drawback/auth_server',
    method: 'post',
    data: data
  })
}

export function auth_finance(data) {
  return request({
    url: '/product/drawback/auth_finance',
    method: 'post',
    data: data
  })
}

export function getYunList(data) {
  return request({
    url: '/product/yun/List',
    method: 'post',
    data: data
  })
}

export function getYunHostList(data) {
  return request({
    url: '/product/yun/host',
    method: 'post',
    data: data
  })
}

export function supplement(data) {
  return request({
    url: '/product/supplement',
    method: 'post',
    data: data
  })
}

export function supplementUpdate(data) {
  return request({
    url: '/product/supplement/update',
    method: 'post',
    data: data
  })
}

export function yearPrice(data) {
  return request({
    url: '/product/yearPrice',
    method: 'post',
    data: data
  })
}

export function yearPriceUpdate(data) {
  return request({
    url: '/product/yearPrice/update',
    method: 'post',
    data: data
  })
}

export function openExperience(data) {
  return request({
    url: '/product/open/experience',
    method: 'post',
    data: data
  })
}

export function yunPlatform(data) {
  return request({
    url: '/product/yun/order',
    method: 'post',
    data: data
  })
}

export function expireEndIndex(data) {
  return request({
    url: '/product/expireEndIndex',
    method: 'post',
    data: data
  })
}

// 获取 升级产品 可用产品列表
export function upgradeProductList(data) {
  return request({
    url: '/product/upgradeProductList',
    method: 'post',
    data: data
  })
}

// 获取 包含产品 列表
export function containProductList(data) {
  return request({
    url: '/product/containProduct',
    method: 'post',
    data: data
  })
}

// 更新 包含产品 列表
export function containProductUpdate(data) {
  return request({
    url: '/product/containProduct/update',
    method: 'post',
    data: data
  })
}

// 升级产品到另一个产品
export function productUpToOtherProduct(data) {
  return request({
    url: '/product/open/productUpToOtherProduct',
    method: 'post',
    data: data
  })
}
// 升级产品到另一个产品
export function jzthoustlists(data) {
  return request({
    url: '/product/jzthoustlists',
    method: 'post',
    data: data
  })
}
// 产品详情
export function show(data) {
  return request({
    url: '/product/show',
    method: 'post',
    data: data
  })
}

export function beforeJztipList(data) {
  return request({
    url: '/product/beforeJztipList',
    method: 'post',
    data: data
  })
}

export function typePricelist(data) {
  return request({
    url: '/product/typePricelist',
    method: 'post',
    data: data
  })
}

export function financedomain(data) {
  return request({
    url: '/product/financedomain',
    method: 'post',
    data: data
  })
}

export function keyopenList(data) {
  return request({
    url: '/product/keyopenList',
    method: 'post',
    data: data
  })
}
// 密钥产品订单申请退款
export function drawback_keycreate(data) {
  return request({
    url: '/product/drawback/keycreate',
    method: 'post',
    data: data
  })
}
// 密钥产品订单取消退款
export function drawback_keycancel(data) {
  return request({
    url: '/product/drawback/keycancel',
    method: 'post',
    data: data
  })
}
// 密钥产品退款订单
export function drawback_keylist(data) {
  return request({
    url: '/product/drawback/keylist',
    method: 'post',
    data: data
  })
}
// 经销商平台发起资源池可用区查询
export function yidongyun_poolinfo(data) {
  return request({
    url: '/yidongyun/poolinfo',
    method: 'post',
    data: data
  })
}
// 经销商平台发起资源池智能推荐查询
export function yidongyun_defaultpoolinfo(data) {
  return request({
    url: '/yidongyun/defaultpoolinfo',
    method: 'post',
    data: data
  })
}
// 销商平台发起创建经销商终端客户
export function yidongyun_customercreate(data) {
  return request({
    url: '/yidongyun/customercreate',
    method: 'post',
    data: data
  })
}
// 销商平台发起创建经销商终端客户
export function yidongyun_qryOffer(data) {
  return request({
    url: '/yidongyun/qryOffer',
    method: 'post',
    data: data
  })
}
// 销商平台发起创建经销商终端客户
export function yidongyun_userOperate(data) {
  return request({
    url: '/yidongyun/userOperate',
    method: 'post',
    data: data
  })
}
// 销商平台发起创建经销商终端客户
export function yidongyun_vpclist(data) {
  return request({
    url: '/yidongyun/vpclist',
    method: 'post',
    data: data
  })
}
// 销商平台发起创建经销商终端客户
export function yidongyun_vpcCreate(data) {
  return request({
    url: '/yidongyun/vpcCreate',
    method: 'post',
    data: data
  })
}
// 销商平台发起创建经销商终端客户
export function yidongyun_securityGroup(data) {
  return request({
    url: '/yidongyun/securityGroup',
    method: 'post',
    data: data
  })
}
// 销商平台发起创建经销商终端客户
export function yidongyun_securityGroupCreateReq(data) {
  return request({
    url: '/yidongyun/securityGroupCreateReq',
    method: 'post',
    data: data
  })
}
// 销商平台发起创建经销商终端客户
export function yidongyun_securityGroupRule(data) {
  return request({
    url: '/yidongyun/securityGroupRule',
    method: 'post',
    data: data
  })
}
// 查询VPC下网络
export function yidongyun_vpcNetworkResps(data) {
  return request({
    url: '/yidongyun/vpcNetworkResps',
    method: 'post',
    data: data
  })
}
// 查询VPC下网络
export function yidongyun_securityGroupRuleCreateReq(data) {
  return request({
    url: '/yidongyun/securityGroupRuleCreateReq',
    method: 'post',
    data: data
  })
}
// 查询VPC下网络
export function yidongyun_securityGroupShow(data) {
  return request({
    url: '/yidongyun/securityGroupShow',
    method: 'post',
    data: data
  })
}

export function yidongyun_createOrderUnify(data) {
  return request({
    url: '/yidongyun/createOrderUnify',
    method: 'post',
    data: data
  })
}

export function yidongyun_resellerOrderOperateVerify(data) {
  return request({
    url: '/yidongyun/resellerOrderOperateVerify',
    method: 'post',
    data: data
  })
}

export function yidongyun_Aksklist(data) {
  return request({
    url: '/yidongyun/Aksklist',
    method: 'post',
    data: data
  })
}

export function yidongyun_AkskCreate(data) {
  return request({
    url: '/yidongyun/AkskCreate',
    method: 'post',
    data: data
  })
}

export function yidongyun_index(data) {
  return request({
    url: '/yidongyun/index',
    method: 'post',
    data: data
  })
}

export function yidongyun_iframe(data) {
  return request({
    url: '/yidongyun/userToken',
    method: 'post',
    data: data
  })
}

export function yidongyun_resellerOrder(data) {
  return request({
    url: '/yidongyun/resellerOrder',
    method: 'post',
    data: data
  })
}

export function yidongyun_orderDetail(data) {
  return request({
    url: '/yidongyun/orderDetail',
    method: 'post',
    data: data
  })
}

export function yidongyun_renewResellerOrder(data) {
  return request({
    url: '/yidongyun/renewResellerOrder',
    method: 'post',
    data: data
  })
}

export function yidongyun_changeResellerOrder(data) {
  return request({
    url: '/yidongyun/changeResellerOrder',
    method: 'post',
    data: data
  })
}

export function yidongyun_orderChangePrice(data) {
  return request({
    url: '/yidongyun/orderChangePrice',
    method: 'post',
    data: data
  })
}

export function yidongyun_proindex(data) {
  return request({
    url: '/yidongyun/proindex',
    method: 'post',
    data: data
  })
}

export function yidongyun_productExcle(data) {
  return request({
    url: '/yidongyun/productExcle',
    method: 'post',
    data: data
  })
}

export function yidongyun_imglist(data) {
  return request({
    url: '/yidongyun/imglist',
    method: 'post',
    data: data
  })
}

export function yidongyun_createBandwidthUnify(data) {
  return request({
    url: '/yidongyun/createBandwidthUnify',
    method: 'post',
    data: data
  })
}

export function yidongyun_qryofferchas(data) {
  return request({
    url: '/yidongyun/qryofferchas',
    method: 'post',
    data: data
  })
}

export function yidongyun_createDataDisksOrderUnify(data) {
  return request({
    url: '/yidongyun/createDataDisksOrderUnify',
    method: 'post',
    data: data
  })
}

export function yidongyun_createEosorderUnify(data) {
  return request({
    url: '/yidongyun/createEosorderUnify',
    method: 'post',
    data: data
  })
}

export function yidongyun_importing(data) {
  return request({
    url: '/yidongyun/importingindex',
    method: 'post',
    data: data
  })
}

export function yidongyun_monthlystatement(data) {
  return request({
    url: '/yidongyun/monthlystatement',
    method: 'post',
    data: data
  })
}

export function yidongyun_estimatedbill(data) {
  return request({
    url: '/yidongyun/estimatedbill',
    method: 'post',
    data: data
  })
}

export function yidongyun_cximporting(data) {
  return request({
    url: '/yidongyun/importing',
    method: 'post',
    data: data
  })
}

