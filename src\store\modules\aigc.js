import { cateAllApi } from "@/api/aigc"

const state = {
  categories: [],
  formTypes: [
    { label: '文本', value: 'text' },
    { label: '下拉框', value: 'select' },
    { label: '多行文本', value: 'textarea' }
  ]
}

const mutations = {
  SET_CATEGORIES(state, categories) {
    state.categories = categories
  }
}

const actions = {
  fetchCategories({ commit }) {
    return new Promise((resolve, reject) => {
      cateAllApi()
        .then(response => {
          if (response.code === 200) {
            commit('SET_CATEGORIES', response.data || [])
            resolve(response.data)
          } else {
            reject(new Error('获取文案类型失败'))
          }
        })
        .catch(error => {
          reject(error)
        })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}