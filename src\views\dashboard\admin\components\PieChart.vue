<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    chartData: {
      type: Array,
      required: true
    },
    legend: {
      type: Object,
      required: true
    },
    colorList: {
      type: Array,
      required: false,
      default: () => []
    },
    showLegend: {
      type: Boolean,
      required: false,
      default: true
    },
    grid: {
      type: Object,
      required: false,
      default: () => ({
        left: 10,
        right: 10,
        bottom: 20,
        top: 50,
        containLabel: true
      })
    },
    roseType: {
      type: [String, Boolean],
      default: false
    },
    radius: {
      type: Array,
      default: () => []
    },
    dataType: {
      type: String,
      default: 'normal'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.setOptions(this.chartData)
    },
    setOptions(chartData = []) {
      const options = {
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            return `${params.name} ： <br/> 下载量：${params.value} <br/> 占比：${params.percent}%`
          }
        },
        legend: this.legend,
        series: [{
          type: 'pie',
          roseType: this.roseType,
          radius: this.radius,
          center: ['50%', '38%'],
          animationEasing: 'cubicInOut',
          animationDuration: 2600,
          label: {
            show: false,
            position: 'outside'
          },
          labelLine: {
            show: false,
            length: 5,
            length2: 10
          }
        }]
      }
      if (this.dataType === 'dataset') {
        options.dataset = {
          source: chartData || this.chartData
        }
      } else {
        options.series[0].data = chartData || this.chartData
      }

      try {
        this.chart.setOption(options)
      } catch (e) {
        console.log(e)
      }
    }
  }
}
</script>
