<template>
  <div>
    <el-row>
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>订单信息</span>
          </div>
          <el-form label-position="right">
            <el-form-item label="订单编号：">
              {{ formData.no }}
            </el-form-item>
            <el-form-item label="产品名称：">
              {{ formData.snapshot.name }}
            </el-form-item>
            <el-form-item label="兑换数量：">
              {{ formData.number }}
            </el-form-item>
            <el-form-item label="积分单价：">
              {{ formData.price }}
            </el-form-item>
            <el-form-item label="积分总价：">
              {{ formData.total }}
            </el-form-item>
            <el-form-item label="福利币单价：">
              {{ formData.money }}
            </el-form-item>
            <el-form-item label="福利币总价：">
              {{ formData.total_money }}
            </el-form-item>
            <el-form-item label="下单时间：">
              {{ formData.created_at }}
            </el-form-item>
            <el-form-item label="订单状态：">
              <el-tag v-if="formData.status === 0" type="warning">待发货</el-tag>
              <el-tag v-if="formData.status === 1" type="success">已发货</el-tag>
              <el-tag v-if="formData.status === 2" type="success">交易完成</el-tag>
              <el-tag v-if="formData.status === 4" type="success">待退款</el-tag>
              <el-tag v-if="formData.status === 5" type="success">退款完成</el-tag>
            </el-form-item>
            <el-form-item label="兑换人：">
              {{ formData.user.name }}
            </el-form-item>
            <el-form-item label="联系电话：">
              {{ formData.user.phone }}
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-col v-if="formData.status==4||formData.status==5" :span="24" class="mt20">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>退款信息</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="updatereturnOrder">保存</el-button>
          </div>
          <el-form ref="formData" label-position="right" :model="formData" :rules="rules">
            <el-form-item label="服务类型：">
              {{ orderreturn.type }}
            </el-form-item>
            <el-form-item label="退款原因：">
              {{ orderreturn.title }}
            </el-form-item>
            <el-form-item label="退款说明：">
              {{ orderreturn.reason }}
            </el-form-item>
            <el-form-item label="退款凭证：">
              <div style="width:350px;">
                <el-row v-if="orderreturn.imgarrlist" :gutter="10">
                  <el-col v-for="item in orderreturn.imgarrlist" :key="item" :span="4" justify="center">
                    <div>
                      <el-image
                        style="width: 100%;"
                        :src="item"
                        :preview-src-list="orderreturn.imgarrlist"
                        fit="fill"
                      />
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-form-item>
            <el-form-item v-if="orderreturnck!=0" label="退款状态：">
              <el-tag v-if="orderreturn.status === 0" type="warning">待审核</el-tag>
              <el-tag v-if="orderreturn.status === 1" type="success">同意</el-tag>
              <el-tag v-if="orderreturn.status === 2" type="success">驳回</el-tag>
            </el-form-item>
            <el-form-item v-if="orderreturnck!=0" label="说明：">
              {{ orderreturn.content }}
            </el-form-item>
            <el-form-item v-if="orderreturnck==0" label="退款状态：">
              <el-select v-model="orderreturn.status" prop="status" placeholder="请选择">
                <el-option label="待审核" :value="0" />
                <el-option label="同意" :value="1" />
                <el-option label="驳回" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item v-if="orderreturnck==0" label="说明：" prop="content">
              <el-input v-model="orderreturn.content" style="width: 80%" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-col :span="24" class="mt20">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>收货信息</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="updateOrder">保存</el-button>
          </div>
          <el-form ref="formData" label-position="right" :model="formData" :rules="rules">
            <el-form-item label="收件人：">
              {{ formData.express_info.name }}
            </el-form-item>
            <el-form-item label="收件人电话：">
              {{ formData.express_info.phone }}
            </el-form-item>
            <el-form-item label="收货地址：">
              {{ formData.express_info.province }}  {{ formData.express_info.city }}  {{ formData.express_info.area }} {{ formData.express_info.address }}
            </el-form-item>
            <el-form-item label="修改状态：">
              <el-select v-model="formData.status" placeholder="请选择">
                <el-option label="待发货" :value="0" />
                <el-option label="已发货" :value="1" />
                <el-option label="交易完成" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item label="快递公司：">
              <el-select v-model="formData.express_name" placeholder="请选择">
                <el-option
                  v-for="item in optionsCompany"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
            <el-form-item v-if="formData.express_name != '自提'" label="快递单号：" prop="express_number">
              <el-input v-model="formData.express_number" size="small" style="width: 80%" />
            </el-form-item>
            <el-form-item label="发货时间：">
              <el-date-picker
                v-model="formData.express_time"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                placeholder="选择日期时间"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-col v-if="formData.express_name != '自提' && formData.express_number" :span="24" class="mt20">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>物流信息</span>
          </div>
          <el-timeline :reverse="false">
            <el-timeline-item
              v-for="(item, index) in express"
              :key="index"
              :timestamp="item.time"
            >
              {{ item.message }}
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { detail, store, express, returnstore } from '../../api/shop_orders'
export default {
  name: 'OrderDetail',
  data() {
    return {
      formData: {
        snapshot: {},
        express_info: {},
        user: {}
      },
      orderreturnck: 0,
      orderreturn: {},
      rules: {},
      // optionsCompany: ['圆通速递', '申通快递', '中通快递', '百世快递', '韵达快递', '顺丰速运', '天天快递', '中国邮政', 'EMS', '京东快递', '德邦快递', '极兔速递']
      optionsCompany: ['圆通速递', '申通快递', '百世快递', '自提'],
      express: []
    }
  },
  created() {
    this.getOrder()
  },
  methods: {
    getOrder() {
      detail(this.$route.query).then(response => {
        this.formData = response.data
        this.orderreturn = response.data.orderreturn
        if (this.orderreturn) {
          this.orderreturnck = this.orderreturn.status
        }
        if (this.formData.express_name && this.formData.express_number) {
          this.getExpress()
        }
      })
    },
    updatereturnOrder() {
      returnstore(this.orderreturn).then(response => {
        this.$message.success('操作成功')
        this.getOrder()
      })
    },
    updateOrder() {
      store(this.formData).then(response => {
        this.$message.success('更新成功')
        this.getOrder()
      })
    },
    getExpress() {
      express({ no: this.formData.no }).then(response => {
        this.express = response.data.express
      })
    }
  }
}
</script>

<style scoped>

</style>
