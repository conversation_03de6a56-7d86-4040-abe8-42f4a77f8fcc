<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import OrderUpdate from '@/views/businessCard/order/OrderUpdate.vue'
import { order_list } from '@/api/businessCard/order'
import { objToQueryString } from '@/utils'

export default {
  name: 'List',
  components: { OrderUpdate, StatisticsTemplate },
  data() {
    return {
      config: {
        key: 'order',
        filters: [
          {
            label: '订单号',
            prop: 'order_no',
            type: 'input'
          },
          {
            label: '套餐名称',
            prop: 'title',
            type: 'input'
          },
          {
            label: '公司',
            prop: 'company_unique_id',
            type: 'input'
          },
          {
            label: '状态',
            prop: 'status',
            type: 'select',
            settings: {
              options: [
                {
                  label: '全部',
                  value: ''
                },
                {
                  label: '待付款',
                  value: 1
                },
                {
                  label: '已付款',
                  value: 2
                },
                {
                  label: '已取消',
                  value: 3
                },
                {
                  label: '超时取消',
                  value: 4
                },
                {
                  label: '已退款',
                  value: 5
                }
              ]
            }
          }
        ],
        tableSettings: {
          api: order_list,
          field: {
            page: 'page',
            limit: 'perPage',
            total: 'total'
          },
          columns: [
            {
              label: 'ID',
              prop: 'id',
              width: 60
            },
            {
              label: '名称',
              prop: 'user_na',
              width: 100
            },
            {
              label: '手机号',
              prop: 'user_phone',
              width: 140
            },
            {
              label: '订单号',
              prop: 'order_no',
              width: 200
            },
            {
              label: '套餐名称',
              prop: 'title',
              width: 200
            },
            {
              label: '姓名',
              prop: 'username',
              width: 80
            },
            {
              label: '电话',
              prop: 'phone',
              width: 140
            },
            /* {
              label: '公司',
              prop: 'company_nam',
              width: 200
            }, */
            {
              label: '产品单价',
              prop: 'price'
            },
            {
              label: '总价',
              prop: 'total_amount'
            },
            {
              label: '状态',
              prop: 'status',
              width: 100,
              isSlot: true
            },
            {
              label: '名片ID',
              prop: 'cards_id'
            },
            {
              label: '激活时间',
              prop: 'activate_time',
              width: 180
            },
            {
              label: '到期时间',
              prop: 'expire_end',
              width: 180
            },
            {
              label: '创建时间',
              prop: 'created_at',
              width: 180
            },
            {
              label: '更新时间',
              prop: 'updated_at',
              width: 180
            },
            /* {
              label: '操作',
              prop: 'action',
              width: 200,
              fixed: 'right',
              align: 'center',
              isSlot: true
            } */
          ]
        }
      },
      updateVisible: false,
      row: {}
    }
  },
  methods: {
    handleUpdate(row) {
      this.row = row
      this.updateVisible = true
    },
    handleDelete(row) {
      const data = this.config.tableSettings.data.filter(item => item.id !== row.id)
      this.$set(this.config.tableSettings, 'data', data)
    },
    handleExport(){
      const params = this.$refs.tableRef.params
      const obj = {
        order_no: params.order_no,
        title: params.title,
        company_unique_id: params.company_unique_id,
        status: params.status
      }
      window.open(process.env.VUE_APP_BASE_API + '/card/orderexportExcel?' + objToQueryString(obj), '_blank')
    }
  }
}
</script>

<template>
  <div class="page-wrap">
    <StatisticsTemplate ref="tableRef" :config="config">
      <template #topActions>
        <el-button type="primary" @click="handleExport">导出</el-button>
      </template>
      <template #order_status="{row}">
        <el-tag v-if="row.status===1" type="success">待付款</el-tag>
        <el-tag v-if="row.status===2" type="primary">已付款</el-tag>
        <el-tag v-if="row.status===3" type="danger">已取消</el-tag>
        <el-tag v-if="row.status===4" type="warning">超时取消</el-tag>
        <el-tag v-if="row.status===5" type="info">已退款</el-tag>
      </template>
      <template #order_action="{row}">
        <el-button size="mini" type="primary" @click="handleUpdate(row)">修改</el-button>
        <el-popconfirm
          title="确定删除吗？"
          style="margin-left: 10px;"
          @onConfirm="handleDelete(row)"
        >
          <template v-slot:reference>
            <el-button type="danger" size="mini">删除</el-button>
          </template>
        </el-popconfirm>
      </template>
    </StatisticsTemplate>
    <OrderUpdate :visible.sync="updateVisible" :data="row" />
  </div>
</template>

<style scoped lang="scss">
.page-wrap {
  height: 100%;
}
</style>
