import request from '@/utils/request'

// 获取代理商列表
export function index(data) {
  return request({
    url: '/agent/list',
    method: 'post',
    data: data
  })
}
// 代理商添加
export function store(data) {
  return request({
    url: '/agent/store',
    method: 'post',
    data: data
  })
}
// 代理商修改
export function update(data) {
  return request({
    url: '/agent/update',
    method: 'post',
    data: data
  })
}

export function destroy(data) {
  return request({
    url: '/agent/delete',
    method: 'post',
    data: data
  })
}
// 代理商详情
export function detail(data) {
  return request({
    url: '/agent/detail',
    method: 'post',
    data: data
  })
}

export function audit(data) {
  return request({
    url: '/agent/audit',
    method: 'post',
    data: data
  })
}

export function price(data) {
  return request({
    url: '/product/price',
    method: 'post',
    data: data
  })
}

export function apply(data) {
  return request({
    url: '/product/apply',
    method: 'post',
    data: data
  })
}

export function customers(data) {
  return request({
    url: '/agent/customers',
    method: 'post',
    data: data
  })
}
export function agentcustomers(data) {
  return request({
    url: 'statistic/agentcustomers',
    method: 'post',
    data: data
  })
}
export function agentUserStatus(data) {
  return request({
    url: '/agent/agentUserStatus',
    method: 'post',
    data: data
  })
}
