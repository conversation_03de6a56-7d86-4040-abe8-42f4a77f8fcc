import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/activity/list',
    method: 'post',
    data: data
  })
}

export function detail(data) {
  return request({
    url: '/activity/detail',
    method: 'post',
    data: data
  })
}

export function store(data) {
  return request({
    url: '/activity/store',
    method: 'post',
    data: data
  })
}

export function destory(data) {
  return request({
    url: '/activity/destory',
    method: 'post',
    data: data
  })
}

export function activityAgentProduct(data) {
  return request({
    url: '/activity/activityAgentProduct',
    method: 'post',
    data: data
  })
}

export function lcAgentList(data) {
  return request({
    url: '/activity/lcAgentList',
    method: 'post',
    data: data
  })
}

export function otherAgentList(data) {
  return request({
    url: '/activity/otherAgentList',
    method: 'post',
    data: data
  })
}

export function agentList(data) {
  return request({
    url: '/activity/agentList',
    method: 'post',
    data: data
  })
}

export function setStatus(data) {
  return request({
    url: '/activity/setStatus',
    method: 'post',
    data: data
  })
}
