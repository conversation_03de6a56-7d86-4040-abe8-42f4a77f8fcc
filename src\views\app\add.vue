<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <!-- <span>新增产品</span> -->
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
      </div>
      <app-form :type="true" @transfer="sonSave" />
    </el-card>
  </div>
</template>

<script>
import AppForm from './components/app-form'
import { store } from '@/api/app'
export default {
  name: 'Add',
  components: { AppForm },
  methods: {
    sonSave(form) {
      store(form).then(response => {
        this.$message.success('添加成功')
        this.$router.go(-1)
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .list-wrap {
    height: 100%;

    .box-card {
      border: none;
      height: 100%;

      ::v-deep .el-card__header {
        padding-top: 0;
      }

      ::v-deep .el-card__body {
        height: calc(100% - 59px);
        overflow: auto;
      }
    }
  }
</style>
