<template>
  <div class="list-wrap mt20">
    <div class="filter">
      <el-form :inline="true" :model="listQuery" class="demo-form-inline" size="small">
        <el-form-item label="站点名称：">
          <el-input v-model="listQuery.template_name" placeholder="站点名称" clearable />
        </el-form-item>
        <el-form-item label="绑定域名：">
          <el-input v-model="listQuery.domain" placeholder="绑定域名" clearable />
        </el-form-item>
        <el-form-item label="企业名称：">
          <el-input v-model="listQuery.company_name" placeholder="企业名称" clearable />
        </el-form-item>
        <el-form-item label="审核状态：">
          <el-select v-model="listQuery.status" placeholder="请选择" clearable>
            <el-option label="待审核" :value="1" />
            <el-option label="已上线" :value="2" />
            <el-option label="已拒绝" :value="3" />
            <el-option label="已到期" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="发布状态：">
          <el-select v-model="listQuery.state" placeholder="请选择" clearable>
            <el-option label="等待发布" :value="1" />
            <el-option label="发布成功" :value="2" />
            <el-option label="发布失败" :value="3" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="付费状态：">
          <el-select v-model="listQuery.pay_state" placeholder="请选择" clearable>
            <el-option label="所有" :value="0" />
            <el-option label="付费版" :value="1" />
            <el-option label="免费版" :value="2" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="备案状态：">
          <el-select v-model="listQuery.filing" placeholder="请选择" clearable>
            <el-option label="已备案" :value="1" />
            <el-option label="未备案" :value="2" />
            <el-option label="所有" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="分公司：">
          <el-select v-model="listQuery.district_id" placeholder="请选择" clearable>
            <el-option
              v-for="district_info in districtsOptions"
              :key="district_info.id"
              :label="district_info.name"
              :value="district_info.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="现制作人：">
          <el-input v-model="listQuery.producer2" placeholder="现制作人" clearable />
        </el-form-item>
        <el-form-item label="审核时间：">
          <el-date-picker
            v-model="listQuery.times"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item label="提交时间：">
          <el-date-picker
            v-model="listQuery.send_times"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getQueryList">过滤</el-button>
          <el-button type="primary" :loading="downloadLoading" icon="el-icon-download" @click="exportExcel">导出</el-button>
        </el-form-item>
        <switch-package-type @switchPackageCallback="switchPackageCallback" />
        <el-form-item>
          <el-button type="primary" icon="el-icon-refresh-right" @click="getList">刷新</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="mt20">
      <el-alert title="审核提示" type="info" description="审核操作时，如果是自有服务器请确保域名已解析至对应服务器。在进行下一步操作，否会导致发布失败！" show-icon />
    </div>
    <div class="table mt20">
      <el-table ref="tableRef" v-loading="loading" :data="tableData" style="width: 100%">
        <el-table-column prop="id" label="ID" :width="jztPackageType === '1.1' ? 75 : undefined" />
        <el-table-column label="备案信息" width="120">
          <template slot-scope="scope">
            <template v-if="scope.row.host">
              <el-image
                v-if="scope.row.host.img_url && Array.isArray(scope.row.host.img_url)"
                :src="scope.row.host.img_url[0]"
                fit="contain"
                :preview-src-list="scope.row.host.img_url"
                :lazy="true"
                style="width: 100px; height: 50px"
              />
              <el-tag v-else type="info" effect="dark" size="small">暂无备案</el-tag>
            </template>
            <el-tag v-else type="info" effect="dark" size="small">暂无备案</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="免费版">
          <template slot-scope="scope">
            <template v-if="scope.row.order">
              <el-tag
                v-if="scope.row.order.open_type === 1 && scope.row.order.is_free === 2"
                type="success"
                effect="dark"
                size="small"
              >是</el-tag>
              <el-tag v-else type="danger" effect="dark" size="small">否</el-tag>
            </template>
            <el-tag v-else type="danger" effect="dark" size="small">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="站点名称" width="200">
          <template slot-scope="scope">
            <template v-if="scope.row.host">{{ scope.row.host.web_name }}</template>
            <template v-else>{{ scope.row.title }}</template>
          </template>
        </el-table-column>
        <el-table-column v-if="jztPackageType === '1.1'" prop="hy_title" label="所属行业" width="130" />
        <el-table-column label="绑定域名" width="150">
          <template slot-scope="scope">
            <template v-if="scope.row.host && jztPackageType === '1.1'">{{ scope.row.host.domain }}</template>
            <template v-if="jztPackageType === '2.0'">{{ scope.row.domain }}</template>
          </template>
        </el-table-column>
        <el-table-column label="公司名称" width="200">
          <template slot-scope="scope">
            <template v-if="scope.row.company">{{ scope.row.company.name }}</template>
          </template>
        </el-table-column>
        <el-table-column v-if="jztPackageType === '1'" label="分公司" width="200" show-overflow-tooltip>
          <template slot-scope="scope">
            <template v-if="scope.row.district_info">{{ scope.row.district_info.name }}</template>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="host.producer" label="创建人" /> -->
        <el-table-column v-if="jztPackageType === '1.1'" label="创建人">
          <template slot-scope="scope">
            <template v-if="scope.row.host">{{ scope.row.host.producer }}</template>
          </template>
        </el-table-column>
        <el-table-column prop="producer2" label="现制作人" />
        <el-table-column label="客服">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.customer" type="success" effect="dark" size="small">{{ scope.row.customer }}</el-tag>
            <el-tag v-else type="info" effect="dark" size="small">未分配</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="审核状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 1" type="warning" effect="dark" size="small">待审核</el-tag>
            <el-tag v-if="scope.row.status === 2" type="success" effect="dark" size="small">已通过</el-tag>
            <el-tag v-if="scope.row.status === 3" type="danger" effect="dark" size="small">已拒绝</el-tag>
            <el-tag v-if="scope.row.status === 4" type="info" effect="dark" size="small">已到期</el-tag>
            <el-tag v-if="scope.row.status === 6" type="info" effect="dark" size="small">待发布</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="发布状态" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.state === 1" type="info" effect="dark" size="small">等待发布</el-tag>
            <el-tag v-if="scope.row.state === 2" type="success" effect="dark" size="small">发布成功</el-tag>
            <el-tag v-if="scope.row.state === 3" type="danger" effect="dark" size="small">发布失败</el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column label="付费状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.pay_state === 1" type="info" effect="dark" size="small">付费版</el-tag>
            <el-tag v-if="scope.row.pay_state === 2" type="success" effect="dark" size="small">免费版</el-tag>
          </template>
        </el-table-column> -->
        <el-table-column label="自有">
          <template slot-scope="scope">
            <template v-if="jztPackageType === '1.1'">
              <template v-if="scope.row.host">
                <el-tag v-if="scope.row.host.state === 1" type="success" effect="dark" size="small">是</el-tag>
                <el-tag v-if="scope.row.host.state === 2" type="danger" effect="dark" size="small">否</el-tag>
              </template>
              <template v-else>
                <el-tag type="danger" effect="dark" size="small">暂无</el-tag>
              </template>
            </template>
            <template v-else>
              <el-tag v-if="scope.row.state === 1" type="success" effect="dark" size="small">是</el-tag>
              <el-tag v-if="scope.row.state === 2" type="danger" effect="dark" size="small">否</el-tag>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="客户端编辑" width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.is_edit"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
              @change="setCaseEdit($event, scope.row.site_id)"
            />
          </template>
        </el-table-column>
        <el-table-column v-if="jztPackageType === '1.1'" label="发布案例">
          <template slot-scope="scope">
            <el-switch
              v-if="scope.row.status !== 2"
              v-model="scope.row.is_case"
              disabled
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="2"
              :inactive-value="1"
              @change="setCaseStatus($event, scope.row.id)"
            />
            <el-switch
              v-else
              v-model="scope.row.is_case"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="2"
              :inactive-value="1"
              @change="setCaseStatus($event, scope.row.id)"
            />
          </template>
        </el-table-column>
        <el-table-column label="绑定服务器" width="135">
          <template slot-scope="scope">
            <el-tag effect="dark" size="small">{{ scope.row.server_info_ip }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="审核人" width="135">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.user && scope.row.status !== 1" effect="dark" size="small">{{ scope.row.user.nickname
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="服务器信息" width="145">
          <template slot-scope="scope">
            <el-link
              v-if="scope.row.server_info && scope.row.status === 2"
              :underline="false"
              type="primary"
              @click="handleCopy(scope.row.server_info.name, $event)"
            >点击复制</el-link>
            <el-link v-else :underline="false" disabled type="info">点击复制</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="audit_at" label="审核时间" width="175" />
        <el-table-column label="提交时间" width="175">
          <template slot-scope="scope">
            <template v-if="scope.row.host">
              {{ scope.row.host.update_time || scope.row.host.create_time }}
            </template>
          </template>
        </el-table-column>
        <el-table-column label="到期时间" width="175">
          <template slot-scope="scope">
            {{ scope.row.order ? scope.row.order.expiration_at : '' }}
          </template>
        </el-table-column>
        <el-table-column label="空间到期时间" width="175">
          <template slot-scope="scope">
            {{ scope.row.spacetime }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="审核备注" width="200" show-overflow-tooltip />
        <el-table-column label="操作" width="100px" fixed="right">
          <template slot-scope="scope">
            <el-dropdown trigger="click">
              <span class="el-dropdown-link">
                更多操作<i class="el-icon-arrow-down el-icon--right" />
              </span>
              <el-dropdown-menu slot="dropdown">
                <template v-if="scope.row.host">
                  <el-dropdown-item
                    v-if="scope.row.host.state !== 2 && scope.row.status === 2 && jztPackageType !== '2.0'"
                    @click.native="unsubscribeService(scope.row)"
                  >退订服务器</el-dropdown-item>
                  <el-dropdown-item
                    v-if="scope.row.host.state !== 2 && scope.row.status === 2"
                    @click.native="syncExpiration(scope.row)"
                  >同步到期时间</el-dropdown-item>
                  <el-dropdown-item
                    v-if="scope.row.host.state === 2 && scope.row.status === 2"
                    @click.native="changeDomain(scope.row.host)"
                  >修改域名</el-dropdown-item>
                </template>
                <template v-if="scope.row.orders">
                  <el-dropdown-item
                    v-if="scope.row.orders.open_type === 1 && scope.row.orders.is_free === 3"
                    @click.native="distributionCustomer(scope.row)"
                  >分配客服</el-dropdown-item>
                </template>
                <el-dropdown-item v-if="scope.row.status === 1" @click.native="auditView(scope.row)">审核</el-dropdown-item>
                <el-dropdown-item @click.native="previewSite(scope.row)">预览</el-dropdown-item>
                <!--<el-dropdown-item @click.native="dirUpdatedialog(scope.row)">修改非根目录</el-dropdown-item>-->
                <el-dropdown-item @click.native="editHyTypeBtn(scope.row)">修改所属行业</el-dropdown-item>
                <el-dropdown-item
                  v-if="scope.row.status === 2 && scope.row.state !== 3"
                  @click.native="goSite(scope.row)"
                >访问</el-dropdown-item>
                <el-dropdown-item @click.native="detail(scope.row.host, scope.row.status)">详情</el-dropdown-item>
                <el-dropdown-item
                  v-if="scope.row.status === 2"
                  @click.native="reSendSite(scope.row)"
                >重新发布</el-dropdown-item>
                <el-dropdown-item
                  v-if="scope.row.status === 2 && scope.row.state !== 3"
                  @click.native="bingDomain(scope.row)"
                >域名绑定</el-dropdown-item>
                <el-dropdown-item
                  v-if="scope.row.status === 2 && scope.row.state !== 3 && scope.row.is_common_lib === 1"
                  @click.native="sendTemplate(scope.row)"
                >发布为模版</el-dropdown-item>
                <template v-if="scope.row.is_xnlogin">
                  <el-dropdown-item
                    v-if="scope.row.is_xnlogin.is_virtual_login === 1"
                    @click.native="khd_login(scope.row)"
                  >登录客户端</el-dropdown-item>
                  <el-dropdown-item
                    v-if="scope.row.is_xnlogin.is_virtual_login === 1"
                    @click.native="zzd_login(scope.row)"
                  >登录制作端</el-dropdown-item>
                </template>
                <el-dropdown-item v-if="scope.row.status === 1 && scope.row.caseaudit != 1" @click.native="setCaseStatus($event, scope.row.id)">
                  提交到案例库
                </el-dropdown-item>
                <el-dropdown-item @click.native="handleSetMaterial(scope.row)">设置素材空间</el-dropdown-item>
                <!-- 订单查询 -->
                <el-dropdown-item @click.native="read_order(scope.row)">订单查询</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </div>
    <el-dialog :close-on-click-modal="false" title="模版发布" :visible.sync="TemplateDialogVisible" width="20%">
      <el-form
        ref="ruleForm_template"
        :model="ruleForm_template"
        :rules="rules_template"
        label-width="150px"
        class="demo-ruleForm"
        size="small"
      >
        <el-form-item label="模版分类：" prop="type_id">
          <el-select v-model="ruleForm_template.type_id" placeholder="请选择">
            <el-option v-for="category in categories" :key="category.id" :label="category.title" :value="category.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="TemplateDialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="TemplateDialogLoading" size="small" @click="sendTemplateHandle">确
          定</el-button>
      </span>
    </el-dialog>
    <el-dialog :close-on-click-modal="false" title="审核发布" :visible.sync="dialogVisible" width="30%">
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="150px" class="demo-ruleForm">
        <el-form-item label="审核状态：" prop="status">
          <el-select v-model="ruleForm.status" placeholder="请选择">
            <el-option label="通过申请" :value="2" />
            <el-option label="拒绝申请" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="ruleForm.status === 2 && ruleForm.state === 1" label="选择服务器：" prop="server_id">
          <el-select v-model="servers_id" placeholder="请选择" @change="changeValue">
            <el-option v-for="server in serverList" :key="server.id" :label="server.name" :value="server.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="审核备注：" prop="remark">
          <el-input v-model="ruleForm.remark" type="textarea" placeholder="备注信息" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="btn_loading" @click="auditHandle">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 自备服务器修改域名 -->
    <el-dialog :close-on-click-modal="false" title="修改域名" :visible.sync="domainVisible" width="30%">
      <el-form ref="ruleForm" :model="domainData" label-width="120px" class="demo-ruleForm">
        <el-form-item label="域名：" prop="domain">
          <el-input v-model="domainData.domain" placeholder="请输入域名" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="domainVisible = false">取 消</el-button>
        <el-button type="primary" :loading="btn_loading" @click="domainHandle">确 定</el-button>
      </span>
    </el-dialog>
    <!-- ftp详情 -->
    <el-dialog :close-on-click-modal="false" title="发布详情" :visible.sync="detailVisible" width="30%">
      <el-form ref="ruleForm" :model="detailData" label-width="120px" class="demo-ruleForm">
        <el-form-item label="FTP地址：">
          <template v-if="is_edit_ftp">
            <el-input v-model="detailData.ftp" placeholder="请输入FTP地址" />
          </template>
          <template v-else>{{ detailData.ftp }}</template>
        </el-form-item>
        <el-form-item label="FTP非根目录：">
          <template v-if="is_edit_ftp">
            <el-input v-model="detailData.ftp_dir" placeholder="请输入FTP非根目录" />
          </template>
          <template v-else>{{ detailData.ftp_dir }}</template>
        </el-form-item>
        <el-form-item label="FTP账户：">
          <template v-if="is_edit_ftp">
            <el-input v-model="detailData.username" placeholder="请输入FTP账户" />
          </template>
          <template v-else>{{ detailData.username }}</template>
        </el-form-item>
        <el-form-item label="FTP密码：">
          <template v-if="is_edit_ftp">
            <el-input v-model="detailData.password" placeholder="请输入FT密码" />
          </template>
          <template v-else>{{ detailData.password }}</template>
        </el-form-item>
        <el-form-item label="FTP端口：">
          <template v-if="is_edit_ftp">
            <el-input v-model="detailData.port" placeholder="请输入FTP端口" />
          </template>
          <template v-else>{{ detailData.port }}</template>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="detailVisible = false">关 闭</el-button>
        <template v-if="detailData.audit_status === 2">
          <el-button v-if="!is_edit_ftp" type="warning" @click="is_edit_ftp = true">修 改</el-button>
          <template v-else>
            <el-button type="warning" @click="is_edit_ftp = false">取消修改</el-button>
            <el-button type="success" @click="editFTP(detailData)">保 存</el-button>
          </template>
        </template>
        <el-button
          type="primary"
          @click="handleCopy('FTP地址：' + detailData.ftp + '\n' + 'FTP非根目录：' + detailData.ftp_dir + '\n' + 'FTP账户：' + detailData.username + '\n' + 'FTP密码：' + detailData.password + '\n' + 'FTP端口：' + detailData.ftp_port, $event)"
        >复制全部</el-button>
      </span>
    </el-dialog>

    <el-dialog :close-on-click-modal="false" title="修改FTP非根目录" :visible.sync="ftpDirVisible" width="30%">
      <el-form ref="ruleForm" :model="detailData" label-width="100px" class="demo-ruleForm">
        <el-form-item label="FTP非目录：">
          <el-input
            v-model="detailData.ftp_dir"
            placeholder="FTP非目录"
            clearable
            autocomplete="off"
            @input="change($event)"
          />
          <span class="el-dropdown-link">
            服务器、空间有目录请填写"\\ABC\\XXX\\"格式
          </span>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="ftpDirVisible = false">关 闭</el-button>
        <el-button type="primary" @click="dirUpdate(detailData.site_id, detailData.ftp_dir)">修改</el-button>
      </span>
    </el-dialog>

    <!-- 分配客服 -->
    <el-dialog :close-on-click-modal="false" title="分配客服" :visible.sync="customerDialog" width="30%">
      <el-form ref="ruleForm" :model="detailData" label-width="100px" class="demo-ruleForm">
        <el-form-item label="选择客服：" prop="customer_id">
          <el-select
            v-model="customer_id"
            style="width:100%;"
            multiple
            placeholder="请选择"
            filterable
            :loading="customersLoading"
            @change="changeCustomerValue"
          >
            <el-option
              v-for="customer in customersList"
              :key="customer.id"
              :label="customer.id + '--' + customer.name + '--' + customer.username"
              :value="customer.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="customerDialog = false">关 闭</el-button>
        <el-button type="primary" @click="assignCustomerSure(detailData.site_id)">分配</el-button>
      </span>
    </el-dialog>
    <!-- 修改模板分类 -->

    <el-dialog :close-on-click-modal="false" title="修改所属行业" :visible.sync="detailHyVisible" width="40%">
      <el-form v-if="row" ref="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="案例分类：">
          <el-select v-model="category_pid_d" placeholder="请选择" clearable>
            <el-option v-for="item in categories" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
          <el-select v-if="categoryChildren_d.length > 0" v-model="category_id_d" placeholder="请选择" clearable>
            <el-option v-for="item in categoryChildren_d" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="detailHyVisible = false">关 闭</el-button>
        <el-button type="primary" :loading="btn_loading" @click="caseEdit">修改</el-button>
      </span>
    </el-dialog>
    <!-- 站点订单 -->
    <site-order ref="siteOrderRef" />
    <el-dialog :close-on-click-modal="false" title="设置素材空间" :visible.sync="librarySpaceVisible" width="500px">
      <el-form v-if="librarySpaceVisible" ref="librarySpaceForm" :model="librarySpaceData" label-width="120px" class="demo-ruleForm">
        <el-form-item
          label="选择素材空间："
          prop="type"
          :rules="[
            { required: true, message: '请选择', trigger: 'change' }
          ]"
        >
          <el-select v-model="detailData.type" placeholder="请选择" clearable>
            <el-option v-for="item in librarySpaceList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="librarySpaceVisible = false">关 闭</el-button>
        <el-button type="primary" :loading="btn_loading" @click="librarySpaceSure">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { parseTime } from '@/utils'
import clip from '@/utils/clipboard' // use clipboard directly
import {
  sites,
  audit,
  districts,
  setCase,
  sendTemplate,
  resend,
  setCaseClientEdit,
  dirUpdate,
  unsubscribe,
  customerList,
  assignCustomer,
  editHyType,
  editFtpApi,
  editDomainApi,
  syncExpirationApi,
  setBucketApi, bucketListApi
} from '@/api/jzt/sites'
import { servers } from '@/api/jzt/servers'
import { zzd_login, khd_login } from '@/api/jzt/user'
import { cates } from '@/api/jzt/lib'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import SiteOrder from './components/siteOrder.vue'
import { mapGetters } from 'vuex'
import SwitchPackageType from '@/views/jzt/components/SwitchPackageType.vue'

export default {
  name: 'List',
  components: { SwitchPackageType, Pagination, SiteOrder },
  data() {
    return {
      TemplateDialogLoading: false,
      TemplateDialogVisible: false,
      btn_loading: false,
      dialogVisible: false,
      detailVisible: false,
      ftpDirVisible: false,
      customerDialog: false,
      ruleForm: {},
      zzdruleForm: {},
      khdruleForm: {},
      ruleForm_template: {},
      detailData: {},
      tableData: [],
      serverList: [],
      districtsOptions: [],
      loading: false,
      listQuery: {
        page: 1,
        limit: 10
      },
      total: 0,
      rules: {
        status: [{ required: true, message: '审核状态必选', trigger: 'change' }]
      },
      rules_template: {},
      categories: [],
      downloadLoading: false,
      autoWidth: true,
      bookType: 'xlsx',
      filename: '已上线网站数据',
      row: {},
      servers_id: 0,
      customerListQuery: {},
      customersList: [],
      customersLoading: false,
      customer_id: null,
      /* 详情行业分类 */
      detailHyVisible: false,
      category_pid_d: null,
      category_id_d: null,
      categoryChildren_d: [],
      /* 编辑ftp */
      is_edit_ftp: false,
      /* 自备服务器修改域名 */
      domainVisible: false,
      domainData: {},
      librarySpaceVisible: false,
      librarySpaceData: {
        type: undefined
      },
      librarySpaceList: [
        { value: 1, label: '建站通空间' }, { value: 2, label: '自备空间' }, { value: 3, label: '阿里云oss' }
      ]
    }
  },
  computed: {
    ...mapGetters(['jztPackageType', 'jztPackageTypeList'])
  },
  watch: {
    category_pid_d() {
      const category_pid_d = this.category_pid_d
      this.categoryChildren_d = this.getCateP(category_pid_d)
      if (this.isShowEdit) {
        this.isShowEdit = false
      } else {
        this.category_id_d = null
      }
    }
  },
  created() {
    this.getList()
    this.getServers()
    this.getTemplateCates()
    this.district()
    this.getCustomerList()
  },
  methods: {
    handleSetMaterial(row) {
      console.log(row)
      this.librarySpaceData = row
      this.librarySpaceVisible = true
      this.getBucketListApi()
    },
    librarySpaceSure() {
      setBucketApi(this.librarySpaceData).then(res => {
        if (res.code === 200) {
          this.$message.success('修改成功')
          this.librarySpaceVisible = false
          this.getList()
        }
      })
    },
    // 获取套餐列表
    async getBucketListApi() {
      const res = await bucketListApi({ site_id: this.librarySpaceData.id })
      if (res.code === 200) {
        this.librarySpaceData = res.data
      }
    },
    // 切换套餐
    async switchPackageCallback() {
      this.getList()
      this.getServers()
      this.getTemplateCates()
      this.district()
      this.getCustomerList()
    },
    // 查询订单
    read_order(row) {
      this.$refs.siteOrderRef.openDialog(row)
    },
    // 同步到期时间
    async syncExpiration(row) {
      const { site_id } = JSON.parse(JSON.stringify(row))
      const expiration_time = row.spacetime
      if (!site_id) return this.$message.error('缺少站点id')
      else if (!expiration_time) return this.$message.error('缺少到期时间')
      const res = await syncExpirationApi({ site_id, expiration_time })
      if (res.code === 200) this.$message.success('同步成功')
    },
    // 编辑ftp
    async editFTP() {
      const that = this
      // console.log(that.detailData)
      const { id, ftp, ftp_dir, username, password, port } = JSON.parse(JSON.stringify(that.detailData))
      if (!id) return that.$message.error('缺少站点id')
      const res = await editFtpApi({ site_id: id, ftp, ftp_dir, username, password, port })
      // console.log(res)
      if (res.code === 200) {
        that.$message.success('修改成功')
        that.is_edit_ftp = false
        this.getList()
      }
    },
    // 点击修改域名按钮
    changeDomain(host) {
      this.domainData = JSON.parse(JSON.stringify(host))
      this.domainVisible = true
    },
    // 修改自备服务器域名
    async domainHandle() {
      const that = this
      // console.log(that.domainData)
      const { id, domain } = JSON.parse(JSON.stringify(that.domainData))
      if (!id) return that.$message.error('缺少站点id')
      const res = await editDomainApi({ site_id: id, domain })
      // console.log(res)
      if (res.code === 200) {
        that.$message.success('修改成功')
        this.domainVisible = false
        this.getList()
      }
    },
    // 修改所属行业
    editHyTypeBtn(row) {
      this.row = row
      this.isShowEdit = true
      this.category_pid_d = null
      this.category_id_d = null
      if (row.type_pid === 0 && row.type_id !== 0) {
        this.category_pid_d = row.type_id
      } else if (row.type_pid === 0 && row.type_id === 0) {
        this.category_pid_d = null
      } else {
        this.category_pid_d = row.type_pid
        this.category_id_d = row.type_id
      }
      this.detailHyVisible = true
    },
    // 分类选择处理
    getCateP(category_pid) {
      const categories = this.categories
      const newArr = categories.filter((item) => item.id === category_pid)
      let categoryChildren = []
      if (category_pid !== null && category_pid !== '' && newArr.length > 0) categoryChildren = newArr[0].children
      return categoryChildren
    },
    // 修改所属行业 修改
    caseEdit() {
      this.editQuery = {
        id: this.row.id
      }
      this.btn_loading = true
      const category_pid_d = this.category_pid_d
      const category_id_d = this.category_id_d
      if (category_id_d !== null && category_id_d !== '') {
        this.editQuery.type_id = category_id_d
      } else {
        this.editQuery.type_id = category_pid_d
      }
      editHyType(this.editQuery).then(response => {
        this.$message.success('修改成功')
        this.btn_loading = false
        this.detailHyVisible = false
        this.getList()
      })
    },
    // 分配客服点击
    distributionCustomer(row) {
      if (row.customer_id) {
        let customer_id = row.customer_id.toString().split(',')
        customer_id = customer_id.map(item => Number(item))
        row.customer_id = customer_id
        this.customer_id = customer_id
      } else {
        this.customer_id = null
      }
      this.detailData = row
      this.customerDialog = true
    },
    // 获取客服列表
    getCustomerList() {
      this.customersLoading = true
      customerList(this.customerListQuery).then(response => {
        this.customersList = response.data
      }).finally(() => {
        this.customersLoading = false
      })
    },
    // 监听分配客服下拉框选择
    changeCustomerValue(selVal) {
      this.customer_id = selVal
    },
    // 分配客服确定
    assignCustomerSure(site_id) {
      if (this.customer_id) {
        assignCustomer({
          site_id: site_id,
          customer_id: this.customer_id.join(',')
        }).then(response => {
          this.$message.success('操作成功')
          this.customerDialog = false
          this.getList()
        })
      } else {
        this.$message({
          type: 'warning',
          message: '请选择需要分配的客服'
        })
      }
    },
    reSendSite(row) {
      resend(row).then(response => {
        this.$message.success('操作成功')
        this.getList()
      })
    },
    bingDomain(row) {
      // console.log(row)
      this.$router.push({
        name: 'sites-domain',
        query: { id: row.host.id }
      })
    },
    sendTemplateHandle() {
      this.TemplateDialogLoading = true
      sendTemplate(this.ruleForm_template).then(response => {
        this.TemplateDialogVisible = false
        this.$message.success('设置成功')
        this.TemplateDialogLoading = false
        this.getList()
      })
    },
    changeValue(selVal) {
      console.log('变化前：' + this.ruleForm.server_id + '===' + this.servers_id)
      this.ruleForm.server_id = selVal
      this.servers_id = selVal
      console.log('变化后：' + this.ruleForm.server_id + '===' + this.servers_id)
    },
    sendTemplate(row) {
      this.ruleForm_template = row
      this.TemplateDialogVisible = true
    },
    dirUpdatedialog(row) {
      this.ftpDirVisible = true
      this.detailData = row
      this.detailData.ftp_dir = row.host.ftp_dir
      console.log(row)
    },
    dirUpdate(id, dir) {
      console.log(id)
      // console.log(dir)
      dirUpdate({ site_id: id, ftp_dir: dir }).then(response => {
        this.ftpDirVisible = false
        this.$message.success('修改成功')
        this.ftpDirVisible = false
        this.getList()
      })
    },
    change(e) {
      this.$forceUpdate()
    },
    setCaseStatus(value, id) {
      if (this.jztPackageType === '2.0') {
        this.$confirm('确定要提交到案例库吗？', '提示').then(() => {
          setCase({ id, is_case: value }).then(response => {
            this.$message.success('设置成功')
            this.getList()
          })
        }).catch(() => {
        })
      } else {
        setCase({ id, is_case: value }).then(response => {
          this.$message.success('设置成功')
          this.getList()
        })
      }
    },
    detail(row, status) {
      this.detailData = JSON.parse(JSON.stringify(row))
      this.detailData.audit_status = status
      this.detailVisible = true
    },
    handleCopy(text, event) {
      clip(text, event)
    },
    getServers() {
      servers({ all: true }).then(response => {
        this.serverList = response.data
      })
    },
    getList() {
      this.listQuery.all = false
      this.loading = true
      sites(this.listQuery).then(response => {
        this.tableData = response.data.data
        this.total = response.data.total
        this.loading = false
      }).finally(() => {
        this.loading = false
        this.$refs.tableRef.doLayout()
      })
    },
    // 点击过滤 执行方法
    getQueryList() {
      this.listQuery.page = 1
      this.getList()
    },
    auditView(row) {
      this.ruleForm = {}
      this.btn_loading = false
      this.ruleForm.id = row.id
      this.row = row
      this.servers_id = row.host.server_id
      this.ruleForm.state = row.host.state
      this.dialogVisible = true
    },
    auditHandle(row) {
      this.ruleForm.server_id = this.servers_id
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.btn_loading = true
          audit(this.ruleForm).then(response => {
            this.$message.success('审核成功')
            this.dialogVisible = false
            this.btn_loading = false
            if (+this.ruleForm.status === 2) {
              this.$router.push({
                name: 'sites-domain',
                query: { id: this.row.host.id }
              })
            } else {
              this.getList()
            }
          }).catch(() => {
            this.btn_loading = false
          })
        }
      })
    },
    goSite(row) {
      window.open('http://' + row.host.domain)
    },
    previewSite(row) {
      window.open(row.temp_url + '&company_id=' + row.host.company_id + '&site_id=' + row.site_id + '&layout_id=' + row.layout_id)
    },
    getTemplateCates() {
      cates({ all: true }).then(response => {
        this.categories = response.data
      })
    },
    exportExcel() {
      this.listQuery.all = true
      this.downloadLoading = true
      sites(this.listQuery).then(response => {
        import('@/vendor/Export2Excel').then(excel => {
          let tHeader = ['ID', '企业名称', '域名', '审核时间', '提交时间', '分公司', '制作人']
          let filterVal = ['id', 'company_name', 'domain', 'audit_at', 'create_time', 'district_name', 'producer']
          if (this.jztPackageType === '2.0') {
            tHeader = ['ID', '企业名称', '域名', '审核时间', '提交时间', '制作人']
            filterVal = ['id', 'company_name', 'domain', 'audit_at', 'create_time', 'producer']
          }
          const list = response.data
          const data = this.formatJson(filterVal, list)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.filename,
            autoWidth: this.autoWidth,
            bookType: this.bookType
          })
          this.downloadLoading = false
        })
      })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        if (j === 'create_time') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },
    zzd_login(zzdForm) {
      console.log(zzdForm)
      zzd_login(zzdForm).then(response => {
        console.log(response.data)
        this.$message.success('授权成功')
        var curl = response.data
        // window.location.href = curl;
        window.open(curl, '_blank')
      }).catch(() => {
      })
    },
    khd_login(khdForm) {
      khd_login(khdForm).then(response => {
        this.$message.success('授权成功')
        var curl = response.data
        // window.location.href = curl;
        window.open(curl, '_blank')
      }).catch(() => {
      })
    },
    district() {
      districts().then(response => {
        this.districtsOptions = response.data
      })
    },
    // 开启客户端编辑
    setCaseEdit(value, id) {
      setCaseClientEdit({ id, is_edit: value }).then(response => {
        this.$message.success('设置成功')
        this.getList()
      })
    },
    // 退订服务器
    unsubscribeService(row) {
      const that = this
      that.$confirm('确定要退定当前服务器吗？').then(_ => {
        unsubscribe({ id: row.site_id }).then(response => {
          that.$message.success('退订成功')
          that.getList()
        })
      }).catch(_ => { })
    }
  }
}
</script>

<style scoped>
.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}

.el-icon-arrow-down {
  font-size: 12px;
}
</style>

<style lang="scss" scoped>
.list-wrap{
  .filter{
    border-bottom: 1px solid #e5e5e5;
  }
}
</style>
