<script>
import SelfCard from '@/views/dashboard/admin/components/SelfCard.vue'
import WorkOrderList from '@/views/workorder/list.vue'

export default {
  name: 'LatestTickets',
  components: { WorkOrderList, SelfCard },
  methods: {
  }
}
</script>

<template>
  <SelfCard title="最新工单" :show-more="true" class="latest-tickets">
    <WorkOrderList style="height: 228px" :show-page="false" :simple="true" class="latest" />
  </SelfCard>
</template>

<style scoped lang="scss">
.latest-tickets {
  height: 100%;
  .latest{
    ::v-deep .el-card__body {
      padding: 0;
    }
    ::v-deep .el-link.el-link--primary{
      color: #4D80FF;
    }
  }
}
</style>
