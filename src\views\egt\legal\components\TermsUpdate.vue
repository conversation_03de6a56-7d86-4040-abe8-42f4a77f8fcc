<script>
import SelfFormTemp from '@/views/task/components/form/SelfFormTemp.vue'
import update from '@/views/task/mixins/update'
import { addContractClauseApi, getContractClauseUpdateApi, updateContractClauseApi } from '@/api/egt/contract'

export default {
  name: 'TermsUpdate',
  components: { SelfFormTemp },
  mixins: [update],
  data() {
    return {
      filters: [
        {
          label: '条款名称',
          type: 'input',
          prop: 'title'
        },
        {
          label: '排序',
          type: 'number',
          prop: 'sort_id'
        },
        {
          label: '隐藏标题',
          type: 'switch',
          prop: 'hide_title'
        }
      ],
      formData: {
        id: '',
        title: '',
        sort_id: 0,
        hide_title: 0
      },
      rules: {
        title: [
          { required: true, message: '请输入条款名称', trigger: 'blur' }
        ],
        sort_id: [
          { required: true, message: '请输入排序', trigger: 'blur' }
        ],
        hide_title: [
          { required: true, message: '请选择隐藏标题', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    _visible(val){
      if (val) {
        this.getParams()
      }
    }
  },
  methods: {
    async getParams() {
      const api = this.data.id ? getContractClauseUpdateApi : undefined
      if (!api) return
      try {
        this.loading = true
        const res = await api({ id: this.data.id })
        if (res.code === 200 && res.data) {
          this.formData = res.data.data
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false
      }
    },
    async submitCallback() {
      const api = this.data.id ? updateContractClauseApi : addContractClauseApi
      if (!api) return
      this.formData.model_id = this.data.model_id
      if (this.data.id) {
        this.formData.id = this.data.id
      }
      try {
        this.submitLoading = true
        const postData = JSON.parse(JSON.stringify(this.formData))
        postData.item_title = postData.title
        delete postData.title
        const res = await api(postData)
        if (res.code === 200) {
          this.$emit('submit-success')
          return Promise.resolve({ success: true })
        } else {
          return Promise.reject({ success: false, msg: this.data.id? '编辑失败' : '新增失败' })
        }
      } catch (error) {
        return Promise.reject({ success: false, msg: this.data.id? '编辑失败' : '新增失败' })
      } finally {
        this.submitLoading = false
      }
    }
  }
}
</script>

<template>
  <el-dialog :close-on-click-modal="false" :title="title + '合同条款'" :visible.sync="_visible" append-to-body>
    <self-form-temp ref="formWrap" :filters="filters" :form-data.sync="formData" :rules="rules">
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </div>
    </self-form-temp>
    <div slot="footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit">保存</el-button>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
