<script>
export default {
  name: 'Layout',
  props: {
    btns: {
      type: Array,
      default: () => []
    },
    active: {
      type: String,
      default: ''
    }
  },
  computed: {
    activeBtn: {
      get() {
        return this.active || this.btns[0].value
      },
      set(val) {
        this.$emit('update:active', val)
      }
    },
    activeComponent() {
      return this.btns.find(btn => btn.value === this.activeBtn).component
    },
    activeBtnData() {
      return this.btns.find(btn => btn.value === this.activeBtn)
    }
  }
}
</script>

<template>
  <div style="height: 100%">
    <div class="btns">
      <div class="radio-wrap">
        <el-radio-group v-model="activeBtn">
          <el-radio-button v-for="btn in btns" :key="btn.value" :label="btn.value">
            {{ btn.label }}
          </el-radio-button>
        </el-radio-group>
      </div>
      <div class="actions">
        <slot name="actions" />
      </div>
    </div>
    <div class="content mt20">
      <component :is="activeComponent" :id="activeBtn" ref="listRef" :active-data="activeBtnData" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.btns {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .radio-wrap{
    flex: 1;
  }
}
.content {
  height: calc(100% - 36px - 20px);
}
</style>
