<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="50%">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="文案类型" prop="title">
        <el-input v-model="form.title" placeholder="请输入文案类型"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { cateStoreApi } from '@/api/aigc';

export default {
  name: 'AiDocumentEditCate',
  data() {
    return {
      dialogTitle: '',
      dialogVisible: false,
      form: {
        pid: 0,
        title: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入文案类型', trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    // 打开弹窗
    openDialog(row = null) {
      if (row) {
        this.dialogTitle = '编辑文案类型'
        this.form = {
          id: row.id,
          pid: row.pid,
          title: row.title
        }
      } else {
        this.dialogTitle = '添加文案类型'
        this.form = {
          id: '',
          pid: 0,
          title: ''
        }
      }
      this.dialogVisible = true;
    },
    // 提交表单
    submitForm() {
      this.$refs.formRef.validate(valid => {
        console.log('表单验证结果:', valid);
        if (!valid) return;
        if (valid) {
          cateStoreApi(this.form).then(() => {
            this.$message.success('操作成功');
            this.$store.dispatch('aigc/fetchCategories'); // 刷新文案类型列表
            this.$emit('refresh'); // 通知父组件刷新数据
            this.dialogVisible = false;
          }).catch(error => {
            console.log(`操作失败: ${error.message}`);
          })
        } else {
          console.error('表单验证失败');
        }
      });
    }
  }
};
</script>