/*eslint-disable*/
import requestOpen from "@/utils/requestOpen";

// 获取公司列表
export function getCompanyAuthList (data) {
  return requestOpen({
    url: '/tripartite/customized',
    method: 'post',
    data
  })
}

// 获取公司权限
export function getCompanyAuthTree (data) {
  return requestOpen({
    url: '/tripartite/customizedPower',
    method: 'post',
    data
  })
}

// 根据公司获取权限
export function getCompanyAuth (data) {
  return requestOpen({
    url: '/tripartite/customizedModInfo',
    method: 'post',
    data
  })
}

// 保存
export function saveCompanyAuth (data) {
  return requestOpen({
    url: '/tripartite/customizedMod',
    method: 'post',
    data
  })
}
