<script>
/*eslint-disable*/
import {addCount, countDetail, getStoreList} from "@/api/appDownload";

export default {
  name: "CountDetail",
  props: ['id', 'data'],
  watch: {
    id: {
      handler(v){
        console.log(v);
        if (v){
          this.handleGetDetail(v);
        }
      },
      deep: true,
      immediate: true
    }
  },
  data(){
    return {
      loading: false,
      form: {
        marketname: undefined,
        apptypename: undefined,
        downloads: 0,
        totalday: ''
      },
      rules: {},
      storeList: [],
      appTypeList: [
        {
          id: 1,
          name: "Android"
        },
        {
          id: 2,
          name: "IOS"
        },
      ]
    }
  },
  methods: {
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          addCount(this.form).then(res => {
            if (res.code === 200){
              this.$message.success(res.msg || res.message || '保存成功');
              this.close(true);
            }else{
              this.$message.error(res.msg || res.message || '保存失败');
            }
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    close(refresh){
      if (typeof refresh !== 'boolean'){
        refresh = false;
      }
      this.$refs.form.resetFields();
      this.$emit("closeDialog", refresh);
    },
    handleGetStoreList() {
      this.loading = true;
      getStoreList().then(res => {
        if (res.code === 200 && res.data) {
          this.storeList = res.data;
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    handleGetDetail(id){
      this.loading = true;
      countDetail({id}).then(res => {
        if (res.code && res.data){
          this.form = res.data;
        }
      }).finally(() => {
        this.loading = false;
      })
    }
  },
  mounted() {
    this.handleGetStoreList();
  }
}
</script>

<template>
<div id="addCount">
  <el-form :model="form" ref="form" label-width="100px">
    <el-form-item label="应用市场" prop="marketname">
      {{form.marketname}}
    </el-form-item>
    <el-form-item label="操作系统" prop="apptypename">
      {{form.apptypename}}
    </el-form-item>
    <el-form-item label="下载量" prop="downloads">
      {{form.downloads}}
    </el-form-item>
    <el-form-item label="统计日期" prop="totalday">
      {{form.totalday}}
    </el-form-item>
  </el-form>
</div>
</template>

<style scoped>

</style>
