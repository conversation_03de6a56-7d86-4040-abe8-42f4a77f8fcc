import request from '@/utils/request'

export function jianzhantongIndex(data) {
  return request({
    url: '/otherProduct/jianzhantong/index',
    method: 'post',
    data: data
  })
}

export function jianzhantongIframe(data) {
  return request({
    url: '/otherProduct/jianzhantong/iframe',
    method: 'post',
    data: data
  })
}

export function jianzhantongDestory(data) {
  return request({
    url: '/otherProduct/jianzhantong/destory',
    method: 'post',
    data: data
  })
}
export function jianzhantongDomain(data) {
  return request({
    url: '/otherProduct/jianzhantong/domain',
    method: 'post',
    data: data
  })
}

export function sslstore(data) {
  return request({
    url: '/otherProduct/jianzhantong/sslstore',
    method: 'post',
    data: data
  })
}

export function sslstorelist(data) {
  return request({
    url: '/otherProduct/jianzhantong/sslstorelist',
    method: 'post',
    data: data
  })
}

export function sslupdate(data) {
  return request({
    url: '/otherProduct/jianzhantong/sslupdate',
    method: 'post',
    data: data
  })
}

export function ssfileList(data) {
  return request({
    url: '/otherProduct/jianzhantong/ssllist',
    method: 'post',
    data: data
  })
}

export function ssldown(data) {
  return request({
    url: '/otherProduct/jianzhantong/ssldown',
    method: 'post',
    data: data
  })
}

export function sslupload(data) {
  return request({
    url: '/otherProduct/jianzhantong/sslupload',
    method: 'post',
    data: data
  })
}

export function ipstore(data) {
  return request({
    url: '/otherProduct/jianzhantong/ipstore',
    method: 'post',
    data: data
  })
}

export function downexportExcel(data) {
  return request({
    url: '/otherProduct/jianzhantong/downexportExcel',
    method: 'post',
    data: data
  })
}

export function jwpsitelist(data) {
  return request({
    url: '/otherProduct/jianzhantong/jwpsitelist',
    method: 'post',
    data: data
  })
}

export function jwpsiteshow(data) {
  return request({
    url: '/otherProduct/jianzhantong/jwpsiteshow',
    method: 'post',
    data: data
  })
}
