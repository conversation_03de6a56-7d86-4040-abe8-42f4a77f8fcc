<template>
  <el-form ref="ruleForm" v-loading="formLoading" :model="custType === 'CUST_PERSONAL'?ruleForm_person:ruleForm_org" :rules="custType==='CUST_PERSONAL'?rules_person:rules_org" label-width="220px" class="demo-ruleForm">
    <el-row>
      <el-col style="max-width: 470px">
        <el-form-item label="域名持有者类型：" prop="legal_person">
          {{ ruleForm_person.custType=='CUST_PERSONAL'?'个人':'企业' }}
        </el-form-item>
        <el-divider content-position="left">持有者基本信息</el-divider>
        <div :hidden="custType !== 'CUST_PERSONAL'">
          <el-form-item label="持有者名称(中文)：" prop="custName">
            {{ ruleForm_person.custName }}
          </el-form-item>
          <el-form-item label="持有者名称(英文/拼音)：" prop="custNameEn">
            {{ ruleForm_person.custNameEn }}
          </el-form-item>
          <el-form-item label="所在省市：" prop="agentArea">
            {{ province }} {{ city }}
          </el-form-item>
          <el-form-item label="街道地址(中文)：" prop="street">
            {{ ruleForm_person.street }}
          </el-form-item>
          <el-form-item label="街道地址(英文/拼音)：" prop="streetEn">
            {{ ruleForm_person.streetEn }}
          </el-form-item>
          <el-form-item label="邮编：" prop="zipCode">
            {{ ruleForm_person.zipCode }}
          </el-form-item>
          <el-form-item label="电子邮箱：" prop="email">
            {{ ruleForm_person.email }}
          </el-form-item>
          <el-divider content-position="left">联系人信息</el-divider>
          <el-form-item label="联系人名称(中文)：" prop="linkman">
            {{ ruleForm_person.linkman }}
          </el-form-item>
          <el-form-item label="联系人名称(英文/拼音)：" prop="linkmanEn">
            {{ ruleForm_person.linkmanEn }}
          </el-form-item>
          <el-form-item label="手机号码：" prop="mobile">
            {{ ruleForm_person.mobile }}
          </el-form-item>
          <el-form-item label="备注：" prop="memo">
            {{ ruleForm_person.memo }}
          </el-form-item>
          <el-form-item label="身份证件资料：">
            <el-image :src="ruleForm.idImgUrl" fit="fill" :preview-src-list="[ruleForm.idImgUrl]" style="width: 190px;height: 130px" />
          </el-form-item>
          <el-divider content-position="left">CNNIC证件信息</el-divider>
          <el-form-item label="证件类型：" prop="certType">
            {{ ruleForm_person.certType }}
          </el-form-item>
          <el-form-item label="证件号码：" prop="certNumber">
            {{ ruleForm_person.certNumber }}
          </el-form-item>
          <el-divider content-position="left">核验库证件信息</el-divider>
          <el-form-item label="证件类型：" prop="orgIdType">
            {{ ruleForm_person.orgIdType }}
          </el-form-item>
          <el-form-item label="证件号码：" prop="orgIdCode">
            {{ ruleForm_person.orgIdCode }}
          </el-form-item>
          <el-form-item label="身份证件资料：">
            <el-image :src="ruleForm.idImgUrl" fit="fill" :preview-src-list="[ruleForm.idImgUrl]" style="width: 190px;height: 130px" />
          </el-form-item>
        </div>
        <div :hidden="custType === 'CUST_PERSONAL'">
          <el-form-item label="持有者单位名称(中文)：" prop="custName">
            {{ ruleForm_org.custName }}
          </el-form-item>
          <el-form-item label="持有者单位名称(英文/拼音)：" prop="custNameEn">
            {{ ruleForm_org.custNameEn }}
          </el-form-item>
          <el-form-item label="营业年限：" prop="operAge">
            {{ ruleForm_org.operAge }}
          </el-form-item>
          <el-form-item label="企业规模：" prop="firmSize">
            {{ ruleForm_org.firmSize }}
          </el-form-item>
          <el-form-item label="行业分类：" prop="industryType">
            {{ ruleForm_org.industryType }}
          </el-form-item>
          <el-form-item label="主营产品：" prop="mainBusiness">
            {{ ruleForm_org.mainBusiness }}
          </el-form-item>
          <el-form-item label="网址：" prop="url">
            {{ ruleForm_org.url }}
          </el-form-item>
          <el-form-item label="所在省市：" prop="agentArea">
            {{ province }} {{ city }}
          </el-form-item>
          <el-form-item label="街道地址(中文)：" prop="street">
            {{ ruleForm_org.street }}
          </el-form-item>
          <el-form-item label="街道地址(英文/拼音)：" prop="streetEn">
            {{ ruleForm_org.streetEn }}
          </el-form-item>
          <el-form-item label="邮编：" prop="zipCode">
            {{ ruleForm_org.zipCode }}
          </el-form-item>
          <el-form-item label="固定电话：" prop="firstNum1">
            {{ ruleForm_org.firstNum1 }}{{ ruleForm_org.lastNum1 }}
          </el-form-item>
          <el-form-item prop="lastNum1" />
          <el-form-item label="传真：" prop="fax">
            {{ ruleForm_org.fax }}
          </el-form-item>
          <el-form-item label="电子邮箱：" prop="email">
            {{ ruleForm_org.email }}
          </el-form-item>
          <el-divider content-position="left">联系人信息</el-divider>
          <el-form-item label="联系人名称(中文)：" prop="linkman">
            {{ ruleForm_org.linkman }}
          </el-form-item>
          <el-form-item label="联系人名称(英文/拼音)：" prop="linkmanEn">
            {{ ruleForm_org.linkmanEn }}
          </el-form-item>
          <el-form-item label="手机号码：" prop="mobile">
            {{ ruleForm_org.mobile }}
          </el-form-item>
          <el-form-item label="备注：" prop="memo">
            {{ ruleForm_org.memo }}
          </el-form-item>
          <el-form-item label="身份证件资料：">
            <el-image :src="ruleForm.idImgUrl" fit="fill" :preview-src-list="[ruleForm.idImgUrl]" style="width: 190px;height: 130px" />
          </el-form-item>
          <el-divider content-position="left">CNNIC证件信息</el-divider>
          <el-form-item label="证件类型：" prop="certType">
            {{ ruleForm_org.certType }}
          </el-form-item>
          <el-form-item label="证件号码：" prop="certNumber">
            {{ ruleForm_org.certNumber }}
          </el-form-item>
          <el-divider content-position="left">核验库证件信息</el-divider>
          <el-form-item label="证件类型：" prop="orgIdType">
            {{ ruleForm_org.orgIdType }}
          </el-form-item>
          <el-form-item label="证件号码：" prop="orgIdCode">
            {{ ruleForm_org.orgIdCode }}
          </el-form-item>
          <el-form-item label="实名认证资料：">
            <el-image :src="ruleForm.orgImgUrl" fit="fill" :preview-src-list="[ruleForm.orgImgUrl]" style="width: 190px;height: 130px" />
          </el-form-item>
        </div>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import { idcCity, detail, certification } from '@/api/agent/domain'
export default {
  name: 'DomainTelement',
  data() {
    return {
      formLoading: false,
      id: this.$route.query.id,
      t_id: this.$route.query.t_id,
      disable: !!this.$route.query.t_id,
      ruleForm_person: {},
      agentArea: '',
      province: '',
      city: '',
      ruleForm_org: {
        firstNum1: '0451',
        lastNum1: '83152780'
      },
      ruleForm: {
        idImgUrl: '',
        orgImgUrl: ''
      },
      provinceAndCityData: [],
      activeName: 'first',
      custType: 'CUST_PERSONAL',
      rules_person: {
        custName: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        custNameEn: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        agentArea: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        street: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        streetEn: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        zipCode: [{ required: false, message: '必填', trigger: ['blur', 'change'] }],
        email: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        linkman: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        linkmanEn: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        mobile: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        memo: [{ required: false, message: '必填', trigger: ['blur', 'change'] }],
        certType: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        certNumber: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        orgIdType: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        orgIdCode: [{ required: true, message: '必填', trigger: ['blur', 'change'] }]
      },
      rules_org: {
        custName: [{ required: true, message: '必填', trigger: 'blur' }],
        custNameEn: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        operAge: [{ required: false, message: '必填', trigger: 'blur' }],
        firmSize: [{ required: false, message: '必填', trigger: 'blur' }],
        industryType: [{ required: false, message: '必填', trigger: 'change' }],
        mainBusiness: [{ required: false, message: '必填', trigger: ['blur', 'change'] }],
        url: [{ required: false, message: '必填', trigger: 'blur' }],
        agentArea: [{ required: true, message: '必选', trigger: 'change' }],
        street: [{ required: true, message: '必选', trigger: 'blur' }],
        streetEn: [{ required: true, message: '必选', trigger: ['blur', 'change'] }],
        zipCode: [{ required: false, message: '必选', trigger: 'blur' }],
        firstNum1: [{ required: true, message: '必选', trigger: ['blur', 'change'] }],
        lastNum1: [{ required: true, message: '必选', trigger: ['blur', 'change'] }],
        fax: [{ required: false, message: '必选', trigger: ['blur', 'change'] }],
        email: [{ required: true, message: '必选', trigger: 'blur' }],
        linkman: [{ required: true, message: '必填', trigger: 'blur' }],
        linkmanEn: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        mobile: [{ required: true, message: '必填', trigger: 'blur' }],
        memo: [{ required: false, message: '必填', trigger: 'blur' }],
        certType: [{ required: true, message: '必填', trigger: 'change' }],
        certNumber: [{ required: true, message: '必填', trigger: 'blur' }],
        orgIdType: [{ required: true, message: '必填', trigger: 'change' }],
        orgIdCode: [{ required: true, message: '必填', trigger: 'blur' }]
      },
      scales: [
        {
          'label': '少于50人',
          'value': 'FIRM_SIZE_01'
        },
        {
          'label': '50-150人',
          'value': 'FIRM_SIZE_02'
        },
        {
          'label': '150-500人',
          'value': 'FIRM_SIZE_03'
        },
        {
          'label': '500-1000人',
          'value': 'FIRM_SIZE_04'
        },
        {
          'label': '1000-5000人',
          'value': 'FIRM_SIZE_05'
        },
        {
          'label': '5000-10000人',
          'value': 'FIRM_SIZE_06'
        },
        {
          'label': '10000人以上',
          'value': 'FIRM_SIZE_07'
        }
      ],
      trades: [
        {
          'label': 'IT/通信/电子/互联网',
          'value': 'BUSINESS_CLASSIFY_01'
        },
        {
          'label': '教育/培训',
          'value': 'BUSINESS_CLASSIFY_03'
        },
        {
          'label': '传媒/文化/出版/印刷',
          'value': 'BUSINESS_CLASSIFY_07'
        },
        {
          'label': '农业牧渔',
          'value': 'BUSINESS_CLASSIFY_14'
        },
        {
          'label': '能源/电器/地质',
          'value': 'BUSINESS_CLASSIFY_13'
        },
        {
          'label': '金融/保险/证券',
          'value': 'BUSINESS_CLASSIFY_09'
        },
        {
          'label': '房地产/建筑',
          'value': 'BUSINESS_CLASSIFY_04'
        },
        {
          'label': '贸易/批发/零售',
          'value': 'BUSINESS_CLASSIFY_05'
        },
        {
          'label': '商业服务/咨询/法律/财务',
          'value': 'BUSINESS_CLASSIFY_06'
        },
        {
          'label': '加工制造/机械/仪表设备',
          'value': 'BUSINESS_CLASSIFY_02'
        },
        {
          'label': '酒店/餐饮/旅游',
          'value': 'BUSINESS_CLASSIFY_12'
        },
        {
          'label': '制药医疗/生物/卫生保健',
          'value': 'BUSINESS_CLASSIFY_11'
        },
        {
          'label': '汽车/交通运输/仓储',
          'value': 'BUSINESS_CLASSIFY_10'
        },
        {
          'label': '政府/非盈利机构',
          'value': 'BUSINESS_CLASSIFY_08'
        },
        {
          'label': '其他',
          'value': 'BUSINESS_CLASSIFY_00'
        }
      ],
      PersonCertType: [
        {
          'label': '身份证',
          'value': 'SFZ'
        },
        {
          'label': '军官证',
          'value': 'JGZ'
        }, {
          'label': '护照',
          'value': 'HZ'
        }
      ],
      OrgCertType: [
        {
          'label': '营业执照',
          'value': 'YYZZ'
        },
        {
          'label': '组织机构代码证',
          'value': 'ORG'
        },
        {
          'label': '统一社会信用代码证书',
          'value': 'TYDM'
        },
        {
          'label': '事业单位法人证书',
          'value': 'SYDWFR'
        },
        {
          'label': '社会团体法人登记证书',
          'value': 'SHTTFR'
        },
        {
          'label': '民办非企业单位登记证书',
          'value': 'MBFQY'
        },
        {
          'label': '律师事务所执业许可证',
          'value': 'LSZY'
        },
        {
          'label': '社会服务机构登记证书',
          'value': 'SHFWJG'
        },
        {
          'label': '民办学校办学许可证',
          'value': 'MBXXBX'
        },
        {
          'label': '其他',
          'value': 'QT'
        },
        {
          'label': '其他证件，只允许提交统一社会信用代码',
          'value': 'QTTYDM'
        }
      ],
      PersonOrgIdType: [
        {
          'label': '身份证',
          'value': 'HPT_SFZ'
        },
        {
          'label': '护照',
          'value': 'HPT_HZ'
        },
        {
          'label': '港澳居民来往内地通行证',
          'value': 'HPT_GATXZ'
        },
        {
          'label': '台湾居民来往大陆通行证',
          'value': 'HPT_TJLTXZ'
        },
        {
          'label': '外国人永久居留身份证',
          'value': 'HPT_WYJSFZ'
        }
      ],
      OrgOrgIdType: [
        {
          'label': '工商营业执照',
          'value': 'HT_YYZZ'
        },
        {
          'label': '组织机构代码证',
          'value': 'HT_ORG'
        }, {
          'label': '统一社会信用代码证书',
          'value': 'HT_XYDM'
        }, {
          'label': '部队代号',
          'value': 'HT_BDDH'
        }, {
          'label': '军队单位对外有偿服务许可证',
          'value': 'HT_JYCXKZ'
        },
        {
          'label': '事业单位法人证书',
          'value': 'HT_SYZS'
        },
        {
          'label': '外国企业常驻代表机构登记证',
          'value': 'HT_WQCZDBJGZ'
        },
        {
          'label': '社会团体法人登记证书',
          'value': 'HT_STFDZ'
        },
        {
          'label': '宗教活动场所登记证',
          'value': 'HT_ZHCDZ'
        },
        {
          'label': '民办非企业单位登记证书',
          'value': 'HT_MFQDJZ'
        },
        {
          'label': '基金会法人登记证书',
          'value': 'HT_JFDJZ'
        },
        {
          'label': '律师事务所执业许可证',
          'value': 'HT_LSZYXKZ'
        },
        {
          'label': '外国在华文化中心登记证',
          'value': 'HT_WZWZDJZ'
        },
        {
          'label': '外国政府旅游部门常驻代表机构批准登记证',
          'value': 'HT_WZLBCDJPDJZ'
        },
        {
          'label': '民办学校办学许可',
          'value': 'HT_MBXXBX'
        },
        {
          'label': '司法鉴定许可证',
          'value': 'HT_SFJXKZ'
        }
      ]
    }
  },
  created() {
    if (this.t_id) {
      this.detail()
      this.getDetailInfo()
    }
  },
  methods: {
    getOptions() {
      idcCity().then(res => {
        this.provinceAndCityData = res.data
        var province = this.province
        var city = this.city
        res.data.forEach(function(item) {
          if (province === item['id']) {
            province = item['name']
            if (item['children']) {
              item['children'].forEach(function(item1) {
                if (city === item1['id']) {
                  city = item1['name']
                }
              })
            }
          }
        })
        this.province = province
        this.city = city
        console.log(province)
        console.log(city)
      })
    },
    getDetailInfo() {
      this.formLoading = true
      certification({ id: this.id, t_id: this.t_id }).then(res => {
        this.formLoading = false
        if (res.data.certification) {
          res.data.certification['idImgUrl'] = 'https://images.china9.cn/' + res.data.certification['idImg']
          res.data.certification['orgImgUrl'] = 'https://images.china9.cn/' + res.data.certification['orgImg']
          this.ruleForm = res.data.certification
        }
      }).catch(() => {
        this.formLoading = false
      })
    },
    detail() {
      this.formLoading = true
      detail({ id: this.id, t_id: this.t_id }).then(res => {
        this.formLoading = false
        this.scales.forEach(function(item) {
          if (item['value'] === res.data.base.firmSize) {
            res.data.base.firmSize = item['label']
          }
        })
        this.trades.forEach(function(item) {
          if (item['value'] === res.data.base.industryType) {
            res.data.base.industryType = item['label']
          }
        })
        this.agentArea = ''

        res.data.base.agentArea = [res.data.base.province, res.data.base.city]
        this.province = parseInt(res.data.base.province)
        this.city = parseInt(res.data.base.city)
        res.data.template_id = this.t_id
        this.custType = res.data.base.custType
        if (this.custType === 'CUST_PERSONAL') {
          this.PersonCertType.forEach(function(item) {
            if (item['value'] === res.data.base.certType) {
              res.data.base.certType = item['label']
            }
          })
          this.PersonOrgIdType.forEach(function(item) {
            if (item['value'] === res.data.base.orgIdType) {
              res.data.base.orgIdType = item['label']
            }
          })
          this.ruleForm_person = res.data.base
        } else {
          this.OrgCertType.forEach(function(item) {
            if (item['value'] === res.data.base.certType) {
              res.data.base.certType = item['label']
            }
          })
          this.OrgOrgIdType.forEach(function(item) {
            if (item['value'] === res.data.base.orgIdType) {
              res.data.base.orgIdType = item['label']
            }
          })
          this.ruleForm_org = res.data.base
        }
        this.getOptions()
      }).catch(() => {
        this.formLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .demo-ruleForm{
    margin-top: 50px;
    margin-left: 50px;
  }
>>>.el-divider{
  margin-top: 60px;
  margin-bottom: 60px;
}
>>>.el-divider__text{
  font-size: 20px;
}
 .el-icon-circle-close{
    color:#f00!important;
  }
</style>
