<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" style="display: flex; justify-content: space-between; align-items: center;">
        <!-- <span>文章分类</span> -->
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
        <el-button style="padding: 3px 0" type="text" @click="addCategory">新增分类</el-button>
      </div>
      <el-table :data="tableData" stripe style="width: 100%">
        <el-table-column label="名称" prop="name" />
        <el-table-column label="排序" prop="level" />
        <el-table-column label="级别" prop="top" />
        <el-table-column label="状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 1" type="primary">启用</el-tag>
            <el-tag v-if="scope.row.status === 0" type="danger">停用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="update(scope.row)">编辑</el-button>
            <el-button type="text" @click="destory(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <cateForm :dialog-visible="dialogVisible" :form="form" @transfer="updateOrCreate" />
  </div>
</template>

<script>
import cateForm from './components/cate-form'
import { list, store, edit, destory } from '../../api/developer_doc_category'
export default {
  name: 'NewsCateList',
  components: { cateForm },
  data() {
    return {
      tableData: [],
      form: {},
      id: '',
      dialogVisible: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.dialogVisible = false
      list().then(response => {
        console.log(response.data)
        this.tableData = response.data
      })
    },
    addCategory() {
      this.form = {
        'name': '',
        'pid': 0,
        'level': 0,
        'status': 1
      }
      this.id = null
      this.dialogVisible = true
      this.$forceUpdate()
    },
    update(form) {
      this.form = form
      this.id = form.id
      this.dialogVisible = true
      this.$forceUpdate()
    },
    updateOrCreate(form) {
      console.log(form)
      if (form.id) {
        edit(form).then(response => {
          this.$message.success('更新成功')
          this.getList()
        })
      } else {
        store(form).then(response => {
          this.$message.success('新增成功')
          this.getList()
        })
      }
    },
    destory(id) {
      this.$alert('删除后不可恢复', '确定删除么', {
        confirmButtonText: '确定',
        callback: action => {
          if (action === 'confirm') {
            destory({ id: id }).then(response => {
              this.$message.success('删除成功')
              this.getList()
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .list-wrap {
    height: 100%;

    ::v-deep .el-tabs {
      height: 100%;

      .el-tabs__content {
        height: calc(100% - 41px);

        .el-tab-pane {
          height: 100%;
        }
      }
    }

    ::v-deep .box-card {
      height: 100%;
      border: none;

      .el-card__header {
        padding-top: 0;
      }

      .el-card__body {
        height: calc(100% - 59px);
        overflow: auto;
        padding: 0;
        margin-top: 20px;
      }
    }
  }
</style>
