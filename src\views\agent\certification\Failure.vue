<script>
import update from '@/views/task/mixins/update'
import { invalidCertificate } from '@/api/agent/certificate'

export default {
  name: 'Failure',
  mixins: [update],
  data() {
    return {
      formData: {
        remark: ''
      },
      rules: {
        remark: [
          { required: true, message: '请输入失效原因', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    submitCallback() {
      this.$confirm({
        title: '提示',
        message: '请核对企业名称， 是否继续？',
        confirm: async() => {
          try {
            this.loading = true
            const postData = {
              id: this.data.id,
              remark: this.formData.remark,
              phone: this.data.phone
            }
            const res = await invalidCertificate(postData)
            if (res.code === 200) {
              this.$message({
                message: '失效成功',
                type: 'success'
              })
              this.handleCancel()
            } else {
              this.$message({
                message: res.msg || '失效失败',
                type: 'warning'
              })
            }
          } catch (e) {
            console.log(e)
          } finally {
            this.loading = false
          }
        }
      })
    }
  }
}
</script>

<template>
  <el-dialog :close-on-click-modal="false" title="失效企业" :visible.sync="_visible" width="500px">
    <el-form ref="form" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="失效原因" prop="remark">
        <el-input v-model="formData.remark" type="textarea" :autosize="{ minRows: 3, maxRows: 5 }" placeholder="请输入失效原因" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
