<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import { resultTableColumns } from '../../mixins/resultTable'
import OtherResultUpdate from './OtherResultUpdate.vue'
import { deleteOtherTestResultListApi, getOtherTestResultListApi } from '@/api/egt/recruitment'
import { rateMap } from '@/views/egt/hr/utils/rateMap'

export default {
  name: 'OtherResult',
  components: {
    StatisticsTemplate,
    OtherResultUpdate
  },
  data() {
    return {
      config: {
        key: 'otherResult',
        tableSettings: {
          api: getOtherTestResultListApi,
          field: {
            page: 'pageIndex',
            limit: 'pageSize',
            total: 'total'
          },
          columns: [
            {
              label: 'ID',
              prop: 'id',
              width: 60
            },
            {
              label: '评分标准',
              prop: 'type',
              width: 100,
              isSlot: true
            },
            ...resultTableColumns
          ]
        }
      },
      updateVisible: false,
      row: {}
    }
  },
  methods: {
    handleAdd() {
      this.updateVisible = true
      this.row = {}
    },
    handleUpdate(row) {
      this.updateVisible = true
      this.row = row
    },
    async handleDelete(row) {
      if (!row.id) return
      try {
        this.$refs.listWrapRef.setLoading(row.id, true)
        const res = await deleteOtherTestResultListApi({ id: row.id })
        if (res.code === 200) {
          this.$message.success('删除成功')
          await this.$refs.listWrapRef.handleGetData()
        } else {
          this.$message.error(res.msg || res.message || '删除失败')
        }
      } catch (e) {
        console.log(e)
        this.$message.error('删除失败')
      } finally {
        this.$refs.listWrapRef.setLoading(row.id, false)
      }
    },
    getRate(type) {
      if (!type) return ''
      return rateMap[type] || ''
    },
    submitSuccess() {
      this.$refs.listWrapRef.handleGetData()
    }
  }
}
</script>

<template>
  <div class="list">
    <statistics-template ref="listWrapRef" :config="config">
      <template #topActions>
        <div style="margin-bottom: 10px;text-align: right;width: 100%;">
          <el-button type="primary" @click="handleAdd">添加</el-button>
        </div>
      </template>
      <template #otherResult_type="{row}">
        {{ getRate(row.type) }}
      </template>
      <template #otherResult_action="{row}">
        <el-button size="mini" @click="handleUpdate(row)">修改</el-button>
        <el-popconfirm
          title="确定删除吗？"
          @onConfirm="handleDelete(row)"
        >
          <el-button slot="reference" size="mini" type="danger">删除</el-button>
        </el-popconfirm>
      </template>
    </statistics-template>
    <OtherResultUpdate
      :visible.sync="updateVisible"
      :data="row"
      @submitSuccess="submitSuccess"
    />
  </div>
</template>

<style scoped lang="scss">
.list{
  height: 100%;
}
</style>
