import request from '@/utils/requestJzt'

export function overview(data) {
  return request({
    url: '/statistics/overview',
    method: 'post',
    data
  })
}

export function sites(data) {
  return request({
    url: '/statistics/sites',
    method: 'post',
    data
  })
}

export function templateRankList(data) {
  return request({
    url: '/statistics/template',
    method: 'post',
    data
  })
}
export function loginLogList(data) {
  return request({
    url: '/statistics/loginlog',
    method: 'post',
    data
  })
}

export function templateRankDetail(data) {
  return request({
    url: '/statistics/templateDetail',
    method: 'post',
    data
  })
}
export function templateRanks(data) {
  return request({
    url: '/statistics/template_rank',
    method: 'post',
    data
  })
}

export function saffRankList(data) {
  return request({
    url: '/statistics/saffRank',
    method: 'post',
    data
  })
}

export function fighting(data) {
  return request({
    url: '/statistics/fighting',
    method: 'post',
    data
  })
}
export function managesite(data) {
  return request({
    url: '/statistics/managesite',
    method: 'post',
    data
  })
}

// 代理商站点列表 domain（域名筛选）、company_name（公司名称筛选）、web_name（站点名称筛选）、send_times（提交时间筛选   时间段格式）、dls_id（代理商筛选）、limit、page
export function agentSites(data) {
  return request({
    url: '/statistics/dlsSite',
    method: 'post',
    data
  })
}

// 代理商列表 limit、page
export function agents(data) {
  return request({
    url: '/statistics/dlsLst',
    method: 'post',
    data
  })
}

// 站点设置代理商  site_id、dls_id
export function setAgent(data) {
  return request({
    url: '/statistics/setDls',
    method: 'post',
    data
  })
}

// 同步云经理统计 分公司
export function agentCount(data) {
  return request({
    url: '/statistics/pubLst',
    method: 'post',
    data
  })
}

// AI搜索统计
export function aiSearch(data) {
  return request({
    url: '/statistics/aisearch',
    method: 'post',
    data
  })
}

// 综合统计 站点数据统计 数据
export function siteDataApi(data) {
  return request({
    url: '/zhymans/tongji',
    method: 'post',
    data
  })
}

// 综合统计 站点数据统计 pv uv
export function siteLineDataApi(data) {
  return request({
    url: '/zhymans/pvuv',
    method: 'post',
    data
  })
}

// 综合统计 站点数据统计 筛选
export function siteTotalDataApi(data) {
  return request({
    url: '/zhymans/statistics',
    method: 'post',
    data
  })
}
