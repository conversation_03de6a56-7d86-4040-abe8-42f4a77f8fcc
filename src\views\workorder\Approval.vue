<script>

import SelfFormTemp from '@/views/task/components/form/SelfFormTemp.vue'

export default {
  name: 'Approval',
  components: { SelfFormTemp },
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      visible: false,
      formData: {
        company: '',
        info: '',
        idNum: '',
        name: '',
        sex: '',
        remarks: '',
        file: '',
        opinion: ''
      },
      formSettings: [
        {
          label: '申诉企业',
          value: '资海云演示账号（旗舰版）',
          prop: 'company',
          type: 'text'
        },
        {
          label: '申诉信息',
          value: '123456',
          prop: 'info',
          type: 'text'
        },
        {
          label: '变更身份证号码',
          value: '1111111111111111',
          prop: 'idNum',
          type: 'text'
        },
        {
          label: '变更身份证姓名',
          value: '张三',
          prop: 'name',
          type: 'text'
        },
        {
          label: '变更身份证性别',
          value: '男',
          prop: 'sex',
          type: 'text'
        },
        {
          label: '备注',
          value: '北京市朝阳区',
          prop: 'remarks',
          type: 'text'
        },
        {
          label: '附件',
          value: 'https://images.china9.cn/images/ldHgMuOAl5hX2Vt1uyYK29dkv4ec0MtIcLKr7a9W.jpeg',
          prop: 'file',
          type: 'img'
        },
        {
          label: '驳回申诉',
          value: '',
          prop: 'opinion',
          type: 'textarea',
          placeholder: '如驳回请填写驳回原因'
        }
      ]
    }
  },
  methods: {
    approval(status) {
      const statusTipsMap = {
        1: '同意申诉',
        2: '驳回申诉'
      }
      this.$confirm(`确定${statusTipsMap[status]}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message({
          type: 'success',
          message: `${statusTipsMap[status]}成功!`
        })
      })
    }
  }
}
</script>

<template>
  <div style="display: inline-block">
    <el-button icon="el-icon-edit" type="primary" size="mini" @click="visible = true">详情</el-button>
    <el-dialog :close-on-click-modal="false" title="申诉详情" :visible.sync="visible" append-to-body>
      <self-form-temp ref="formRef" :form-data.sync="formData" :filters="formSettings" self-key="appeal" :inline="false" label-width="120px" />
      <!--已通过审核不显示-->
      <div slot="footer" class="dialog-footer" style="text-align: right">
        <el-button type="primary" @click="approval(1)">同意申诉</el-button>
        <el-button type="danger" @click="approval(2)">驳回申诉</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
*{
  text-align: left;
}
::v-deep .el-form {
  display: flex;
  flex-wrap: wrap;
  .el-form-item {
    width: 50%;
  }
}
</style>
