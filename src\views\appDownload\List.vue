<script>
/*eslint-disable*/
import {delStore, getStoreList} from "@/api/appDownload";
import AddStore from "@/views/appDownload/components/AddStore.vue";
import mixins from "@/mixins/dialog";

export default {
  name: "AppDownload",
  components: {AddStore},
  data() {
    return {
      loading: false,
      tableData: [],
      addVisible: false,
      addId: undefined,
      addTitle: '',
      addData: null
    }
  },
  filters: {
    getStatus(value){
      const statusMap = {
        1: '启用',
        0: '不启用'
      }
      return statusMap[value]
    }
  },
  mixins: [mixins],
  methods: {
    handleGetStoreList() {
      this.loading = true;
      getStoreList().then(res => {
        if (res.code === 200 && res.data) {
          this.tableData = res.data;
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    closeAdd(refresh) {
      this.changeDialogVisible({
        name: 'addVisible',
        id: undefined,
        idField: 'addId',
        title: '添加市场',
        titleField: 'addTitle',
        data: null,
        dataField: 'addData',
        visible: false
      });
      if (refresh) {
        this.handleGetStoreList();
      }
    },
    del(id){
      if (id){
        this.$confirm('确认删除该手机商店？', '温馨提示')
          .then(_ => {
            this.loading = true;
            delStore({id}).then(res => {
              if (res.code === 200){
                this.$message.success(res.msg || res.message || '删除成功');
                this.handleGetStoreList();
              }else{
                this.$message.error(res.msg || res.message || '删除失败');
              }
            }).finally(() => {
              this.loading = false
            })
          })
          .catch(_ => {});
      }
    }
  },
  mounted() {
    this.handleGetStoreList();
  }
}
</script>

<template>
  <div id="appDownload" class="app-container">
    <el-col :span="12">
      <el-card class="box-card" style="margin-bottom: 15px">
        <div slot="header" style="display: flex;justify-content: space-between;align-items: center">
          <span>列表</span>
          <el-button type="text" @click="changeDialogVisible({
            name: 'addVisible',
            id: undefined,
            idField: 'addId',
            title: '添加应用市场',
            titleField: 'addTitle',
            data: null,
            dataField: 'addData',
            visible: true
          })">添加应用市场
          </el-button>
        </div>
        <div class="content-wrap">
          <el-table
            v-loading="loading"
            :data="tableData"
            stripe
            style="width: 100%"
          >
            <el-table-column align="center" type="index" label="序号" width="80"/>
            <el-table-column align="center" prop="name" label="应用市场"/>
            <el-table-column align="center" prop="status" label="是否启用">
              <template slot-scope="scope">
                {{scope.row.status | getStatus}}
              </template>
            </el-table-column>
            <el-table-column align="center" fixed="right" label="操作">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  @click="changeDialogVisible({
                    name: 'addVisible',
                    id: scope.row.id,
                    idField: 'addId',
                    title: '编辑应用市场',
                    titleField: 'addTitle',
                    data: scope.row,
                    dataField: 'addData',
                    visible: true
                  })">编辑</el-button>
                <el-button type="text" size="small" @click="del(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-dialog :destroy-on-close="true" :close-on-click-modal="false" :title="addTitle" :visible.sync="addVisible"
                   width="500px">
          <add-store @closeDialog="closeAdd" :id="addId" :data="addData" />
        </el-dialog>
      </el-card>
    </el-col>
  </div>
</template>

<style scoped>

</style>
