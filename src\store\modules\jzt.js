const state = {
  packageType: '1.1',
  packageTypeList: [
    {
      label: '1.1',
      value: '1.1'
    },
    {
      label: '2.0',
      value: '2.0'
    }
  ]
}
const mutations = {
  SET_PACKAGE_TYPE: (state, packageType) => {
    state.packageType = packageType
  },
  SET_PACKAGE_TYPE_LIST: (state, packageTypeList) => {
    state.packageTypeList = packageTypeList
  }
}
const actions = {
  setPackageType({ commit }, packageType) {
    commit('SET_PACKAGE_TYPE', packageType)
  },
  setPackageTypeList({ commit }, packageTypeList) {
    commit('SET_PACKAGE_TYPE_LIST', packageTypeList)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
