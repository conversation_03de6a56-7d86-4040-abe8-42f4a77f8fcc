<template>
  <div id="console" v-loading="loading">
    <div class="order-info info-wrap card-wrap" style="margin-top: 0;">
      <div class="card-title">抢单数据</div>

      <ul class="order-content">
        <li v-for="(item, index) in orderInfo" :key="index">
          <div class="img">
            <img :src="item.img" :alt="item.title">
          </div>

          <div class="right">
            <div class="value" v-html="item.value" />
            <div class="title">{{ item.title }}</div>
          </div>
        </li>
      </ul>
    </div>

    <div class="chart-wrap">
      <div class="over-wrap card-wrap">
        <div class="card-title">项目完结率</div>

        <div class="chart-content">
          <div id="over-chart" />

          <div class="detail bg-gray">
            <div v-for="(item, index) in overInfo" :key="index">
              <div>{{ item.label }}</div>
              <div>{{ item.value }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="position-wrap card-wrap">
        <div class="card-title">认证岗位占比</div>

        <div class="chart-content">
          <div id="position-chart" />

          <div class="detail bg-gray">
            <div v-for="(item, index) in positionInfo" :key="index">
              <div class="flex items-center">
                <div class="dot" :style="`border-color: ${positionColor[index]}`" />
                <div>{{ item.name }}</div>
              </div>

              <div>{{ item.value }}%</div>
            </div>
          </div>
        </div>
      </div>

      <div class="grade-wrap card-wrap">
        <div class="card-title">人员星级统计</div>

        <div class="chart-content">
          <div v-for="(item, index) in gradeInfo" :key="index" class="grade">
            <el-rate v-model="item.grade" disabled :max="item.grade" />

            <div class="label">{{ item.label }}</div>

            <div class="value">{{ item.value }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="trend-wrap card-wrap">
      <div class="title-wrap flex justify-between items-center">
        <div class="card-title">抢单趋势</div>

        <div class="tab-wrap flex justify-between items-center">
          <div
              v-for="item in trendTab"
              :key="item.value"
              :class="['tab-item', item.value === trendActiveTab ? 'active' : '']"
              @click="changeTrendTab(item.value)"
          >
            {{ item.label }}
          </div>
        </div>
      </div>

      <div v-loading="trendLoading" class="trend-chart-wrap">
        <div id="trend-chart" />
      </div>
    </div>

    <div class="chart-wrap">
      <div class="publish-info card-wrap">
        <div class="title-wrap flex justify-between items-center">
          <div class="card-title">项目发布TOP榜</div>

          <div class="tab-wrap flex justify-between items-center">
            <div
                v-for="item in pubulishTab"
                :key="item.value"
                :class="[
                'tab-item',
                item.value === publishActiveTab ? 'active' : '',
              ]"
                @click="changeTopTab(item.value, 'publish')"
            >
              {{ item.label }}
            </div>
          </div>
        </div>

        <div v-loading="publishRankLoading" class="publish-wrap">
          <rank
              :data="publishRankInfo"
              :show-more-btn="showPublishMoreBtn"
              :replace-props="publishReplaceProps"
              :options="publishOptions"
              @showMore="showMore('publish')"
          />
        </div>
      </div>

      <div class="over-info card-wrap">
        <div class="title-wrap flex justify-between items-center">
          <div class="card-title">抢单完结TOP榜</div>

          <div class="tab-wrap flex justify-between items-center">
            <div
                v-for="item in pubulishTab"
                :key="item.value"
                :class="[
                'tab-item',
                item.value === overActiveTab ? 'active' : '',
              ]"
                @click="changeTopTab(item.value, 'over')"
            >
              {{ item.label }}
            </div>
          </div>
        </div>

        <div v-loading="overRankLoading" class="publish-wrap">
          <rank
              :data="overRankInfo"
              :show-more-btn="showOverMoreBtn"
              :options="overOptions"
              @showMore="showMore('over')"
          />
        </div>
      </div>

      <div class="certificate-info card-wrap">
        <div class="title-wrap">
          <div class="card-title">人员认证统计</div>
        </div>

        <div v-loading="certificateLoading" class="certificate-wrap publish-wrap">
          <el-table :data="certificateInfo" header-row-class-name="my-header" height="calc(100% - 62px)">
            <el-table-column prop="enterprise_name" label="公司名称" min-width="100px" align="center" />
            <el-table-column prop="position_rate" label="总人数" align="center" />
            <el-table-column prop="grade1" label="一星" align="center" />
            <el-table-column prop="grade2" label="二星" align="center" />
            <el-table-column prop="grade3" label="三星" align="center" />
          </el-table>

          <div class="page">
            <el-pagination
                :page-size="page.limit"
                :current-page="page.page"
                :page-sizes="[9, 18, 27, 36]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @current-change="getList"
                @size-change="handleSizeChange"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="area card-wrap">
      <div class="card-title">地域认证统计</div>

      <div class="map-wrap">
        <div id="map" v-loading="mapLoading" />

        <div v-loading="areaLoading" class="map">
          <el-table stripe style="width: 100%" :data="areaInfo">
            <el-table-column type="index" label="序号" align="center" width="50" :index="indexMethod" />
            <el-table-column prop="name" label="认证地域" align="center" />
            <el-table-column prop="count" label="认证人数" align="center" />
          </el-table>
          <div class="page" style="text-align: center;margin-top: 20px">
            <el-pagination
                :current-page="areaPage.page"
                :page-size="areaPage.limit"
                layout="prev, pager, next"
                :total="areaTotal"
                @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import selfRequest from './mixins/selfRequest'
/* eslint-disable */
import Rank from "./components/Rank.vue";
import mapJson from '@/assets/map/map.json'
import * as echarts from "echarts";

export default {
  name: "Console",
  components: { Rank },
  data() {
    return {
      orderInfo: [
        {
          img: require("@/assets/img/publish-total.png"),
          value: 0,
          title: "项目发布总数",
        },
        {
          img: require("@/assets/img/publish-money.png"),
          value: `<span class="small">￥</span>0`,
          title: "项目发布金额",
        },
        {
          img: require("@/assets/img/over-total.png"),
          value: 0,
          title: "完结项目总数",
        },
        {
          img: require("@/assets/img/over-money.png"),
          value: `<span class="small">￥</span>0`,
          title: "完结项目金额",
        },
        {
          img: require("@/assets/img/user-total.png"),
          value: 0,
          title: "平台认证用户数",
        },
      ],
      overInfo: [
        {
          label: "已完结项目",
          value: 0,
        },
        {
          label: "未完结项目",
          value: 0,
        },
      ],
      positionInfo: [
        {
          value: 0,
          name: "php",
        },
        {
          value: 0,
          name: "UI设计",
        },
        {
          value: 0,
          name: "Web前端",
        },
        {
          value: 0,
          name: "JAVA",
        },
        {
          value: 0,
          name: "产品经理",
        },
        {
          value: 0,
          name: "其他",
        },
      ],
      positionColor: ["#1269FF", "#00DEA2", "#FFAC37", "#544FC5", "#FE6A35", "#8b3cf6"],
      gradeInfo: [
        {
          grade: 1,
          label: "一星",
          value: 0,
        },
        {
          grade: 2,
          label: "二星",
          value: 0,
        },
        {
          grade: 3,
          label: "三星",
          value: 0,
        },
      ],
      trendTab: [
        {
          label: "近30日",
          value: 1,
        },
        {
          label: "近12月",
          value: 2,
        },
      ],
      trendActiveTab: 1,
      pubulishTab: [
        {
          label: "本月",
          value: 1,
        },
        {
          label: "全部",
          value: 2,
        },
      ],
      publishActiveTab: 1,
      overActiveTab: 1,
      publishRankInfo: [],
      overRankInfo: [],
      publishReplaceProps: {
        name: 'enterprise_name',
      },
      publishOptions: [
        {
          field: "taskcount",
          color: "#2C6DF2",
          unit: "",
          unitPosition: "left",
          label: "项目发布数量",
        },
        {
          field: "release_money",
          color: "#00DEA2",
          unit: "￥",
          unitPosition: "left",
          label: "项目发布金额",
        },
      ],
      overOptions: [
        {
          field: "wjcount",
          color: "#FFB700",
          unit: "",
          unitPosition: "left",
          label: "抢单完结数量",
        },
        {
          field: "hall_user_money",
          color: "#726DED",
          unit: "￥",
          unitPosition: "left",
          label: "抢单完结金额",
        },
      ],
      certificateInfo: [],
      page: {
        page: 1,
        limit: 10
      },
      total: 0,
      loading: false,
      overPercent: 0,
      trendLoading: false,
      trendInfo: {
        date: [],
        send_task: [],
        rob_task: [],
        end_task: []
      },
      publishRankLoading: false,
      publishRankPage: {
        page: 1,
        limit: 10
      },
      showPublishMoreBtn: true,
      overRankLoading: false,
      overRankPage: {
        page: 1,
        limit: 10
      },
      showOverMoreBtn: true,
      certificateLoading: false,
      areaInfo: [],
      areaPage: {
        page: 1,
        limit: 10
      },
      areaTotal: 0,
      mapInfo: [],
      areaLoading: false,
      mapLoading: false
    };
  },
  mixins: [selfRequest],
  methods: {
    // 完结数据统计饼图
    initOverChart() {
      this.$nextTick(() => {
        let myChart = echarts.init(document.getElementById("over-chart"));

        // 指定图表的配置项和数据
        let option = {
          title: [
            {
              text: "项目完结率",
              x: "center",
              top: "40%",
              textStyle: {
                fontSize: 14,
                color: "#000",
                fontFamily: "DINAlternate-Bold, DINAlternate",
                foontWeight: "500",
              },
            },
            {
              text: this.overPercent + '%',
              x: "center",
              top: "52%",
              textStyle: {
                color: "#000",
                fontSize: 24,
                fontWeight: "400",
              },
            },
          ],
          polar: {
            radius: ["82%", "100%"],
            center: ["50%", "50%"],
          },
          angleAxis: {
            max: 100,
            show: false,
          },
          radiusAxis: {
            type: "category",
            show: true,
            axisLabel: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
          series: [
            {
              name: "",
              type: "bar",
              roundCap: true,
              barWidth: 30,
              showBackground: true,
              backgroundStyle: {
                color: "transparent",
              },
              data: [this.overPercent],
              coordinateSystem: "polar",
              itemStyle: {
                color: "#1269FF",
              },
            },
            {
              name: "",
              type: "pie",
              radius: ["62%", "82%"],
              hoverAnimation: false,
              center: ["50%", "50%"],
              itemStyle: {
                color: "#EDEDED",
              },
              data: [100],
              label: false,
            },
            {
              name: "",
              type: "pie",
              clockWise: false,
              hoverAnimation: false,
              radius: ["62%", "82%"],
              center: ["50%", "50%"],
              label: {
                show: false,
              },
              data: [
                {
                  value: 50,
                  name: "value",
                },
                {
                  value: 50,
                  name: "total",
                  itemStyle: {
                    color: "rgba(0,0,0,0)",
                  },
                },
              ],
              itemStyle: {
                color: echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  {
                    offset: 0,
                    color: "#EDEDED",
                  },
                  {
                    offset: 1,
                    color: "#D6D7D6",
                  },
                ]),
              },
            },
          ],
        };

        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
      })
    },
    // 岗位统计饼图
    initPositionChart() {
      this.$nextTick(() => {
        let myChart = echarts.init(
            document.getElementById("position-chart")
        );

        let seriesData = this.positionInfo.map((v, i) => ({
          ...v,
          itemStyle: {
            color: this.positionColor[i],
            borderColor: "#fff",
            borderWidth: 10,
          },
        }));

        let option = {
          tooltip: {
            show: true
          },
          title: {
            text: "认证岗位占比",
            x: "center",
            y: "center",
            textStyle: {
              fontSize: 14,
              color: "#000",
              fontFamily: "DINAlternate-Bold, DINAlternate",
              foontWeight: "500",
            },
          },
          series: [
            {
              name: "",
              type: "pie",
              radius: ["65%", "100%"],
              data: seriesData,
              label: {
                show: false,
              },
            },
          ],
        };

        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
      })
    },
    // 趋势tab切换
    changeTrendTab(tab) {
      this.trendActiveTab = tab;
      this.getTrend();
    },
    // 趋势图
    initTrendChart() {
      this.$nextTick(() => {
        let myChart = echarts.init(document.getElementById("trend-chart"));

        let option = {
          tooltip: {
            trigger: "axis",
          },
          grid: {
            left: "2%",
            right: "2%",
            top: "5%",
            bottom: "16%",
            containLabel: true,
          },
          legend: {
            x: "center",
            bottom: "0%",
            textStyle: {
              color: "#999",
              fontSize: 14,
            },
            itemGap: 40,
            data: ["发单量", "抢单量", "完结量"],
          },
          xAxis: {
            type: "category",
            axisLine: {
              lineStyle: {
                color: "#A7ABBB",
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              textStyle: {
                color: "#999999",
                fontSize: 12,
              },
            },
            boundaryGap: false,
            data: this.trendInfo.date,
          },
          yAxis: {
            axisLine: {
              show: true,
              lineStyle: {
                color: "#A7ABBB",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#E8EAF4",
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              textStyle: {
                color: "#999999",
                fontSize: 12,
              },
            },
            type: "value",
          },
          series: [
            {
              name: "发单量",
              type: "line",
              symbolSize: 8,
              symbol: "emptyCircle",
              data: this.trendInfo.send_task,
              itemStyle: {
                color: "#3765FF",
              },
            },
            {
              name: "抢单量",
              type: "line",
              symbolSize: 8,
              symbol: "emptyCircle",
              data: this.trendInfo.rob_task,
              itemStyle: {
                color: "#FF6800",
              },
            },
            {
              name: "完结量",
              type: "line",
              symbolSize: 8,
              symbol: "emptyCircle",
              data: this.trendInfo.end_task,
              itemStyle: {
                color: "#07BA7E",
              },
            },
          ],
        };

        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
      })
    },
    // 排行榜切换tab
    changeTopTab(tab, name) {
      this[name + "RankPage"].page = 1;
      this[name + "ActiveTab"] = tab;
      this.getRank(name);
    },
    // 发布top榜
    getRank(name) {
      let upperName = name.charAt(0).toUpperCase() + name.slice(1);
      this.requestApi({
        apiName: 'get' + upperName + 'Rank',
        data: { time: this[name + 'ActiveTab'], ...this[name + 'RankPage'] },
        loadingField: name + 'RankLoading',
        success: ({ data }) => {
          if (data.data) {
            this[name + 'RankInfo'] = data.data;
          }
          if (data.last_page <= this[name + 'RankPage'].page) {
            this['show' + upperName + 'MoreBtn'] = false;
          }
        }
      })
    },
    // 完结top榜翻页
    showMore(name) {
      this[name + 'RankPage'].page++;
      this.getRank(name);
    },
    // 获取列表
    getList(page) {
      if (page) {
        this.page.page = page;
      }
      this.requestApi({
        apiName: 'getCertificate',
        data: this.page,
        loadingField: 'certificateLoading',
        success: ({ data }) => {
          if ('total' in data) {
            if (data.data) {
              this.certificateInfo = data.data;
            }
            this.total = data.total;
          } else {
            this.certificateInfo = data;
          }
        }
      })
    },
    // 每页条数改变
    handleSizeChange(limit) {
      this.page.limit = limit;
      this.getList();
    },
    // 第一二排统计信息
    getConsole() {
      this.requestApi({
        apiName: 'getConsole',
        data: {},
        loadingField: 'loading',
        success: ({ data }) => {
          // 抢单数据和项目完结率
          if (data.release) {
            this.orderInfo[0].value = data.release.release_num || 0;
            this.orderInfo[1].value = data.release.release_money || 0;
            this.orderInfo[2].value = this.overInfo[0].value = data.release.end_num || 0;
            this.orderInfo[3].value = data.release.end_money || 0;
            this.orderInfo[4].value = data.release.user_num || 0;
            this.overPercent = data.release.end_rate || 0;
            this.overInfo[1].value = data.release.noend_num || 0;
          }

          // 岗位认证占比
          if (data.position_rate) {
            this.positionInfo = [];
            for (const key in data.position_rate) {
              if (Object.hasOwnProperty.call(data.position_rate, key)) {
                const element = data.position_rate[key];
                this.positionInfo.push({
                  name: key,
                  value: element
                })
              }
            }
          }

          // 星级统计
          if (data.grade) {
            this.gradeInfo[0].value = data.grade.grade1;
            this.gradeInfo[1].value = data.grade.grade2;
            this.gradeInfo[2].value = data.grade.grade3;
          }

          // 渲染图表
          this.$nextTick(() => {
            this.initOverChart();
            this.initPositionChart();
          })
        }
      })
    },
    // 趋势
    getTrend() {
      this.requestApi({
        apiName: 'getTrend',
        data: { time: this.trendActiveTab },
        loadingField: 'trendLoading',
        success: ({ data }) => {
          this.trendInfo = data;
          this.$nextTick(() => {
            this.initTrendChart();
          });
        }
      })
    },
    /**
     * 查找数组中的最大值
     */
    findMax(arr) {
      let max = Math.max(...arr)
      return max;
    },
    // 地域
    initMap() {
      echarts.registerMap('map', mapJson);
      let myChart = echarts.init(document.getElementById("map"));
      let max = this.findMax(this.mapInfo.map(item => item.value));

      const options = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}<br/>{c}'
        },
        toolbox: {
          show: true,
          orient: 'vertical',
          left: 'right',
          top: 'center'
        },
        visualMap: {
          show: false,
          min: 0,
          max: max,
          text: ['High', 'Low'],
          realtime: false,
          calculable: true,
          inRange: {
            color: ['lightskyblue', 'yellow', 'orangered']
          }
        },
        series: [
          {
            name: '活跃用户地域分布',
            type: 'map',
            map: 'map',
            // zoom: 1.6,
            label: {
              show: false
            },
            data: this.mapInfo
          }
        ]
      };
      myChart.setOption(options);
    },
    // 页数改变
    handleCurrentChange(page) {
      this.getAreaPage(page)
    },
    // 获取地域列表
    getAreaPage(page) {
      if (page) {
        this.areaPage.page = page;
      }
      this.requestApi({
        apiName: 'getCityCertificate',
        data: this.areaPage,
        loadingField: 'areaLoading',
        success: ({ data }) => {
          if (data.data) {
            this.areaInfo = data.data;
          }
          this.areaTotal = data.total;
        }
      })
    },
    /**
     * 获取地图数据
     */
    getMap() {
      this.requestApi({
        apiName: 'getMapData',
        loadingField: 'mapLoading',
        data: {},
        success: ({ data }) => {
          this.mapInfo = data.map(v => ({ ...v, ...{ value: v.count } }));
          this.initMap();
        }
      })
    },
    /**
     * 地域列表序号
     */
    indexMethod(index) {
      return (this.areaPage.page - 1) * this.areaPage.limit + index + 1;
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getConsole();
      this.getTrend();
      this.getRank('publish');
      this.getRank('over');
      this.getList();
      this.getMap();
      this.getAreaPage(1);
    })
  },
};
</script>

<style lang="scss" scoped>
#console {
  height: 100%;
  overflow: auto;

  .page-title {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 500;
    color: #222222;
  }

  .bg-white {
    background: white;
  }

  ::v-deep .el-card__header {
    border-bottom: none;
  }

  .card-title {
    font-size: 19px;
    font-family: PingFang SC;
    font-weight: 800;
    color: #111111;
    padding-bottom: 16px;
  }

  .card-wrap {
    padding: 24px 30px;
    background: white;
    margin-top: 24px; // 增加卡片间距
    position: relative;
    border-radius: 8px;
  }

  .info-wrap {
    .order-content {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      grid-column-gap: 28px;
      margin-top: 35px;
      padding-left: 0;
      grid-row-gap: 32px;

      li {
        list-style: none;
        border-right: 1px solid #ebeef5; // 调整分割线颜色
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .img {
          img {
            display: block;
            margin-right: 22px;
          }
        }

        .value {
          font-size: 32px;
          font-family: Bahnschrift;
          font-weight: 400;
          color: #111111;

          ::v-deep .small {
            font-size: 20px;
          }
        }

        .title {
          font-size: 16px;
          font-family: PingFang SC;
          font-weight: 500;
          color: #666666;
          margin-top: 12px;
        }
      }
    }
  }

  .bg-gray {
    background: #f9f9fa;
  }

  .tab-wrap {
    .tab-item {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #9aa8be;
      cursor: pointer;
      padding: 0 11px;
      position: relative;

      &.active {
        color: #1269ff;
      }

      &+.tab-item::before {
        content: "";
        display: block;
        height: 90%;
        width: 1px;
        background: #dfe2e9;
        position: absolute;
        top: 0;
        bottom: 0;
        left: -1px;
        margin: auto;
      }
    }
  }

  .chart-wrap {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-column-gap: 16px;
    margin-top: 0;
    grid-row-gap: 24px;

    &>div {
      background: #fff;
      min-height: 270px;
      padding: 24px;
      border-radius: 8px;

      .detail {
        flex: 1;
        height: auto !important;
        margin-left: 30px;

        &>div {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          align-items: center;
          padding: 12px;
          border-radius: 6px;

          &+div {
            margin-top: 16px;
          }

          &>div:first-of-type {
            font-size: 12px;
            font-family: PingFang SC;
            font-weight: 500;
            color: #111111;
            flex-wrap: nowrap;
          }

          &>div:last-of-type {
            font-size: 18px;
            font-family: DIN Medium;
            font-weight: 400;
            color: #111111;
            text-align: right;
          }
        }
      }

      &.over-wrap {
        display: flex;
        flex-direction: column;

        .chart-content {
          justify-content: space-between;

          #over-chart {
            width: 47%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .detail {
            max-width: 227px;
            padding: 25px 30px;
          }
        }
      }

      &.position-wrap {
        display: flex;
        flex-direction: column;

        #position-chart {
          width: 46.98%;
        }

        .detail {
          padding: 23px 28px;

          .dot {
            border-width: 2px;
            border-style: solid;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 9px;
          }
        }
      }

      &.grade-wrap {
        display: flex;
        flex-direction: column;

        .chart-content {
          display: flex;
          justify-content: space-between;
          flex: 1;

          .grade {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 30.8%;
            background: rgba(245, 247, 249, 0.7);

            .label {
              font-size: 16px;
              font-family: PingFang SC;
              font-weight: 500;
              color: #666666;
              margin-top: 18px;
            }

            .value {
              font-size: 32px;
              font-family: Bahnschrift;
              font-weight: 400;
              color: #111111;
              margin-top: 20px;
            }
          }
        }
      }
    }

    .card-wrap {
      .chart-content {
        margin-top: 27px;
        flex: 1;
        display: flex;
        align-items: center;

        &>div {
          height: 100%;
        }
      }
    }
  }

  .trend-wrap {
    .trend-chart-wrap {
      padding-top: 24px;
      margin-top: 16px;
      height: 229px;

      #trend-chart {
        height: 100%;
      }
    }
  }

  .el-table {
    border: 1px solid #f0f2f7;
    &::before {
      background-color: #f0f2f7; // 表格边框颜色统一
    }
  }

  .publish-info,
  .over-info,
  .certificate-info {
    padding-bottom: 0;

    .publish-wrap {
      margin-top: 30px;
      height: 538px;
    }
  }

  .certificate-info {
    ::v-deep .certificate-wrap .my-header {
      th.el-table__cell.is-leaf {
        background: #f6f9fe;
      }
    }

    .page {
      border-top: 1px solid #f0f2f7; // 分页器上方分割线
      height: 62px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding-top: 16px;
      margin-top: 16px;
    }
  }

  .map-wrap {
    margin-top: 27px;
    display: flex;

    #map,
    .map {
      width: 40%;
      height: 600px;
    }

    #map {
      width: 60%;
    }
  }
}

.flex{
  display: flex;
  flex-wrap: wrap;
}
.justify-between{
  justify-content: space-between;
}
.justify-center{
  justify-content: center;
}
.items-center{
  align-items: center;
}
.w-full{
  width: 100%;
}
.h-full{
  height: 100%;
}
.justify-end{
  justify-content: flex-end;
}
.text-center{
  text-align: center;
}
.text-right{
  text-align: right;
}
</style>
