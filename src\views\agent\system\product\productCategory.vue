<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <!--<span>产品种类</span>-->
        <el-button style="float: right; padding: 3px 0" type="text" @click="createView">新增</el-button>
      </div>
      <div class="filter">
        <el-form ref="form" :model="searchForm" :inline="true" class="demo-form-inline" size="small">
          <el-form-item label="产品种类">
            <el-input v-model="searchForm.name" placeholder="产品种类" clearable />
          </el-form-item>
          <el-form-item label="审核状态">
            <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
              <el-option label="已启用" :value="1" />
              <el-option label="已禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="table mt20">
        <el-table
          :data="tableData"
          style="width: 100%"
        >
          <el-table-column
            prop="id"
            label="ID"
            width="75"
          />
          <el-table-column
            prop="name"
            label="产品种类"
          />
          <el-table-column
            prop="company"
            label="所属公司"
          />
          <el-table-column label="所属分类">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.type === 1" type="info">软件</el-tag>
              <el-tag v-if="scope.row.type === 2" type="info">硬件</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="是否启用">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.status"
                active-color="#13ce66"
                inactive-color="#ff4949"
                :active-value="1"
                :inactive-value="0"
                @change="setStatus(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="primary" size="small" @click="editView(scope.row)">编辑</el-button>
              <el-button type="warning" plain size="small" @click="editRatio(scope.row)">分成</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="searchForm.total>0"
          :total="searchForm.total"
          :page.sync="searchForm.page"
          :limit.sync="searchForm.perPage"
          style="text-align:center;"
          @pagination="getList"
        />
      </div>
    </el-card>

    <!--    新增编辑模态框-->
    <el-dialog
      :title="isUpdate ? '编辑产品种类' : '新增产品种类'"
      :visible.sync="dialogVisible"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="产品分类:" prop="type" style="width: 70%">
          <el-select v-model="ruleForm.type" placeholder="请选择">
            <el-option label="软件" :value="1" />
            <el-option label="硬件" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="种类名称：" prop="name" style="width: 65%">
          <el-input v-model="ruleForm.name" placeholder="请输入产品种类名称" />
        </el-form-item>
        <el-form-item label="所属公司:" prop="company_id" style="width: 70%">
          <el-select v-model="ruleForm.company_id" placeholder="全部" @change="selectCompany">
            <el-option v-for="item in companies" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="所属系统:" prop="system_id" style="width: 70%">
          <el-select v-model="ruleForm.system_id" placeholder="全部">
            <el-option v-for="item in systems" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="isUpdate ? updateHandle() : saveHandle()">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { moduleList } from '@/api/agent/system'
import { companies } from '@/api/agent/company'
import { categories, store, update, destroy } from '@/api/agent/productCategory'
import Pagination from '@/components/Pagination'
export default {
  name: 'ProductCategory',
  components: { Pagination },
  data() {
    return {
      dialogVisible: false,
      isUpdate: false,
      searchForm: {
        total: 0,
        page: 1,
        perPage: 10
      },
      tableData: [],
      ruleForm: {},
      systems: [],
      companies: [],
      rules: {
        type: [{ required: true, message: '必选', trigger: 'change' }],
        name: [{ required: true, message: '必填', trigger: 'blur' }],
        company_id: [{ required: true, message: '必选', trigger: 'change' }],
        system_id: [{ required: true, message: '必选', trigger: 'change' }]
      }
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.list-wrap > .el-card > .el-card__body')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  methods: {
    getList() {
      categories(this.searchForm).then(response => {
        if (response.code === 200 && response.data) {
          this.tableData = response.data.list
          this.searchForm.total = response.data.meta.total
        }
      })
    },
    createView() {
      this.isUpdate = false
      this.dialogVisible = true
      this.ruleForm = {}
      this.companiesList()
    },
    editView(row) {
      this.isUpdate = true
      this.ruleForm = row
      this.dialogVisible = true
      this.companiesList()
      this.selectCompany(row.company_id)
    },
    editRatio(row) {
      this.$router.push({
        name: 'SystemCompanyRatioRouter',
        query: { id: row.id }
      })
    },
    saveHandle() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.ruleForm.status = 0
          store(this.ruleForm).then(response => {
            if (response.code === 200) {
              this.$message.success('新增成功')
              this.dialogVisible = false
              this.getList()
            }
          })
        } else {
          return false
        }
      })
    },
    updateHandle() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          update(this.ruleForm).then(response => {
            if (response.code === 200) {
              this.$message.success('更新成功')
              this.dialogVisible = false
              this.getList()
            }
          })
        } else {
          return false
        }
      })
    },
    setStatus(row) {
      update(row).then(response => {
        if (response.code === 200) {
          this.$message.success('更新成功')
          this.getList()
        }
      })
    },
    // 获取公司
    companiesList() {
      companies({}).then(response => {
        if (response.code === 200 && response.data) {
          this.companies = response.data
        }
      })
    },
    // 根据公司获取系统列表
    selectCompany(val) {
      moduleList({ company_id: val }).then(response => {
        if (response.code === 200 && response.data) {
          this.systems = response.data
          let tmp
          // eslint-disable-next-line prefer-const
          tmp = this.systems.map(x => {
            return x.id
          })
          if (tmp.indexOf(this.ruleForm.system_id) === -1) {
            delete this.ruleForm.system_id
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap {
  height: 100%;
  .box-card {
    height: 100%;
    ::v-deep .el-card__header {
      padding-top: 0;
    }
    ::v-deep .el-card__body {
      padding: 0;
      margin-top: 20px;
      height: calc(100% - 59px);
      overflow: auto;
    }
  }
  .filter {
    border-bottom: 1px solid #E5E5E5;
  }
  .table {
    margin-top: 20px;
    min-height: 200px;
  }
}
</style>
