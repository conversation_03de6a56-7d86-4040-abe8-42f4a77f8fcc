<template>
  <div class="app-container" style="height: 100%;">
    <div class="filter">
      <el-form ref="form" :inline="true" :model="listQuery" label-width="100px">
        <el-form-item label="时间：">
          <el-date-picker
              v-model="form.created_at"
              type="datetimerange"
              placeholder="选择日期"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button-group>
            <el-button type="primary" size="small" icon="el-icon-search" @click="show">筛选</el-button>
          </el-button-group>
        </el-form-item>
      </el-form>
    </div>
    <div class="list">
      <div style="overflow: hidden;">
        <el-row :gutter="10" class="panel-group mt20">
          <el-col :xs="12" :sm="12" :lg="4" class="card-panel-col">
            <!-- 第一个卡片保持不变 -->
            <div class="card-item">
              <div>
                <div class="card-item-title">订单数量</div>
                <div class="card-item-num">
                  <count-to :start-val="0" :end-val="ordernum" :duration="2600" class="card-panel-num" />
                </div>
              </div>
              <div class="card-item-icon">
                <i class="iconfont icon-dingdan" />
              </div>
            </div>
          </el-col>

          <!-- 修改第二个卡片 -->
          <el-col :xs="12" :sm="12" :lg="4" class="card-panel-col">
            <div class="card-item icon-jifen">
              <div>
                <div class="card-item-title">支付积分</div>
                <div class="card-item-num">
                  <count-to :start-val="0" :end-val="totalnum" :duration="3000" class="card-panel-num" />
                </div>
              </div>
              <div class="card-item-icon">
                <i class="iconfont icon-jifenguizeguankong2" />
              </div>
            </div>
          </el-col>

          <!-- 修改第三个卡片 -->
          <el-col :xs="12" :sm="12" :lg="4" class="card-panel-col">
            <div class="card-item icon-fulibi">
              <div>
                <div class="card-item-title">支付福利币</div>
                <div class="card-item-num">
                  <count-to :start-val="0" :end-val="totalmoney" :duration="3200" class="card-panel-num" />
                </div>
              </div>
              <div class="card-item-icon">
                <i class="iconfont icon-copper-coin-fill" />
              </div>
            </div>
          </el-col>

          <!-- 修改第四个卡片：评论数 -->
          <el-col :xs="12" :sm="12" :lg="4" class="card-panel-col">
            <div class="card-item">
              <div>
                <div class="card-item-title">评论数</div>
                <div class="card-item-num">
                  <count-to :start-val="0" :end-val="evanum" :duration="3600" class="card-panel-num" />
                </div>
              </div>
              <div class="card-item-icon">
                <i class="iconfont icon-pinglun" />
              </div>
            </div>
          </el-col>

          <!-- 修改第五个卡片：商品总数 -->
          <el-col :xs="12" :sm="12" :lg="4" class="card-panel-col">
            <div class="card-item">
              <div>
                <div class="card-item-title">商品总数</div>
                <div class="card-item-num">
                  <count-to :start-val="0" :end-val="goodsnum" :duration="3600" class="card-panel-num" />
                </div>
              </div>
              <div class="card-item-icon">
                <i class="iconfont icon-shangpin" />
              </div>
            </div>
          </el-col>

          <!-- 修改第六个卡片：上架商品总数 -->
          <el-col :xs="12" :sm="12" :lg="4" class="card-panel-col">
            <div class="card-item">
              <div>
                <div class="card-item-title">上架商品总数</div>
                <div class="card-item-num">
                  <count-to :start-val="0" :end-val="goodsshelvesnum" :duration="3600" class="card-panel-num" />
                </div>
              </div>
              <div class="card-item-icon">
                <i class="iconfont icon-shangjiashangpin" />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div style="overflow: hidden;">
        <el-row class="mt20" :gutter="20" style="display: flex; align-items: stretch;">
          <el-col :xs="12" :sm="12" :lg="12" class="card-panel-col">
            <SelfCard class="bottom-item" title="商品销售排行">
              <el-table v-loading="loading" :data="tableData" style="width: 100%">
                <el-table-column type="index" :index="table_index" label="序号" width="50" />
                <el-table-column prop="company.name" label="公司名称" min-width="150" />
                <el-table-column label="产品名称" width="350">
                  <template slot-scope="scope">
                    <el-link :underline="false" type="primary" @click="editView(scope.row)">{{ scope.row.name
                      }}</el-link>
                  </template>
                </el-table-column>
                <el-table-column prop="order_sum" label="总销售数量" width="100" />
              </el-table>
            </SelfCard>
          </el-col>
          <el-col :xs="12" :sm="12" :lg="12" class="card-panel-col">
            <SelfCard class="bottom-item" title="商品返佣排行">
              <el-table v-loading="loading1" :data="tableData1" style="width: 100%">
                <el-table-column type="index" :index="table_index1" label="序号" width="50" />
                <el-table-column prop="company_name" label="公司名称" min-width="150" />
                <el-table-column label="产品名称" width="350">
                  <template slot-scope="scope">
                    <el-link :underline="false" type="primary" @click="editView(scope.row)">{{ scope.row.name
                      }}</el-link>
                  </template>
                </el-table-column>
                <el-table-column prop="flmoney_sum" label="返福利币" width="100" />
              </el-table>
            </SelfCard>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>
<script>
import CountTo from 'vue-count-to'
import { getSales, commission, shopshow } from '../../api/shop_sales'
import SelfCard from '../dashboard/admin/components/SelfCard.vue'
export default {
  name: 'List',
  components: { CountTo, SelfCard },
  data() {
    return {
      loading: false,
      loading1: false,
      totalnum: 0,
      totalmoney: 0,
      ordernum: 0,
      evanum: 0,
      goodsnum: 0,
      goodsshelvesnum: 0,
      total: 1,
      tableData: [],
      total1: 1,
      tableData1: [],
      categories: [],
      form: { created_at: [] },
      listQuery: {
        page: 1,
        limit: 10
      },
      listQuery1: {
        page: 1,
        limit: 10
      }
    }
  },

  created() {
    this.getList()
    this.getList1()
    this.getshopshow()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    table_index(index) {
      return (this.listQuery.page - 1) * this.total + index + 1
    },
    table_index1(index) {
      return (this.listQuery1.page - 1) * this.total1 + index + 1
    },
    handleSetLineChartData(type) {
      this.$emit('handleSetLineChartData', type)
    },
    getList() {
      this.loading = true
      getSales(this.form).then(response => {
        this.tableData = response.data.data
        this.total = response.data.total
        this.loading = false
      })
    },
    getList1() {
      this.loading1 = true
      commission(this.form).then(response => {
        this.tableData1 = response.data.data
        this.total1 = response.data.total
        this.loading1 = false
      })
    },
    getshopshow() {
      shopshow(this.form).then(response => {
        this.totalnum = response.data.total
        this.totalmoney = response.data.total_money
        this.ordernum = response.data.ordernum
        this.evanum = response.data.evanum
        this.goodsnum = response.data.goodsnum
        this.goodsshelvesnum = response.data.goodsshelvesnum
        // this.tableData1 = response.data.data
        // this.total1 = response.data.total
      })
    },
    show() {
      this.getList()
      this.getList1()
      this.getshopshow()
    },
    editView(row) {
      this.$router.push({
        name: 'goods-edit',
        query: { id: row.id }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.card-wrap ::v-deep .el-card__body {
  height: 100%;
}
  .filter {
    border-bottom: 1px solid #e5e5e5;
  }

  .list {
    box-sizing: border-box;
  }

  .panel-group {

    .card-panel-col {
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .card-panel {
      height: 100px;
      border: 1px solid #ebeef5;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
        transform: scale(1.02);
      }

      .card-panel-icon-wrapper {
        margin: 10px 0 0 10px;
        padding: 15px 5px;

        .card-panel-text {
          font-size: 14px;
        }
      }

      .card-panel-description {
        margin: 20px;

        .card-panel-num {
          font-size: 24px;
          color: #303133;
        }
      }
    }
  }

  @media (max-width:550px) {
    .panel-group {
      margin: 8px 0;

      .card-panel {
        height: auto;
        padding: 10px 0;

        .card-panel-icon-wrapper {
          margin: 5px 0;
          padding: 5px;

          .card-panel-text {
            font-size: 12px;
          }
        }

        .card-panel-description {
          margin: 10px;

          .card-panel-num {
            font-size: 18px;
          }
        }
      }
    }
  }

  .card-item {
    // 通用卡片样式
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
    color: #fff;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);

    // 默认背景（第一个卡片）
    // 修改第一个卡片渐变
    background: linear-gradient(145deg, #5a85ff, #6c9bff);

    &-title {
      font-size: 14px;
      opacity: 0.9;
    }

    &-num {
      font-size: 24px;
      margin-top: 10px;
      font-weight: bold;
    }

    &-icon {
      .iconfont {
        font-size: 40px;
        color: rgba(255, 255, 255, 0.9);
        filter: drop-shadow(2px 2px 3px rgba(0, 0, 0, 0.2));
      }
    }

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }
  }

.bottom-item {
    border: 1px solid #ebeef5;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;

    &:hover {
        border-color: #d3d6dd;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }

    ::v-deep .el-card__header {
        border-bottom: 1px solid #ebeef5;
        background: linear-gradient(to right, #f8f9ff, #f6f7ff);
    }
}

  // 移动端适配
  @media (max-width: 768px) {
    .card-item {
      padding: 15px;

      &-title {
        font-size: 12px;
      }

      &-num {
        font-size: 20px;
      }

      &-icon .iconfont {
        font-size: 32px;
      }
    }
    .bottom-item {
        border-radius: 6px;
        ::v-deep .el-card__header {
            padding: 12px 15px;
        }
    }
  }

</style>
