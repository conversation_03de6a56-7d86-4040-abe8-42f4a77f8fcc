<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>热门产品</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="createView">新增产品</el-button>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="65"
        />
        <el-table-column prop="name" label="姓名" width="100">
          <template slot-scope="scope">
            <el-image fit="contain" style="height: 48px;width: 48px" :src="scope.row.icon" />
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="应用名称"
        />
        <el-table-column label="产品状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status" type="success" size="small" effect="dark">启用</el-tag>
            <el-tag v-else type="danger" effect="dark" size="small">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-link :underline="false" type="primary" @click="editView(scope.row)">编辑</el-link>
            <el-popconfirm
              title="确定删除当前记录吗？"
              @onConfirm="deleteHandle(scope.row)"
            >
              <el-link slot="reference" :underline="false" type="danger">删除</el-link>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!--表单-->
    <el-dialog
      title="热门产品配置"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="选择产品：" prop="app_id">
          <el-select v-model="ruleForm.app_id" placeholder="请选择">
            <el-option
              v-for="item in apps"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态：">
          <el-switch
            v-model="ruleForm.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button :loading="btnLoading" type="primary" @click="saveHandle">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { children_apps, hot, hot_store, hot_delete } from '../../api/app'
export default {
  name: 'Hot',
  data() {
    return {
      btnLoading: false,
      dialogVisible: false,
      loading: false,
      tableData: [],
      apps: [],
      ruleForm: {},
      rules: {
        app_id: [
          { required: true, message: '产品必须选择', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.list()
    this.children_list()
  },
  methods: {
    list() {
      this.loading = true
      hot().then(response => {
        this.tableData = response.data
        this.loading = false
      })
    },
    children_list() {
      children_apps().then(response => {
        this.apps = response.data
      })
    },
    createView() {
      this.dialogVisible = true
      this.ruleForm = {
        status: 1
      }
    },
    editView(row) {
      this.ruleForm = row
      this.dialogVisible = true
    },
    saveHandle() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.btnLoading = true
          hot_store(this.ruleForm).then(() => {
            this.$message.success('添加成功')
            this.dialogVisible = false
            this.list()
            this.btnLoading = false
          }).catch(() => {
            this.btnLoading = false
          })
        }
      })
    },
    deleteHandle(row) {
      hot_delete(row).then(() => {
        this.$message.success('删除成功')
        this.list()
      })
    }
  }
}
</script>

<style scoped>

</style>
