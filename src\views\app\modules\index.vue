<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <!-- <span>常用操作</span> -->
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
          <el-button style="padding: 3px 0" type="text" @click="createView">添加</el-button>
        </div>
      </div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        style="width: 100%"
        :default-sort="{prop: 'sort', order: 'ascending'}"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="90"
        />
        <el-table-column
          prop="title"
          label="操作名称"
        />
        <el-table-column
          prop="url"
          label="跳转地址"
        />
        <el-table-column
          prop="sort"
          label="排序"
          sortable
        />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="editView(scope.row)">编辑</el-button>
            <el-divider direction="vertical" />
            <el-popconfirm
              title="确定删除当前记录吗？"
              @onConfirm="deleteHandle(scope.row)"
            >
              <el-button slot="reference" :loading="loading" type="text" style="color: red">删除</el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </el-card>
    <el-dialog
      title="操作管理"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm" size="small">
        <el-form-item label="操作名称" prop="title" style="width: 90%">
          <el-input v-model="ruleForm.title" />
        </el-form-item>
        <el-form-item label="跳转地址" prop="url" style="width: 90%">
          <el-input v-model="ruleForm.url" />
        </el-form-item>
        <el-form-item label="排序" prop="sort" style="width: 90%">
          <el-input-number v-model="ruleForm.sort" :min="0" :max="10" label="数字越小越靠前最小0" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button size="small" type="primary" :loading="btnLoading" @click="saveHandle">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import { modules, modules_del, modules_store } from '../../../api/app'

export default {
  name: 'Index',
  components: { Pagination },
  data() {
    return {
      dialogVisible: false,
      tableLoading: false,
      tableData: [],
      ruleForm: {},
      total: 0,
      listQuery: {
        page: 1,
        limit: 10
      },
      loading: false,
      btnLoading: false,
      rules: {
        title: [{ required: true, message: '请输入操作名称', trigger: 'blur' }],
        sort: [{ required: true, message: '排序必须填写', trigger: 'blur' }],
        url: [
          { required: true, message: '请输入跳转地址', trigger: 'blur' },
          { type: 'url', message: '请输入正确的URL地址', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listQuery.app_id = this.$route.query.id
      this.tableLoading = true
      modules(this.listQuery).then(response => {
        this.tableData = response.data.data
        this.total = response.data.total
        this.tableLoading = false
      })
    },
    createView() {
      this.ruleForm = {
        sort: 0
      }
      this.dialogVisible = true
    },
    editView(row) {
      this.ruleForm = row
      this.dialogVisible = true
    },
    saveHandle() {
      this.ruleForm.app_id = this.$route.query.id
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.btnLoading = true
          modules_store(this.ruleForm).then(() => {
            this.$message.success('操作成功')
            this.btnLoading = false
            this.getList()
            this.dialogVisible = false
          }).catch(() => {
            this.btnLoading = false
          })
        }
      })
    },
    deleteHandle(row) {
      this.loading = true
      modules_del({ id: row.id }).then(response => {
        this.$message.success('删除成功')
        this.loading = false
        this.getList()
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .list-wrap {
    height: 100%;

    .box-card {
      border: none;
      height: 100%;

      ::v-deep .el-card__body {
        height: calc(100% - 59px);
        overflow: auto;
      }
    }
  }
</style>
