<script>
import Layout from './components/Layout.vue'
import List from './components/workday/List.vue'

export default {
  name: 'WorkDay',
  components: { Layout },
  data() {
    return {
      btns: [
        {
          label: '单休工作日',
          value: '1',
          component: List,
          showFilter: false,
          tips: '',
          isExclude: true
        },
        {
          label: '法定工作日',
          value: '2',
          component: List,
          showFilter: false,
          tips: '添加法定节假日，通过排除获得法定工作日',
          isExclude: false
        },
        {
          label: '法定节假日',
          value: '3',
          component: List,
          showFilter: false,
          tips: '',
          isExclude: true
        }
      ],
      activeBtn: '1'
    }
  },
  methods: {
    handleAdd() {
      this.$refs.layoutRef.$refs.listRef.handleAdd()
    }
  }
}
</script>

<template>
  <Layout ref="layoutRef" :btns="btns" :active.sync="activeBtn">
    <template #actions>
      <el-button type="primary" size="mini" @click="handleAdd()">新增</el-button>
    </template>
  </Layout>
</template>

<style scoped lang="scss">
::v-deep .el-card__body{
  height: 100%;
}
.content{
  height: calc(100% - 36px - 20px);
}
</style>
