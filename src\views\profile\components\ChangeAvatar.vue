<template>
  <div class="app-container">
    <el-form>
      <el-form-item>
        <el-row type="flex" justify="center">
          <el-col :span="12">
            <el-upload
              class="avatar-uploader"
              :action="upload_url"
              :headers="headers"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="ruleForm.avatar" :src="ruleForm.avatar" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon" />
              <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过2M</div>
            </el-upload>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item>
        <el-row type="flex" justify="center">
          <el-col :span="8">
            <el-button type="primary" size="small" @click="saveAvatar">保存</el-button>
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { changeProfile } from '../../../api/user'
export default {
  name: 'ChangeAvatar',
  props: {
    user: {
      type: Object,
      default: () => {
        return {
          avatar: ''
        }
      }
    }
  },
  data() {
    return {
      ruleForm: {
        avatar: this.user.avatar
      },
      upload_url: process.env.VUE_APP_BASE_API + '/upload',
      headers: {
        Authorization: 'Bearer ' + this.token
      }
    }
  },
  methods: {
    handleAvatarSuccess(res, file) {
      this.ruleForm.avatar = res.data.url
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPng = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG && !isPng) {
        this.$message.error('上传头像图片只能是 JPG 或 PNG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
      }
      return (isJPG || isPng) && isLt2M
    },
    saveAvatar() {
      this.ruleForm.type = 2
      changeProfile(this.ruleForm).then(response => {
        this.$message.success('更新成功，刷新页面后生效')
      })
    }
  }
}
</script>

<style>
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 100px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>
