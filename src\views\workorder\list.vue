<template>
  <div class="list-wrap">
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      highlight-current-row
      style="width: 100%"
      :height="showPage ? 'calc(100% - 96px)' : '100%'"
    >
      <el-table-column
        prop="work_num"
        label="工单编号"
      />
      <el-table-column
        prop="question"
        label="问题描述"
      />
      <el-table-column
        v-if="!simple"
        prop="category"
        label="问题类型"
      />
      <el-table-column v-if="!simple" label="工单状态" width="120">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 0" type="danger" effect="dark">待回复</el-tag>
          <el-tag v-if="scope.row.status === 1" type="warning" effect="dark">处理中</el-tag>
          <el-tag v-if="scope.row.status === 2" type="success" effect="dark">已回复</el-tag>
          <el-tag v-if="scope.row.status === 3" type="info" effect="dark">已关闭</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        v-if="!simple"
        prop="created_at"
        label="提问时间"
        width="200"
      />
      <el-table-column
        prop="username"
        label="提问人"
        width="100"
      />
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-link v-if="scope.row.status !== 3" :type="!simple ? 'success' : 'primary'" :underline="false" @click="detail(scope.row)">回复</el-link>
          <el-popconfirm
            v-if="scope.row.status !== 3"
            title="确定关闭当前工单吗？"
            style="margin-left: 10px"
            @onConfirm="closeOrderHandle(scope.row)"
          >
            <el-link slot="reference" :underline="false" :type="!simple ? 'danger' : 'primary'">关闭</el-link>
          </el-popconfirm>
          <el-link v-if="scope.row.status === 3 && !simple" :type="!simple ? 'primary' : 'primary'" :underline="false" style="margin-left: 10px" @click="detail(scope.row)">详情</el-link>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0 && showPage" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="orders" />
  </div>
</template>

<script>
import { list, closeOrder } from '../../api/workorder'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
export default {
  name: 'WorkOrderList',
  components: { Pagination },
  props: {
    showPage: {
      type: Boolean,
      default: true
    },
    simple: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableLoading: false,
      total: 0,
      listQuery: {
        page: 1,
        limit: 20
      },
      tableData: []
    }
  },
  created() {
    this.orders()
  },
  methods: {
    orders() {
      this.tableLoading = false
      list(this.listQuery).then(response => {
        this.total = response.data.total
        this.tableData = response.data.data
        this.tableLoading = false
      })
    },
    detail(row) {
      this.$router.push({
        path: '/workorder/detail',
        query: {
          id: row.id
        }
      })
    },
    closeOrderHandle(row) {
      closeOrder(row).then(response => {
        this.$message.success('工单已关闭')
        this.orders()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap {
  height: 100%;

  .box-card {
    border: none;
    height: 100%;

    ::v-deep .el-card__body {
      height: 100%;
      overflow: auto;
    }
  }
}
</style>
