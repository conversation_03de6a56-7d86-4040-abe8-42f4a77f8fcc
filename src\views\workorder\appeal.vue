<script>
import Layout from '@/views/egt/hr/components/Layout.vue'
import Leave from '@/views/egt/leave/leave.vue'
import Appeal from '@/views/egt/leave/appeal.vue'

export default {
  name: 'Appeal',
  components: { Layout },
  data() {
    return {
      btns: [
        {
          label: '留言列表',
          value: '1',
          component: Leave
        },
        {
          label: '申诉列表',
          value: '2',
          component: Appeal
        }
      ],
      activeBtn: '1'
    }
  },
  methods: {
    handleAdd() {
      this.$refs.layoutRef.$refs.listRef.handleAdd()
    }
  }
}
</script>

<template>
  <Layout ref="layoutRef" :btns="btns" :active.sync="activeBtn">
  </Layout>
</template>

<style scoped>

</style>
