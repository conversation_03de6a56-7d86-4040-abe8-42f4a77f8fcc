import request from '@/utils/request'

export function getRoles() {
  return request({
    url: '/roles/list',
    method: 'post'
  })
}
export function storeRole(data) {
  return request({
    url: '/roles/store',
    method: 'post',
    data
  })
}

export function updateRole(data) {
  return request({
    url: '/roles/update',
    method: 'post',
    data
  })
}

export function deleteRole(data) {
  return request({
    url: '/roles/delete',
    method: 'post',
    data
  })
}
