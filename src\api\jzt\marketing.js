import request from '@/utils/requestJzt'

export function getGameList(data) {
  return request({
    url: '/zhymans/gamelst',
    method: 'post',
    data
  })
}

export function delGame(data) {
  return request({
    url: '/zhymans/delgame',
    method: 'post',
    data
  })
}

export function editGame(data) {
  return request({
    url: '/zhymans/editgame',
    method: 'post',
    data
  })
}

// 添加编辑套餐
export function editMeal(data) {
  return request({
    url: '/marketing/game_edit',
    method: 'post',
    data
  })
}
