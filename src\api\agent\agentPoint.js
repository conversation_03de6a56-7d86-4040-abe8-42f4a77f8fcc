import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/agentpoint/list',
    method: 'post',
    data: data
  })
}

export function noticelist(data) {
  return request({
    url: '/agentpoint/noticelist',
    method: 'post',
    data: data
  })
}

export function add(data) {
  return request({
    url: '/agentpoint/add',
    method: 'post',
    data: data
  })
}

export function update(data) {
  return request({
    url: '/agentpoint/update',
    method: 'post',
    data: data
  })
}

export function show(data) {
  return request({
    url: '/agentpoint/show',
    method: 'post',
    data: data
  })
}

export function delect(data) {
  return request({
    url: '/agentpoint/delect',
    method: 'post',
    data: data
  })
}
