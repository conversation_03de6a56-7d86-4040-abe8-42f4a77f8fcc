<script>
import { addDISC<PERSON>ist<PERSON><PERSON>, editDISCList<PERSON>pi, getAddDISCListApi, getEditDISCListApi } from '@/api/egt/recruitment'

const optionRules = {
  required: true,
  message: '请输入选项',
  trigger: 'blur'
}
const cateRules = {
  required: true,
  message: '请选择',
  trigger: 'change'
}
export default {
  name: 'DiscUpdate',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      optionRules,
      cateRules,
      labels: ['A', 'B', 'C', 'D'],
      form: {
        options: [
          {
            label: 'A',
            id: '',
            value: '',
            cate: ''
          },
          {
            label: 'B',
            id: '',
            value: '',
            cate: ''
          },
          {
            label: 'C',
            id: '',
            value: '',
            cate: ''
          },
          {
            label: 'D',
            id: '',
            value: '',
            cate: ''
          }
        ]
      },
      options: [],
      loading: false,
      saveLoading: false
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    title() {
      return this.data.id ? '编辑' : '新增'
    }
  },
  watch: {
    _visible(val) {
      if (val) {
        if (this.data.id) {
          this.getUpdateParams()
        } else {
          this.getAddParams()
        }
      } else {
        this.$refs.form.resetFields()
        this.options = []
      }
    }
  },
  methods: {
    // 表单提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const postData = this.form.options.map((item, index) => {
            return {
              id: this.data.id ? item.id : undefined,
              title: item.value,
              psychology: item.cate
            }
          })
          this.saveData(postData)
        } else {
          return false
        }
      })
    },
    // 保存
    async saveData(data) {
      try {
        this.saveLoading = true
        let api = addDISCListApi
        const params = new URLSearchParams()

        // 构建类似serialize格式的参数
        data.forEach((item, index) => {
          if (this.data.id) {
            params.append(`title[${item.id}]`, String(item.title)) // 确保值为字符串
            params.append(`psychology[${item.id}]`, String(item.psychology))
          } else {
            params.append(`title[${index}]`, String(item.title)) // 确保值为字符串
            params.append(`psychology[${index}]`, String(item.psychology))
          }
        })

        if (this.data.id) {
          params.append('id', this.data.id)
          api = editDISCListApi
        }

        // 生成最终字符串（类似jQuery.serialize()）
        const serialized = params.toString()

        const res = await api(serialized) // 发送序列化后的字符串
        if (res.code === 200) {
          this.$message.success(this.data.id ? '编辑成功' : '新增成功')
          this.$emit('submit', this.form)
          this.handleCancel()
        } else {
          this.$message.error(res.msg || res.message || (this.data.id ? '编辑失败' : '新增失败'))
        }
      } catch (err) {
        console.log('🚀 ~ addDISC ~ err: ', err)
        this.$message.error(this.data.id ? '编辑失败' : '新增失败')
      } finally {
        this.saveLoading = false
      }
    },
    // 表单重置
    handleCancel() {
      this._visible = false
      // 清空表单
      this.$refs.form.resetFields()
      this.$emit('cancel')
    },
    // 处理获取的选项数据
    handleGetCateOptions(data) {
      if (!data) return
      for (const typeKey in data) {
        const typeValue = data[typeKey]
        this.options.push({
          label: typeValue,
          value: typeKey
        })
      }
    },
    // 获取新增参数
    async getAddParams() {
      this.options = []
      try {
        this.loading = true
        const res = await getAddDISCListApi()
        if (res.code === 200) {
          if (res.data && res.data.data) {
            this.handleGetCateOptions(res.data.data.type)
          }
        }
      } catch (err) {
        console.log('🚀 ~ getAddParams ~ err: ', err)
      } finally {
        this.loading = false
      }
    },
    // 获取编辑参数
    async getUpdateParams() {
      this.options = []
      try {
        this.loading = true
        const res = await getEditDISCListApi({ id: this.data.id })
        if (res.code === 200) {
          if (res.data && res.data.data) {
            const formData = []
            for (const key in res.data.data) {
              const value = res.data.data[key]
              formData.push({
                id: value.id,
                value: value.title,
                cate: value.psychology
              })
            }
            this.form.options = formData.map((item, index) => {
              return {
                label: this.labels[index],
                id: item.id,
                value: item.value,
                cate: item.cate
              }
            })
          }
          if (res.data && res.data.type) {
            this.handleGetCateOptions(res.data.type)
          }
        }
      } catch (err) {
        console.log('🚀 ~ getAddParams ~ err: ', err)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<template>
  <el-dialog :close-on-click-modal="false" :title="title + '测试题'" :visible.sync="_visible">
    <el-form ref="form" v-loading="loading" :model="form" label-width="80px">
      <el-form-item
        v-for="(option, index) in form.options"
        :key="index"
        :label="'选项' + option.label"
      >
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item :rules="optionRules" :prop="'options.' + index + '.value'">
              <el-input v-model="option.value" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item :rules="cateRules" :prop="'options.' + index + '.cate'">
              <el-select v-model="option.cate" placeholder="请选择">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="handleCancel()">取 消</el-button>
      <el-button :loading="saveLoading" type="primary" @click="handleSubmit()">确 定</el-button>
    </div>
  </el-dialog>
</template>
