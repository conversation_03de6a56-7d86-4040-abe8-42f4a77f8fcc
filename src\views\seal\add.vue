<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>新增更新</span>
      </div>
      <el-col :span="12">
        <version-form :type="true" @transfer="sonSave" />
      </el-col>
    </el-card>
  </div>
</template>

<script>
import VersionForm from './components/version-form'
import { store } from '@/api/seal'
export default {
  name: 'Add',
  components: { VersionForm },
  data() {
    return {
    }
  },
  methods: {
    sonSave(form) {
      store(form).then(response => {
        this.$message.success('添加成功')
        this.$router.go(-1)
      })
    }
  }
}
</script>

<style scoped>

</style>
