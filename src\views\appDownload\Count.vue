<script>
/*eslint-disable*/
import {delCount, getCountList} from "@/api/appDownload";
import mixins from "@/mixins/dialog";
import AddCount from "@/views/appDownload/components/AddCount.vue";
import AddStore from "@/views/appDownload/components/AddStore.vue";
import CountDetail from "@/views/appDownload/components/CountDetail.vue";

export default {
  name: "DownloadCount",
  components: {CountDetail, AddStore, AddCount},
  data(){
    return {
      loading: false,
      tableData: [],
      addVisible: false,
      addId: undefined,
      addTitle: '',
      addData: null,
      page: {
        page: 1,
        limit: 10
      },
      total: 0,
      detailVisible: false,
    }
  },
  mixins: [mixins],
  methods: {
    handleGetCountList(page){
      if(page){
        this.page.page = page
      }
      this.loading = true;
      getCountList(this.page).then(res => {
        if (res.code === 200 && res.data) {
          if(res.data.data){
            this.tableData = res.data.data;
          }
          this.total = res.data.total
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    closeAdd(refresh) {
      this.changeDialogVisible({
        name: 'addVisible',
        id: undefined,
        idField: 'addId',
        title: '',
        titleField: 'addTitle',
        data: null,
        dataField: 'addData',
        visible: false
      });
      if (refresh) {
        this.handleGetCountList();
      }
    },
    closeDetail(refresh) {
      this.changeDialogVisible({
        name: 'detailVisible',
        id: undefined,
        idField: 'addId',
        title: '',
        titleField: 'addTitle',
        data: null,
        dataField: 'addData',
        visible: false
      });
      if (refresh) {
        this.handleGetCountList();
      }
    },
    del(id){
      if (id){
        this.$confirm('确认删除该手机商店？', '温馨提示')
          .then(_ => {
            this.loading = true;
            delCount({id}).then(res => {
              if (res.code === 200){
                this.$message.success(res.msg || res.message || '删除成功');
                this.handleGetCountList();
              }else{
                this.$message.error(res.msg || res.message || '删除失败');
              }
            }).finally(() => {
              this.loading = false
            })
          })
          .catch(_ => {});
      }
    }
  },
  mounted() {
    this.handleGetCountList(1);
  }
}
</script>

<template>
  <div id="appDownload" class="app-container">
    <el-card class="box-card" style="margin-bottom: 15px" shadow="hover">
      <div slot="header" style="display: flex;justify-content: space-between;align-items: center">
        <span>列表</span>
        <el-button type="text" @click="changeDialogVisible({
            name: 'addVisible',
            id: undefined,
            idField: 'addId',
            title: '录入下载量',
            titleField: 'addTitle',
            data: null,
            dataField: 'addData',
            visible: true
          })">录入下载量
        </el-button>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
      >
        <el-table-column align="center" type="index" label="序号" width="80"/>
        <el-table-column align="center" prop="marketname" label="应用市场"/>
        <el-table-column align="center" prop="apptypename" label="操作系统"/>
        <el-table-column align="center" prop="downloads" label="下载量"/>
        <el-table-column align="center" prop="totalday" label="统计日期"/>
        <el-table-column align="center" fixed="right" label="操作">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="changeDialogVisible({
                  name: 'detailVisible',
                  id: scope.row.id,
                  idField: 'addId',
                  title: '下载详情',
                  titleField: 'addTitle',
                  data: scope.row,
                  dataField: 'addData',
                  visible: true
                })"
            >查看
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="changeDialogVisible({
                  name: 'addVisible',
                  id: scope.row.id,
                  idField: 'addId',
                  title: '下载详情',
                  titleField: 'addTitle',
                  data: scope.row,
                  dataField: 'addData',
                  visible: true
                })"
            >编辑
            </el-button>
            <el-button type="text" size="small" @click="del(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="page" style="text-align: center;margin-top: 20px;">
        <el-pagination
          v-show="total>0"
          :total="total"
          :page.sync="page.page"
          :limit.sync="page.limit"
          @pagination="handleGetCountList"
        />
      </div>
    </el-card>

    <el-dialog :destroy-on-close="true" :close-on-click-modal="false" :title="addTitle" :visible.sync="addVisible"
               width="500px">
      <add-count @closeDialog="closeAdd" :id="addId" :data="addData" />
    </el-dialog>
    <el-dialog :destroy-on-close="true" :close-on-click-modal="false" :title="addTitle" :visible.sync="detailVisible"
               width="500px">
      <count-detail @closeDialog="closeDetail" :id="addId" :data="addData" />
    </el-dialog>
  </div>
</template>

<style scoped>

</style>
