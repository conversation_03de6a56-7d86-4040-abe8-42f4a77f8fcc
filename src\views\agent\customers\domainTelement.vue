<template>
  <el-form ref="ruleForm" v-loading="formLoading" :model="custType === 'CUST_PERSONAL'?ruleForm_person:ruleForm_org" :rules="custType==='CUST_PERSONAL'?rules_person:rules_org" label-width="220px" class="demo-ruleForm">
    <el-row>
      <el-col style="max-width: 470px">
        <el-form-item label="域名持有者类型：" prop="legal_person">
          <el-radio v-model="custType" label="CUST_PERSONAL" :disabled="disable">个人</el-radio>
          <el-radio v-model="custType" label="CUST_CORPORATE" :disabled="disable">企业</el-radio>
        </el-form-item>
        <el-divider content-position="left">填写持有者基本信息</el-divider>

        <div :hidden="custType !== 'CUST_PERSONAL'">
          <el-form-item label="持有者名称(中文)：" prop="custName">
            <el-input v-model="ruleForm_person.custName" placeholder="请输入域名持有者名称(中文)" :disabled="disable" @blur="toChinaPinyin(ruleForm_person.custName,'custNameEn')" />
          </el-form-item>
          <el-form-item label="持有者名称(英文/拼音)：" prop="custNameEn">
            <el-input v-model="ruleForm_person.custNameEn" placeholder="请输入域名持有者名称(英文/拼音)" :disabled="disable" />
          </el-form-item>
          <el-form-item label="所在省市：" prop="agentArea">
            <el-cascader
              v-model="ruleForm_person.agentArea"
              style="width: 100%"
              :props="{ value: 'id',label:'name' }"
              :options="provinceAndCityData"
              filterable
              clearable
              placeholder="可搜索"
            />
          </el-form-item>
          <el-form-item label="街道地址(中文)：" prop="street">
            <el-input v-model="ruleForm_person.street" placeholder="请输入街道地址(中文)" @blur="toChinaPinyin(ruleForm_person.street,'streetEn')" />
          </el-form-item>
          <el-form-item label="街道地址(英文/拼音)：" prop="streetEn">
            <el-input v-model="ruleForm_person.streetEn" placeholder="请输入街道地址(英文/拼音)" />
          </el-form-item>
          <el-form-item label="邮编：" prop="zipCode">
            <el-input v-model="ruleForm_person.zipCode" placeholder="请输入邮编" />
          </el-form-item>
          <el-form-item label="电子邮箱：" prop="email">
            <el-input v-model="ruleForm_person.email" placeholder="请输入电子邮箱" />
          </el-form-item>

          <el-divider content-position="left">填写联系人信息</el-divider>
          <el-form-item label="联系人名称(中文)：" prop="linkman">
            <el-input v-model="ruleForm_person.linkman" placeholder="请输入填写联系人信息" :disabled="disable" @blur="toChinaPinyin(ruleForm_person.linkman,'linkmanEn')" />
          </el-form-item>
          <el-form-item label="联系人名称(英文/拼音)：" prop="linkmanEn">
            <el-input v-model="ruleForm_person.linkmanEn" placeholder="请输入联系人名称(英文/拼音)" :disabled="disable" />
          </el-form-item>
          <el-form-item label="手机号码：" prop="mobile">
            <el-input v-model="ruleForm_person.mobile" placeholder="请输入手机号码" />
          </el-form-item>
          <el-form-item label="备注：" prop="memo">
            <el-input v-model="ruleForm_person.memo" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>

          <el-divider content-position="left">填写CNNIC证件信息</el-divider>
          <el-form-item label="证件类型：" prop="certType">
            <el-select v-model="ruleForm_person.certType" placeholder="请选择" :disabled="disable">
              <el-option
                v-for="item in PersonCertType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="证件号码：" prop="certNumber">
            <el-input v-model="ruleForm_person.certNumber" placeholder="请输入证件号码" :disabled="disable" />
          </el-form-item>

          <el-divider content-position="left">填写核验库证件信息</el-divider>
          <el-form-item label="证件类型：" prop="orgIdType">
            <el-select v-model="ruleForm_person.orgIdType" placeholder="请选择" :disabled="disable">
              <el-option
                v-for="item in PersonOrgIdType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="证件号码：" prop="orgIdCode">
            <el-input v-model="ruleForm_person.orgIdCode" placeholder="请输入证件号码" :disabled="disable" />
          </el-form-item>

        </div>
        <div :hidden="custType === 'CUST_PERSONAL'">
          <el-form-item label="持有者单位名称(中文)：" prop="custName">
            <el-input v-model="ruleForm_org.custName" placeholder="请输入持有者单位名称(中文)" :disabled="disable" @blur="toChinaPinyin(ruleForm_org.custName,'custNameEn')" />
          </el-form-item>
          <el-form-item label="持有者单位名称(英文/拼音)：" prop="custNameEn">
            <el-input v-model="ruleForm_org.custNameEn" placeholder="请输入持有者单位名称(英文/拼音)" :disabled="disable" />
          </el-form-item>
          <el-form-item label="营业年限：" prop="operAge">
            <el-input v-model="ruleForm_org.operAge" type="number" placeholder="请输入营业年限" />
          </el-form-item>
          <el-form-item label="企业规模：" prop="firmSize">
            <el-select v-model="ruleForm_org.firmSize" style="width: 100%">
              <el-option v-for="item in scales" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="行业分类：" prop="industryType">
            <el-select v-model="ruleForm_org.industryType" style="width: 100%">
              <el-option v-for="(item,index) in trades" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="主营产品：" prop="mainBusiness">
            <el-input v-model="ruleForm_org.mainBusiness" placeholder="请输入主营产品" />
          </el-form-item>
          <el-form-item label="网址：" prop="url">
            <el-input v-model="ruleForm_org.url" placeholder="请输入公司网址" />
          </el-form-item>
          <el-form-item label="所在省市：" prop="agentArea">
            <el-cascader
              v-model="ruleForm_org.agentArea"
              style="width: 100%"
              :props="{ value: 'id',label:'name' }"
              :options="provinceAndCityData"
              filterable
              clearable
              placeholder="可搜索"
            />
          </el-form-item>
          <el-form-item label="街道地址(中文)：" prop="street">
            <el-input v-model="ruleForm_org.street" placeholder="请输入街道地址(中文)" @blur="toChinaPinyin(ruleForm_org.street,'streetEn')" />
          </el-form-item>
          <el-form-item label="街道地址(英文/拼音)：" prop="streetEn">
            <el-input v-model="ruleForm_org.streetEn" placeholder="请输入街道地址(英文/拼音)" />
          </el-form-item>
          <el-form-item label="邮编：" prop="zipCode">
            <el-input v-model="ruleForm_org.zipCode" placeholder="请输入邮编" />
          </el-form-item>
          <el-form-item label="固定电话：" prop="firstNum1">
            <el-row>
              <el-input v-model="ruleForm_org.firstNum1" style="width: 100px" placeholder="区号" />
              <el-input v-model="ruleForm_org.lastNum1" style="width: 155px" placeholder="座机号码" />
            </el-row>
          </el-form-item>
          <el-form-item prop="lastNum1" />
          <el-form-item label="传真：" prop="fax">
            <el-input v-model="ruleForm_org.fax" placeholder="请输入传真" />
          </el-form-item>
          <el-form-item label="电子邮箱：" prop="email">
            <el-input v-model="ruleForm_org.email" placeholder="请输入电子邮箱" />
          </el-form-item>

          <el-divider content-position="left">填写联系人信息</el-divider>
          <el-form-item label="联系人名称(中文)：" prop="linkman">
            <el-input v-model="ruleForm_org.linkman" placeholder="请填写联系人信息" :disabled="disable" @blur="toChinaPinyin(ruleForm_org.linkman,'linkmanEn')" />
          </el-form-item>
          <el-form-item label="联系人名称(英文/拼音)：" prop="linkmanEn">
            <el-input v-model="ruleForm_org.linkmanEn" placeholder="请填写联系人名称(英文/拼音)" :disabled="disable" />
          </el-form-item>
          <el-form-item label="手机号码：" prop="mobile">
            <el-input v-model="ruleForm_org.mobile" placeholder="请输入手机号码" />
          </el-form-item>
          <el-form-item label="备注：" prop="memo">
            <el-input v-model="ruleForm_org.memo" type="textarea" row="3" placeholder="请输入备注信息" />
          </el-form-item>

          <el-divider content-position="left">填写CNNIC证件信息</el-divider>
          <el-form-item label="证件类型：" prop="certType">
            <el-select v-model="ruleForm_org.certType" placeholder="请选择" :disabled="disable">
              <el-option
                v-for="item in OrgCertType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="证件号码：" prop="certNumber">
            <el-input v-model="ruleForm_org.certNumber" placeholder="请输入证件号码" :disabled="disable" />
          </el-form-item>

          <el-divider content-position="left">填写核验库证件信息</el-divider>
          <el-form-item label="证件类型：" prop="orgIdType">
            <el-select v-model="ruleForm_org.orgIdType" placeholder="请选择" :disabled="disable">
              <el-option
                v-for="item in OrgOrgIdType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="证件号码：" prop="orgIdCode">
            <el-input v-model="ruleForm_org.orgIdCode" placeholder="请输入证件号码" :disabled="disable" />
          </el-form-item>

        </div>

        <el-button style="margin-bottom: 50px;margin-top:30px;margin-left: 190px" type="primary" @click="submit">提交保存</el-button>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import { create, idcCity, detail } from '@/api/agent/domain'
import { detail as custdetail } from '@/api/agent/customers'
import vPinyin from '@/utils/vue-py'
import Vue from 'vue'
export default {
  name: 'DomainTelement',
  data() {
    return {
      formLoading: false,
      id: this.$route.query.id,
      t_id: this.$route.query.t_id,
      disable: !!this.$route.query.t_id,
      ruleForm_person: {},
      ruleForm_org: {
        firstNum1: '0451',
        lastNum1: '83152780'
      },
      provinceAndCityData: [],
      activeName: 'first',
      custType: 'CUST_CORPORATE',
      rules_person: {
        custName: [{ required: true, message: '必填', trigger: ['blur', 'change'] }, { min: 2, max: 150, message: '长度在 2 到 150 个字符', trigger: 'blur' }],
        custNameEn: [{ required: true, message: '必填', trigger: ['blur', 'change'] }, { max: 150, message: '长度最多150个字符', trigger: 'blur' }],
        agentArea: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        street: [{ required: true, message: '必填', trigger: ['blur', 'change'] }, { max: 150, message: '长度最多150个字符', trigger: 'blur' }],
        streetEn: [{ required: true, message: '必填', trigger: ['blur', 'change'] }, { max: 63, message: '长度最多63个字符', trigger: 'blur' }],
        zipCode: [{ required: false, message: '必填', trigger: ['blur', 'change'] }],
        email: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        linkman: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        linkmanEn: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        mobile: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        memo: [{ required: false, message: '必填', trigger: ['blur', 'change'] }],
        certType: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        certNumber: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        orgIdType: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        orgIdCode: [{ required: true, message: '必填', trigger: ['blur', 'change'] }]
      },
      rules_org: {
        custName: [{ required: true, message: '必填', trigger: 'blur' }, { min: 2, max: 150, message: '长度在 2 到 150 个字符', trigger: 'blur' }],
        custNameEn: [{ required: true, message: '必填', trigger: ['blur', 'change'] }, { max: 150, message: '长度最多150个字符', trigger: 'blur' }],
        operAge: [{ required: false, message: '必填', trigger: 'blur' }],
        firmSize: [{ required: false, message: '必填', trigger: 'blur' }],
        industryType: [{ required: false, message: '必填', trigger: 'change' }],
        mainBusiness: [{ required: false, message: '必填', trigger: ['blur', 'change'] }],
        url: [{ required: false, message: '必填', trigger: 'blur' }],
        agentArea: [{ required: true, message: '必选', trigger: 'change' }],
        street: [{ required: true, message: '必选', trigger: 'blur' }, { max: 150, message: '长度最多150个字符', trigger: 'blur' }],
        streetEn: [{ required: true, message: '必选', trigger: ['blur', 'change'] }, { max: 63, message: '长度最多63个字符', trigger: 'blur' }],
        zipCode: [{ required: false, message: '必选', trigger: 'blur' }],
        firstNum1: [{ required: true, message: '必选', trigger: ['blur', 'change'] }],
        lastNum1: [{ required: true, message: '必选', trigger: ['blur', 'change'] }],
        fax: [{ required: false, message: '必选', trigger: ['blur', 'change'] }],
        email: [{ required: true, message: '必选', trigger: 'blur' }, { type: 'email', message: '邮箱格式不正确', trigger: ['blur', 'change'] }],
        linkman: [{ required: true, message: '必填', trigger: 'blur' }],
        linkmanEn: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        mobile: [{ required: true, message: '必填', trigger: 'blur' }],
        memo: [{ required: false, message: '必填', trigger: 'blur' }],
        certType: [{ required: true, message: '必填', trigger: 'change' }],
        certNumber: [{ required: true, message: '必填', trigger: 'blur' }],
        orgIdType: [{ required: true, message: '必填', trigger: 'change' }],
        orgIdCode: [{ required: true, message: '必填', trigger: 'blur' }]
      },
      scales: [
        {
          'label': '少于50人',
          'value': 'FIRM_SIZE_01'
        },
        {
          'label': '50-150人',
          'value': 'FIRM_SIZE_02'
        },
        {
          'label': '150-500人',
          'value': 'FIRM_SIZE_03'
        },
        {
          'label': '500-1000人',
          'value': 'FIRM_SIZE_04'
        },
        {
          'label': '1000-5000人',
          'value': 'FIRM_SIZE_05'
        },
        {
          'label': '5000-10000人',
          'value': 'FIRM_SIZE_06'
        },
        {
          'label': '10000人以上',
          'value': 'FIRM_SIZE_07'
        }
      ],
      trades: [
        {
          'label': 'IT/通信/电子/互联网',
          'value': 'BUSINESS_CLASSIFY_01'
        },
        {
          'label': '教育/培训',
          'value': 'BUSINESS_CLASSIFY_03'
        },
        {
          'label': '传媒/文化/出版/印刷',
          'value': 'BUSINESS_CLASSIFY_07'
        },
        {
          'label': '农业牧渔',
          'value': 'BUSINESS_CLASSIFY_14'
        },
        {
          'label': '能源/电器/地质',
          'value': 'BUSINESS_CLASSIFY_13'
        },
        {
          'label': '金融/保险/证券',
          'value': 'BUSINESS_CLASSIFY_09'
        },
        {
          'label': '房地产/建筑',
          'value': 'BUSINESS_CLASSIFY_04'
        },
        {
          'label': '贸易/批发/零售',
          'value': 'BUSINESS_CLASSIFY_05'
        },
        {
          'label': '商业服务/咨询/法律/财务',
          'value': 'BUSINESS_CLASSIFY_06'
        },
        {
          'label': '加工制造/机械/仪表设备',
          'value': 'BUSINESS_CLASSIFY_02'
        },
        {
          'label': '酒店/餐饮/旅游',
          'value': 'BUSINESS_CLASSIFY_12'
        },
        {
          'label': '制药医疗/生物/卫生保健',
          'value': 'BUSINESS_CLASSIFY_11'
        },
        {
          'label': '汽车/交通运输/仓储',
          'value': 'BUSINESS_CLASSIFY_10'
        },
        {
          'label': '政府/非盈利机构',
          'value': 'BUSINESS_CLASSIFY_08'
        },
        {
          'label': '其他',
          'value': 'BUSINESS_CLASSIFY_00'
        }
      ],
      PersonCertType: [
        {
          'label': '身份证',
          'value': 'SFZ'
        },
        {
          'label': '军官证',
          'value': 'JGZ'
        }, {
          'label': '护照',
          'value': 'HZ'
        }
      ],
      OrgCertType: [
        {
          'label': '营业执照',
          'value': 'YYZZ'
        },
        {
          'label': '组织机构代码证',
          'value': 'ORG'
        },
        {
          'label': '统一社会信用代码证书',
          'value': 'TYDM'
        },
        {
          'label': '事业单位法人证书',
          'value': 'SYDWFR'
        },
        {
          'label': '社会团体法人登记证书',
          'value': 'SHTTFR'
        },
        {
          'label': '民办非企业单位登记证书',
          'value': 'MBFQY'
        },
        {
          'label': '律师事务所执业许可证',
          'value': 'LSZY'
        },
        {
          'label': '社会服务机构登记证书',
          'value': 'SHFWJG'
        },
        {
          'label': '民办学校办学许可证',
          'value': 'MBXXBX'
        },
        {
          'label': '医疗机构执业许可证',
          'value': 'YLJGZY'
        },
        {
          'label': '公证机构执业证',
          'value': 'GZJGZYZ'
        },
        {
          'label': '北京市外国驻华使馆人员子女学校办学许可证',
          'value': 'BJWSXX'
        },
        {
          'label': '其他',
          'value': 'QT'
        },
        {
          'label': '其他证件，只允许提交统一社会信用代码',
          'value': 'QTTYDM'
        }
      ],
      PersonOrgIdType: [
        {
          'label': '身份证',
          'value': 'HPT_SFZ'
        },
        {
          'label': '护照',
          'value': 'HPT_HZ'
        },
        {
          'label': '港澳居民来往内地通行证',
          'value': 'HPT_GATXZ'
        },
        {
          'label': '台湾居民来往大陆通行证',
          'value': 'HPT_TJLTXZ'
        },
        {
          'label': '外国人永久居留身份证',
          'value': 'HPT_WYJSFZ'
        }
      ],
      OrgOrgIdType: [
        {
          'label': '工商营业执照',
          'value': 'HT_YYZZ'
        },
        {
          'label': '组织机构代码证',
          'value': 'HT_ORG'
        }, {
          'label': '统一社会信用代码证书',
          'value': 'HT_XYDM'
        }, {
          'label': '部队代号',
          'value': 'HT_BDDH'
        }, {
          'label': '军队单位对外有偿服务许可证',
          'value': 'HT_JYCXKZ'
        },
        {
          'label': '事业单位法人证书',
          'value': 'HT_SYZS'
        },
        {
          'label': '外国企业常驻代表机构登记证',
          'value': 'HT_WQCZDBJGZ'
        },
        {
          'label': '社会团体法人登记证书',
          'value': 'HT_STFDZ'
        },
        {
          'label': '宗教活动场所登记证',
          'value': 'HT_ZHCDZ'
        },
        {
          'label': '民办非企业单位登记证书',
          'value': 'HT_MFQDJZ'
        },
        {
          'label': '基金会法人登记证书',
          'value': 'HT_JFDJZ'
        },
        {
          'label': '律师事务所执业许可证',
          'value': 'HT_LSZYXKZ'
        },
        {
          'label': '外国在华文化中心登记证',
          'value': 'HT_WZWZDJZ'
        },
        {
          'label': '外国政府旅游部门常驻代表机构批准登记证',
          'value': 'HT_WZLBCDJPDJZ'
        },
        {
          'label': '民办学校办学许可',
          'value': 'HT_MBXXBX'
        },
        {
          'label': '医疗机构执业许可证',
          'value': 'HT_YLJGZY'
        },
        {
          'label': '公证机构执业证',
          'value': 'HT_GZJGZYZ'
        },
        {
          'label': '北京市外国驻华使馆人员子女学校办学许可证',
          'value': 'HT_BJWSXX'
        },
        {
          'label': '司法鉴定许可证',
          'value': 'HT_SFJXKZ'
        },
        {
          'label': '其他',
          'value': 'HT_QT'
        }
      ]
    }
  },
  created() {
    this.getOptions()
    if (this.t_id) {
      this.detail()
    } else {
      this.getDetail()
    }
  },
  methods: {
    getOptions() {
      idcCity().then(res => {
        if (res.code === 200 && res.data) {
          this.provinceAndCityData = res.data.list
        }
      })
    },
    getDetail() {
      custdetail({ id: this.$route.query.id, edit: true }).then(response => {
        this.$set(this.ruleForm_person, 'custName', response.data.name)
        this.tonewChinaPinyin(this.ruleForm_person.custName, 'custNameEn', 1)
        this.$set(this.ruleForm_person, 'agentArea', response.data.agentArea)
        this.$set(this.ruleForm_person, 'street', response.data.address)
        this.$set(this.ruleForm_person, 'email', response.data.responsible_email)
        this.$set(this.ruleForm_person, 'linkman', response.data.legal_person)
        this.$set(this.ruleForm_person, 'mobile', response.data.legal_phone)
        this.$set(this.ruleForm_person, 'certType', 'SFZ')
        this.$set(this.ruleForm_person, 'certNumber', response.data.legal_card)
        this.$set(this.ruleForm_person, 'orgIdType', 'HPT_SFZ')
        this.$set(this.ruleForm_person, 'orgIdCode', response.data.legal_card)
        this.tonewChinaPinyin(this.ruleForm_person.street, 'streetEn', 1)
        this.tonewChinaPinyin(this.ruleForm_person.linkman, 'linkmanEn', 1)

        this.$set(this.ruleForm_org, 'custName', response.data.name)
        this.$set(this.ruleForm_org, 'firmSize', response.data.scale)
        this.$set(this.ruleForm_org, 'url', response.data.website)
        this.$set(this.ruleForm_org, 'street', response.data.address)
        this.$set(this.ruleForm_org, 'email', response.data.responsible_email)
        this.$set(this.ruleForm_org, 'linkman', response.data.legal_person)
        this.$set(this.ruleForm_org, 'mobile', response.data.legal_phone)
        this.$set(this.ruleForm_org, 'certNumber', response.data.license_number)
        this.$set(this.ruleForm_org, 'orgIdCode', response.data.license_number)

        this.tonewChinaPinyin(this.ruleForm_org.custName, 'custNameEn', 2)
        this.tonewChinaPinyin(this.ruleForm_org.street, 'streetEn', 2)
        this.tonewChinaPinyin(this.ruleForm_org.linkman, 'linkmanEn', 2)
      })
    },
    removeChildren(temp) {
      var that = this
      temp.forEach(function(item) {
        if (item['children'].length === 0) {
          delete (item['children'])
        } else {
          that.removeChildren(item['children'])
        }
      })
    },
    tradeChange(arr) {
      this.ruleForm.trade_first = arr[0]
      this.ruleForm.trade_second = arr[1]
    },
    detail() {
      this.formLoading = true
      detail({ id: this.id, t_id: this.t_id }).then(res => {
        this.formLoading = false
        res.data.base.agentArea = [res.data.base.province, res.data.base.city]
        res.data.template_id = this.t_id
        this.custType = res.data.base.custType
        if (this.custType === 'CUST_PERSONAL') {
          this.ruleForm_person = res.data.base
        } else {
          this.ruleForm_org = res.data.base
        }
      }).catch(() => {
        this.formLoading = false
      })
    },
    submit() {
      var fromData = this.custType === 'CUST_PERSONAL' ? this.ruleForm_person : this.ruleForm_org
      this.$refs['ruleForm'].validate((valida) => {
        if (valida) {
          fromData['id'] = this.id
          fromData['t_id'] = this.t_id
          fromData['custType'] = this.custType
          this.formLoading = true
          create(fromData).then(res => {
            this.formLoading = false
            this.$message.success(res.message)
            this.$router.go(-1)
          }).catch(() => {
            this.formLoading = false
          })
        }
      })
    },
    toChinaPinyin(text, key) {
      var pinYin = vPinyin.chineseToPinYin(text)
      if (this.custType === 'CUST_PERSONAL') {
        Vue.set(this.ruleForm_person, key, pinYin)
      } else {
        Vue.set(this.ruleForm_org, key, pinYin)
      }
    },
    tonewChinaPinyin(text, key, custType) {
      var pinYin = vPinyin.chineseToPinYin(text)
      if (custType === 1) {
        Vue.set(this.ruleForm_person, key, pinYin)
      } else {
        Vue.set(this.ruleForm_org, key, pinYin)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .demo-ruleForm{
    margin-top: 50px;
    margin-left: 50px;
  }
>>>.el-divider{
  margin-top: 60px;
  margin-bottom: 60px;
}
>>>.el-divider__text{
  font-size: 20px;
}
</style>
