<script>
import SelfFormTemp from '@/views/task/components/form/SelfFormTemp.vue'
import { resultUpdateFormData, resultUpdateFormRules, resultUpdateFormSettings } from '../../mixins/resultUpdate'
import update from '@/views/task/mixins/update'
import {
  addDISCResultListApi,
  editDISCResultListApi,
  getAddDISCResultListApi,
  getEditDISCResultListApi
} from '@/api/egt/recruitment'

export default {
  name: 'DiscResultUpdate',
  components: {
    SelfFormTemp
  },
  mixins: [update],
  data() {
    return {
      filters: [
        {
          label: '性格类型',
          prop: 'type',
          type: 'select',
          settings: {
            placeholder: '请选择性格类型',
            options: []
          }
        },
        ...resultUpdateFormSettings
      ],
      form: {
        type: '',
        ...resultUpdateFormData
      },
      rules: {
        type: [
          { required: true, message: '请选择性格类型', trigger: 'change' }
        ],
        ...resultUpdateFormRules
      }
    }
  },
  watch: {
    _visible(val) {
      if (val) {
        this.getParams()
      }
    }
  },
  methods: {
    async getParams() {
      try {
        this.loading = true
        const api = this.data.id ? getEditDISCResultListApi : getAddDISCResultListApi
        if (!api) return
        const res = await api({ id: this.data.id })
        if (res.code === 200 && res.data) {
          let typeInFormData = {}
          if (this.data.id) {
            if (res.data.type) {
              typeInFormData = res.data.type
            }
            if (res.data.data) {
              this.form = res.data.data
            }
          } else {
            if (res.data.data && res.data.data.type) {
              typeInFormData = res.data.data.type
            }
          }
          const type = []
          //   把res.data.data.type是一个对象变成数组
          for (const key in typeInFormData) {
            type.push({
              label: typeInFormData[key],
              value: +key
            })
            const typeInForm = this.filters.find(item => item.prop === 'type')
            this.$set(typeInForm.settings, 'options', type)
          }
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    },
    async submitCallback() {
      let api = addDISCResultListApi
      if (this.data.id) {
        api = editDISCResultListApi
        this.form.id = this.data.id
      }
      if (!api) return
      try {
        this.submitLoading = true
        const res = await api(this.form)
        if (res.code === 200) {
          this.$emit('submit-success')
          return Promise.resolve({ success: true })
        } else {
          return Promise.reject({ success: false, msg: res.msg || res.message || (this.data.id ? '编辑失败' : '保存失败') })
        }
      } catch (e) {
        console.log(e)
        return Promise.reject({ success: false, msg: '操作失败' })
      } finally {
        this.submitLoading = false
      }
    }
  }
}
</script>

<template>
  <div>
    <el-dialog :close-on-click-modal="false" :visible.sync="_visible" title="DISC测试结果" width="700px">
      <SelfFormTemp ref="formWrap" :filters="filters" :form-data.sync="form" :rules="rules" label-width="120px" :inline="false" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button :loading="submitLoading" type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
