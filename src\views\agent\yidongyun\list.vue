<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix" style="text-align: right">
        <!--<span>移动云主机列表</span>-->
        <el-button style="padding: 0" type="text" @click="monthlystatement()">
          移动云月度账单
        </el-button>
        <el-button style="padding: 0" type="text" @click="estimatedbill()">
          移动云月度账详单
        </el-button>
        <el-button style="padding: 0" type="text" :loading="downloadLoading" @click="exportExcel">导出</el-button>
      </div>
      <div class="filter">
        <el-form ref="form" :inline="true" :model="searchForm" label-width="12ss0px" size="small">
          <el-row>
            <el-col :span="24">
              <el-form-item label="用户名称">
                <el-input v-model="searchForm.customer" placeholder="请输入用户名称" clearable />
              </el-form-item>
              <el-form-item label="到期时间">
                <el-date-picker
                  v-model="searchForm.date"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="dateChange"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" size="small" icon="el-icon-search" @click="getList">搜索</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="table">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          style="width: 100%"
          height="calc(100% - 96px)"
        >
          <el-table-column
            prop="id"
            label="用户编号"
            width="80"
          />
          <el-table-column
            label="用户名称"
            prop="customer"
          />
          <el-table-column
            prop="agent"
            label="所属代理商"
          />
          <el-table-column
            label="主机产品"
            prop="proname"
          />
          <el-table-column
            label="运行状态"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.status === 0" style="color: #FFBA00">等待开通</span>
              <span v-if="scope.row.status === 1" style="color: #30B08F">运行中</span>
              <span v-if="scope.row.status === 2" style="color: #C03639">停止</span>
              <span v-if="scope.row.status === -1" style="color: #C03639">删除</span>
            </template>
          </el-table-column>
          <el-table-column
            label="到期时间"
            prop="expire_end"
          />
          <el-table-column
            label="操作"
            width="150"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.product_id!==403">
                <el-button type="text" @click="infoShow(scope.row)">详情</el-button>
              </span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="searchForm.total>0"
          :total="searchForm.total"
          :page.sync="searchForm.page"
          :limit.sync="searchForm.perPage"
          style="text-align:center;"
          @pagination="getList"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import moment from 'moment'
import { mapGetters } from 'vuex'
import { yidongyun_index, yidongyun_resellerOrder } from '@/api/agent/product'
import { getToken } from '@/utils/auth'
import { download } from '../utils/download'
import { ydHostListExport } from '@/api/agent/downloads'

export default {
  components: { Pagination },
  data() {
    return {
      actionUrl: process.env.VUE_APP_BASE_API + '/auth/image', // 自己上传文件的地址
      info: {
        type: 'ssl'
      },
      detailIndex: 0,
      fileList: [], // 文件列表
      limitNum: 1, // 选择文件个数
      isShowFile: false,
      rules: {
        file: [
          { required: true, message: '请上传文件', trigger: 'blur' }
        ]
      },
      iprules: {
        ip: [
          { required: true, message: '选择主机ip', trigger: 'blur' }
        ]
      },
      uploadSuccessFiles: [],
      dialogFormVisible: false,
      dialogUploadsslVisible: false,
      dialogUploadipVisible: false,
      dialogsslFormVisible: false,
      tableLoading: false,
      dialogLoading: false,
      dialogsslLoading: false,
      dialogipLoading: false,
      dialoguploadLoading: false,
      isdomain: true,
      form: {},
      sslform: {},
      uploadsslform: {},
      ipform: {},
      filename: '',
      searchForm: {
        page: 1,
        perPage: 10,
        total: 0
      },
      domrules: {
        domain: [
          { required: true, message: '请输入域名名称,不要带后缀', trigger: 'blur' }
        ],
        product_id: [
          { required: true, message: '请选择域名类型', trigger: 'blur' }
        ]
      },
      productsList: [],
      jzthoustlists: [],
      tableData: [],
      history_list: [],
      ip_list: [],
      ssfiles_list: [],
      form_loading: false,
      downloadLoading: false
    }
  },
  computed: {
    // 别名处理防止冲突
    ...mapGetters({
      userType: 'agentUserType',
      username: 'name'
    })
  },
  created() {
    this.yun_api = process.env.VUE_APP_YUN_API + '/api/upload/image'
    if (this.$route.query) {
      if (parseInt(this.$route.query['page']) > -2) {
        this.searchForm.page = parseInt(this.$route.query['page'])
      }
    }
    this.getList()
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.list-wrap > .el-card > .el-card__body')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  methods: {
    getList() {
      this.tableLoading = true
      yidongyun_index(this.searchForm).then(response => {
        if (response.code === 200 && response.data) {
          this.tableData = response.data.list
          this.searchForm.total = response.data.meta.total
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    monthlystatement() {
      this.$router.push({
        name: 'agentYidongyunMonthlyBilling',
        query: {}
      })
    },
    estimatedbill() {
      this.$router.push({
        name: 'agentYidongyunMonthlyDetails',
        query: {}
      })
    },
    showDetail(row) {
      this.$router.push({
        name: 'IDCYdyunhost_DetailRouter',
        query: {
          id: row.id
        }
      })
    },
    showDialog(row) {
      this.form = {
        id: row.finance_id,
        domain: '',
        product_id: ''
      }
      this.dialogFormVisible = true
    },
    infoShow(item) {
      this.$router.push({
        name: 'agentYidongyunDetail',
        query: { id: item.hostid, customer_id: item.id }
      })
    },
    async exportExcel() {
      try {
        this.downloadLoading = true
        const params = {
          customer: this.searchForm.customer,
          date: this.searchForm.date,
          agent: this.searchForm.agent,
          agent_exact: this.searchForm.agent_exact,
        }
        const res = await ydHostListExport(params)
        if (res.code === 200) {
          this.$message.success('导出成功，请到下载中心下载')
        } else {
          this.$message.error(res.msg || '导出失败')
        }
      } catch (error) {
        this.$message.error(error.message || '导出失败')
      } finally {
        this.downloadLoading = false
      }
    },
    dateChange() {
      if (this.searchForm.date) {
        this.searchForm.date[0] = moment(this.searchForm.date[0]).format('YYYY-MM-DD H:m:s')
        this.searchForm.date[1] = moment(this.searchForm.date[1]).format('YYYY-MM-DD H:m:s')
      }
      if (this.searchForm.kjdate) {
        this.searchForm.kjdate[0] = moment(this.searchForm.kjdate[0]).format('YYYY-MM-DD H:m:s')
        this.searchForm.kjdate[1] = moment(this.searchForm.kjdate[1]).format('YYYY-MM-DD H:m:s')
      }
      if (this.searchForm.ssldate) {
        this.searchForm.ssldate[0] = moment(this.searchForm.ssldate[0]).format('YYYY-MM-DD H:m:s')
        this.searchForm.ssldate[1] = moment(this.searchForm.ssldate[1]).format('YYYY-MM-DD H:m:s')
      }
    }
  }

}
</script>

<style scoped lang="scss">
.list-wrap {
  height: 100%;
  .box-card {
    height: 100%;
    ::v-deep .el-card__header {
      padding-top: 0;
    }
    ::v-deep .el-card__body {
      padding: 0;
      margin-top: 20px;
      height: calc(100% - 59px);
      overflow: auto;
    }
  }
  .filter {
    border-bottom: 1px solid #E5E5E5;
  }
  .table {
    margin-top: 20px;
    min-height: 200px;
  }
}
</style>
