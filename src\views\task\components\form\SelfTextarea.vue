<script>
export default {
  name: 'SelfTextarea',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入'
    },
    autosize: {
      type: Object,
      default: () => ({
        minRows: 2,
        maxRows: 6
      })
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    inputValue: {
      get() {
        return this.value ? this.value.trim() : this.value
      },
      set(val) {
        this.$emit('update:value', val)
      }
    }
  }
}
</script>

<template>
  <el-input v-model="inputValue" type="textarea" :autosize="autosize" :placeholder="placeholder" :disabled="disabled" />
</template>

<style scoped>

</style>
