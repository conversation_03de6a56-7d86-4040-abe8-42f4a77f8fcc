<template>
  <div class="list-wrap">
    <div class="filter">
      <el-form :inline="true" :model="listQuery" class="demo-form-inline" size="small">
        <el-form-item label="案例编号：">
          <el-input v-model="listQuery.case_number" placeholder="案例编号" clearable />
        </el-form-item>
        <el-form-item label="案例名称：">
          <el-input v-model="listQuery.template_name" placeholder="案例名称" clearable />
        </el-form-item>
        <el-form-item label="案例分类：">
          <el-select v-model="category_pid" placeholder="请选择" clearable>
            <el-option v-for="item in categories" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
          <el-select v-if="categoryChildren.length > 0" v-model="category_id" placeholder="请选择" clearable>
            <el-option v-for="item in categoryChildren" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="listQuery.is_release != 2" label="绑定域名：">
          <el-input v-model="listQuery.domain" placeholder="绑定域名" clearable />
        </el-form-item>
        <el-form-item v-if="listQuery.is_release != 2" label="企业名称：">
          <el-input v-model="listQuery.company_name" placeholder="企业名称" clearable />
        </el-form-item>
        <el-form-item v-if="listQuery.is_release == 1" label="分公司：">
          <el-select v-model="listQuery.district_id" placeholder="请选择" clearable>
            <el-option v-for="district_info in districtsOptions" :key="district_info.id" :label="district_info.name" :value="district_info.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="案例状态：">
          <el-select v-model="listQuery.open" placeholder="请选择" clearable>
            <el-option label="已上架" :value="1" />
            <el-option label="已下架" :value="2" />
            <el-option label="已拒绝" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="案例来源：">
          <el-select v-model="listQuery.is_release" placeholder="请选择">
            <el-option label="已发布站点" :value="1" />
            <el-option label="已有模板" :value="2" />
            <el-option label="建站通2.0" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getList">过滤</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-refresh-right" @click="getList">刷新</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table mt20">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        height="calc(100% - 96px)"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="75"
        />
        <el-table-column
          prop="case_number"
          label="案例编号"
          width="180"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.case_number_qz + scope.row.case_number }}</span>
          </template>
        </el-table-column>
        <el-table-column width="200" label="缩略图" align="center">
          <template slot-scope="scope">
            <el-image v-if="scope.row.pc_img || scope.row.layout" :src="scope.row.pc_img || scope.row.layout.thumb" class="img-color" :preview-src-list="scope.row.pc_img?[scope.row.pc_img] : [scope.row.layout.thumb]" fit="contain">
              <div slot="placeholder" class="image-slot">
                加载中<span class="dot">...</span>
              </div>
            </el-image>
            <span v-else>未设置</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="title"
          label="案例名称"
          width="200"
        />
        <el-table-column prop="type_title" label="所属分类">
          <template slot-scope="scope">
            <span>{{ scope.row.type_title || '无' }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="listQuery.is_release != 2" label="绑定域名" width="150">
          <template slot-scope="scope">
            <template v-if="scope.row.source == 2">{{ scope.row.domain }}</template>
            <template v-if="scope.row.host">{{ scope.row.host.domain }}</template>
          </template>
        </el-table-column>
        <el-table-column v-if="listQuery.is_release != 2" label="公司名称" width="200">
          <template slot-scope="scope">
            <template v-if="scope.row.source == 2">{{ scope.row.company_name }}</template>
            <template v-if="scope.row.company">{{ scope.row.company.name }}</template>
          </template>
        </el-table-column>
        <el-table-column v-if="listQuery.is_release != 2" label="分公司">
          <template slot-scope="scope">
            <template v-if="scope.row.source == 2">无</template>
            <template v-if="scope.row.district_info">{{ scope.row.district_info.name }}</template>
          </template>
        </el-table-column>
        <!--<el-table-column label="制作人" width="100" align="center">
          <template slot-scope="scope">
            {{ scope.row.source == 2 ? scope.row?.user_name : scope.row?.host?.producer }}
          </template>
        </el-table-column>-->
        <el-table-column
          label="案例状态"
          width="120"
        >
          <template slot-scope="scope">
            <!-- <el-switch
              v-model="scope.row.open"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="2"
              @change="setCaseStatus($event, scope.row.id)"
            /> -->
            <el-radio-group
              v-model="scope.row.open"
              @change="setCaseStatus($event, scope.row.id)"
            >
              <el-radio :label="1">上架</el-radio>
              <el-radio :label="2">下架</el-radio>
              <el-radio :label="3">拒绝</el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column label="关闭域名" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.hide_domain"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="2"
              @change="setCaseDomain($event, scope.row.id)"
            />
          </template>
        </el-table-column>
        <el-table-column label="是否推荐" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.recommend"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="2"
              @change="setCaseRecommend($event, scope.row.id)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100px" fixed="right">
          <template slot-scope="scope">
            <el-dropdown trigger="click">
              <span class="el-dropdown-link">
                更多操作<i class="el-icon-arrow-down el-icon--right" />
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="detail(scope.row)">详情</el-dropdown-item>
                <el-dropdown-item @click.native="guestbook(scope.row.id)">查看留言</el-dropdown-item>
                <el-dropdown-item @click.native="previewSite(scope.row)">预览</el-dropdown-item>
                <el-dropdown-item v-if="scope.row.site_id && listQuery.is_release != 2" @click.native="goSite(scope.row)">访问</el-dropdown-item>
                <el-dropdown-item @click.native="del(scope.row)">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>
    <el-dialog
      title="留言列表"
      :visible.sync="guestbookVisible"
      width="60%"
    >
      <el-table
        :data="messageList"
        style="width: 100%"
      >
        <el-table-column
          prop="user_name"
          label="姓名"
          width="80"
        />
        <el-table-column
          prop="user_tel"
          label="联系电话"
          width="180"
        />
        <el-table-column
          prop="content"
          label="留言内容"
        />
        <el-table-column
          prop="create_time"
          label="留言日期"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.create_time | timeStamp2String('Y-m-d h:i:s') }}</span>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="guestbookVisible = false; messageList = []">关 闭</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="案例详情"
      :visible.sync="detailVisible"
      width="60%"
    >
      <el-form v-if="detailData" ref="ruleForm" :rules="rules" :model="editQuery" label-width="100px" class="demo-ruleForm">
        <el-form-item label="案例名称：">
          {{ detailData.title }}
        </el-form-item>
        <el-form-item label="案例分类：">
          <el-select v-model="category_pid_d" placeholder="请选择" clearable>
            <el-option v-for="item in categories" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
          <el-select v-if="categoryChildren_d.length > 0" v-model="category_id_d" placeholder="请选择" clearable>
            <el-option v-for="item in categoryChildren_d" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="案例颜色：">
          <div class="color-box">
            <span
              v-for="item in colorList"
              :key="item.id"
              :style="`background-color: ${item.title}`"
              :class="['color-item', {'active' : editQuery.color_id === item.id}]"
              @click="editQuery.color_id = item.id"
            />
          </div>
        </el-form-item>
        <el-form-item label="案例类型：" prop="case_type">
          <el-radio v-model="editQuery.case_type" label="1">经典版</el-radio>
          <el-radio v-model="editQuery.case_type" label="2">旗舰版</el-radio>
          <el-radio v-model="editQuery.case_type" label="3">豪华版</el-radio>
        </el-form-item>
        <el-form-item label="是否双语：" prop="case_bilingual">
          <el-radio v-model="editQuery.case_bilingual" label="1">是</el-radio>
          <el-radio v-model="editQuery.case_bilingual" label="0">否</el-radio>
        </el-form-item>
        <el-form-item label="pc展示图：" prop="pc_img">
          <el-upload
            class="avatar-uploader"
            :action="uploadUrl"
            :headers="{Authorization: `Bearer ${token}`}"
            :on-preview="handlePictureCardPreview"
            :on-success="(res) => handleAvatarSuccess(res, 'pc')"
            :on-remove="handleRemove"
            :show-file-list="false"
            accept="image/*"
            :before-upload="handleBeforeUpload"
            :on-error="handlerError"
          >
            <el-image v-if="editQuery.pc_img" :src="editQuery.pc_img" class="avatar" fit="contain">
              <div slot="placeholder" class="image-slot">
                加载中<span class="dot">...</span>
              </div>
            </el-image>
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
        </el-form-item>
        <el-form-item label="手机展示图：" label-width="150" prop="phone_img">
          <el-upload
            class="avatar-uploader"
            :action="uploadUrl"
            :headers="{Authorization: `Bearer ${token}`}"
            :on-preview="handlePictureCardPreview"
            :on-success="(res) => handleAvatarSuccess(res, 'phone')"
            :on-remove="handleRemove"
            :show-file-list="false"
            accept="image/*"
            :before-upload="handleBeforeUpload"
            :on-error="handlerError"
          >
            <el-image v-if="editQuery.phone_img" :src="editQuery.phone_img" class="avatar" fit="contain">
              <div slot="placeholder" class="image-slot">
                加载中<span class="dot">...</span>
              </div>
            </el-image>
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
        </el-form-item>
      </el-form>
      <el-dialog :close-on-click-modal="false" :visible.sync="dialogVisible">
        <img width="100%" :src="dialogImageUrl" alt="">
      </el-dialog>
      <span slot="footer" class="dialog-footer">
        <el-button @click="detailVisible = false">关 闭</el-button>
        <el-button type="primary" :loading="btn_loading" @click="caseEdit">修改</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { districts } from '@/api/jzt/sites'
import { cates } from '@/api/jzt/lib'
import { bases, setCase, colorList, editCase, setCaseRecommend, getCaseMessage, hideDomain, delCase } from '@/api/jzt/cases'
import { getToken } from '@/utils/auth'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
export default {
  name: 'Base',
  components: { Pagination },
  data() {
    return {
      uploadUrl: process.env.VUE_APP_JZT_API_PROXY + '/zhyman/libuploads',
      dialogImageUrl: '',
      dialogVisible: false, // 预览图弹窗
      detailVisible: false, // 查看详情弹窗
      guestbookVisible: false, // 查看留言弹窗
      btn_loading: false, // 修改按钮加载
      ruleForm: {},
      detailData: null,
      tableData: [],
      districtsOptions: [],
      loading: false,
      listQuery: {
        open: null,
        is_release: 1,
        page: 1,
        limit: 10
      },
      /* 列表筛选行业分类 */
      category_pid: null,
      category_id: null,
      categoryChildren: [],
      /* 详情行业分类 */
      category_pid_d: null,
      category_id_d: null,
      categoryChildren_d: [],
      editQuery: {

      },
      total: 0,
      rules: {
        case_type: [{ required: true, message: '案例类型必选' }],
        pc_img: [{ required: true, message: 'pc展示图必传' }],
        phone_img: [{ required: true, message: '手机展示图必传' }]
      },
      categories: [],
      isShowEdit: false,
      messageList: [],
      imgMaxSize: 1024 * 1024
    }
  },
  computed: {},
  watch: {
    category_pid() {
      const category_pid = this.category_pid
      this.categoryChildren = this.getCateP(category_pid)
      this.category_id = null
    },
    category_pid_d() {
      const category_pid_d = this.category_pid_d
      this.categoryChildren_d = this.getCateP(category_pid_d)
      if (this.isShowEdit) {
        this.isShowEdit = false
      } else {
        this.category_id_d = null
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.tab-content')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      console.log(tabContentHeight, filterHeight)
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  created() {
    this.getList()
    this.getTemplateCates()
    this.district()
    this.getColorList()
    this.token = getToken()
  },
  methods: {
    // 分类选择处理
    getCateP(category_pid) {
      const categories = this.categories
      const newArr = categories.filter((item) => item.id === category_pid)
      let categoryChildren = []
      if (category_pid !== null && category_pid !== '' && newArr.length > 0) categoryChildren = newArr[0].children
      return categoryChildren
    },
    // 上下架
    setCaseStatus(value, id) {
      setCase({ id, open: value }).then(response => {
        this.$message.success('设置成功')
        this.getList()
      })
    },
    // 推荐
    setCaseRecommend(value, id) {
      setCaseRecommend({ id, recommend: value }).then(response => {
        this.$message.success('设置成功')
        this.getList()
      })
    },
    // 隐藏域名
    setCaseDomain(value, id) {
      hideDomain({ id, hide_domain: value }).then(response => {
        this.$message.success('设置成功')
        this.getList()
      })
    },
    // 案例详情
    detail(row) {
      this.detailData = row
      this.isShowEdit = true
      this.category_pid_d = null
      this.category_id_d = null
      if (row.type_pid === 0 && row.type_id !== 0) {
        this.category_pid_d = row.type_id
      } else if (row.type_pid === 0 && row.type_id === 0) {
        this.category_pid_d = null
      } else {
        this.category_pid_d = row.type_pid
        this.category_id_d = row.type_id
      }
      // console.log(row.case_applet)
      this.editQuery = {
        id: row.id,
        category_id: row.type_id,
        color_id: row.color_id,
        case_type: row.case_type,
        case_bilingual: row.case_bilingual,
        case_applet: row.case_applet ? row.case_applet.split(',') : [],
        pc_img: row.pc_img,
        phone_img: row.phone_img
      }
      // console.log(this.editQuery)
      this.detailVisible = true
    },
    // 获取案例库列表
    getList() {
      this.listQuery.all = false
      this.loading = true
      this.listQuery.category_id = this.category_id || this.category_pid
      bases(this.listQuery).then(response => {
        console.log(response)

        this.tableData = response.data.data
        this.total = response.data.total
        this.loading = false
      })
    },
    // 获取颜色列表
    getColorList() {
      colorList({}).then(response => {
        this.colorList = response.data
      })
    },
    // 删除
    del(row) {
      this.$confirm('此操作将永久删除该该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delCase({ id: row.id }).then(response => {
          this.$message.success('操作成功')
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    // 访问
    goSite(row) {
      window.open('http://' + row.host.domain)
    },
    // 预览
    previewSite(row) {
      if (row.site_id && row.host.company_id) window.open(row.temp_url + '&company_id=' + row.host.company_id + '&site_id=' + row.site_id + '&layout_id=' + row.layout_id)
      else window.open(row.temp_url + '&layout_id=' + row.layout_id)
    },
    // 获取行业分类
    getTemplateCates() {
      cates({ all: true }).then(response => {
        this.categories = response.data
      })
    },
    // 获取分公司列表
    district() {
      districts().then(response => {
        this.districtsOptions = response.data
      })
    },
    // 监听图片删除
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    // 预览图片
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.response
      this.dialogVisible = true
    },
    // 图片上传成功回调
    handleAvatarSuccess(response, type) {
      if (response.code === 200) {
        switch (type) {
          case 'pc':
            this.editQuery.pc_img = response.data
            break
          case 'phone':
            this.editQuery.phone_img = response.data
            break
        }
      } else {
        this.$message.error(response.msg)
      }
    },
    // 修改
    caseEdit() {
      this.btn_loading = true
      const category_pid_d = this.category_pid_d
      const category_id_d = this.category_id_d
      if (category_id_d !== null && category_id_d !== '') {
        this.editQuery.type_id = category_id_d
      } else {
        this.editQuery.type_id = category_pid_d
      }
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.editQuery.case_applet = this.editQuery.case_applet.join(',')
          editCase(this.editQuery).then(response => {
            this.$message.success('修改成功')
            this.btn_loading = false
            this.detailVisible = false
            this.getList()
          })
        } else {
          this.btn_loading = false
        }
      })
    },
    // 查看留言
    guestbook(rowId) {
      this.guestbookVisible = true
      this.getMessage(rowId)
    },
    // 获取留言接口
    getMessage(rowId) {
      getCaseMessage({ id: rowId }).then(response => {
        this.messageList = response.data
      })
    },
    //   上传图片大小限制
    handleBeforeUpload(file) {
      if (file.size > 1024 * 1024) {
        this.$message.error('请上传小于1M的图片')
        return false
      }
    },
    //   上传失败
    handlerError(err) {
      this.$message.error(err)
    }
  }
}
</script>

<style scoped>
  .el-dropdown-link {
    cursor: pointer;
    color: #409EFF;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .img-color {
    width: 100%;
    height: 150px;
    border-radius: 5px;
  }
  .color-box .color-item {
    width: 32px;
    height: 32px;
    display: inline-block;
    cursor: pointer;
    transition: all ease-in-out .3s;
  }
  .color-box .color-item.active, .color-box .color-item:hover {
    transform: scale(1.2);
  }
  .avatar-uploader ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader ::v-deep .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>

<style lang="scss" scoped>
.list-wrap{
  .filter{
    border-bottom: 1px solid #e5e5e5;
  }
 .table{
    min-height: 200px;
 }
}
</style>
