<script>
import SelfCard from '../SelfCard.vue'
import { getUsageData } from '@/api/dashboard'
import CountTo from 'vue-count-to'
import img1 from '@/assets/dashboard/img1.png'
import img2 from '@/assets/dashboard/img2.png'
import img3 from '@/assets/dashboard/img3.png'
import img4 from '@/assets/dashboard/img4.png'

export default {
  name: 'UseData',
  components: { SelfCard, CountTo },
  data() {
    return {
      date: '',
      cardList: [
        {
          title: '累计用户数（截止月末）',
          data: 0,
          img: img1
        },
        {
          title: '月活跃用户数',
          data: 0,
          img: img2
        },
        {
          title: '平均日使用量',
          data: 0,
          img: img3
        },
        {
          title: '平均日活跃用户数',
          data: 0,
          img: img4
        }
      ]
    }
  },
  mounted() {
    this.handleGetUsageData()
  },
  methods: {
    handleGetUsageData() {
      if (!this.date) {
        this.date = new Date().getFullYear().toString() + '-' + (new Date().getMonth() + 1).toString().padStart(2, '0')
      }
      getUsageData({ date: this.date }).then(res => {
        if (res.code === 200 && res.data) {
          console.log(res.data)
          this.cardList[0].data = res.data.totalcount
          this.cardList[1].data = res.data.monthconnt
          this.cardList[2].data = res.data.daycount
          this.cardList[3].data = res.data.dayusage
        }
      })
    }
  }
}
</script>

<template>
  <self-card title="用户使用数据">
    <el-row :gutter="30" style="margin-bottom: -20px;">
      <el-col v-for="(item, index) in cardList" :key="index" :xs="12" :sm="12" :lg="6" style="margin-bottom: 20px;">
        <div class="card-item">
          <div class="left">
            <div class="card-title">{{ item.title }}</div>
            <div class="card-data">
              <CountTo
                :start-val="0"
                :end-val="item.data"
                :duration="1000"
                class="card-panel-num"
              />
            </div>
          </div>
          <div class="right">
            <img :src="item.img" alt="">
          </div>
        </div>
      </el-col>
    </el-row>
  </self-card>
</template>

<style lang="scss" scoped>
.card-item{
  width: 100%;
  height: 120px;
  background: #F5F8FF;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding: 30px;
  .card-title{
    font-size: 14px;
    color: #666666;
  }
  .card-data{
    margin-top: 17px;
    font-size: 30px;
    color: #222222;
    .card-panel-num{
      font-size: 30px;
      color: #222222;
    }
  }
}
</style>
