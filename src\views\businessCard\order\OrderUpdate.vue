<script>
import SelfFormTemp from '@/views/task/components/form/SelfFormTemp.vue'
import update from '@/views/task/mixins/update'

export default {
  name: 'OrderUpdate',
  components: { SelfFormTemp },
  mixins: [update],
  data() {
    return {
      formData: {
      },
      filters: [
        {
          label: '订单编号',
          prop: 'order_no',
          type: 'input'
        },
        {
          label: '套餐名称',
          prop: 'plan_name',
          type: 'input'
        },
        {
          label: '公司',
          key: 'company_name',
          type: 'input'
        },
        {
          label: '姓名',
          key: 'name',
          type: 'input'
        },
        {
          label: '电话',
          key: 'phone',
          type: 'input'
        },
        {
          label: '产品单价',
          key: 'price',
          type: 'input'
        },
        {
          label: '总价',
          key: 'total_price',
          type: 'input'
        },
        {
          label: '状态',
          key: 'status',
          type: 'select',
          options:[
            {
              label:'待支付',
              value:1
            },
            {
              label:'已支付',
              value:2
            },
            {
              label:'已完成',
              value:3
            },
            {
              label:'已取消',
              value:4
            }
          ]
        },
        {
          label: '激活时间',
          prop: 'active_time',
          type: 'input'
        },
        {
          label: '到期时间',
          prop: 'expire_time',
          type: 'input'
        },
        {
          label: '创建时间',
          prop: 'add_time',
          type: 'input'
        },
        {
          label: '更新时间',
          prop: 'update_time',
          type: 'input'
        }
      ]
    }
  }
}
</script>

<template>
  <el-dialog :close-on-click-modal="false" :title="'订单管理' + title" width="50vw" :visible.sync="_visible">
    <SelfFormTemp ref="formWrap" :form-data.sync="formData" :filters="filters" :inline="false" label-width="130px" />
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
