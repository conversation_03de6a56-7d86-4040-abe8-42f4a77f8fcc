<script>
import SelfCard from '@/views/dashboard/admin/components/SelfCard.vue'
import LineEchart from '@/views/dashboard/admin/components/new/LineEchart.vue'
import { getAliveTime } from '@/api/dashboard'

export default {
  name: 'TimeTrends',
  components: { LineEchart, SelfCard },
  data() {
    return {
      date: '',
      chartData: {
        x: [],
        y: []
      }
    }
  },
  mounted() {
    this.handleGetAliveTimeData()
  },
  methods: {
    handleGetAliveTimeData() {
      if (!this.date) {
        this.date = new Date().getFullYear().toString() + '-' + (new Date().getMonth() + 1).toString().padStart(2, '0')
      }
      getAliveTime({ date: this.date }).then(res => {
        if (res.code === 200 && res.data) {
          if (res.data.title) {
            this.chartData.x = res.data.title.map(v => v[0])
          }
          if (res.data.arr) {
            this.chartData.y = res.data.arr
          }
        }
      })
    }
  }
}
</script>

<template>
  <SelfCard title="分时段用户活跃数">
    <template v-slot:right>
      <div class="statistics">
        <el-date-picker
          v-model="date"
          type="date"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          placeholder="选择日期"
          style="width: 160px;"
          @change="handleGetAliveTimeData"
        />
      </div>
    </template>
    <LineEchart color="#5AC84B" :data="chartData" />
  </SelfCard>
</template>

<style scoped lang="scss">
.statistics {
  display: flex;
  align-items: center;
  & > span {
    font-size: 14px;
    color: #3C3C3C;
    margin-right: 19px;
  }
}
</style>
