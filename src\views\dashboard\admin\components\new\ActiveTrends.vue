<script>
import SelfCard from '@/views/dashboard/admin/components/SelfCard.vue'
import LineEchart from '@/views/dashboard/admin/components/new/LineEchart.vue'
import { getAliveTendency } from '@/api/dashboard'

export default {
  name: 'ActiveTrends',
  components: { LineEchart, SelfCard },
  data() {
    return {
      date: '',
      chartData: {
        x: [],
        y: []
      },
      avarage: 0,
      total: 0
    }
  },
  mounted() {
    this.handleGetAliveTendency()
  },
  methods: {
    // 获取用户活跃趋势
    handleGetAliveTendency() {
      if (!this.date) {
        this.date = new Date().getFullYear().toString()
      }
      getAliveTendency({ date: this.date }).then(res => {
        if (res.code === 200 && res.data) {
          if (res.data.title) {
            this.chartData.x = res.data.title.map(v => v[0])
          }
          if (res.data.arr) {
            this.chartData.y = res.data.arr
          }
          this.total = res.data.count
          this.avarage = res.data.monthcount
        }
      })
    }
  }
}
</script>

<template>
  <SelfCard title="用户活跃趋势">
    <template v-slot:right>
      <div class="statistics">
        <span>合计：{{ total }}人</span>
        <span>均值：{{ avarage }}人</span>
        <el-date-picker
          v-model="date"
          type="year"
          value-format="yyyy"
          format="yyyy"
          placeholder="选择日期"
          style="width: 160px;margin-left: 26px;"
          @change="handleGetAliveTendency"
        />
      </div>
    </template>
    <LineEchart color="#3765FF" :data="chartData" />
  </SelfCard>
</template>

<style scoped lang="scss">
.statistics {
  display: flex;
  align-items: center;
  & > span {
    font-size: 14px;
    color: #3C3C3C;
    margin-right: 19px;
  }
}
</style>
