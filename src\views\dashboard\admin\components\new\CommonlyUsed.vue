<script>
import SelfCard from '../SelfCard.vue'
import img5 from '@/assets/dashboard/img5.png'
import img6 from '@/assets/dashboard/img6.png'
import img7 from '@/assets/dashboard/img7.png'
import img8 from '@/assets/dashboard/img8.png'
import img9 from '@/assets/dashboard/img9.png'
import img10 from '@/assets/dashboard/img10.png'
import img11 from '@/assets/dashboard/img11.png'
import img12 from '@/assets/dashboard/img12.png'
import img13 from '@/assets/dashboard/img13.png'
import img14 from '@/assets/dashboard/img14.png'

export default {
  name: 'CommonlyUsed',
  components: {
    SelfCard
  },
  data() {
    return {
      cards: [
        {
          title: '产品管理',
          img: img5,
          to: '/app/list'
        },
        {
          title: '发布公告',
          img: img6,
          to: '/news/edit'
        },
        {
          title: '工单列表',
          img: img7,
          to: '/workorder/list'
        },
        {
          title: '工种审批',
          img: img8,
          to: '/task/taskApproval'
        },
        {
          title: '福利商城<br>商品列表',
          img: img9,
          to: '/shop/list'
        },
        {
          title: '福利商城<br>订单列表',
          img: img10,
          to: '/shop/orders'
        },
        {
          title: '站点列表',
          img: img11,
          to: '/jzt/site/list'
        },
        {
          title: '云名片<br>订单列表',
          img: img12,
          to: '/businessCard/order/list'
        },
        {
          title: '代理商管理',
          img: img13,
          to: '/agent/agent/list'
        },
        {
          title: '运营列表',
          img: img14,
          to: '/report/report/task'
        }
      ]
    }
  }
}
</script>

<template>
  <SelfCard title="常用功能" style="height: 100%">
    <div class="commonly-used">
      <router-link v-for="(card, index) in cards" :key="index" :to="card.to" class="commonly-used-item">
        <div>
          <img :src="card.img" :alt="card.title">
        </div>
        <div class="used-title" v-html="card.title" />
      </router-link>
    </div>
  </SelfCard>
</template>

<style scoped lang="scss">
.commonly-used {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: -20px;
  .commonly-used-item{
    margin-bottom: 20px;
    width: 10%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .used-title{
      font-size: 14px;
      color: #666666;
      margin-top: 10px;
      text-align: center;
      line-height: 1.5;
    }
  }
}
@media screen and (max-width: 1600px){
  .commonly-used {
    .commonly-used-item{
      width: 20%;
    }
  }
}
@media screen and (max-width: 780px){
  .commonly-used {
    .commonly-used-item{
      width: 33%;
    }
  }
}
</style>
