<template>
  <AgentCard
    title="代理商消费数据"
    :statistics="statistics"
    :color="{card: '#f5f8ff', chart: '#4d80ff'}"
    :charge-radio="chargeRadio"
    :chart-data="{x: trend_keys, y: trend_data1}"
    :table-data="lists"
    :form.sync="searchForm"
    chart-id="cost"
    :map-loading="mapLoading"
    :date.sync="value2"
    @get-active="getActive"
  />
</template>

<script>
import { consumptiondata } from '@/api/agent/dashboard'
import AgentCard from './AgentCard.vue'

export default {
  name: 'Agentxiaofei',
  components: { AgentCard },
  props: {
    allData: {
      type: Object,
      default() {
        return {}
      }
    },
    loading: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  data() {
    return {
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      value2: '',
      mapLoading: false,
      statistics: [
        {
          title: '代理商消费总额',
          value: 0,
          img: 'https://scrm-api.china9.cn/web/images/dlsxf.png'
        },
        {
          title: '软件消费金额',
          value: 0,
          img: 'https://scrm-api.china9.cn/web/images/rjxf.png'
        },
        {
          title: '硬件消费金额',
          value: 0,
          img: 'https://scrm-api.china9.cn/web/images/yjxf.png'
        }
      ],
      chargeRadio: [
        {
          label: '消费趋势',
          value: 0
        },
        {
          label: '消费排行',
          value: 1
        }
      ],
      searchForm: {
        status: '',
        page: 1,
        perPage: 5,
        total: 0,
        is_buy: false
      },
      lists: [],
      indexOfDate: 0,
      indexOfType: 0,
      indexOfCate: 0,
      chartLine: null,
      dataSource: [],
      legend: ['今日', '前一日'],
      trend_keys: [],
      trend_data1: [],
      trend_data2: []
    }
  },
  watch: {
    allData(value) {
      this.allData = value

      this.legend = ['今日']
      // 今日
      // this.trend_data1 = this.allData.map_today
      // this.trend_data2 = this.allData.map_last_day// 前一日
    }
  },
  mounted() {
    this.init()
    this.getActive()
  },
  methods: {
    selectType(index) {
      this.indexOfType = index
      this.init()
    },
    changeDay() {
      this.init()
    },
    getActive() {
      // 产品活跃度排行
      this.$emit('getActive')
    },
    async init() {
      this.getRechargedata()
    },
    async getRechargedata() {
      this.mapLoading = true
      await consumptiondata({ date: this.value2, page: this.searchForm.page }).then(response => {
        // this.mapLoading = false
        this.value2 = response.data.date
        this.trend_data1 = response.data.allprice
        this.trend_keys = response.data.detelist
        this.statistics[0].value = +response.data.alltotalprice
        this.statistics[1].value = +response.data.financerjprice
        this.statistics[2].value = +response.data.financeyjprice
        this.lists = response.data.agentall
        this.searchForm.total = response.data.agentallprice.total
        // this.allData = response.data
        // this.lists = this.allData.data
      }).catch(error => {
        this.mapLoading = false
        console.log(error)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
