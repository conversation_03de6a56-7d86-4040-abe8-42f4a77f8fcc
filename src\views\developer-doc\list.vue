<template>
  <div class="list-wrap">
    <el-card class="card-wrap" shadow="never">
      <div slot="header" class="clearfix">
        <!-- <span>开发文档列表</span> -->
        <el-button style="float: right; padding: 3px 0" type="text" @click="addNews">新增文章</el-button>
        <el-button style="float: right; padding: 3px 20px" type="text" @click="categoryList">类别管理</el-button>
      </div>
      <el-table
        :data="tableData"
        stripe
        style="width: 100%"
        height="calc(100% - 96px)"
      >
        <el-table-column label="ID" prop="id" width="65" />
        <el-table-column label="标题" prop="title" />
        <el-table-column label="分类" prop="category" />
        <el-table-column label="置顶">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.is_top" type="success" size="small" effect="dark">置顶</el-tag>
            <el-tag v-else type="info" size="small" effect="dark">正常</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="发布时间" prop="publish_at" />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="detail(scope.row.id)">编辑</el-button>
            <el-button type="text" @click="destory(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="query.page" :limit.sync="query.perPage" @pagination="getList" />
    </el-card>
  </div>
</template>

<script>
import { list, destory } from '../../api/developer_doc'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
export default {
  components: { Pagination },
  data() {
    return {
      total: 0,
      tableData: [],
      query: { page: 1, perPage: 10 },
      dialogVisible: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.dialogVisible = true
      list(this.query).then(response => {
        this.dialogVisible = false
        this.tableData = response.data.data
        this.total = response.data.total
      }).catch(error => {
        console.log(error)
        this.dialogVisible = false
      })
    },
    addNews() {
      this.$router.push({
        'path': '/developer-doc/edit'
      })
    },
    categoryList() {
      this.$router.push({
        'path': '/developer-doc/cateList'
      })
    },
    detail(id) {
      this.$router.push({
        'path': '/developer-doc/edit',
        'query': { 'id': id }
      })
    },
    destory(id) {
      this.$alert('删除后不可恢复', '确定删除么', {
        confirmButtonText: '确定',
        callback: action => {
          if (action === 'confirm') {
            destory({ id: id }).then(response => {
              this.$message.success('删除成功')
              this.getList()
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .list-wrap {
    height: 100%;

    ::v-deep .el-tabs{
      height: 100%;
      .el-tabs__content{
        height: calc(100% - 41px);
        .el-tab-pane{
          height: 100%;
        }
      }
    }

    ::v-deep .card-wrap {
      height: 100%;
      border: none;

      .el-card__header{
        padding-top: 0;
      }

      .el-card__body {
        height: calc(100% - 59px);
        margin-top: 20px;
        padding: 0;
      }

      .box-card {
        height: 100%;
        border: none;

        .el-card__body {
          height: calc(100% - 59px);
          overflow: auto;
        }
      }
    }
  }
</style>
