<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import OperatorUpdate from '@/views/system/components/OperatorUpdate.vue'

export default {
  name: 'OperatorSManual',
  components: { OperatorUpdate, StatisticsTemplate },
  data() {
    return {
      config: {
        key: 'operatorSManual',
        filters: [
          {
            label: '操作手册名称',
            prop: 'name',
            type: 'input'
          }
        ],
        tableSettings: {
          selection: true,
          data: [
            {
              id: 1,
              name: '操作手册1',
              description: '操作手册1',
              status: 1
            }
          ],
          columns: [
            {
              label: 'ID',
              prop: 'id',
              width: 60,
              sortable: true
            },
            {
              label: '手册名称',
              prop: 'name',
              sortable: true
            },
            {
              label: '手册介绍',
              prop: 'description',
              sortable: true
            },
            {
              label: '状态',
              prop: 'status'
            },
            {
              label: '操作',
              prop: 'action',
              width: 150,
              fixed: 'right',
              align: 'center',
              isSlot: true
            }
          ]
        }
      },
      updateVisible: false,
      row: {},
      selection: []
    }
  },
  methods: {
    handleAdd() {
      this.updateVisible = true
      this.row = {}
    },
    handleUpdate(row) {
      this.updateVisible = true
      this.row = row
    },
    handleDelete(row) {
      const data = this.config.tableSettings.data.filter(item => item.id !== row.id)
      this.$set(this.config.tableSettings, 'data', data)
    },
    handleSelectionChange(val) {
      this.selection = val
    }
  }
}
</script>

<template>
  <div class="app-container">
    <el-card>
      <div slot="header">
        操作手册
      </div>
      <div class="page-warp">
        <StatisticsTemplate :config="config" @selection-change="handleSelectionChange">
          <template v-slot:topActions>
            <el-button type="primary" @click="handleAdd()">添加</el-button>
            <el-popconfirm
              title="确定删除吗？"
              style="margin-left: 10px;"
              @onConfirm="handleDelete()"
            >
              <el-button slot="reference" type="danger">删除</el-button>
            </el-popconfirm>
          </template>
          <template v-slot:operatorSManual_action="{row}">
            <el-button size="mini" @click="handleUpdate(row)">编辑</el-button>
            <el-popconfirm
              title="确定删除吗？"
              style="margin-left: 10px;"
              @onConfirm="handleDelete(row)"
            >
              <el-button slot="reference" type="danger" size="mini">删除</el-button>
            </el-popconfirm>
          </template>
        </StatisticsTemplate>
        <OperatorUpdate :visible.sync="updateVisible" :data="row" />
      </div>
    </el-card>
  </div>
</template>

<style scoped lang="scss">
.app-container{
  height: 100%;
  ::v-deep .el-card{
    height: 100%;
    .el-card__body{
      height: calc(100% - 56px);
      overflow: auto;
    }
  }
}
</style>
