<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <!--<span>新闻分类</span>-->
        <el-button style="float: right; padding: 3px 0" type="text" @click="addCategory">新增分类</el-button>
      </div>
      <el-table :data="tableData" stripe style="width: 100%" height="100%">
        <el-table-column label="名称" prop="name" />
        <el-table-column label="排序" prop="level" />
        <el-table-column label="级别" prop="top" />
        <el-table-column label="状态">
          <template slot-scope="scope">
            <el-button v-if="scope.row.status === 1" type="text">启用</el-button>
            <el-button v-if="scope.row.status === 0" type="text" style="color:red;">停用</el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="update(scope.row)">编辑</el-button>
            <el-button type="text" @click="destory(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <cateForm :dialog-visible="dialogVisible" :form="form" @transfer="updateOrCreate" />
  </div>
</template>

<script>
import cateForm from './components/cate-form'
import { list, store, edit, destory } from '../../api/news_category'
export default {
  name: 'NewsCateList',
  components: { cateForm },
  data() {
    return {
      tableData: [],
      form: {},
      id: '',
      dialogVisible: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.dialogVisible = false
      list().then(response => {
        console.log(response.data)
        this.tableData = response.data
      })
    },
    addCategory() {
      this.form = {
        'name': '',
        'pid': 0,
        'level': 0,
        'status': 1
      }
      this.id = null
      this.dialogVisible = true
      this.$forceUpdate()
    },
    update(form) {
      this.form = form
      this.id = form.id
      this.dialogVisible = true
      this.$forceUpdate()
    },
    updateOrCreate(form) {
      console.log(form)
      if (form.id) {
        edit(form).then(response => {
          this.$message.success('更新成功')
          this.getList()
        })
      } else {
        store(form).then(response => {
          this.$message.success('新增成功')
          this.getList()
        })
      }
    },
    destory(id) {
      this.$alert('删除后不可恢复', '确定删除么', {
        confirmButtonText: '确定',
        callback: action => {
          if (action === 'confirm') {
            destory({ id: id }).then(response => {
              this.$message.success('删除成功')
              this.getList()
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .list-wrap {
    height: 100%;

    .box-card {
      border: none;
      height: 100%;

      ::v-deep .el-card__header {
        padding-top: 0;
      }

      ::v-deep .el-card__body {
        height: calc(100% - 59px);
        overflow: auto;
        padding: 0;
        margin-top: 20px;
      }
    }
  }
</style>
