@font-face {
  font-family: "iconfont"; /* Project id 4855398 */
  src: url('iconfont.woff2?t=1741773279562') format('woff2'),
       url('iconfont.woff?t=1741773279562') format('woff'),
       url('iconfont.ttf?t=1741773279562') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-jifenguizeguankong2:before {
  content: "\e6ae";
}

.icon-pinglun:before {
  content: "\e63a";
}

.icon-dingdan:before {
  content: "\e669";
}

.icon-shangpin:before {
  content: "\e6d8";
}

.icon-shangjiashangpin:before {
  content: "\e6df";
}

.icon-copper-coin-fill:before {
  content: "\e7b5";
}

