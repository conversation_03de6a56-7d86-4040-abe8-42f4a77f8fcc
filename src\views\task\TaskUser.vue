<template>
  <div id="taskUser" class="app-container">
    <el-card class="box-card" style="margin-bottom: 15px" shadow="hover">
      <div slot="header" class="clearfix">
        <span>搜索栏</span>
      </div>
      <el-form
        :inline="true"
        :model="query"
        class="demo-form-inline"
        method="get"
      >
        <el-form-item>
          <el-input v-model="query.name" placeholder="请输入申请人姓名" />
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="query.month"
            type="month"
            value-format="yyyy-MM"
            format="yyyy-MM"
            placeholder="选择日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="page.page = 1; getList()">搜索</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card class="box-card" shadow="hover">
      <div slot="header" class="clearfix">
        <span>列表</span>
      </div>
      <el-row style="padding: 20px 0;border-bottom: 1px solid #dfe6ec">
        <div style="font-size: 14px;">用户数量：{{ total }}</div>
      </el-row>
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%;margin-top: 40px;"
      >
        <el-table-column align="center" prop="name" label="用户" width="100px" />
        <el-table-column align="center" prop="enterprise_name" label="公司名称" width="220px" />
        <el-table-column align="center" prop="position" label="当前岗位/星级" min-width="200px">
          <template #default="{row}">
            <span v-for="(item, index) in row.positionList" :key="item.id">
              {{ item.title }} / {{ getGradeValue(item) }}
              {{ index === row.positionList.length - 1 ? '' : '、' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="monthDeservedSum" label="应得金额（元）" width="120px" />
        <el-table-column align="center" prop="monthMoneySum" label="实得金额（元）" width="120px" />
        <el-table-column align="center" prop="hallNum" label="累计接取项目" width="100px" />
        <el-table-column align="center" prop="taskNum" label="累计接取任务" width="100px" />
        <el-table-column align="center" prop="moneySum" label="累计完结任务金额（元）" width="120px" />
        <el-table-column align="center" label="操作" fixed="right" width="180px">
          <template slot-scope="{row}">
            <el-button type="text" size="small" @click="handleEdit(row)">修改岗位星级</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="page" style="text-align: center;margin-top: 20px;">
        <el-pagination
          v-show="total>0"
          :total="total"
          :current-page="page.page"
          :page-sizes="pageSizes"
          :page-size="page.limit"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="getList"
          @size-change="sizeChange"
        />
      </div>
    </el-card>

    <el-dialog :destroy-on-close="true" :close-on-click-modal="false" title="修改岗位星级" :visible.sync="taskUserEditVisible" width="600px">
      <el-form ref="form" :model="form" label-width="120px">
        <el-form-item
          v-for="(val, index) in form.position"
          :key="index"
          :label="val.title"
          :prop="'position.' + index + '.grade'"
          :rules="{
            required: true, message: '星级不能为空', trigger: 'change'
          }"
        >
          <el-select v-model="val.grade" placeholder="请选择" style="width: 100%">
            <el-option v-for="(item, key) in gradeList" :key="key" :label="item" :value="key" />
          </el-select>
        </el-form-item>
        <div style="text-align: center">
          <el-button type="primary" @click="hanldeSubmit()">确认修改</el-button>
          <el-button type="default" @click="closeEdit(false)">取消</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ElStatistic from '@/components/ElStatistic'
import selfRequest from '@/mixins/selfRequest'
import dialog from '@/mixins/dialog'
import form from '@/mixins/form'
import sizeChange from '@/mixins/sizeChange'
import { getPageMax } from '@/utils/getPageMax'
export default {
  name: 'TaskUser',
  mixins: [selfRequest, dialog, form, sizeChange],
  data() {
    return {
      query: {
        name: '',
        company: ''
      },
      userNum: 0,
      loading: false,
      tableData: [
        {
          id: 1,
          name: '王小虎',
          company: '长春龙采/技术部',
          position: 'PHP/3星、产品经理/1星',
          totalProject: 0,
          totalTask: 0,
          totalMoney: 0
        }
      ],
      userId: '',
      user: {},
      taskUserEditVisible: false,
      taskUserEditTitle: '',
      form: {
        position: [
          {
            position: 'php',
            grade: '3'
          },
          {
            position: 'java',
            grade: '3'
          }
        ]
      },
      gradeList: {
        1: '1星',
        2: '2星',
        3: '3星'
      }
    }
  },
  mounted() {
    this.getList(1)
  },
  methods: {
    getGradeValue(data) {
      return data.gradeValue || this.gradeList[data.grade]
    },
    getList(pageNum) {
      if (pageNum) {
        this.$set(this.page, 'page', pageNum)
      }
      const data = { ...this.query, ...this.page }
      this.requestApi({
        apiName: 'getTaskList',
        data,
        loadingField: 'loading',
        errMsg: '获取失败',
        success: ({ data }) => {
          if (data.data) {
            this.tableData = data.data
          }
          this.total = data.count
          this.pageSizes = getPageMax(this.total, this.pageSizes)
        }
      })
    },
    handleEdit(data) {
      this.taskUserEditVisible = true
      this.form.position = data.positionList
      this.form.unique_id = data.unique_id
      this.form.id = data.id
    },
    closeEdit(refresh) {
      this.taskUserEditVisible = false
      if (typeof refresh !== 'boolean') {
        refresh = false
      }
      if (refresh) {
        this.getList()
      }
    },
    hanldeSubmit() {
      this.validateForm('form', this.validateSuccess)
    },
    validateSuccess() {
      let data = JSON.parse(JSON.stringify(this.form.position))
      data = data.map(v => ({
        work_id: v.work_id,
        grade: v.grade
      }))
      // 走请求
      this.requestApi({
        apiName: 'updateGrade',
        data: {
          id: this.form.id,
          unique_id: this.form.unique_id,
          position: data
        },
        loadingField: 'loading',
        errMsg: '更新失败',
        successMsg: '更新成功',
        success: ({ data }) => {
          this.closeEdit(true)
        }
      })
    }
  }
}
</script>

<style>

</style>
