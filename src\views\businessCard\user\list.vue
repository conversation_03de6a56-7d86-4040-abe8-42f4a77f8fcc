<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import UserUpdate from '@/views/businessCard/user/UserUpdate.vue'
import { list } from '@/api/businessCard/user'
import { objToQueryString } from '@/utils'

export default {
  name: 'List',
  components: { UserUpdate, StatisticsTemplate },
  data() {
    return {
      config: {
        key: 'user',
        filters: [
          {
            label: '姓名',
            prop: 'name',
            type: 'input'
          },
          {
            label: '手机号',
            prop: 'phone',
            type: 'input'
          },
          /* {
            label: '公司',
            prop: 'company',
            type: 'input'
          }, */
          {
            label: '状态',
            prop: 'status',
            type: 'select',
            settings: {
              options: [
                {
                  label: '待审核',
                  value: 0
                },
                {
                  label: '已通过',
                  value: 1
                },
                {
                  label: '已拒绝',
                  value: 2
                }
              ]
            }
          }
        ],
        tableSettings: {
          api: list,
          field: {
            page: 'page',
            limit: 'perPage',
            total: 'total'
          },
          columns: [
            {
              label: 'ID',
              prop: 'id',
              width: 60
            },
            {
              label: '姓名',
              prop: 'name',
              width: 80,
            },
            {
              label: '手机号',
              prop: 'phone',
              width: 120,
            },
            {
              label: '公司',
              prop: 'company',
              width: 220,
            },
            {
              label: 'LOGO',
              prop: 'header_url',
              width: 80,
              isSlot: true
            },
            {
              label: '职位',
              prop: 'company_job',
              width: 300,
            },
            {
              label: '社会职务',
              prop: 'social_job'
            },
            {
              label: '个人荣誉',
              prop: 'honor_job'
            },
            {
              label: '微信二维码',
              prop: 'wx',
              width: 100,
              isSlot: true
            },
            {
              label: '到期时间',
              prop: 'expire_end',
              width: 100
            },
            {
              label: '状态',
              prop: 'is_examine',
              width: 100,
              isSlot: true
            },
            /* {
              label: '操作',
              prop: 'action',
              width: 200,
              isSlot: true,
              fixed: 'right',
              align: 'center'
            } */
          ]
        }
      },
      row: {},
      updateVisible: false
    }
  },
  methods: {
    handleUpdate(row) {
      this.row = row
      this.updateVisible = true
    },
    handleDelete(row) {
      const data = this.config.tableSettings.data.filter(item => item.id !== row.id)
      this.$set(this.config.tableSettings, 'data', data)
    },
    handleExport(){
      window.open(process.env.VUE_APP_BASE_API + '/card/exportExcel?' + objToQueryString(this.$refs.tableRef.params), '_blank')
    }
  }
}
</script>

<template>
  <div class="page-wrap">
    <StatisticsTemplate ref="tableRef" :config="config">
      <template #topActions>
        <el-button type="primary" @click="handleExport">导出</el-button>
      </template>
      <template #user_header_url="{row}">
        <el-image v-if="row.header_url" width="80px" :src="row.header_url" :fit="'fit'" :preview-src-list="[row.header_url]" />
      </template>
      <template #user_wx="{row}">
        <el-image v-if="row.wx" width="80px" :src="row.wx" :fit="'fit'" :preview-src-list="[row.wx]" />
      </template>
      <template #user_is_examine="{row}">
        <el-tag v-if="row.is_examine === 0" type="info">待审核</el-tag>
        <el-tag v-if="row.is_examine === 1" type="success">已通过</el-tag>
        <el-tag v-if="row.is_examine === 2" type="danger">已拒绝</el-tag>
      </template>
      <template #user_action="{row}">
        <el-button size="mini" type="primary" @click="handleUpdate(row)">修改</el-button>
        <el-popconfirm
          title="确定删除吗？"
          style="margin-left: 10px;"
          @onConfirm="handleDelete(row)"
        >
          <template v-slot:reference>
            <el-button type="danger" size="mini">删除</el-button>
          </template>
        </el-popconfirm>
      </template>
    </StatisticsTemplate>
    <UserUpdate :visible.sync="updateVisible" :data="row" />
  </div>
</template>

<style scoped lang="scss">
.page-wrap{
  height: 100%;
}
</style>
