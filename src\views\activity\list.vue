<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>活动列表</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="createView">添加活动</el-button>
      </div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          fixed
          prop="id"
          label="ID"
          width="65"
        />
        <el-table-column
          prop="uuid"
          label="活动标识"
        />
        <el-table-column
          fixed
          prop="name"
          label="活动名称"
        />
        <el-table-column
          prop="category.name"
          label="活动分类"
        />
        <el-table-column label="进行状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.run_status === '进行中'" type="success" effect="dark" size="small">{{ scope.row.run_status }}</el-tag>
            <el-tag v-else-if="scope.row.run_status === '已结束'" type="danger" effect="dark" size="small">{{ scope.row.run_status }}</el-tag>
            <el-tag v-else type="warning" effect="dark" size="small">{{ scope.row.run_status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="活动状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status" type="success" effect="dark" size="small">启用</el-tag>
            <el-tag v-else type="danger" effect="dark" size="small">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="publish_at"
          label="开始时间"
          width="155"
        />
        <el-table-column
          prop="stop_time"
          label="结束时间"
          width="155"
        />
        <el-table-column
          prop="url"
          label="活动地址"
        />
        <el-table-column label="操作" fixed="right" width="150">
          <template slot-scope="scope">
            <el-link :underline="false" type="success" @click="addApp(scope.row)">产品</el-link>
            <el-link :underline="false" type="primary" @click="editDialog(scope.row)">编辑</el-link>
            <el-popconfirm
              title="确定删除当前记录吗？"
              @onConfirm="deleteHandle(scope.row)"
            >
              <el-link slot="reference" :underline="false" type="danger">删除</el-link>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 表单   -->
    <el-dialog
      title="活动管理"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="活动名称：" prop="name">
          <el-col :span="12">
            <el-input v-model="ruleForm.name" />
          </el-col>
        </el-form-item>
        <el-form-item label="活动类型：" prop="activity_category_id">
          <el-select v-model="ruleForm.activity_category_id" clearable placeholder="请选择">
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="活动属性：" prop="activity_attr_id">
          <el-select v-model="ruleForm.activity_attr_id" clearable placeholder="请选择" @change="getRuleList">
            <el-option
              v-for="attr in attrs"
              :key="attr.id"
              :label="attr.name"
              :value="attr.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="活动规则：" prop="activity_attr_id">
          <el-select v-model="ruleForm.activity_rule_id" clearable placeholder="请选择">
            <el-option
              v-for="attr_rule in attr_rules"
              :key="attr_rule.id"
              :label="attr_rule.name"
              :value="attr_rule.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="活动链接：" prop="url">
          <el-col :span="20">
            <el-input v-model="ruleForm.url" />
          </el-col>
        </el-form-item>
        <el-form-item label="活动时间：" prop="times">
          <el-date-picker
            v-model="ruleForm.times"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="按钮文字：" prop="btn_text">
          <el-col :span="12">
            <el-input v-model="ruleForm.btn_text" />
          </el-col>
        </el-form-item>
        <el-form-item label="活动状态：" prop="status">
          <el-switch
            v-model="ruleForm.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item label="活动描述：" prop="content">
          <el-col :span="18">
            <Editor :content.sync="ruleForm.content" />
          </el-col>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="saveHandle">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { attr_list } from '../../api/activity_attr'
import { category_list } from '../../api/activity_category'
import { list, store, activity_delete } from '../../api/activity'
import { attrs_rule_list } from '../../api/attr_rules'
import Editor from '../../components/editor'
export default {
  name: 'List',
  components: { Editor },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      tableLoading: false,
      ruleForm: {},
      tableData: [],
      rules: {
        name: [
          { required: true, message: '活动名称必填', trigger: 'blur' }
        ],
        activity_category_id: [
          { required: true, message: '活动类型必选', trigger: 'change' }
        ],
        activity_attr_id: [
          { required: true, message: '活动属性必选', trigger: 'change' }
        ],
        activity_rule_id: [
          { required: true, message: '活动规则必选', trigger: 'change' }
        ],
        url: [
          { required: true, message: '活动地址必填', trigger: 'blur' }
        ],
        times: [
          { required: true, message: '活动时间必填', trigger: 'blur' }
        ],
        btn_text: [
          { required: true, message: '按钮文字必填', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '活动状态必须选', trigger: 'change' }
        ]
      },
      attrs: [],
      categories: [],
      attr_rules: [],
      total: 0
    }
  },
  created() {
    this.getActivities()
    attr_list().then(response => {
      this.attrs = response.data
    })
    category_list().then(response => {
      this.categories = response.data
    })
  },
  methods: {
    getActivities() {
      this.tableLoading = true
      list().then(response => {
        this.total = response.data.total
        this.tableData = response.data.data
      }).finally(() => {
        this.tableLoading = false
      })
    },
    createView() {
      this.ruleForm = {
        status: 1
      }
      this.dialogVisible = true
    },
    editDialog(row) {
      this.getRuleList(row.activity_attr_id)
      this.ruleForm = row
      this.ruleForm.times = [row.publish_at, row.stop_time]
      this.dialogVisible = true
    },
    saveHandle() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.loading = true
          store(this.ruleForm).then(response => {
            this.dialogVisible = false
            this.loading = false
            this.getActivities()
          }).catch(() => {
            this.loading = false
          })
        }
      })
    },
    deleteHandle(row) {
      activity_delete(row).then(response => {
        this.$message.success('删除成功')
        this.getActivities()
      })
    },
    getRuleList(attr_id) {
      delete this.ruleForm.activity_rule_id
      if (attr_id) {
        attrs_rule_list({ id: attr_id }).then(response => {
          this.attr_rules = response.data
        })
      }
    },
    addApp(row) {
      this.$router.push({
        path: '/activity/apps',
        query: {
          id: row.id
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
