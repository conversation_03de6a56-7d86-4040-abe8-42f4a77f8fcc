<template>
  <div v-loading="pageLoading" class="list-wrap">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
        <span style="margin-left: 30px;">{{ info.company.name }}的发票审核</span>
      </div>
      <el-card style="margin-bottom: 20px" shadow="never">
        <div slot="header" class="clearfix">
          <span>开票信息</span>
        </div>
        <el-form label-width="120px">
          <el-form-item label="发票类型:">
            <span v-if="info.invoice_info.type === 1">增值税普通发票</span>
            <span v-if="info.invoice_info.type === 2">增值税专用发票</span>
          </el-form-item>
          <el-form-item label="抬头类型:">
            <span v-if="info.invoice_info.name_type === 1">个人</span>
            <span v-if="info.invoice_info.name_type === 2">企业</span>
            <span v-if="info.invoice_info.name_type === 3">组织</span>
          </el-form-item>
          <el-form-item label="抬头名称:">
            <span>{{ info.invoice_info.name }}</span>
          </el-form-item>
          <el-form-item v-if="info.invoice_info.name_type !== 1" label="信用代码:">
            <span>{{ info.invoice_info.code }}</span>
          </el-form-item>
          <el-form-item v-if="info.invoice_info.name_type !== 1" label="开户银行:">
            <span>{{ info.invoice_info.bank_name }}</span>
          </el-form-item>
          <el-form-item v-if="info.invoice_info.name_type !== 1" label="开户账号:">
            <span>{{ info.invoice_info.bank_number }}</span>
          </el-form-item>
          <el-form-item v-if="info.invoice_info.name_type !== 1" label="注册地址:">
            <span>{{ info.invoice_info.company_addr }}</span>
          </el-form-item>
          <el-form-item v-if="info.invoice_info.name_type !== 1" label="注册电话:">
            <span>{{ info.invoice_info.company_phone }}</span>
          </el-form-item>
          <el-form-item label="开票金额:">
            <span>&#165; {{ info.money }} 元</span>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card style="margin-bottom: 20px" shadow="never">
        <div slot="header" class="clearfix">
          <span>开票订单</span>
        </div>
        <el-table
          :data="info.orders"
          style="width: 100%"
        >
          <el-table-column
            prop="order_no"
            label="订单编号"
            width="180"
          />
          <el-table-column
            prop="content"
            label="订单内容"
          />
          <el-table-column
            prop="amount"
            label="订单金额"
          />
        </el-table>
      </el-card>
      <el-card style="margin-bottom: 20px" shadow="never">
        <div slot="header" class="clearfix">
          <span>邮寄信息</span>
        </div>
        <el-form label-width="120px">
          <el-form-item label="收件人:">
            {{ info.address.name }}
          </el-form-item>
          <el-form-item label="联系电话:">
            {{ info.address.phone }}
          </el-form-item>
          <el-form-item label="邮政编码:">
            {{ info.address.code }}
          </el-form-item>
          <el-form-item label="收件地址:">
            <span v-for="item in info.address.areas" :key="item"> {{ item }}</span>
            {{ info.address.address }}
          </el-form-item>
        </el-form>
      </el-card>
      <el-card v-if="info.status !== 0" style="margin-bottom: 20px" shadow="never">
        <div slot="header" class="clearfix">
          <span>审核信息</span>
        </div>
        <el-form ref="ruleForm" :model="form" :rules="rules" class="demo-ruleForm" label-width="120px">
          <el-form-item label="审核状态:" prop="status">
            <el-select v-model="info.status" placeholder="请选择" disabled="disabled">
              <el-option label="审核通过并邮寄" :value="1" />
              <el-option label="驳回请求" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="info.status === 1" label="发票编号:" prop="invoice_number">
            <el-col :span="10">
              <el-input v-model="info.invoice_number" placeholder="请输入内容" disabled="disabled" />
            </el-col>
          </el-form-item>
          <el-form-item v-if="info.status === 1" label="快递公司:" prop="express_name">
            <el-col :span="10">
              <el-input v-model="info.express_name" placeholder="请输入内容" disabled="disabled" />
            </el-col>
          </el-form-item>
          <el-form-item v-if="info.status === 1" label="快递单号:" prop="express_code">
            <el-col :span="10">
              <el-input v-model="info.express_code" placeholder="请输入内容" disabled="disabled" />
            </el-col>
          </el-form-item>
          <el-form-item v-if="info.status === 2" label="驳回理由:" prop="remark">
            <el-col :span="10">
              <el-input v-model="info.remark" type="textarea" placeholder="请输入内容" disabled="disabled" />
            </el-col>
          </el-form-item>
          <el-form-item>
            <el-button v-if="info.status === 0 " type="primary" size="small" :loading="btnLoading" @click="saveHandle">保存并返回</el-button>
            <el-button v-else type="primary" size="small" @click="$router.go(-1)">返回</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card v-if="info.status === 0" style="margin-bottom: 20px" shadow="never">
        <div slot="header" class="clearfix">
          <span>发票审核</span>
        </div>
        <el-form ref="ruleForm" :model="form" :rules="rules" class="demo-ruleForm" label-width="120px">
          <el-form-item label="审核状态:" prop="status">
            <el-select v-model="form.status" placeholder="请选择">
              <el-option label="审核通过并邮寄" :value="1" />
              <el-option label="驳回请求" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="form.status === 1" label="发票编号:" prop="invoice_number">
            <el-col :span="10">
              <el-input v-model="form.invoice_number" placeholder="请输入内容" />
            </el-col>
          </el-form-item>
          <el-form-item v-if="form.status === 1" label="快递公司:" prop="express_name">
            <el-col :span="10">
              <el-input v-model="form.express_name" placeholder="请输入内容" />
            </el-col>
          </el-form-item>
          <el-form-item v-if="form.status === 1" label="快递单号:" prop="express_code">
            <el-col :span="10">
              <el-input v-model="form.express_code" placeholder="请输入内容" />
            </el-col>
          </el-form-item>
          <el-form-item v-if="form.status === 2" label="驳回理由:" prop="remark">
            <el-col :span="10">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-col>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" :loading="btnLoading" @click="saveHandle">保存并返回</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </el-card>
  </div>
</template>

<script>
import { detail, store } from '../../api/invoice'
export default {
  name: 'Detail',
  data() {
    return {
      btnLoading: false,
      pageLoading: false,
      info: {
        invoice_info: {},
        address: {},
        company: {}
      },
      form: {
        status: 1
      },
      rules: {
        status: [
          { required: true, message: '请选择审核状态', trigger: 'change' }
        ],
        invoice_number: [
          { required: true, message: '请输入发票编号', trigger: 'blur' }
        ],
        express_name: [
          { required: true, message: '请输入快递公司', trigger: 'blur' }
        ],
        express_code: [
          { required: true, message: '请输入快递单号', trigger: 'blur' }
        ],
        remark: [
          { required: true, message: '请输入驳回理由', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getDetail()
  },
  methods: {
    getDetail() {
      this.pageLoading = true
      detail({ id: this.$route.query.id }).then(response => {
        this.info = response.data
        this.pageLoading = false
      })
    },
    saveHandle() {
      this.form.id = this.info.id
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.btnLoading = true
          store(this.form).then(response => {
            this.$message.success('审核成功')
            this.$router.go(-1)
            this.btnLoading = false
          }).catch(() => {
            this.$message.error('审核失败')
            this.btnLoading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap {
  height: 100%;

  .box-card {
    border: none;
    height: 100%;

    ::v-deep .el-card__header {
      padding-top: 0;
    }

    ::v-deep .el-card__body {
      height: calc(100% - 59px);
      overflow: auto;
    }
  }
}
</style>
