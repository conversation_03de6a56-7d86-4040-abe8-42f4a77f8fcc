<template>
  <div class="app-container" style="height: 100%;">
    <el-card class="card-wrap" style="height: 100%;">
      <div class="list-wrap">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          style="width: 100%"
          height="calc(100% - 96px)"
        >
          <el-table-column
            prop="id"
            label="ID"
            width="100"
          />
          <el-table-column
            prop="company"
            label="公司"
            min-width="180"
          />
          <el-table-column label="提现金额" width="200">
            <template slot-scope="scope">
              <el-tag type="danger" effect="dark" size="small">&#165; {{ scope.row.amount }} 元</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="提现类型" width="120">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.type === 1" type="info" effect="dark" size="small">个人</el-tag>
              <el-tag v-if="scope.row.type === 2" type="info" effect="dark" size="small">企业</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="120">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.status === 0" effect="dark" size="small">待审批</el-tag>
              <el-tag v-if="scope.row.status === 1" type="success" effect="dark" size="small">已通过</el-tag>
              <el-tag v-if="scope.row.status === 2" type="success" effect="dark" size="small">已拒绝</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="detail(scope.row)">详情</el-link>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="query.page" :limit.sync="query.perPage" @pagination="getList" />
      </div>
    </el-card>
  </div>
</template>

<script>
import { list } from '../../api/withdrawal'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
export default {
  name: 'List',
  components: { Pagination },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      query: { page: 1, perPage: 10 },
      total: 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.tableLoading = true
      list(this.query).then(response => {
        this.total = response.data.total
        this.tableData = response.data.data
        this.tableLoading = false
      })
    },
    detail(row) {
      this.$router.push({
        path: '/withdrawal/detail',
        query: {
          id: row.id
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.card-wrap ::v-deep .el-card__body {
  height: 100%;
}
.list-wrap{
  height: 100%;
  ::v-deep .box-card{
    border: none;
    height: 100%;
    .el-card__body{
      height: 100%;
    }
  }
}
</style>
