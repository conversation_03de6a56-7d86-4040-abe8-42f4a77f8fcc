<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <!--<span>产品管理</span>-->
        <el-button style="float: right; padding: 3px 0" type="text" @click="addApp">新增产品</el-button>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        highlight-current-row
        height="calc(100% - 96px)"
      >
        <el-table-column prop="id" label="产品ID" width="80" />
        <el-table-column label="产品图标" width="100">
          <template slot-scope="scope">
            <el-image fit="contain" style="height: 48px;width: 48px" :src="scope.row.icon" />
          </template>
        </el-table-column>
        <el-table-column prop="name" label="产品名称" width="180" />
        <el-table-column label="产品状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status" type="success" size="small" effect="dark">启用</el-tag>
            <el-tag v-else type="danger" effect="dark" size="small">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="产品套餐">
          <template slot-scope="scope">
            <el-button type="text" @click="packages(scope.row.id)">套餐管理</el-button>
          </template>
        </el-table-column>
        <el-table-column label="小程序管理">
          <template slot-scope="scope">
            <el-button type="text" @click="miniAppList(scope.row.id)">程序管理</el-button>
          </template>
        </el-table-column>
        <el-table-column label="常用操作">
          <template slot-scope="scope">
            <el-button type="text" @click="modules(scope.row.id)">常用管理</el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-link :underline="false" type="success" @click="showKeys(scope.row)">密钥</el-link>
            <el-link :underline="false" type="primary" @click="editApp(scope.row.id)">编辑</el-link>
            <el-link :underline="false" type="danger" @click="destoryApp(scope.row.id)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        style="margin-top: 0;"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getAppList"
      />
    </el-card>
    <!-- 开发者密钥-->
    <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="开发者密钥" :visible.sync="dialogVisible" width="30%">
      <el-form ref="form" :model="apps" label-width="120px" size="small">
        <el-form-item label="APP_KEY:">
          <el-input v-model="apps.app_key" readonly>
            <el-button
              slot="append"
              v-clipboard:copy="apps.app_key"
              v-clipboard:success="clipboardSuccess"
              type="primary"
              icon="el-icon-document"
            >复制</el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="APP_SECRET:">
          <el-input v-model="apps.app_secret" readonly>
            <el-button
              slot="append"
              v-clipboard:copy="apps.app_secret"
              v-clipboard:success="clipboardSuccess"
              type="primary"
              icon="el-icon-document"
            >复制</el-button>|
            <el-button
              slot="append"
              :loading="btnLoading"
              icon="el-icon-refresh"
              @click="resetSecretHandle(apps)"
            >重置</el-button>
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { list, destory, resetSecret } from '../../api/app'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import clipboard from '@/directive/clipboard/index.js' // use clipboard by v-directive
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
export default {
  components: { Pagination },
  directives: {
    clipboard,
    elDragDialog
  },
  data() {
    return {
      total: 0,
      listQuery: {
        page: 1,
        limit: 10
      },
      loading: false,
      formType: 'create',
      tableData: [],
      appMiniShow: false,
      appMiniUploadShow: false,
      appId: '',
      apps: {},
      dialogVisible: false,
      btnLoading: false
    }
  },
  created() {
    this.getAppList()
  },
  methods: {
    clipboardSuccess() {
      this.$message({
        message: '复制成功',
        type: 'success',
        duration: 1500
      })
    },
    resetSecretHandle(apps) {
      this.btnLoading = true
      resetSecret(apps).then(response => {
        this.apps = response.data
        this.btnLoading = false
      }).catch(() => {
        this.btnLoading = false
      })
    },
    showKeys(row) {
      this.apps = row
      this.dialogVisible = true
    },
    getAppList() {
      this.loading = true
      list(this.listQuery).then(response => {
        this.total = response.data.total
        this.tableData = response.data.data
        this.loading = false
      })
    },
    addApp() {
      this.$router.push({
        name: 'app-add'
      })
    },
    editApp(id) {
      this.$router.push({
        name: 'app-edit',
        query: { 'id': id }
      })
    },
    destoryApp(id) {
      this.$alert('删除后不可恢复', '确定删除么', {
        confirmButtonText: '确定',
        callback: action => {
          if (action === 'confirm') {
            destory({ id: id }).then(() => {
              this.$message.success('删除成功')
              this.getAppList()
            })
          }
        }
      })
    },
    packages(id) {
      this.$router.push({
        path: '/packages',
        query: {
          'id': id
        }
      })
    },
    miniAppList(id) {
      this.$router.push({
        path: '/appMiniList',
        query: {
          'id': id
        }
      })
    },
    modules(id) {
      this.$router.push({
        name: 'AppModules',
        query: {
          'id': id
        }
      })
    },
    tableRowClassName({ row }) {
      if (row.pid === 0) {
        return 'success-row'
      }
      return ''
    }
  }
}
</script>

<style scoped lang="scss">
  ::v-deep .el-table .success-row {
    background: #f0f9eb;
    ;
  }

  .list-wrap {
    height: 100%;

    .box-card {
      height: 100%;
      border: none;

      ::v-deep .el-card__header {
        padding-top: 0;
      }

      ::v-deep .el-card__body {
        height: calc(100% - 39px);
        overflow: auto;
        padding: 20px 0 0;
      }
    }
  }
</style>
