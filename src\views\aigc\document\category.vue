<template>
  <div class="document-wrap">
    <div class="header">
      <el-button type="primary" @click="addCategory">新增文案类型</el-button>
    </div>
    <div class="list-wrapper">
      <div class="table-box">
        <el-table
          v-loading="loading"
          :data="category"
          height="100%"
          style="width: 100%"
        >
          <el-table-column prop="title" label="文案类型" />
          <el-table-column prop="created_at" label="创建时间" width="220" />
          <el-table-column label="操作" width="180">
            <template slot-scope="{ row }">
              <el-button type="text" @click="editRow(row)">编辑</el-button>
              <el-button type="text" @click="deleteRow(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-box">
        <el-pagination
          v-if="listTotal > 0"
          background
          layout="total, prev, pager, next, jumper"
          :page-size="listQ.size"
          :current-page="listQ.page"
          :total="listTotal"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
    <edit-cate ref="editCateRef" @refresh="refreshData" />
  </div>
</template>

<script>
import EditCate from "./components/editCate.vue";
import { cateListApi, cateDeleteApi } from "@/api/aigc";

export default {
  name: "AiDocumentCategory",
  components: { EditCate },
  data() {
    return {
      loading: false,
      category: [],
      listTotal: 0,
      listQ: { page: 1, size: 10 },
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    // 加载数据
    fetchData() {
      this.loading = true;
      cateListApi(this.listQ)
        .then((res) => {
          console.log("分类列表", res);
          let { data, total } = res.data;
          if (res.code == 200) {
            if (total > 0 && data.length == 0) {
              this.listQ.page = this.listQ.page - 1;
              this.fetchData();
              return;
            }
            this.category = data;
            this.listTotal = total;
          }
        })
        .catch(() => {
          console.error("加载分类数据失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 刷新数据
    refreshData() {
      this.listQ.page = 1;
      this.fetchData();
    },
    handleSizeChange(size) {
      this.listQ.size = size;
      this.fetchData();
    },
    handleCurrentChange(page) {
      this.listQ.page = page;
      this.fetchData();
    },
    // 添加分类
    addCategory() {
      this.$refs.editCateRef.openDialog();
    },
    // 编辑分类
    editRow(row) {
      this.$refs.editCateRef.openDialog(row);
    },
    // 删除分类
    deleteRow(row) {
      this.$confirm('确定删除该文案类型吗？').then(() => {
        cateDeleteApi({ id: row.id }).then(() => {
          this.$message.success('删除成功');
          this.fetchData();
        });
      });
    },
  },
};
</script>

<style scoped lang="scss">
.document-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .header {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
  }
  .list-wrapper {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .table-box {
      flex: 1;
      overflow: hidden;
    }
    .pagination-box {
      text-align: center;
      padding: 10px 0 0;
    }
  }
}
</style>