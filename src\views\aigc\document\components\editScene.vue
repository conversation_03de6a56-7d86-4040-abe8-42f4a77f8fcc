<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="50%">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="场景类型" prop="category">
        <el-select v-model="form.category" placeholder="请选择文案类型">
          <el-option
            v-for="item in aigcCategories"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="场景名称" prop="title">
        <el-input v-model="form.title" placeholder="请输入场景名称"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex';
import { sceneStoreApi } from '@/api/aigc';

export default {
  name: 'AiDocumentEditCate',
  data() {
    return {
      dialogTitle: '',
      dialogVisible: false,
      form: {
        category: '',
        title: ''
      },
      rules: {
        category: [
          { required: true, message: '请选择文案类型', trigger: 'change' }
        ],
        title: [
          { required: true, message: '请输入场景名称', trigger: 'blur' }
        ]
      },
      categories: [] // 用于存储文案类型列表
    };
  },
  computed: {
    ...mapGetters(['aigcCategories']),
  },
  methods: {
    // 打开弹窗
    openDialog(row = null) {
      if (row) {
        this.dialogTitle = '编辑文案场景'
        this.form = {
          id: row.id,
          category: row.category,
          title: row.title
        }
      } else {
        this.dialogTitle = '添加文案场景'
        this.form = {
          id: '',
          category: '',
          title: ''
        }
      }
      this.dialogVisible = true;
    },
    // 提交表单
    submitForm() {
      this.$refs.formRef.validate(valid => {
        console.log('表单验证结果:', valid);
        if (!valid) return;
        if (valid) {
          sceneStoreApi(this.form).then(() => {
            this.$message.success('操作成功');
            this.$emit('refresh'); // 通知父组件刷新数据
            this.dialogVisible = false;
          }).catch(error => {
            console.log(`操作失败: ${error.message}`);
          })
        } else {
          console.error('表单验证失败');
        }
      });
    }
  }
};
</script>