<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <!--<span>到期列表</span>-->
        <el-button style="float: right; padding: 0" type="text" :loading="downloadLoading" @click="exportExcel">导出</el-button>
      </div>
      <div class="filter">
        <el-form :inline="true" :model="queryList" class="demo-form-inline" size="small">
          <el-form-item v-if="userType === 1" label="代理商名称">
            <el-input v-model="queryList.agent" placeholder="代理商名称" />
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="queryList.agent_exact" value="1">精准</el-checkbox>
          </el-form-item>
          <el-form-item label="用户名称">
            <el-input v-model="queryList.customer" placeholder="用户名称" />
          </el-form-item>
          <el-form-item label="产品名称">
            <el-input v-model="queryList.product" placeholder="请输入产品名称" />
          </el-form-item>

          <el-form-item label="到期时间">
            <el-date-picker
              v-model="queryList.expired_at"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="dateChange"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="table mt20">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          stripe
          style="width: 100%"
          height="calc(100% - 96px)"
        >
          <el-table-column
            prop="id"
            label="id"
          />
          <el-table-column
            label="产品名称"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.product }}</span>
              <span v-if="scope.row.version">({{ scope.row.version }})</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="userType === 1"
            prop="agent"
            label="代理名称"
          />
          <el-table-column
            prop="customer"
            label="用户名称"
          />
          <el-table-column
            prop="domain"
            label="备注"
          />
          <el-table-column
            prop="created_at"
            label="购买时间"
          />
          <el-table-column
            prop="expire_end"
            label="到期时间"
          />
          <el-table-column
            show-overflow-tooltip
            prop="content"
            label="审核内容"
          />
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryList.page"
          :limit.sync="queryList.perPage"
          style="text-align:center;"
          @pagination="getList"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { expireEndIndex } from '@/api/agent/product'
import { mapGetters } from 'vuex'
import Pagination from '@/components/Pagination'
import moment from 'moment'
import { download } from '../utils/download'
import { expireListExport } from '@/api/agent/downloads'
export default {
  name: 'List',
  components: { Pagination },
  data() {
    return {
      tableLoading: false,
      dialogFormVisible: false,
      dialogLoading: false,
      dialogDrawbackVisible: false,
      dialogFollowUpVisible: false,
      tableData: [],
      allPrice: 0,
      queryList: {
        page: 1,
        perPage: 10
      },
      form: {},
      total: 0,
      downloadLoading: false
    }
  },
  computed: {
    // 别名处理防止冲突
    ...mapGetters({
      userType: 'agentUserType'
    })
  },
  created() {
    this.getList()
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.list-wrap > .el-card > .el-card__body')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  methods: {
    getList() {
      this.tableLoading = true
      expireEndIndex(this.queryList).then(response => {
        if (response.code === 200 && response.data) {
          this.tableData = response.data.list
          this.total = response.data.meta.total
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    async exportExcel() {
      try {
        this.downloadLoading = true
        const params = {
          agent: this.queryList.agent,
          expired_at: this.queryList.expired_at,
          customer: this.queryList.customer,
          agent_exact: this.queryList.agent_exact,
          product: this.queryList.product,
        }
        const res = await expireListExport(params)
        if (res.code === 200) {
          this.$message.success('导出成功，请到下载中心下载')
        } else {
          this.$message.error(res.msg || '导出失败')
        }
      } catch (error) {
        this.$message.error(error.message || '导出失败')
      } finally {
        this.downloadLoading = false
      }
    },
    dateChange() {
      if (this.queryList.expired_at) {
        this.queryList.expired_at[0] = moment(this.queryList.expired_at[0]).format('YYYY-MM-DD')
        this.queryList.expired_at[1] = moment(this.queryList.expired_at[1]).format('YYYY-MM-DD')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap {
  height: 100%;
  .box-card {
    height: 100%;
    ::v-deep .el-card__header {
      padding-top: 0;
    }
    ::v-deep .el-card__body {
      padding: 0;
      margin-top: 20px;
      height: calc(100% - 59px);
      overflow: auto;
    }
  }
  .filter {
    border-bottom: 1px solid #E5E5E5;
  }
  .statistic{
    margin-top: 20px;

    /* 方案2：强调数字风格 */
    font-size: 14px;
    color: #333;
    span {
      color: #409EFF;
      font-weight: 500;
      font-size: 16px;
    }

    /* 通用增强效果 */
    transition: all 0.3s;
    font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
  }

  .table {
    margin-top: 20px;
    min-height: 200px;
  }
}
</style>
