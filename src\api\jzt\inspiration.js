import request from '@/utils/requestJzt'

// 获取灵感列表
export function List(data) {
  return request({
    url: '/zhymans/inslst',
    method: 'post',
    data
  })
}
// 添加灵感
export function Add(data) {
  return request({
    url: '/zhymans/addins',
    method: 'post',
    data
  })
}
// 修改灵感
export function Edit(data) {
  return request({
    url: '/zhymans/editins',
    method: 'post',
    data
  })
}
// 删除灵感
export function Delete(data) {
  return request({
    url: '/zhymans/delins',
    method: 'post',
    data
  })
}

// 获取灵感分类
export function Category() {
  return request({
    url: '/zhymans/instypeList',
    method: 'post'
  })
}
// 添加灵感分类
export function AddCategory(data) {
  return request({
    url: '/zhymans/addinstype',
    method: 'post',
    data
  })
}
// 修改灵感分类
export function EditCategory(data) {
  return request({
    url: '/zhymans/editinstype',
    method: 'post',
    data
  })
}
// 删除灵感分类
export function DeleteCategory(data) {
  return request({
    url: '/zhymans/delinstype',
    method: 'post',
    data
  })
}
