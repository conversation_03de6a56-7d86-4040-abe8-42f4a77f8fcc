<script>
import SelfFormTemp from '@/views/task/components/form/SelfFormTemp.vue'
import { resultUpdateFormData, resultUpdateFormRules, resultUpdateFormSettings } from '../../mixins/resultUpdate'
import update from '@/views/task/mixins/update'
import {
  addOtherTestResultListApi,
  editOtherTestResultListApi,
  getAddOtherTestResultListApi, getEditOtherTestResultListApi
} from '@/api/egt/recruitment'

export default {
  name: 'OtherResultUpdate',
  components: {
    SelfFormTemp
  },
  mixins: [update],
  data() {
    return {
      filters: [
        {
          label: '测试题分类',
          prop: 'category',
          type: 'select',
          settings: {
            placeholder: '测试题分类',
            options: [],
            props: {
              label: 'title',
              value: 'id'
            }
          }
        },
        {
          label: '评分标准',
          prop: 'type',
          type: 'select',
          settings: {
            placeholder: '评分标准',
            options: []
          }
        },
        ...resultUpdateFormSettings
      ],
      form: {
        category: '',
        type: '',
        ...resultUpdateFormData
      },
      rules: {
        category: [
          { required: true, message: '请选择性格类型', trigger: 'change' }
        ],
        type: [
          { required: true, message: '请输入评分标准', trigger: 'blur' }
        ],
        ...resultUpdateFormRules
      }
    }
  },
  watch: {
    _visible(val) {
      if (val) {
        this.getParams()
      }
    }
  },
  methods: {
    async getParams() {
      const api = this.data.id ? getEditOtherTestResultListApi : getAddOtherTestResultListApi
      if (!api) return
      const params = {
        id: this.data.id || undefined
      }
      try {
        this.loading = true
        const res = await api(params)
        if (res.code === 200 && res.data) {
          if (res.data.list) {
            const category = this.filters.find(item => item.prop === 'category')
            category.settings.options = res.data.list
          }
          if (res.data.type) {
            const type = this.filters.find(item => item.prop === 'type')
            type.settings.options = []
            for (const typeKey in res.data.type) {
              const item = res.data.type[typeKey]
              type.settings.options.push({
                label: item,
                value: +typeKey
              })
            }
          }
          if (this.data.id && res.data.data) {
            this.form = res.data.data
          }
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    },
    async submitCallback() {
      let api = addOtherTestResultListApi
      if (this.data.id) {
        this.form.id = this.data.id
        api = editOtherTestResultListApi
      }
      if (!api) return
      try {
        this.submitLoading = true
        const res = await api(this.form)
        if (res.code === 200) {
          this.$message.success('操作成功')
          this.$emit('submitSuccess')
          return Promise.resolve({ success: true })
        } else {
          return Promise.reject({ success: false, msg: res.msg || res.message || '操作失败' })
        }
      } catch (e) {
        console.log(e)
        return Promise.reject({ success: false, msg: '操作失败' })
      } finally {
        this.submitLoading = false
      }
    }
  }
}
</script>

<template>
  <div>
    <el-dialog :close-on-click-modal="false" :visible.sync="_visible" title="其他测试结果" width="700px">
      <SelfFormTemp
        ref="formWrap"
        :filters="filters"
        :form-data.sync="form"
        :rules="rules"
        label-width="120px"
        :inline="false"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button :loading="submitLoading" type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
