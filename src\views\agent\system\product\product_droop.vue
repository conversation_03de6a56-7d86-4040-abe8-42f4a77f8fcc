<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <!--<span>垂类产品列表</span>-->
        <!--<el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>-->
        <el-button style="float: right; padding: 3px 0" type="text" @click="createView">新增</el-button>
      </div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        stripe
        style="width: 100%"
        height="100%"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="68"
        />
        <el-table-column
          prop="name"
          label="产品名称"
        />
        <el-table-column
          prop="category.name"
          label="所属分类"
        />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="editView(scope.row)">编辑</el-button>
            <el-divider direction="vertical" />
            <el-button type="text" disabled @click="del(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <el-dialog
      :title="ruleForm.id ? '操作' : '新增'"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="所属分类" prop="category_id">
          <el-select v-model="ruleForm.category_id" placeholder="请选择分类">
            <el-option
              v-for="item in companies"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="产品名称" prop="name" style="width: 70%">
          <el-input v-model="ruleForm.name" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click=" saveHandle() ">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import { categories } from '@/api/agent/productCategory'
import { list, store, destroy } from '@/api/agent/productDroop'
export default {
  data() {
    return {
      tableLoading: false,
      dialogVisible: false,
      tableData: [],
      companies: [],
      ruleForm: {},
      rules: {
        name: [{ required: true, message: '产品名称必填', trigger: 'blur' }],
        category_id: [{ required: true, message: '分类必须选择', trigger: 'change' }]
      }
    }
  },
  created() {
    this.categoriesList()
    this.getList()
  },
  methods: {
    categoriesList() {
      categories({}).then(response => {
        if (response.code === 200 && response.data) {
          var arr = []
          response.data.list.forEach(function(item) {
            if (item.id > 18) {
              arr.push(item)
            }
          })

          this.companies = arr
        }
      })
    },
    getList() {
      this.tableLoading = true
      list({}).then(response => {
        if (response.code === 200 && response.data) {
          this.tableData = response.data
          this.tableLoading = false
        }
      }).catch(() => {
        this.tableLoading = false
      })
    },
    createView() {
      this.ruleForm = {}
      this.dialogVisible = true
    },
    editView(row) {
      this.ruleForm = row
      this.dialogVisible = true
    },
    del(row) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        destroy({ id: row.id }).then(response => {
          if (response.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          }
        })
      })
    },
    saveHandle() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          store(this.ruleForm).then(response => {
            this.dialogVisible = false
            this.$message.success(response.message)
            this.getList()
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap {
  height: 100%;
  .box-card {
    height: 100%;
    ::v-deep .el-card__header {
      padding-top: 0;
    }
    ::v-deep .el-card__body {
      padding: 0;
      margin-top: 20px;
      height: calc(100% - 59px);
      overflow: auto;
    }
  }
  .filter {
    border-bottom: 1px solid #E5E5E5;
  }
  .table {
    margin-top: 20px;
    min-height: 200px;
  }
}
</style>

