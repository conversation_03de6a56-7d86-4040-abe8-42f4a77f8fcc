// 岗位下拉列表
import request from '@/utils/requestOpen'

export function getPositionList(data) {
  return request({
    url: '/users/workType/index',
    method: 'POST',
    data
  })
}

// 项目/任务下拉列表
export function getProjectList(data) {
  return request({
    url: '/tripartite/getStatisticsHallType',
    method: 'POST',
    data
  })
}

// 获取代理商列表
export function getAgentList(data) {
  return request({
    url: '/tripartite/getStatisticsCompany',
    method: 'POST',
    data
  })
}

// 按照人员统计
export function getStatisticsUser(data) {
  return request({
    url: '/tripartite/getStatisticsByUser',
    method: 'POST',
    data
  })
}

// 按照岗位统计
export function getStatisticsPosition(data) {
  return request({
    url: '/tripartite/getStatisticsByWork',
    method: 'POST',
    data
  })
}

// 按照代理商发布项目/任务统计
export function getStatisticsCompany(data) {
  return request({
    url: '/tripartite/getStatisticsByCtht',
    method: 'POST',
    data
  })
}

// 按照项目/任务统计
export function getStatisticsHallType(data) {
  return request({
    url: '/tripartite/getStatisticsByHallType',
    method: 'POST',
    data
  })
}

// 个人任务统计
export function getStatisticsUserTask(data) {
  return request({
    url: '/tripartite/getStatisticsByPerson',
    method: 'POST',
    data
  })
}
