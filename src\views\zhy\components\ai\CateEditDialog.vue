<!-- 编辑数据 -->
<template>
  <el-dialog :close-on-click-modal="false" :title="`${openTitle}分类`" :visible.sync="dialogVisible" width="40%">
    <el-form v-if="dialogVisible" ref="ruleForm" :model="formData" :rules="rules" label-width="80px"
      class="demo-ruleForm">
      <!-- 分类选择 -->
      <!-- <el-form-item label="父级分类" prop="class_id">
        <el-select v-model="formData.class_id" placeholder="请选择" filterable style="width: 100%;">
          <el-option label="顶级分类" :value="0"></el-option>
          <el-option v-for="item in cateData" :key="item.id" :label="item.title" :value="item.id"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="内容" prop="content">
        <el-input v-model="formData.content" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item label="图标" prop="icon">
        <SingleImg v-model="formData.icon" />
      </el-form-item>
      <!-- 排序 -->
      <el-form-item label="排序" prop="orderid">
        <el-input-number v-model="formData.orderid" :min="0" :max="100" label="排序" />
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="saveHandle">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Editor from '@/components/editor'
import SingleImg from '@/components/Upload/SingleImg'
import { aiTypeAllApi } from '@/api/zhy_ai'

export default {
  components: {
    Editor, SingleImg
  },
  props: {
    editApi: {
      type: Function,
      default: () => { }
    }
  },
  data() {
    return {
      cateData: [],
      dialogVisible: false,
      loading: false,
      openTitle: '新增',
      formData: {
        class_id: 0,
        orderid: 0
      },
    }
  },
  computed: {
    rules() {
      let obj = {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
        ],
        class_id: [
          { required: true, message: '请选择父级分类', trigger: 'blur' },
        ]
      }
      return obj
    }
  },
  mounted() {

  },
  methods: {
    // 获取全部分类
    async getCateData() {
      const result = await aiTypeAllApi()
      this.cateData = result.data.data
    },
    // 打开弹窗
    openDialog(data) {
      if (data) {
        this.openTitle = '修改'
        const { id, title, icon, content, class_id, orderid } = data
        this.formData = {
          id, name: title, icon, content, class_id, orderid
        }
      } else {
        this.formData = { class_id: 0, orderid: 0 }
        this.openTitle = '新增'
      }
      this.getCateData()
      this.dialogVisible = true
    },
    // 保存
    saveHandle() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          this.loading = true
          // 请求接口
          try {
            const result = await this.editApi(this.formData)
            // 成功
            this.loading = false
            this.dialogVisible = false
            this.$message.success('保存成功')
            this.$emit('getList')
          } catch (error) {
            this.loading = false
          }
        } else {
          return false
        }
      })
    },
    // 关闭弹窗
    closeDialog() {
      this.dialogVisible = false
    },
  },
}
</script>
<style lang='scss' scoped></style>
