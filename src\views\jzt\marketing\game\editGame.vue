<template>
  <el-form
    ref="ruleForm"
    v-loading="loading"
    :model="formInfo[from].formData"
    :rules="formInfo[from].rules"
    label-width="80px"
  >
    <template v-for="(item, index) in formInfo[from].formSet">
      <el-form-item :key="index" :prop="item.key" :label="item.label">
        <el-input v-if="item.type == 'input'" v-model="formInfo[from].formData[item.key]" :placeholder="`请填写${item.label}`" />
        <template v-if="item.type == 'img'">
          <el-upload
            class="avatar-uploader"
            :action="uploadUrl"
            :headers="{ Authorization: `Bearer ${token}` }"
            :before-upload="beforeAvatarUpload"
            :on-preview="handlePictureCardPreview"
            :on-success="res => handleAvatarSuccess(res, item.key)"
            :on-remove="handleRemove"
            :show-file-list="false"
            accept="image/*"
          >
            <el-image v-if="formInfo[from].formData[item.key]" :src="formInfo[from].formData[item.key]" class="avatar" fit="contain">
              <div slot="placeholder" class="image-slot">
                加载中<span class="dot">...</span>
              </div>
            </el-image>
            <i v-else class="el-icon-plus avatar-uploader-icon" />
            <template slot="tip">
              <div class="el-upload__tip" style="margin-left: 0px">
                只能上传jpg/png文件，且不超过2M
              </div>
            </template>
          </el-upload>
          <el-dialog :close-on-click-modal="false" :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
          </el-dialog>
        </template>
        <template v-if="item.type == 'radio'">
          <el-radio-group v-model="formInfo[from].formData[item.key]">
            <el-radio v-for="(v, i) in item.value" :key="i" :label="Number(i)">{{ v }}</el-radio>
          </el-radio-group>
        </template>
      </el-form-item>
    </template>
    <div style="text-align: center">
      <el-button style="width: 100px;height: 34px" type="primary" @click="submitData">确定</el-button>
      <el-button style="width: 100px;height: 34px" type="default" @click="cancelData">取消</el-button>
    </div>
  </el-form>
</template>

<script>
import { getToken } from '@/utils/auth'
import { editGame, editMeal } from '@/api/jzt/marketing'

export default {
  name: 'EditGame',
  props: {
    from: {
      type: String,
      default: 'game'
    }
  },
  data() {
    return {
      token: null,
      uploadUrl: process.env.VUE_APP_JZT_API_PROXY + '/cases/imgajax',
      dialogImageUrl: '',
      dialogVisible: false, // 预览图弹窗
      loading: false,

      formInfo: {
        'game': {
          formSet: [
            { key: 'title', label: '游戏标题', type: 'input' },
            { key: 'game_url', label: '游戏链接', type: 'input' },
            { key: 'icon', label: '游戏图标', type: 'img' },
            { key: 'status', label: '游戏状态', type: 'radio', value: { 0: '下线', 1: '上线' }}
          ],
          formData: { title: '', game_url: '', icon: '', status: 1 },
          rules: {
            title: [{ required: true, message: '请填写游戏标题', trigger: 'blur' }],
            game_url: [{ required: true, message: '请填写游戏链接', trigger: 'blur' }],
            icon: [{ required: true, message: '请上传游戏图标', trigger: 'change' }],
            status: [{ required: true, message: '请选择游戏状态', trigger: 'change' }]
          },
          editApi: editGame
        },
        'meal': {
          formSet: [
            { key: 'title', label: '套餐标题', type: 'input' },
            { key: 'days', label: '套餐天数', type: 'input' },
            { key: 'price', label: '套餐价格', type: 'input' },
            { key: 'state', label: '套餐状态', type: 'radio', value: { 0: '下线', 1: '上线' }}
          ],
          formData: { title: '', days: '', price: '', state: 1 },
          rules: {
            title: [{ required: true, message: '请填套餐标题', trigger: 'blur' }],
            days: [{ required: true, message: '请填写套餐天数', trigger: 'blur' }],
            price: [{ required: true, message: '请填写套餐价格', trigger: 'blur' }],
            state: [{ required: true, message: '请选择套餐状态', trigger: 'change' }]
          },
          editApi: editMeal
        }
      }
    }
  },
  computed: {
    // form
  },
  created() {
    this.token = getToken()
  },
  methods: {
    // 提交
    submitData() {
      this.loading = true
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.formInfo[this.from].editApi(this.formInfo[this.from].formData).then(res => {
            this.loading = false
            this.$message.success(res.msg)
            this.$emit('closeDialog', true)
          })
        } else {
          this.loading = false
        }
      })
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPng = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG && !isPng) {
        ElMessage.error('上传头像图片只能是 JPG 或 PNG 格式!')
      }
      if (!isLt2M) {
        ElMessage.error('上传头像图片大小不能超过 2MB!')
      }
      return (isJPG || isPng) && isLt2M
    },
    // 监听图片删除
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    // 预览图片
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.response
      this.dialogVisible = true
    },
    // 图片上传成功回调
    handleAvatarSuccess(response, key) {
      if (response.code === 200) {
        this.formInfo[this.from].formData[key] = response.data
      }
    },
    // 数据
    initFormData(data) {
      this.formInfo[this.from].formData = data
    },
    // 取消
    cancelData() {
      this.$emit('closeDialog')
      // this.$refs.ruleForm.resetFields()
    }
  }
}
</script>

<style scoped lang="scss">
.avatar-uploader-icon {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  width: 178px;
  height: 178px;
  display: block;
}
</style>
