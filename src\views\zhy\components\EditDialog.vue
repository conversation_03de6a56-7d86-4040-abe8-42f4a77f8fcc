<!-- 编辑数据 -->
<template>
  <el-dialog :close-on-click-modal="false" :title="`${openTitle}平台`" :visible.sync="dialogVisible" width="60%">
    <el-form
      v-if="dialogVisible"
      ref="ruleForm"
      :model="formData"
      :rules="rules"
      label-width="80px"
      class="demo-ruleForm"
    >
      <el-form-item label="名称" prop="title">
        <el-input v-model="formData.title" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item v-if="from == 'private'" label="类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择" style="width: 100%;">
          <el-option label="私域电商" :value="1" />
          <el-option label="带货电商" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="from == 'tradition'" label="类型" prop="class_id">
        <el-select v-model="formData.class_id" placeholder="请选择" filterable style="width: 100%;">
          <el-option v-for="item in cateData" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="图标" prop="icon">
        <SingleImg v-model="formData.icon" />
      </el-form-item>
      <el-form-item label="链接" prop="url">
        <el-input v-model="formData.url" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="排序" prop="orderid">
        <el-input-number v-model="formData.orderid" :min="0" placeholder="请输入序号" />
        &nbsp;&nbsp;<span style="color: #ff4949;">排序越小越靠前</span>
      </el-form-item>
      <el-form-item label="详情" prop="content">
        <Editor :content.sync="formData.content" path="shop" />
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="saveHandle">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Editor from '@/components/editor'
import SingleImg from '@/components/Upload/SingleImg'
import { traditionTypeApi } from '@/api/zhy_shop'

export default {
  components: {
    Editor, SingleImg
  },
  props: {
    from: {
      type: String,
      default: ''
    },
    editApi: {
      type: Function,
      default: () => { }
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      openTitle: '新增',
      formData: {},
      rules: {
        title: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        icon: [
          { required: true, message: '请上传图标', trigger: 'blur' }
        ],
        url: [
          { required: true, message: '请输入链接', trigger: 'blur' }
        ]
      },
      cateData: []
    }
  },
  mounted() {

  },
  methods: {
    // 获取传统电商分类
    async getTypeData() {
      const result = await traditionTypeApi()
      // console.log(result, 'result')
      this.cateData = result.data
    },
    // 打开弹窗
    openDialog(data) {
      if (data) {
        this.openTitle = '修改'
        const { id, title, icon, url, content, orderid } = data
        this.formData = {
          id, title, icon, url, content, orderid
        }
        if (this.from == 'private') this.$set(this.formData, 'type', data.type)
        if (this.from == 'tradition') this.$set(this.formData, 'class_id', data.class_id)
      } else {
        this.formData = {}
        if (this.from == 'tradition') this.$set(this.formData, 'class_id', 1)
        this.openTitle = '新增'
      }
      let typeKey = ''
      if (this.from == 'private') typeKey = 'type'
      if (this.from == 'tradition') typeKey = 'class_id'
      this.rules[typeKey] = [
        { required: true, message: '请选择类型', trigger: 'blur' }
      ]
      if (this.from == 'tradition') this.getTypeData()
      this.dialogVisible = true
    },
    // 保存
    saveHandle() {
      this.$refs.ruleForm.validate(async(valid) => {
        if (valid) {
          this.loading = true
          // 请求接口
          try {
            const result = await this.editApi(this.formData)
            // 成功
            this.loading = false
            this.dialogVisible = false
            this.$message.success('保存成功')
            this.$emit('getList')
          } catch (error) {
            this.loading = false
          }
        } else {
          return false
        }
      })
    },
    // 关闭弹窗
    closeDialog() {
      this.dialogVisible = false
    }
  }
}
</script>
<style lang='scss' scoped>
.el-form {
  height: 58vh;
  overflow-y: auto;
}
</style>
