import request from '@/utils/request'

// 获取文件打印类型列表
export function getPrintTypeList(data) {
  return request({
    url: '/document/documentType',
    method: 'POST',
    data
  })
}
// 获取文件打印列表
export function getPrintListApi(data) {
  return request({
    url: '/document/documentList',
    method: 'POST',
    data
  })
}
// 获取添加文件打印需要参数
export function getPrintAddApi(data) {
  return request({
    url: '/document/getDocumentAdd',
    method: 'POST',
    data
  })
}
// 添加文件打印
export function addPrintApi(data) {
  return request({
    url: '/document/documentCreate',
    method: 'POST',
    data
  })
}
// 获取修改文件打印需要参数
export function getPrintUpdateApi(data) {
  return request({
    url: '/document/getDocumentEdit',
    method: 'POST',
    data
  })
}
// 修改文件打印
export function updatePrintApi(data) {
  return request({
    url: '/document/documentEdit',
    method: 'POST',
    data
  })
}
// 删除文件打印
export function deletePrintApi(data) {
  return request({
    url: '/document/documentDelete',
    method: 'POST',
    data
  })
}
// 获取添加文件打印类型需要参数
export function getPrintCateAddApi(data) {
  return request({
    url: '/document/getDocumentTypeAdd',
    method: 'POST',
    data
  })
}
// 添加文件打印类型
export function addPrintCateApi(data) {
  return request({
    url: '/document/documentTypeCreate',
    method: 'POST',
    data
  })
}
// 获取修改文件打印类型需要参数
export function getPrintCateUpdateApi(data) {
  return request({
    url: '/document/getDocumentTypeEdit',
    method: 'POST',
    data
  })
}
// 修改文件打印类型
export function updatePrintCateApi(data) {
  return request({
    url: '/document/documentTypeEdit',
    method: 'POST',
    data
  })
}
// 删除文件打印类型
export function deletePrintCateApi(data) {
  return request({
    url: '/document/documentTypeDelete',
    method: 'POST',
    data
  })
}
