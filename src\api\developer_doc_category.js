import request from '@/utils/request'

export function list() {
  return request({
    url: '/developerDoc/category/list',
    method: 'POST'
  })
}

export function topList() {
  return request({
    url: '/developerDoc/category/topList',
    method: 'POST'
  })
}

export function store(data) {
  return request({
    url: '/developerDoc/category/store',
    method: 'POST',
    data: data
  })
}

export function edit(data) {
  return request({
    url: '/developerDoc/category/update',
    method: 'POST',
    data: data
  })
}

export function destory(data) {
  return request({
    url: '/developerDoc/category/destory',
    method: 'POST',
    data: data
  })
}
