import request from '@/utils/request'

export function getRootMenus() {
  return request({
    url: '/menus/root',
    method: 'POST'
  })
}

export function getTreeTableMenus(data) {
  return request({
    url: '/menus/treeTable',
    method: 'POST',
    data: data
  })
}

export function createMenu(data) {
  return request({
    url: '/menus/store',
    method: 'POST',
    data: data
  })
}

export function updateMenu(data) {
  return request({
    url: '/menus/update',
    method: 'POST',
    data: data
  })
}

export function deleteMenu(data) {
  return request({
    url: '/menus/delete',
    method: 'POST',
    data: data
  })
}

export function getOrgMenus() {
  return request({
    url: '/menus/original_list',
    method: 'POST'
  })
}
