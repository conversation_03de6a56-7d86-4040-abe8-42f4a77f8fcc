<template>
  <div>
    <div ref="visitDom" :style="`width: ${width}; height: ${height};`" />
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  props: {
    from: {
      type: String,
      default: 'visit'
    },
    data: {
      type: Object,
      default: () => ({})
    },
    width: {
      type: String,
      default: '180px'
    },
    qDColor: {
      type: Array,
      default: () => {
        return ['#3765FF', '#FF6800', '#46C4FF', '#B4A5FF', '#15FFFC']
      }
    },
    height: {
      type: String,
      default: '208px'
    },
    isShowTitle: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {

    }
  },
  watch: {
    data: {
      handler(newVal, oldVal) {
        // console.log(newVal, oldVal)
        if (!newVal) return
        let dataList
        if (newVal.pv) {
          dataList = [
            { name: 'PV', list: newVal.pv },
            { name: 'UV', list: newVal.uv }
          ]
        } else {
          dataList = newVal.data
        }
        // console.log(newVal, "newVal");
        // console.log(dataList, "datalist");
        this.$nextTick(() => {
          this.visitInit(newVal.day, dataList)
        })
      },
      deep: true
      // immediate: true
    }
  },
  methods: {
    visitInit(xLabel, dataList) {
      const that = this
      var dom = that.$refs.visitDom
      var myChart = echarts.init(dom)
      var option

      const nameData = []
      const listData = []
      dataList.map((item, index) => {
        nameData.push({
          name: item.name
        })
        listData.push({
          name: item.name,
          type: 'line',
          // symbol: "circle", // 默认是空心圆（中间是白色的），改成实心圆
          // showAllSymbol: true,
          symbolSize: 8,
          // smooth: true,
          lineStyle: {
            // normal: {
            width: 2,
            color: that.qDColor[index] // 线条颜色
            // },
            // borderColor: "rgba(0,0,0,.4)",
          },
          itemStyle: {
            color: that.qDColor[index],
            // borderColor: "#646ace",
            borderWidth: 2
          },
          areaStyle: {
            // 区域填充样式
            // normal: {
            // 线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: that.qDColor[index] + '80'
                },
                {
                  offset: 1,
                  color: that.qDColor[index] + '00'
                }
              ],
              false
            ),
            shadowColor: that.qDColor[index] + '33', // 阴影颜色
            shadowBlur: 20 // shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
            // },
          },
          data: item.list
        })
      })
      const legend = {
        align: 'left',
        top: '5px',
        type: 'plain',
        textStyle: {
          color: 'rgba(105, 108, 125, 1)',
          fontSize: 16
        },
        // icon: 'rect',
        itemGap: 30,
        itemWidth: 30,
        itemHeight: 4,
        itemStyle: {
          borderWidth: 0
        },
        icon: 'path://M0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z',
        data: nameData
      }

      if (that.from == 'visit') {
        // legend.show = that.isShowTitle;
        legend.left = '45%'
        legend.top = '90%'
      }
      if (that.from == 'report') {
        legend.show = true
        legend.right = '0%'
      }

      option = {
        backgroundColor: '#fff',
        tooltip: {
          trigger: 'axis',
          // backgroundColor: 'rgba(0, 0, 0, 0.4)',
          axisPointer: {
            lineStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(126,199,255,0)' // 0% 处的颜色
                  },
                  {
                    offset: 0.5,
                    color: 'rgba(126,199,255,1)' // 100% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(126,199,255,0)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          }
          // formatter: (p) => {},
        },
        legend,
        grid: {
          top: '10px',
          left: '50px',
          right: '20px',
          bottom: '20%' // containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisLine: {
              // 坐标轴轴线相关设置。数学上的x轴
              show: true,
              lineStyle: {
                color: 'transparent'
              }
            },
            axisLabel: {
              // 坐标轴刻度标签的相关设置
              // textStyle: {
              color: 'rgba(167, 171, 187, 1)',
              padding: 0,
              fontSize: 14,
              // },
              formatter: (data) => {
                return data
              }
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: '#000'
              }
            },
            axisTick: {
              show: false
            },
            data: xLabel
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: that.isShowTitle ? '访问统计' : '',
            nameTextStyle: {
              color: 'rgba(28, 43, 75, 1)',
              fontSize: 16,
              padding: 10,
              marginLeft: -10,
              fontWeight: 'bold'
            },
            min: 0,
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(167, 171, 187, 0.4)'
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(167, 171, 187, 1)'
              }
            },
            axisLabel: {
              show: true,
              // textStyle: {
              color: 'rgba(167, 171, 187, 1)',
              padding: 0,
              // },
              formatter: function formatter(value) {
                if (value === 0) {
                  return value
                }

                return value
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: listData
      }

      myChart.setOption(option)
    }
  }
}
</script>

<style scoped lang='scss'></style>
