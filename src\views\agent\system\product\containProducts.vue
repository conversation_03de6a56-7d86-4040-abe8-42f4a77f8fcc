<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
        <el-button style="float: right; padding: 3px 0" type="text" @click="openProduct">保存</el-button>
      </div>
      <el-form ref="ruleForm" v-loading="loading" label-width="110px" class="demo-ruleForm">
        <el-button type="text" @click="addGivingProduct">添加包含产品</el-button>

        <el-form-item v-for="(item,index) in list" :key="index" label="产品名称：">
          <el-input v-model="item.name" style="width: 200px" placeholder="请输入产品名称" />
          <span style="margin-left: 20px">产品价格：</span>
          <el-input v-model="item.price" style="width: 200px" placeholder="请输入价格" />
          <el-button type="text" style="margin-left: 20px;color: red" @click="deleteGivingProduct(index)">删除</el-button>
        </el-form-item>

      </el-form>
    </el-card>
  </div>
</template>

<script>
import { containProductList, containProductUpdate } from '@/api/agent/product'
export default {
  name: 'Supplement',
  data() {
    return {
      list: [],
      product_id: '',
      loading: false
    }
  },
  created() {
    this.product_id = this.$route.query.id
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      containProductList({ id: this.product_id }).then(res => {
        if (res.code === 200) {
          this.list = res.data
        }
      }).catch(e => {
        console.log(e)
      }).finally(() => {
        this.loading = false
      })
    },
    openProduct() {
      containProductUpdate({ product_id: this.product_id, list: this.list }).then(response => {
        if (response.code === 200) {
          this.$message.success('操作成功')
          this.$router.go(-1)
        }
      })
    },
    // 添加包含产品
    addGivingProduct() {
      this.list.push({ name: '', price: '' })
    },
    // 删除包含产品
    deleteGivingProduct(index) {
      this.list.splice(index, 1)
    }
  }
}
</script>

<style scoped>

</style>
