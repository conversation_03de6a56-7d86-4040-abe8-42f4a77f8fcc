<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import Update from './Update.vue'
import { getCompanyServiceSettingsListApi } from '@/api/egt/companyService'

export default {
  name: 'CompanyServiceSettings',
  components: { StatisticsTemplate, Update },
  data() {
    return {
      config: {
        key: 'settings',
        tableSettings: {
          selection: false,
          api: getCompanyServiceSettingsListApi,
          field: {
            page: 'pageIndex',
            limit: 'pageSize',
            total: 'total'
          },
          index: true,
          columns: [
            {
              prop: 'title',
              label: '标题',
              width: 200
            },
            {
              prop: 'remarks',
              label: '简介'
            },
            {
              prop: 'action',
              label: '操作',
              align: 'center',
              isSlot: true,
              width: 200
            }
          ]
        }
      },
      updateVisible: false,
      row: {}
    }
  },
  methods: {
    handleAdd() {
      this.updateVisible = true
      this.row = {}
    },
    handleUpdate(row) {
      this.updateVisible = true
      this.row = row
    },
    handleSubmitSuccess() {
      this.$refs.listWrapRef.handleGetData()
    }
  }
}
</script>

<template>
  <div class="page-wrap mt20">
    <statistics-template ref="listWrapRef" :config="config" self-key="settings">
      <template #topActions>
        <div style="text-align: right;">
          <el-button type="primary" @click="handleAdd">添加</el-button>
        </div>
      </template>
      <template #settings_action="{row}">
        <el-button size="mini" @click="handleUpdate(row)">编辑</el-button>
      </template>
    </statistics-template>
    <Update :visible.sync="updateVisible" :data="row" @submit-success="handleSubmitSuccess" />
  </div>
</template>

<style lang="scss" scoped>
.page-wrap{
  height: calc(100% - 20px);
}
</style>
