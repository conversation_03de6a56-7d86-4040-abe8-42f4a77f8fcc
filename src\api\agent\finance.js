import request from '@/utils/request'

export function index(data) {
  return request({
    url: '/finance/index',
    method: 'post',
    data: data
  })
}

export function orderDetail(data) {
  return request({
    url: 'finance/detail',
    method: 'post',
    data: data
  })
}

export function audit(data) {
  return request({
    url: '/finance/audit',
    method: 'post',
    data: data
  })
}

export function destory(data) {
  return request({
    url: '/finance/destory',
    method: 'post',
    data: data
  })
}

export function agent(data) {
  return request({
    url: '/finance/agent/index',
    method: 'post',
    data: data
  })
}
export function agentlist(data) {
  return request({
    url: '/finance/agent/agentlist',
    method: 'post',
    data: data
  })
}
export function agenttasks(data) {
  return request({
    url: '/finance/agent/agenttasks',
    method: 'post',
    data: data
  })
}
export function deductionCancel(data) {
  return request({
    url: '/finance/agent/tasksdeductionCancel',
    method: 'post',
    data: data
  })
}
export function agenttotallist(data) {
  return request({
    url: '/finance/agent/agenttotallist',
    method: 'post',
    data: data
  })
}

export function tasksdetail(data) {
  return request({
    url: '/finance/agent/tasksdetail',
    method: 'post',
    data: data
  })
}
export function tasksagentshow(data) {
  return request({
    url: '/finance/agent/tasksagentshow',
    method: 'post',
    data: data
  })
}
export function priceSum(data) {
  return request({
    url: '/finance/agent/priceSum',
    method: 'post',
    data: data
  })
}

export function detail(data) {
  return request({
    url: '/finance/agent/detail',
    method: 'post',
    data: data
  })
}

export function reconciliation(data) {
  return request({
    url: '/finance/agent/reconciliation',
    method: 'post',
    data: data
  })
}

export function agentSure(data) {
  return request({
    url: '/finance/agent/agentSure',
    method: 'post',
    data: data
  })
}

export function financeSure(data) {
  return request({
    url: '/finance/agent/fnanceSure',
    method: 'post',
    data: data
  })
}

export function smsAgent(data) {
  return request({
    url: '/sms/agent',
    method: 'post',
    data: data
  })
}

export function catchAgentPhone(data) {
  return request({
    url: '/sms/catchAgentPhone',
    method: 'post',
    data: data
  })
}

// 1.0 历史订单
export function history(data) {
  return request({
    url: '/finance/history',
    method: 'post',
    data: data
  })
}

export function invoiceTotle(data) {
  return request({
    url: '/finance/invoice/invoiceTotle',
    method: 'post',
    data: data
  })
}

export function invoiceExcle(data) {
  return request({
    url: '/auth/invoiceExcle',
    method: 'post',
    data: data
  })
}

export function invoiceList(data) {
  return request({
    url: '/finance/invoice/list',
    method: 'post',
    data: data
  })
}

export function invoiceAuth(data) {
  return request({
    url: '/finance/invoice/auth',
    method: 'post',
    data: data
  })
}

export function retryProduct(data) {
  return request({
    url: '/finance/retryProduct',
    method: 'post',
    data: data
  })
}

export function domainStatistics(data) {
  return request({
    url: '/finance/domainStatistics',
    method: 'post',
    data: data
  })
}

// 体验订单跟进列表
export function followUpIndex(data) {
  return request({
    url: '/finance/followUp/index',
    method: 'post',
    data: data
  })
}
// 体验订单跟进提交
export function followUpStore(data) {
  return request({
    url: '/finance/followUp/store',
    method: 'post',
    data: data
  })
}

export function changepro(data) {
  return request({
    url: '/finance/changepro',
    method: 'post',
    data: data
  })
}
export function invoiceagents(data) {
  return request({
    url: '/finance/invoice/agents',
    method: 'post',
    data: data
  })
}
//激活密钥产品订单
export function retrykeyProduct(data) {
  return request({
    url: '/finance/retrykeyProduct',
    method: 'post',
    data: data
  })
}

export function invoiceTime(data) {
  return request({
    url: '/finance/invoice/timeshow',
    method: 'post',
    data: data
  })
}

export function invoiceTimeDaile(data) {
  return request({
    url: '/finance/invoice/timedaile',
    method: 'post',
    data: data
  })
}
