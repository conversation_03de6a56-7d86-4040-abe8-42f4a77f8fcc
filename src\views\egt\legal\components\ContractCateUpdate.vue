<script>
import { addContractCategoryApi, updateContractCategoryApi } from '@/api/egt/contract'
import SelfFormTemp from '@/views/task/components/form/SelfFormTemp.vue'
import update from '@/views/task/mixins/update'

export default {
  name: 'ContractCateUpdate',
  components: { SelfFormTemp },
  mixins: [update],
  data() {
    return {
      filters: [
        {
          label: '分类名称',
          prop: 'cate_name',
          type: 'input'
        }
      ],
      formData: {
        cate_name: ''
      },
      rules: {
        cate_name: [
          {
            required: true,
            message: '请输入分类名称',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    async submitCallback() {
      let api = addContractCategoryApi
      if (this.data.id) {
        api = updateContractCategoryApi
        this.formData.id = this.data.id
      }
      try {
        const res = await api(this.formData)
        if (res.code === 200) {
          this.$emit('submit-success')
          return Promise.resolve({ success: true })
        } else {
          return Promise.reject({ success: false, msg: this.data.id ? '编辑失败' : '新增失败' })
        }
      } catch (error) {
        return Promise.reject({ success: false, msg: this.data.id ? '编辑失败' : '新增失败' })
      }
    }
  }
}
</script>

<template>
  <el-dialog :close-on-click-modal="false" :title="title + '合同分类'" :visible.sync="_visible" append-to-body width="500px">
    <self-form-temp ref="formWrap" :filters="filters" :form-data.sync="formData" :rules="rules" :inline="false" label-width="100px" />
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
