import request from '@/utils/requestOpen'

export function getTreeTableMenus(data) {
  return request({
    url: '/tripartite/chinaRoot',
    method: 'POST',
    data: data
  })
}

export function createMenu(data) {
  return request({
    url: '/tripartite/addChinaRoot',
    method: 'POST',
    data: data
  })
}

export function updateMenu(data) {
  return request({
    url: '/tripartite/editChinaRoot',
    method: 'POST',
    data: data
  })
}

export function deleteMenu(data) {
  return request({
    url: '/tripartite/deleteChinaRoot',
    method: 'POST',
    data: data
  })
}

export function getOrgMenus() {
  return request({
    url: '/menus/original_list',
    method: 'POST'
  })
}

// 获取公司列表
export function getCompanyList(data) {
  return request({
    url: '/tripartite/getCompanyList',
    method: 'POST',
    data: data
  })
}
