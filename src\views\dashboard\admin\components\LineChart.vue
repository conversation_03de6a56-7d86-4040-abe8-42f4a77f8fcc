<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
  name: 'Line<PERSON><PERSON>',
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: [Array, Object],
      required: true
    },
    legend: {
      type: Array,
      required: true
    },
    colorList: {
      type: Array,
      required: false,
      default: () => []
    },
    showLegend: {
      type: Boolean,
      required: false,
      default: true
    },
    grid: {
      type: Object,
      required: false,
      default: () => ({
        left: 10,
        right: 10,
        bottom: 20,
        top: 50,
        containLabel: true
      })
    },
    dataType: {
      type: String,
      default: 'normal'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart(this.dataset)
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions(chartData = []) {
      const option = {
        xAxis: {
          boundaryGap: false,
          type: 'category',
          axisTick: {
            show: false
          },
          axisLabel: {
            lineHeight: 40
          }
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: false
          }
        },
        grid: this.grid,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        legend: {
          data: this.legend,
          top: 10,
          show: this.showLegend
        },
        series: []
      }
      if (this.dataType === 'dataset') {
        option.dataset = {
          source: chartData
        }
      } else {
        option.xAxis.data = this.chartData.x
      }

      option.series = this.legend.map((v, i) => {
        return {
          name: v,
          itemStyle: {
            normal: {
              color: this.colorList[i],
              lineStyle: {
                color: this.colorList[i],
                width: 2
              }
            }
          },
          smooth: true,
          type: 'line',
          animationDuration: 2800,
          animationEasing: 'cubicInOut',
          data: this.chartData.y
        }
      })

      try {
        this.chart.setOption(option)
      } catch (e) {
        console.log(e)
      }
    }
  }
}
</script>
