<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <!--<span>开通体验产品</span>-->
        <el-button style="float: right; padding: 3px 0" type="text" :disabled="isDisabled" @click="openProduct">申请开通</el-button>
      </div>
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="110px" class="demo-ruleForm">
        <el-form-item label="选择用户：" prop="customer_id">
          <el-autocomplete
            v-model="customer_name"
            style="width: 400px"
            class="inline-input"
            :fetch-suggestions="querySearch"
            placeholder="请输入内容"
            @select="handleSelect"
          >
            <template slot-scope="{ item }">
              <div class="name">{{ item.name }}</div>
            </template>
          </el-autocomplete>
          <!--<el-select v-model="ruleForm.customer_id" placeholder="请搜索用户名" filterable clearable @change="userList">-->
          <!--<el-option v-for="customer in customers" :key="customer.id" :label="customer.name" :value="customer.id" />-->
          <!--</el-select>-->
          <span style="color: red;margin-left: 10px">(输入用户名称)</span>
        </el-form-item>
        <el-form-item label="产品选择：" prop="category_id">
          <el-select v-model="ruleForm.category_id" placeholder="请选择">
            <el-option v-for="category in categoryList" :key="category.id" :label="category.name" :value="category.id" />
          </el-select>
          <el-select v-model="ruleForm.product_id" placeholder="请选择" filterable clearable :disabled="ruleForm.customer_id===''" @click.native="getProducts(ruleForm.category_id)">
            <el-option v-for="product in productsList" :key="product.id" :label="product.name" :value="product.id" />
          </el-select>
          <el-input v-if="ruleForm.category_id===18" v-model="ruleForm.domain" style="width: 200px" placeholder="请输入域名名称" clearable />
        </el-form-item>
        <el-form-item label="开通期限:" prop="month">
          <el-select v-model="ruleForm.month" placeholder="请选择" filterable clearable @change="changeExpireAt">
            <el-option v-for="item in 365*3" :key="item" :label="item" :value="item" />
          </el-select>
          <span style="margin-left: 10px">天</span>
        </el-form-item>
        <el-form-item label="到期时间：">
          <span>{{ expire_at }}</span>
        </el-form-item>

        <el-form-item v-if="activity" label="优惠活动：">
          <template>
            <el-popover
              placement="top-start"
              :title="activity.name"
              width="300"
              trigger="hover"
            >
              <div v-html="activity.content" />
              <el-button slot="reference" style="color: red" type="text">查看活动</el-button>
            </el-popover>
          </template>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { index } from '@/api/agent/customers'
import { products, openExperience, category } from '@/api/agent/product'

export default {
  data() {
    return {
      isDisabled: false,
      customers: [],
      productsList: [],
      productAllList: [],
      categoryList: [],
      expire_at: '-',
      activity: null,
      ruleForm: { month: '-' },
      rules: {
        customer_name: [{ required: true, message: '必填', trigger: 'blur' }],
        category_id: [{ required: true, message: '必填', trigger: 'blur' }],
        product_id: [{ required: true, message: '必填', trigger: 'blur' }],
        month: [{ required: true, message: '必填', trigger: 'blur' }],
        domain: [{ required: false, message: '必填', trigger: 'blur' }]
      },
      customer_name: ''
    }
  },
  created() {
    this.getCategory()
  },
  methods: {
    // 搜索用户
    querySearch(name, cb) {
      index({ user: name }).then(response => {
        if (response.code === 200 && response.data) {
          this.customers = response.data
          cb(this.customers)
        }
      }).catch(() => {

      })
    },
    // 获取产品种类列表
    getCategory() {
      category({ all: true, apply: this.ruleForm.type }).then(response => {
        if (response.code === 200 && response.data) {
          this.categoryList = response.data
        }
      })
    },
    // 获取产品列表
    getProducts(category_id) {
      products({ all: true, category_id: category_id }).then(response => {
        if (response.code === 200 && response.data) {
          this.productsList = response.data.list
        }
      })
    },
    openProduct() {
      openExperience(this.ruleForm).then(response => {
        if (response.code === 200) {
          this.$message.success(response.message)
          this.$router.push({
            name: 'ProductListRouter'
          })
        }
      })
    },
    handleSelect(item) {
      this.customer_name = item.name
      this.ruleForm.customer_id = item.id
    },
    changeExpireAt() {
      this.expire_at = this.addDate(this.ruleForm.month)
    },
    addDate(days) {
      var d = new Date()
      d.setDate(d.getDate() + days)
      var month = d.getMonth() + 1
      var day = d.getDate()
      if (month < 10) {
        month = '0' + month
      }
      if (day < 10) {
        day = '0' + day
      }
      var val = d.getFullYear() + '/' + month + '/' + day
      return val
    }
  }
}
</script>

<style lang="scss" scoped>
  .plan{
    border: 1px solid #f4f4f4;
    list-style: none;
    li{
      span{
        display: inline-block;
        width: 80px;
      }
    }
  }
</style>
<style scoped lang="scss">
.list-wrap {
  height: 100%;
  .box-card {
    height: 100%;
    ::v-deep .el-card__header {
      padding-top: 0;
    }
    ::v-deep .el-card__body {
      padding: 0;
      margin-top: 20px;
      height: 100%;
      overflow: auto;
    }
  }
  .filter {
    border-bottom: 1px solid #E5E5E5;
  }
  .table {
    margin-top: 20px;
    min-height: 200px;
  }
}
</style>
