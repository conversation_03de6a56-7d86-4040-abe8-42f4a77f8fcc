import request from '@/utils/request'

export function fetchList(data) {
  return request({
    url: '/shop/evaluation/list',
    method: 'post',
    data: data
  })
}

export function store(data) {
  return request({
    url: '/shop/evaluation/store',
    method: 'post',
    data: data
  })
}

export function remove(data) {
  return request({
    url: '/shop/evaluation/remove',
    method: 'post',
    data: data
  })
}
