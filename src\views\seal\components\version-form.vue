<template>
  <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px">
    <el-form-item label="产品标识：" prop="package">
      <el-input v-model="ruleForm.package" placeholder="一般为应用包名" />
    </el-form-item>
    <el-form-item label="外部版本：" prop="version">
      <el-input v-model="ruleForm.version" placeholder="对外展示都产品版本号" />
    </el-form-item>
    <el-form-item label="内部版本：" prop="internal_version">
      <el-input v-model="ruleForm.internal_version" placeholder="产品内部版本号" />
    </el-form-item>
    <el-form-item label="md5：" prop="md5var">
      <el-input v-model="ruleForm.md5var" placeholder="md5" />
    </el-form-item>
    <el-form-item label="更新说明：" prop="content">
      <el-input v-model="ruleForm.content" type="textarea" :rows="6" placeholder="更新展示的概要内容" />
    </el-form-item>
    <el-form-item label="更新详情：" prop="detail">
      <Editor :content.sync="ruleForm.detail" path="version" />
    </el-form-item>
    <!-- <el-form-item label="更新平台：" prop="platform">
      <el-checkbox-group v-model="ruleForm.platform" size="small">
        <el-checkbox-button v-for="platform in platforms" :key="platform.id" :label="platform.id">{{ platform.name }}</el-checkbox-button>
      </el-checkbox-group>
    </el-form-item>
    <el-form-item label="更新模式：" prop="update_model">
      <el-select v-model="ruleForm.update_model" placeholder="请选择">
        <el-option label="全量更新" :value="1" />
        <el-option label="策略更新" :value="2" />
      </el-select>
    </el-form-item> -->
    <el-form-item v-if="ruleForm.update_model === 2" label="手机尾号：" prop="phone_tail">
      <el-input v-model="ruleForm.policy.phone_tail" placeholder="多个尾号请用英文逗号分割 如： 1,3,5" />
    </el-form-item>
    <el-form-item v-if="ruleForm.update_model === 2" label="手机型号：" prop="name">
      <el-select v-model="ruleForm.policy.phone_model" multiple placeholder="请选择">
        <el-option v-for="phone in phoneOptions" :key="phone.id" :label="phone.name" :value="phone.name" />
      </el-select>
    </el-form-item>
    <el-form-item v-if="ruleForm.update_model === 2" label="手机号码：" prop="list">
      <el-input v-model="ruleForm.policy.list" type="textarea" placeholder="多个手机号码请用英文逗号分割 " />
    </el-form-item>
    <el-form-item v-if="ruleForm.update_model === 2" label="区域更新：" prop="area">
      <el-cascader
        v-model="ruleForm.selectedOptions"
        size="large"
        :options="options"
        @change="handleChange"
      />
    </el-form-item>
    <el-form-item label="强制更新：" prop="is_force">
      <el-switch
        v-model="ruleForm.is_force"
        active-color="#13ce66"
        inactive-color="#ff4949"
        :active-value="1"
        :inactive-value="0"
      />
    </el-form-item>
    <el-form-item label="发布时间：" prop="publish_at">
      <el-date-picker
        v-model="ruleForm.publish_at"
        type="datetime"
        value-format="yyyy-MM-dd HH:mm:ss"
        placeholder="选择日期时间"
      />
    </el-form-item>
    <el-form-item label="状态：" prop="status">
      <el-select v-model="ruleForm.status" placeholder="请选择">
        <el-option label="启用" :value="1" />
        <el-option label="禁用" :value="0" />
      </el-select>
    </el-form-item>
    <!--    <el-form-item label="上传文件：" prop="download_url">-->
    <!--      <el-upload-->
    <!--        class="upload-demo"-->
    <!--        drag-->
    <!--        :action="upload_url"-->
    <!--        :headers="headers"-->
    <!--        :on-success="handleAvatarSuccess"-->
    <!--        :before-upload="beforeAvatarUpload"-->
    <!--        :limit="1"-->
    <!--        :file-list="ruleForm.fileList"-->
    <!--      >-->
    <!--        <i class="el-icon-upload" />-->
    <!--        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>-->
    <!--        <div slot="tip" class="el-upload__tip">只能上传.apk文件</div>-->
    <!--      </el-upload>-->
    <!--    </el-form-item>-->
    <el-form-item label="下载地址：" prop="download_url">
      <el-input v-model="ruleForm.download_url" placeholder="请填写文件的下载地址" />
      <el-link :underline="false" type="danger">文件过大自动上传会失败，请手动上传至OSS/OBS后自行输入下载地址，请确保下载地址准确！！！</el-link>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" size="small" @click="saveApp">保存</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { mapGetters } from 'vuex'
import { provinceAndCityDataPlus, CodeToText } from 'element-china-area-data'
import { phoneModels } from '@/api/version'
import Editor from '../../../components/editor'
export default {
  name: 'VersionForm',
  components: { Editor },
  props: {
    type: {
      type: Boolean,
      default: () => false
    },
    form: {
      type: Object,
      default: () => {
        return {
          update_model: 1,
          is_force: 1,
          status: 1,
          platform: [2],
          policy: {
            area: []
          },
          fileList: [],
          selectedOptions: []
        }
      }
    }
  },
  data() {
    return {
      options: provinceAndCityDataPlus,
      upload_url: process.env.VUE_APP_BASE_API + '/upload',
      headers: {
        Authorization: 'Bearer ' + this.token
      },
      ruleForm: this.form,
      phoneOptions: [],
      rules: {
        package: [{ required: true, message: '请输入标识', trigger: 'blur' }],
        version: [{ required: true, message: '请输入外部版本号', trigger: 'blur' }],
        internal_version: [{ required: true, message: '请输入内部版本号', trigger: 'blur' }],
        content: [{ required: true, message: '请输入更新内容', trigger: 'blur' }],
        publish_at: [{ required: true, message: '请选择发布时间', trigger: 'blur' }],
        download_url: [{ required: true, message: '请上传资源文件', trigger: 'blur' }],
        platform: [{ required: true, message: '至少选择一个平台更新', trigger: 'blur' }]
      },
      platforms: [
        { id: 1, name: 'IOS' },
        { id: 2, name: 'Android' }
      ]
    }
  },
  computed: {
    ...mapGetters([
      'token'
    ])
  },
  watch: {
    form: {
      handler(newValue, oldValue) {
        this.ruleForm = newValue
      }
    }
  },
  created() {
    this.phoneModel()
  },
  methods: {
    phoneModel() {
      phoneModels().then(response => {
        this.phoneOptions = response.data
      })
    },
    handleChange(value) {
      console.log(value)
      const tmp = []
      value.forEach(function(item, value) {
        tmp.push(CodeToText[item])
      })
      this.ruleForm.policy.area = tmp
    },
    saveApp() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('transfer', this.ruleForm)
        } else {
          return false
        }
      })
    },

    handleAvatarSuccess(res, file) {
      this.ruleForm.download_url = res.data.url
    },
    beforeAvatarUpload(file) {
      const isApk = file.type === 'application/vnd.android.package-archive'

      if (!isApk) {
        this.$message.error('上传的文件只能是 apk 格式!')
      }
      return isApk
    }
  }
}
</script>
