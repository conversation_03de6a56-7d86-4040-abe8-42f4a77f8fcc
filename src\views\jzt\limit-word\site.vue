<template>
  <div class="list-wrap">
    <div class="filter" style="border: none">
      <el-form class="clearfix" style="width: 100%" inline>
        <div style="display: flex;align-items: center;justify-content: space-between;">
          <div>
            <el-form-item>
              <el-button icon="el-icon-refresh-right" type="primary" @click="getList">刷新</el-button>
            </el-form-item>
            <el-form-item>
              <el-button icon="el-icon-circle-plus-outline" type="primary" @click="dialogVisible = true; dialogTitle = '新增'">新增</el-button>
            </el-form-item>
          </div>
          <switch-package-type @switchPackageCallback="switchPackageCallback" />
        </div>
      </el-form>
    </div>
    <div class="table mt20">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        height="calc(100% - 96px)"
      >
        <el-table-column
          prop="id"
          label="ID"
        />
        <el-table-column
          prop="site_name"
          label="站点名称"
        />
        <el-table-column
          prop="create_time"
          label="创建时间"
          width="175"
        />
        <el-table-column label="操作" width="130" fixed="right" align="center">
          <template slot-scope="scope">
            <!-- <el-button type="text" size="mini" @click="dataEdit(scope.row)">编辑</el-button> -->
            <el-button type="text" size="mini" @click="dataDel(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>
    <el-dialog
      :title="`${dialogTitle}白名单`"
      :visible.sync="dialogVisible"
      width="500px"
    >
      <el-form ref="ruleForm" label-position="left" :model="editQuery" label-width="90px" class="demo-ruleForm">
        <el-form-item v-if="dialogVisible" label="选择站点：" prop="site_id">
          <site-select :site-id.sync="editQuery.site_id" from="limit" :is-multiple="false" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false; editQuery = {}">关 闭</el-button>
        <el-button type="primary" @click="addData">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { whiteList, whiteListAdd, whiteListEdit, whiteListDel } from '@/api/jzt/limit'
import Pagination from '@/components/Pagination'
import SiteSelect from '@/views/jzt/statistics/components/siteSelect.vue'
import SwitchPackageType from '@/views/jzt/components/SwitchPackageType.vue' // secondary package based on el-pagination

export default {
  name: 'Index',
  components: { SwitchPackageType, Pagination, SiteSelect },
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '',
      loading: false,
      listQuery: {
        page: 1,
        limit: 10
      },
      total: 0,
      tableData: [],
      editQuery: {}
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.tab-content')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      console.log(tabContentHeight, filterHeight)
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  methods: {
    switchPackageCallback() {
      this.getList()
    },
    // 获取列表
    getList() {
      this.listQuery.all = false
      this.loading = true
      whiteList(this.listQuery).then(response => {
        this.tableData = response.data.data
        this.total = response.data.total
        this.loading = false
      })
    },
    // 新增/修改
    changeData(apiUrl) {
      const that = this
      const site_id = that.editQuery.site_id
      if (!site_id) {
        that.$message({
          type: 'warning',
          message: '请选择站点'
        })
        return false
      } else {
        apiUrl(this.editQuery).then(response => {
          // console.log(response)
          that.editQuery = {}
          that.dialogVisible = false
          that.getList()
        })
      }
    },
    // 新增
    addData() {
      const dialogTitle = this.dialogTitle
      let api = whiteListAdd
      switch (dialogTitle) {
        case '新增':
          api = whiteListAdd
          break
        case '编辑':
          api = whiteListEdit
          break
      }
      this.changeData(api)
    },
    // 查看
    dataEdit(row) {
      this.dialogVisible = true
      this.dialogTitle = '编辑'
      this.editQuery = {
        id: row.id,
        site_id: row.site_id + ''
      }
    },
    // 删除
    dataDel(id) {
      const that = this
      this.$confirm('此操作将永久删除该该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        whiteListDel({ id }).then(response => {
          that.$message({
            type: 'success',
            message: '删除成功!'
          })
          that.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}
.el-icon-arrow-down {
  font-size: 12px;
}
</style>

<style scoped lang="scss">
.list-wrap {
  height: 100%;

  .box-card {
    height: 100%;
    border: none;

    ::v-deep .el-card__body {
      height: calc(100% - 59px);
      overflow: auto;
    }
  }
}
</style>
