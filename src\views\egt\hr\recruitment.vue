<script>
import Disc from './components/recruitment/Disc.vue'
import Layout from './components/Layout.vue'
import DiscResult from './components/recruitment/DiscResult.vue'
import Other from './components/recruitment/Other.vue'
import OtherResult from './components/recruitment/OtherResult.vue'

export default {
  name: 'Recruitment',
  components: { Layout },
  data() {
    return {
      activeBtn: '2',
      btns: [
        // { value: '1', label: '招聘网站列表', component: WebSite },
        { value: '2', label: 'DISC测试题', component: Disc },
        { value: '3', label: 'DISC测试结果', component: DiscResult },
        { value: '4', label: '其他测试题', component: Other },
        { value: '5', label: '其他测试结果', component: OtherResult }
      ]
    }
  }
}
</script>

<template>
  <Layout :btns="btns" :active.sync="activeBtn" />
</template>

<style scoped lang="scss">

</style>
