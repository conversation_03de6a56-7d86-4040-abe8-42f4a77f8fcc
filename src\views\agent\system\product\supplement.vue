<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <!--<span>配套产品</span>-->
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
        <el-button style="float: right; padding: 3px 0" type="text" @click="openProduct">保存</el-button>
      </div>
      <el-form ref="ruleForm" v-loading="loading" label-width="110px" class="demo-ruleForm">
        <el-button type="text" @click="addGivingProduct">添加赠送产品</el-button>

        <el-form-item v-for="(item,index) in list" :key="index" label="赠送产品选择：">
          <el-select v-model="item.category_id" placeholder="请选择" @change="getProducts(item.category_id)">
            <el-option v-for="category in categoryList" :key="category.id" :label="category.name" :value="category.id" />
          </el-select>
          <el-select v-model="item.product_id" placeholder="请选择" filterable clearable :disabled="item.customer_id===''" @click.native="getProducts(item.category_id)">
            <el-option v-for="product in productsList" :key="product.id" :label="product.name" :value="product.id" />
          </el-select>
          <el-input style="width: 140px" v-model="item.cate" placeholder="请输入数字配对">{{ item.cate }}</el-input>

          <el-button type="text" style="color: red" @click="deleteGivingProduct(item)">删除</el-button>
        </el-form-item>

      </el-form>
    </el-card>
  </div>
</template>

<script>
import { products, category, supplement, supplementUpdate } from '@/api/agent/product'
export default {
  name: 'Supplement',
  data() {
    return {
      productsList: [],
      categoryList: [],
      list: [],
      product_id: '',
      loading: false
    }
  },
  created() {
    this.product_id = this.$route.query.id
    this.getCategory()
    this.getProducts()
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      supplement({ id: this.product_id }).then(res => {
        if (res.code === 200 && res.data) {
          this.list = res.data
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 获取产品种类列表
    getCategory() {
      category({ all: true }).then(response => {
        if (response.code === 200 && response.data) {
          this.categoryList = response.data
        }
      })
    },
    // 获取产品列表
    getProducts(category_id) {
      products({ all: true, category_id: category_id }).then(response => {
        if (response.code === 200 && response.data) {
          this.productsList = response.data.list
        }
      })
    },
    openProduct() {
      supplementUpdate({ id: this.product_id, list: this.list }).then(response => {
        if (response.code === 200) {
          this.$message.success('操作成功')
          this.$router.go(-1)
        }
      })
    },
    // 添加赠送产品
    addGivingProduct() {
      this.list.push({ category_id: '', product_id: '', mark: '' })
    },
    // 删除赠送产品
    deleteGivingProduct(product) {
      console.log(product)
      for (var i = 0; i < this.list.length; i++) {
        var item = this.list[i]
        if (item.product_id === product.product_id) {
          this.list.splice(i, 1)
          break
        }
      }
    }
  }
}
</script>

<style scoped>

</style>
