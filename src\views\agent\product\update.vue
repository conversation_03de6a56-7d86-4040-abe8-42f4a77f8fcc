<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <!--<span>升级产品</span>-->
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
        <el-button style="float: right; " type="primary" :disabled="isDisabled" @click="openProduct">申请升级</el-button>
      </div>
      <el-form ref="ruleForm" v-loading="formLoading" :model="ruleForm" :rules="rules" label-width="110px" class="demo-ruleForm">
        <el-form-item label="选择用户：" prop="customer_id">
          <el-select v-model="ruleForm.customer_id" :disabled="true" placeholder="请选择" filterable clearable>
            <el-option v-for="customer in customers" :key="customer.id" :label="customer.name" :value="customer.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="产品选择：" prop="category_id">
          <el-select v-model="ruleForm.category_id" :disabled="true" placeholder="请选择">
            <el-option v-for="category in categoryList" :key="category.id" :label="category.name" :value="category.id" />
          </el-select>
          <el-select v-model="ruleForm.product_id" placeholder="请选择" filterable clearable @click.native="getProducts(ruleForm.category_id)" @change="sumMainPrice">
            <el-option v-for="product in productsList" :key="product.id" :label="product.name" :value="product.id" />
          </el-select>
          <el-input v-if="ruleForm.category_id===18" v-model="ruleForm.domain" :disabled="true" style="width: 200px" placeholder="请输入域名名称" clearable />
        </el-form-item>
        <!--<el-form-item label="开通期限:" prop="month">-->
        <!--<el-row>-->
        <!--<el-col :span="24">-->
        <!--<el-input v-if="ruleForm.month > 0" v-model="ruleForm.month" :disabled="true" style="width: 200px">-->
        <!--<template slot="append">月</template>-->
        <!--</el-input>-->
        <!--</el-col>-->
        <!--<el-col :span="24">-->
        <!--<el-radio-group v-model="ruleForm.year" size="mini" :disabled="ruleForm.customer_id===''||ruleForm.product_id===''" @change="sumDay">-->
        <!--<el-radio-button :label="1">1年</el-radio-button>-->
        <!--<el-radio-button :label="2">2年</el-radio-button>-->
        <!--<el-radio-button :label="3">3年</el-radio-button>-->
        <!--<el-radio-button :label="4">4年</el-radio-button>-->
        <!--<el-radio-button :label="5">5年</el-radio-button>-->
        <!--</el-radio-group>-->
        <!--</el-col>-->
        <!--</el-row>-->
        <!--</el-form-item>-->

        <el-form-item v-for="(item,index) in supplementList" :key="index" label="配套产品:">
          <el-select v-model="item.category_id" placeholder="请选择" :disabled="true">
            <el-option v-for="category in categoryList" :key="category.id" :label="category.name" :value="category.id" />
          </el-select>
          <el-select v-if="item.list.length > 0" v-model="item.product_id" placeholder="请选择" filterable clearable :disabled="item.list.length===1">
            <el-option v-for="product in item.list" :key="product.id" :label="product.name" :value="product.id" />
          </el-select>
          <el-input v-if="item.category_id===18" v-model="item.domain" style="width: 200px" placeholder="请输入域名名称" clearable />
        </el-form-item>

        <el-form-item label="合同：" prop="contract_id" style="width: 50%">
          <UploadImg
            :url.sync="ruleForm.contract_id"
            :size="2000"
            accept="image/png, image/jpeg"
            tip="请选择不大于2M的JPG、PNG的文件"
          />
        </el-form-item>
        <el-form-item label="扣款明细：" style="width: 700px">
          <ul class="plan">
            <li v-for="(item,index) in detailList" :key="index">
              <span>{{ item.name }}</span>
              <span>{{ item.version }}</span>
              <span>{{ item.expire_end }}</span>
              <span style="color: #0a76a4;padding: 0 10px">{{ item.first_price }}元</span>
              <span style="color: red">{{ item.up }}</span>
            </li>
          </ul>
        </el-form-item>

        <el-form-item label="扣款金额：">
          {{ price }}
          <el-link type="danger" :underline="false">{{ up }}</el-link>
        </el-form-item>
        <el-form-item label="预存款余额：">
          {{ totalPrice }}
        </el-form-item>
        <el-form-item label="扣款后余额：">
          {{ balance }}
          <el-link v-if="balance < 0" type="danger" :underline="false" @click="$router.push({ name: 'FinanceManageRouter'})">余额不足，去充值</el-link>
        </el-form-item>
        <!--<el-form-item label="到期时间：">-->
        <!--{{ expire_at }}-->
        <!--</el-form-item>-->
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { agentPrice, openProduct, products, show } from '@/api/agent/product'
import { orderDetail } from '@/api/agent/finance'
import { info } from '@/api/agent/deposit'
import UploadImg from '@/components/Upload/uploadImg'

export default {
  components: { UploadImg },
  data() {
    return {
      isDisabled: false,
      customers: [],
      productsList: [],
      productsshow: {},
      categoryList: [],
      totalPrice: 0,
      hardusable: 0,
      price: 0,
      balance: 0,
      expire_at: '-',
      up: '',
      ruleForm: { year: 1, month: 12 },
      rules: {
        customer_id: [{ required: true, message: '必填', trigger: 'blur' }],
        category_id: [{ required: true, message: '必填', trigger: 'blur' }],
        product_id: [{ required: true, message: '必填', trigger: 'blur' }],
        contract_id: [{ required: false, message: '选填', trigger: 'blur' }],
        month: [{ required: true, message: '必填', trigger: 'blur' }],
        domain: [{ required: false, message: '必填', trigger: 'blur' }]
      },
      detailList: [],
      supplementList: [],
      formLoading: false
    }
  },
  created() {
    this.getBanks()
    this.getOrderDetail()
  },
  methods: {
    getOrderDetail() {
      this.formLoading = true
      orderDetail({ id: this.$route.query.id }).then(response => {
        this.formLoading = false

        this.ruleForm['customer_id'] = response.data.customer.id
        this.ruleForm['product_id'] = response.data.product.id
        this.ruleForm['category_id'] = response.data.category.id
        this.ruleForm['domain'] = response.data.domain
        this.ruleForm['other_no'] = response.data.other_no
        this.customers = [response.data.customer]
        this.productsList = [response.data.product]
        this.categoryList = [response.data.category]
        this.getProductsshow(response.data.product.id)
        this.sumPrice()
      })
    },
    // 获取产品列表
    getProducts(category_id) {
      products({ all: true, category_id: category_id }).then(response => {
        this.productsList = response.data.list
      })
    },
    // 获取产品列表
    getProductsshow(id) {
      show({ id: id }).then(response => {
        this.productsshow = response.data
        this.sumPrice()
      })
    },
    getBanks() {
      info().then(response => {
        this.totalPrice = response.data.usable
        this.usable = response.data.usable
        this.balance = response.data.usable
        this.shopusable = response.data.shopusable
        this.hardusable = response.data.hardusable
      })
    },
    sumMainPrice(id) {
      var product = {}
      this.productsList.forEach(function(item) {
        if (item.id === id) {
          product = item
        }
      })
      var array = []
      var isrest = false
      product.list.forEach(function(item) {
        array.forEach(function(it) {
          if (it.category_id === item.category_id) {
            it.list.push({ id: item.id, name: item.name })
            isrest = true
          }
        })
        if (isrest === false) {
          array.push({ category_id: item.category_id, product_id: item.id, list: [{ id: item.id, name: item.name }] })
        }
      })
      this.supplementList = array
      console.log(array)
      this.sumPrice()
    },
    sumPrice() {
      if (this.ruleForm.customer_id && this.ruleForm.product_id) {
        agentPrice({ product_id: this.ruleForm.product_id, customer_id: this.ruleForm.customer_id, month: this.ruleForm.month, domain: this.ruleForm.domain, other_no: this.ruleForm.other_no }).then(response => {
          this.price = response.data.first_price
          this.detailList = response.data.list
          if ((this.productsshow.category_id === 19 && this.productsshow.app_id === '12') || (this.productsshow.category_id === 19 && this.productsshow.app_id === '13') || (this.productsshow.category_id === 19 && this.productsshow.app_id === '11') || (this.productsshow.category_id === 19 && this.productsshow.app_id === '27') || (this.productsshow.category_id === 19 && this.productsshow.app_id === '26') || (this.productsshow.category_id === 19 && this.productsshow.app_id === '25') || (this.productsshow.category_id === 19 && this.productsshow.app_id === '30') || (this.productsshow.category_id === 19 && this.productsshow.app_id === '32') || (this.productsshow.category_id === 17) || (this.productsshow.category_id === 18) || (this.productsshow.id === 278) || (this.productsshow.id === 277) || (this.productsshow.category_id === 19 && this.productsshow.app_id === '16') || (this.productsshow.category_id === 19 && this.productsshow.app_id === '17')) {
            this.totalPrice = this.hardusable
            this.balance = parseFloat(this.totalPrice - this.price).toFixed(2)
          } else {
            this.totalPrice = this.usable
            this.balance = parseFloat(this.totalPrice - this.price).toFixed(2)
          }
          if (this.balance < 0) {
            this.isDisabled = true
          } else {
            this.isDisabled = false
          }
        })
      }
    },
    openProduct() {
      this.ruleForm['supplementProducts'] = this.supplementList
      // console.log(this.ruleForm)
      openProduct(this.ruleForm).then(response => {
        if (response.code === 200) {
          this.$message.success('申请已提交，请等待审核')
          this.$router.push({
            name: 'ProductListRouter'
          })
        }
      })
    },
    // 设置购买月份
    sumDay(value) {
      if (value > 0) {
        this.ruleForm.month = value * 12
      } else {
        this.ruleForm.month = -1
      }

      this.sumPrice()
    }
  }
}
</script>

<style lang="scss" scoped>
  .plan{
    border: 1px solid #f4f4f4;
    list-style: none;
    li{
      span{
        display: inline-block;
        width: 80px;
      }
    }
  }
</style>
