<!-- 私域电商、带货电商 -->
<template>
  <div>
    <el-table :data="tableData" style="width: 100%" height="calc(100vh - 380px)" row-key="id" highlight-current-row
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <el-table-column label="ID" prop="id" width="80" />
      <el-table-column label="类型" prop="category" width="180" />
      <el-table-column label="图标" prop="icon" width="180">
        <template slot-scope="scope">
          <el-image style="width: 40px; height: 40px" :src="scope.row.icon" fit="scale-down"></el-image>
        </template>
      </el-table-column>
      <el-table-column prop="title" label="名称" />
      <el-table-column prop="url" label="链接" />
      <el-table-column prop="orderid" label="排序" />
      <el-table-column label="操作" width="180">
        <template slot="header" slot-scope="scope">
          <el-button type="primary" size="small" @click="addDialog">新增平台</el-button>
        </template>
        <template slot-scope="scope">
          <el-link :underline="false" type="primary" @click="editDialog(scope.row)">编辑</el-link>
          <el-popconfirm title="确定删除当前记录吗？" @onConfirm="deleteHandle(scope.row)">
            <el-link slot="reference" :underline="false" type="danger">删除</el-link>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <pagination style="margin-top: 0;" v-show="total > 0" :total="total" :page.sync="listQuery.page"
      :limit.sync="listQuery.limit" @pagination="getList" />
    <!-- 新增 修改 -->
    <edit-dialog ref="editDialogRef" @getList="getList" :editApi="editApi" from="private" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import EditDialog from './EditDialog'
import { privateListApi, privateSaveApi, privateDeleteApi } from '@/api/zhy_shop'

export default {
  components: { Pagination, EditDialog },
  data() {
    return {
      listQuery: {
        page: 1,
        limit: 10
      },
      total: 0,
      tableData: [],
      editApi: privateSaveApi
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    // 获取传统电商列表
    async getList() {
      const that = this
      const result = await privateListApi(that.listQuery)
      const { code, data } = result
      if (code == 200) {
        const { total, data: list } = data
        that.total = total
        that.tableData = list
      }
    },
    // 添加
    addDialog() {
      this.$refs.editDialogRef.openDialog(null)
    },
    // 编辑
    editDialog(row) {
      this.$refs.editDialogRef.openDialog(row)
    },
    // 删除电商平台
    async deleteHandle(row) {
      const that = this
      const result = await privateDeleteApi(row.id)
      const { code, data } = result
      if (code == 200) {
        that.$message.success('删除成功')
        that.getList()
      }
    }
  }
}
</script>
<style lang='scss' scoped></style>
