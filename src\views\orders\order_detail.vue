<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <!--<span>订单详情</span>-->
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.back(-1)">返回</el-button>
      </div>
      <el-form ref="form" :model="order" label-width="120px">
        <el-form-item label="订单名称：">
          {{ order.title }}
        </el-form-item>
        <el-form-item label="订单描述：">
          {{ order.intro }}
        </el-form-item>
        <el-form-item label="订单类型：">
          {{ order.type }}
        </el-form-item>
        <el-form-item label="订单编号：">
          {{ order.no }}
        </el-form-item>
        <el-form-item label="下单时间：">
          {{ order.created_at }}
        </el-form-item>
        <el-form-item label="购买时长：">
          {{ order.number_month }}
        </el-form-item>
        <el-form-item label="产品单价：">
          {{ order.pirce }}
        </el-form-item>
        <el-form-item label="产品总价：">
          {{ order.total_amount }}
        </el-form-item>
        <el-form-item label="优惠金额：">
          {{ order.coupon_amount }}
        </el-form-item>
        <el-form-item label="其他优惠：">
          {{ order.other_preferential }}
        </el-form-item>
        <el-form-item label="优惠总额：">
          {{ order.total_discount_amount }}
        </el-form-item>
        <el-form-item label="实际支付：">
          {{ order.amount }}
        </el-form-item>
        <el-form-item label="购买人：">
          {{ order.user }}
        </el-form-item>
        <el-form-item label="所属公司：">
          {{ order.company }}
        </el-form-item>
        <el-form-item label="订单状态：">
          <el-tag v-if="order.status === '已付款'" type="success" effect="dark">{{ order.status }}</el-tag>
          <el-tag v-if="order.status === '待付款'" type="warning" effect="dark">{{ order.status }}</el-tag>
          <el-tag v-if="order.status === '已取消'" type="info" effect="dark">{{ order.status }}</el-tag>
          <el-tag v-if="order.status === '已超时'" type="info" effect="dark">{{ order.status }}</el-tag>
          <el-tag v-if="order.status === '待退款'" effect="dark">{{ order.status }}</el-tag>
          <el-tag v-if="order.status === '已退款'" type="danger" effect="dark">{{ order.status }}</el-tag>
        </el-form-item>
        <el-form-item v-if="order.payment" label="支付类型：">
          {{ order.payment.pay_type }}
        </el-form-item>
        <el-form-item v-if="order.payment" label="上游编号：">
          {{ order.payment.upstream_order_no }}
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { detail } from '../../api/order'
export default {
  name: 'OrderDetail',
  data() {
    return {
      order: {}
    }
  },
  created() {
    this.order_detail()
  },
  methods: {
    order_detail() {
      detail(this.$route.query).then(response => {
        this.order = response.data
      })
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap {
  height: 100%;

  .box-card {
    border: none;
    height: 100%;

    ::v-deep .el-card__header {
      padding-top: 0;
    }

    ::v-deep .el-card__body {
      height: calc(100% - 59px);
      overflow: auto;
    }
  }
}
</style>
