import request from '@/utils/request'

// 获取考勤界面设置列表
export function getAttendanceListApi(data) {
  return request({
    url: '/template/template',
    method: 'POST',
    data
  })
}
// 添加考勤界面
export function addAttendanceApi(data) {
  return request({
    url: '/template/templateCreate',
    method: 'POST',
    data
  })
}
// 编辑考勤界面
export function editAttendanceApi(data) {
  return request({
    url: '/template/templateEdit',
    method: 'POST',
    data
  })
}
// 删除考勤界面
export function deleteAttendanceApi(data) {
  return request({
    url: '/template/templateDelete',
    method: 'POST',
    data
  })
}
