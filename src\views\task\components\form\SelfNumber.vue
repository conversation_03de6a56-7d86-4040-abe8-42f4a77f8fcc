<script>
export default {
  name: 'SelfNumber',
  props: {
    value: {
      type: [Number, String],
      default: undefined
    },
    placeholder: {
      type: String,
      default: '请输入'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    min: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: undefined
    }
  },
  computed: {
    inputValue: {
      get() {
        return +this.value
      },
      set(val) {
        this.$emit('update:value', val)
      }
    }
  },
  methods: {
    handleChange(val) {
      this.$emit('change', val)
    }
  }
}
</script>

<template>
  <el-input-number v-model="inputValue" controls-position="right" :min="min" :max="max" :placeholder="placeholder" @change="handleChange" />
</template>

<style scoped lang="scss">

</style>
