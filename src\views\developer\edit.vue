<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <!-- <span>{{ title }}</span> -->
        <el-button style="float: right; padding: 3px 0" type="text" @click="back">返回</el-button>
      </div>
      <el-form ref="ruleForm" :model="formData" :rules="rules" label-width="140px" class="demo-ruleForm">
        <el-row>
          <el-col :span="18">
            <el-form-item label="企业名称" prop="name" label-width="140px">
              {{ formData.name }}
            </el-form-item>
            <el-form-item label="企业账号" prop="phone" label-width="140px">
              {{ formData.phone }}
            </el-form-item>
            <el-form-item label="认证状态:" prop="news_category_id">
              <el-select v-model="formData.status" placeholder="请选择">
                <el-option label="通过" :value="2" />
                <el-option label="驳回" :value="3" />
              </el-select>
            </el-form-item>

            <el-form-item label="申请时间:" prop="created_at">
              {{ formData.created_at }}
            </el-form-item>
            <el-form-item label="操作时间:" prop="updated_at">
              {{ formData.created_at }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="content">
              <Editor :content.sync="formData.content" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="commit">确认</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import Editor from '../../components/editor'
import { store, detail } from '../../api/developer'
export default {
  name: 'Edit',
  components: { Editor },
  data() {
    return {
      headers: {
        Authorization: 'Bearer ' + this.token
      },
      title: '创建新闻',
      id: null,
      apps: [],
      formData: {
        cover: ''
      },
      rules: {
        status: [
          { required: true, message: '请选择认证状态', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.id = this.$route.query.id
    if (this.id) {
      this.title = '认证详情'
      this.getDetail()
    }
  },
  methods: {
    getDetail() {
      detail({ id: this.id, ...this.query }).then(response => {
        console.log(response.data)
        this.dialogVisible = false
        this.formData = response.data
      }).catch(error => {
        console.log(error)
        this.dialogVisible = false
      })
    },
    commit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          store(this.formData).then(response => {
            this.$message.success('操作成功')
            this.back()
          })
        }
      })
    },
    back() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
  .list-wrap {
    height: 100%;

    .box-card {
      border: none;
      height: 100%;

      ::v-deep .el-card__body {
        height: calc(100% - 59px);
        overflow: auto;
      }
    }
  }
</style>

