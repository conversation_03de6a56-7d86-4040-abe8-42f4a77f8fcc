<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import LibraryUpdate from '@/views/system/components/LibraryUpdate.vue'

export default {
  name: 'IndustryLibrary',
  components: { LibraryUpdate, StatisticsTemplate },
  data() {
    return {
      config: {
        key: 'library',
        filters: [
          {
            label: '关键字',
            prop: 'keyword',
            type: 'input'
          }
        ],
        tableSettings: {
          selection: true,
          data: [
            {
              id: 1,
              name: '1',
              parent_id: 1
            }
          ],
          columns: [
            {
              label: 'ID',
              prop: 'id',
              width: 60
            },
            {
              label: '名称',
              prop: 'name',
              sortable: true
            },
            {
              label: '父级id',
              prop: 'parent_id'
            },
            {
              label: '操作',
              prop: 'action',
              align: 'center',
              isSlot: true,
              width: 200,
              fixed: 'right'
            }
          ]
        }
      },
      row: {},
      updateVisible: false,
      selection: []
    }
  },
  methods: {
    handleAdd() {
      this.updateVisible = true
      this.row = {}
    },
    handleUpdate(row) {
      this.updateVisible = true
      this.row = row
    },
    handleDelete(row) {
      const data = this.config.tableSettings.data.filter(item => item.id !== row.id)
      this.$set(this.config.tableSettings, 'data', data)
    },
    handleSelectionChange(val) {
      this.selection = val
    }
  }
}
</script>

<template>
  <div class="app-container">
    <el-card>
      <div slot="header">
        行业库
      </div>
      <div class="page-warp">
        <StatisticsTemplate :config="config" @selection-change="handleSelectionChange">
          <template v-slot:topActions>
            <el-button type="primary" @click="handleAdd()">添加</el-button>
            <el-popconfirm
                title="确定删除吗？"
                style="margin-left: 10px;"
                @onConfirm="handleDelete()"
            >
              <el-button slot="reference" type="danger">删除</el-button>
            </el-popconfirm>
          </template>
          <template v-slot:library_action="{row}">
            <el-button size="mini" @click="handleUpdate(row)">编辑</el-button>
            <el-popconfirm
                title="确定删除吗？"
                style="margin-left: 10px;"
                @onConfirm="handleDelete(row)"
            >
              <el-button slot="reference" type="danger" size="mini">删除</el-button>
            </el-popconfirm>
          </template>
        </StatisticsTemplate>
        <LibraryUpdate :visible.sync="updateVisible" :data="row" />
      </div>
    </el-card>
  </div>
</template>

<style scoped lang="scss">
.app-container{
  height: 100%;
  ::v-deep .el-card{
    height: 100%;
    .el-card__body{
      height: calc(100% - 56px);
      overflow: auto;
    }
  }
}
</style>
