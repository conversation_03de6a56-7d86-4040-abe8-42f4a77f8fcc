<template>
  <el-dialog :close-on-click-modal="false" title="小程序上传" :visible.sync="dialogVisible">
    <el-form ref="ruleForm" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="链接类型">
        <el-radio v-model="form.type" :label="1">小程序</el-radio>
        <el-radio v-model="form.type" :label="2">网站</el-radio>
      </el-form-item>
      <el-form-item v-if="form.type===1" label="程序上传">
        <el-upload
          class="upload-demo"
          drag
          :action="upload_url"
          :on-success="handleFileSuccess"
          :before-upload="beforeFileUpload"
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div slot="tip" class="el-upload__tip">只能上传wgt文件，且不超过2Mb</div>
        </el-upload>
      </el-form-item>
      <el-form-item v-if="form.type===2" label="url链接">
        <el-input v-model="form.url" />
      </el-form-item>
      <el-form-item label="版本">
        <el-input v-model="form.version" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">立即创建</el-button>
        <el-button @click="dialogVisible=false">取消</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { miniAppCreate } from '../../../api/app'
export default {
  name: 'AppMiniUpload',
  props: {
    dialogVisible: {
      type: Boolean,
      default: () => false
    },
    appId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      form: { 'id': '', 'type': 1 },
      upload_url: process.env.VUE_APP_BASE_API + '/upload',
      rules: {
        url: [
          { required: true, message: '请输入链接地址', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择链接类型', trigger: 'blur' }
        ],
        version: [
          { required: true, message: '请输入版本号', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    appId: {
      handler(newValue, oldValue) {
        this.appId = newValue
      }
    }
  },
  methods: {
    handleFileSuccess(res, file) {
      this.form.url = res.data.url
    },
    beforeFileUpload(file) {
      console.log(file)
      const fileName = file.name
      const extension = fileName.substr(fileName.lastIndexOf('.') + 1)
      const isWgt = extension === 'wgt'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isWgt) {
        this.$message.error('上传小程序只能是 wgt格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
      }
      return isWgt && isLt2M
    },
    onSubmit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.form['id'] = this.appId
          miniAppCreate(this.form).then(response => {
            this.$message.success('创建成功')
            this.dialogVisible = false
            this.$emit('transfer')
          })
        } else {
          return false
        }
      })
    }
  }
}

</script>

<style scoped>

</style>
