<template>
  <div class="integral app-container" style="height: 100%;padding-top: 0;">
    <div style="display: flex; align-items: center; justify-content: space-between;margin-bottom: 20px">
      <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
      <el-button style="padding: 3px 0" type="text" @click="handelCreate">新增规则</el-button>
    </div>
    <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        :default-sort="{ prop: 'created_at', order: 'descending' }"
        height="calc(100% - 96px - 22px - 20px)"
    >
      <el-table-column label="规则ID" prop="id" width="80" />
      <el-table-column label="规则类型" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.type === 1" size="small" type="success" effect="dark">积分</el-tag>
          <el-tag v-if="scope.row.type === 2" size="small" type="danger" effect="dark">福利币</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="规则任务类型" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.tztype === 1" size="small" effect="dark">充值</el-tag>
          <el-tag v-if="scope.row.tztype === 2" size="small" effect="dark">下单</el-tag>
          <el-tag v-if="scope.row.tztype === 3" size="small" effect="dark">签到</el-tag>
          <el-tag v-if="scope.row.tztype === 4" size="small" effect="dark">分享</el-tag>
          <el-tag v-if="scope.row.tztype === 5" size="small" effect="dark">认证</el-tag>
          <el-tag v-if="scope.row.tztype === 6" size="small" effect="dark">分享云名片</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="名称" prop="name" />
      <el-table-column label="周期" prop="unit" />
      <el-table-column label="奖励" prop="integral" />
      <el-table-column label="日循环次数" prop="circulation" />
      <el-table-column label="额外奖励" prop="extra_integral" />
      <el-table-column label="状态">
        <template slot-scope="scope">
          <el-switch
              v-model="scope.row.status"
              active-color="#13ce66"
              inactive-color="#ff4949"
              @change="handelSwitch(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="created_at" />
      <el-table-column label="更新时间" prop="updated_at" />
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="handelEdit(scope.row)">编辑</el-button>
          <el-divider direction="vertical" />
          <el-popconfirm title="确定删除吗并且无法恢复？" @onConfirm="destroy(scope.row)">
            <el-button slot="reference" type="text">删除</el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="query.page"
        :limit.sync="query.limit"
        @pagination="getList"
    />

    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form" :rules="rules">
        <el-form-item label="名称" :label-width="formLabelWidth" prop="name">
          <el-input v-model="form.name" autocomplete="off" style="width: 400px" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="类型" :label-width="formLabelWidth" prop="type">
          <el-select v-model="form.type" placeholder="请选择">
            <el-option label="积分" :value="1" />
            <el-option label="福利币" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="任务类型" :label-width="formLabelWidth" prop="tztype">
          <el-select v-model="form.tztype" placeholder="请选择">
            <el-option label="充值" :value="1" />
            <el-option label="下单" :value="2" />
            <el-option label="签到" :value="3" />
            <el-option label="分享" :value="4" />
            <el-option label="认证" :value="5" />
            <el-option label="分享云名片" :value="6" />
          </el-select>
        </el-form-item>
        <el-form-item label="图标" prop="att_id" :label-width="formLabelWidth">
          <el-upload
            class="upload-demo"
            name="files[]"
            :action="upload_url"
            :file-list="fileList"
            :limit="1"
            :on-success="handleIconSuccess"
            :on-remove="handleIconRemove"
            :before-upload="beforeIconUpload"
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过2MB</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="周期" :label-width="formLabelWidth">
          <el-select v-model="form.unit" placeholder="请选择规则类型" @change="handelUnit">
            <el-option label="年" value="year" />
            <el-option label="季" value="quarter" />
            <el-option label="月" value="month" />
            <el-option label="周" value="week" />
            <el-option label="日" value="day" />
            <el-option label="永久" value="forever" />
          </el-select>
        </el-form-item>
        <el-form-item label="视图跳转" :label-width="formLabelWidth">
          <el-switch v-model="form.redirect.switch" active-color="#13ce66" inactive-color="#ff4949" />
        </el-form-item>

        <el-form-item v-if="form.redirect.switch" label="Android视图地址" :label-width="formLabelWidth" prop="integral">
          <el-input v-model="form.redirect.android" autocomplete="off" style="width: 400px" />
        </el-form-item>
        <el-form-item v-if="form.redirect.switch" label="IOS视图地址" :label-width="formLabelWidth" prop="integral">
          <el-input v-model="form.redirect.ios" autocomplete="off" style="width: 400px" />
        </el-form-item>

        <el-form-item label="奖励" :label-width="formLabelWidth" prop="integral">
          <el-input v-model.number="form.integral" autocomplete="off" style="width: 400px" />
        </el-form-item>
        <el-form-item label="日循环次数" :label-width="formLabelWidth" prop="circulation">
          <el-input
            v-model.number="form.circulation"
            autocomplete="off"
            style="width: 400px"
            :disabled="circulation_disabled"
          />
        </el-form-item>
        <el-form-item label="额外奖励数" :label-width="formLabelWidth" prop="extra_integral">
          <el-input v-model.number="form.extra_integral" autocomplete="off" style="width: 400px" />
        </el-form-item>
        <el-form-item label="规则描述" :label-width="formLabelWidth" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="5"
            maxlength="100"
            show-word-limit
            placeholder="请输入规则描述"
            style="width: 400px"
          />
        </el-form-item>
        <el-form-item label="状态" :label-width="formLabelWidth">
          <el-switch v-model="form.status" active-color="#13ce66" inactive-color="#ff4949" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { fetchList, store, remove } from '@/api/integral'
import { mapGetters } from 'vuex'

export default {
  name: 'Personal',
  components: { Pagination },
  data() {
    return {
      total: 0,
      query: {
        page: 1,
        limit: 10,
        name: null,
        number: null,
        status: null
      },
      options: [],
      tableData: [],
      loading: true,
      form: {
        name: '',
        att_id: null,
        unit: 'day',
        description: '',
        integral: 1,
        circulation: 1,
        extra_integral: 0,
        status: true,
        redirect: {
          ios: '',
          android: '',
          switch: false
        }
      },
      rules: {
        name: [
          { required: true, message: '规则名称不能为空', trigger: 'blur' }
        ],
        description: [
          { required: false, message: '规则描述不能为空', max: 100, trigger: 'blur' }
        ],
        type: [
          { required: false, message: '规则类型不能不选', trigger: 'blur' }
        ],
        integral: [
          { required: true, message: '奖励积分数不能为空', trigger: 'blur' },
          { type: 'number', min: 0, max: 10000, message: '数值允许设置在0-10000之间', trigger: 'blur' }
        ],
        circulation: [
          { required: true, message: '循环次数不能为空', trigger: 'blur' },
          { type: 'number', min: 0, max: 100, message: '数值允许设置在0-100之间', trigger: 'blur' }
        ],
        extra_integral: [
          { required: true, message: '额外奖励积分数不能为空', trigger: 'blur' },
          { type: 'number', min: 0, max: 10000, message: '数值允许设置在0-100之间', trigger: 'blur' }
        ]
      },
      dialogFormVisible: false,
      formLabelWidth: '150px',
      title: '新增规则',
      circulation_disabled: false,
      upload_url: process.env.VUE_APP_BASE_API + '/uploadReturnId',
      fileList: []
    }
  },
  computed: {
    ...mapGetters([
      'token'
    ])
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      fetchList(this.query).then(response => {
        this.total = response.data.total
        this.tableData = response.data.data
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    handelUnit() {
      if (this.form.unit === 'forever') {
        this.form.circulation = 0
        this.circulation_disabled = true
      } else {
        if (this.form.circulation <= 0) {
          this.form.circulation = 1
        }
        this.circulation_disabled = false
      }
    },
    handelSwitch(row) {
      store(row)
    },
    handelCreate() {
      this.title = '新增规则'
      delete this.form.id
      this.dialogFormVisible = true
      this.fileList = []
    },
    handelEdit(row) {
      this.title = '编辑规则'
      this.fileList = []
      this.form = row
      this.dialogFormVisible = true
      if (this.form.icon) {
        this.form.icon.name = this.form.icon.url.split('/').pop()
        this.fileList.push(this.form.icon)
      }
    },
    beforeIconUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG && !isPNG) {
        this.$message.error('上传的图标只能是 JPG 或 PNG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传的图片大小不能超过 2MB!')
      }
      return (isJPG || isPNG) && isLt2M
    },
    handleIconSuccess(res, file) {
      if (res && res.code === 200) {
        this.form.att_id = res.data[0]
      }
    },
    handleIconRemove(file, fileList) {
      this.form.att_id = null
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          store(this.form).then(_ => {
            this.dialogFormVisible = false
            this.getList()
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    destroy(row) {
      remove({ id: row.id }).then(_ => {
        this.getList()
      })
    }
  }
}
</script>

<style type="text/css" scoped>
.card-wrap ::v-deep .el-card__body {
  height: 100%;
}
  .integral {
    height: 100%;

    ::v-deep .avatar-uploader .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    ::v-deep .avatar-uploader:hover {
      border-color: #4d80ff;
    }

    ::v-deep .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 100px;
    }

    ::v-deep .avatar {
      width: 100px;
      height: 100px;
      display: block;
    }

    .box-card {
      height: 100%;
      border: none;

      .el-card__header{
        padding-top: 0;
      }

     .el-card__body{
        height: calc(100% - 59px);
        overflow: auto;
        padding: 0;
        margin-top: 20px;
     }
    }
  }
</style>
