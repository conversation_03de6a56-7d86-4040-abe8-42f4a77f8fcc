<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>产品分类</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="createView">新增分类</el-button>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="180"
        />
        <el-table-column
          prop="name"
          label="分类名称"
          width="180"
        />
        <el-table-column label="分类状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status" type="success" size="small" effect="dark">启用</el-tag>
            <el-tag v-else type="danger" effect="dark" size="small">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-link :underline="false" type="primary" @click="editView(scope.row)">编辑</el-link>
            <el-popconfirm
              title="确定删除当前记录吗？"
              @onConfirm="destoryApp(scope.row)"
            >
              <el-link slot="reference" :underline="false" type="danger">删除</el-link>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-dialog
      title="分类管理"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-form ref="form" :model="ruleForm" :rules="rules" label-width="120px">
        <el-form-item label="分类名称：" prop="name">
          <el-input v-model="ruleForm.name" />
        </el-form-item>
        <el-form-item label="分类位置：" prop="level">
          <el-input-number v-model="ruleForm.level" :min="0" :max="999" controls-position="right" />
        </el-form-item>
        <el-form-item label="分类状态：" prop="status">
          <el-select v-model="ruleForm.status" placeholder="请选择分类状态">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="b_loading" @click="saveHandle">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import { list, store, del } from '../../api/category'
export default {
  name: 'Category',
  data() {
    return {
      b_loading: false,
      loading: false,
      dialogVisible: false,
      tableData: [],
      ruleForm: {},
      rules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择分类状态', trigger: 'change' }
        ]
      }
    }
  },

  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      list().then(response => {
        this.tableData = response.data
        this.loading = false
      })
    },
    createView() {
      this.dialogVisible = true
      this.ruleForm = {
        status: 1,
        level: 0
      }
    },
    editView(row) {
      this.dialogVisible = true
      this.ruleForm = row
    },
    saveHandle() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.b_loading = true
          store(this.ruleForm).then(response => {
            this.dialogVisible = false
            this.b_loading = false
            this.$message.success('更新成功')
            this.getList()
          })
        } else {
          return false
        }
      })
    },
    destoryApp(row) {
      del(row).then(() => {
        this.$message.success('删除成功')
        this.getList()
      })
    }
  }
}
</script>

