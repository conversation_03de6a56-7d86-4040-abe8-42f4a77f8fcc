<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import Failure from '@/views/agent/certification/Failure.vue'
import { getCertificateList } from '@/api/agent/certificate'
import Detail from '@/views/agent/certification/Detail.vue'

export default {
  name: 'Certification',
  components: { Detail, Failure, StatisticsTemplate },
  data() {
    return {
      config: {
        key: 'certification',
        filters: [
          {
            label: '认证前企业名称',
            prop: 'name',
            type: 'input'
          },
          {
            label: '手机号',
            prop: 'mobile',
            type: 'input'
          },
          {
            label: '认证状态',
            prop: 'status',
            type: 'select',
            settings: {
              options: [
                { label: '全部', value: '' },
                { label: '已驳回', value: 2 },
                { label: '待审核', value: 0 },
                { label: '已通过', value: 1 }
              ]
            }
          }
        ],
        tableSettings: {
          index: true,
          api: getCertificateList,
          field: {
            total: 'total',
            list: 'tableData'
          },
          columns: [
            {
              label: '企业名称',
              prop: 'name'
            },
            {
              label: '姓名',
              prop: 'u_name'
            },
            {
              label: '登录账号',
              prop: 'mobile'
            },
            {
              label: '邮箱',
              prop: 'linkmanemail'
            },
            {
              label: '认证状态',
              prop: 'status',
              isSlot: true,
            },
            {
              label: '创建日期',
              prop: 'create_time'
            },
            {
              label: '申请日期',
              prop: 'update_time'
            },
            {
              label: '审核人',
              prop: 'remark'
            },
            {
              label: '操作',
              prop: 'action',
              width: 200,
              fixed: 'right',
              isSlot: true,
              align: 'center'
            }
          ]
        }
      },
      showFailure: false,
      row: {},
      showDetail: false
    }
  },
  methods: {
    handleView(row) {
      this.row = row
      this.showDetail = true
    },
    handleFailure(row) {
      this.row = row
      this.showFailure = true
    }
  }
}
</script>

<template>
  <div class="page-wrap">
    <StatisticsTemplate :config="config">
      <template v-slot:certification_status="{row}">
        <el-tag v-if="row.status === 0" type="info">待审核</el-tag>
        <el-tag v-if="row.status === 1" type="success">已通过</el-tag>
        <el-tag v-if="row.status === 2" type="danger">已驳回</el-tag>
      </template>
      <template v-slot:certification_action="{row}">
        <el-button size="mini" @click="handleView(row)">查看</el-button>
        <el-button type="danger" size="mini" @click="handleFailure(row)">失效</el-button>
      </template>
    </StatisticsTemplate>
    <Failure :visible.sync="showFailure" :data="row" />
    <Detail :visible.sync="showDetail" :data="row" />
  </div>
</template>

<style scoped lang="scss">
.page-wrap {
  height: calc(100% - 20px);
}
</style>
