import request from '@/utils/request'

// 获取媒体投放列表
export function governmentListApi(data) {
  return request({
    url: '/governments/list',
    method: 'POST',
    data: data
  })
}

// 获取媒体投放列表
export function governmentCityListApi(data) {
  return request({
    url: '/governments/cityList',
    method: 'POST',
    data: data
  })
}

// 获取媒体投放添加、修改
export function governmentAddApi(data) {
  return request({
    url: '/governments/store',
    method: 'POST',
    data: data
  })
}

// 删除媒体投放
export function governmentDelApi(id) {
  return request({
    url: '/governments/destory/',
    method: 'POST',
    data: { id }
  })
}

// 获取媒体投放分类 带分页
export function governmentTypeApi(data) {
  return request({
    url: '/governmenttypes/list',
    method: 'POST',
    data: data
  })
}

// 获取媒体投放分类 不分页
export function governmentTypeAllApi(data) {
  return request({
    url: '/governmenttypes/nonePageList',
    method: 'POST',
    data: data
  })
}

// 获取媒体投放分类添加、修改
export function governmentTypeAddApi(data) {
  return request({
    url: '/governmenttypes/store',
    method: 'POST',
    data: data
  })
}

// 删除媒体投放分类
export function governmentTypeDelApi(id) {
  return request({
    url: '/governmenttypes/destory',
    method: 'POST',
    data: { id }
  })
}
