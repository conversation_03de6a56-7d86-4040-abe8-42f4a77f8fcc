import request from '@/utils/request'

export function list() {
  return request({
    url: '/activity/list',
    method: 'POST'
  })
}

export function store(data) {
  return request({
    url: '/activity/store',
    method: 'POST',
    data
  })
}

export function activity_delete(data) {
  return request({
    url: '/activity/delete',
    method: 'POST',
    data
  })
}
export function set_app(data) {
  return request({
    url: '/activity/set_app',
    method: 'POST',
    data
  })
}
export function get_app(data) {
  return request({
    url: '/activity/get_app',
    method: 'POST',
    data
  })
}

