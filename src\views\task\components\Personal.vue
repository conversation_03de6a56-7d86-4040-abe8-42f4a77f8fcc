<script>
/* eslint-disable */
import selfRequest from '@/mixins/selfRequest'
import ElEmpty from '@/components/ElEmpty';

export default {
  name: 'Personal',
  props: ['data', 'id'],
  components: {ElEmpty},
  data() {
    return {
      userInfo: {
        avatar: '',
        name: '',
        skills: [],
        money_ti: 0,
        money: 0,
        history: 0,
        history_ti: 0,
        remarks: '暂无'
      },
      skills: '',
      showDel: false,
      workSelectIndex: undefined,
      projectSelectIndex: undefined,
      workExperienceList: [],
      projectExperienceList: [],
      loading: false
    }
  },
  mixins: [selfRequest],
  watch: {
    'data.tokens': {
      handler(v){
        if(v){
          this.requestApi({
            apiName: 'getTaskUserInfo',
            data: {
              tokens: v
            },
            loadingField: 'loading',
            errorMsg: '获取失败',
            successCode: 1,
            success: ({data}) => {
              this.userInfo = data;
              this.userInfo.modPosition = this.userInfo.modPosition.map(v => ({
                grade: Number(v.grade),
                id: v.id,
                name: v.name,
                status: v.status,
                work_id: v.work_id
              }));
              this.userInfo.skills = this.userInfo.modPosition.filter(v => v.status === '审核中');
              this.userInfo.positionList = this.userInfo.modPosition.filter(v => v.status === '已通过');
              if(data.workList){
                this.workExperienceList = data.workList;
              }
              if(data.hallList){
                this.projectExperienceList = data.hallList;
              }
            }
          })
        }
      },
      immediate: true
    }
  }
}
</script>

<template>
  <el-card shadow="never" style="width: 70%; margin: 0 auto;" v-loading="loading">
    <div class="content-wrap">
      <div>
        <el-card shadow="never" style="background: #f5f8fa">
          <el-row style="display: flex; align-items: center;">
            <el-col :md="4" :sm="24">
              <div style="display: flex; flex-direction: column; align-items: center">
                <el-avatar :size="70" icon="user" :src="userInfo.avatar"/>
                <div>
                  <h3 class="name" style="font-size: 20px">{{ userInfo.name }}</h3>
                </div>
              </div>
            </el-col>
            <el-col :md="20" :sm="24">
              <div style="color: rgb(16, 16, 16); font-size: 16px; display: flex; align-items: flex-start;line-height: 2">
                <span style="white-space: nowrap">专业技能：</span>
                <div>
                  <div>
                    已通过：
                    <template v-if="userInfo.positionList && userInfo.positionList.length">
                      <span v-for="(item, index) in userInfo.positionList" :key="index" class="relative inline-block">
                        {{ item.name }}
                        <el-rate v-model="item.grade" :max="item.grade" disabled />
                        {{ index === userInfo.positionList.length - 1 ? '&nbsp;&nbsp;&nbsp;' : '、&nbsp;' }}
                      </span>
                    </template>
                    <span v-else>暂无</span>
                  </div>
                  <div style="margin-top: 10px">
                    待审核：
                    <template v-if="userInfo.skills.length">
                      <span v-for="(item, index) in userInfo.skills" :key="index" class="relative inline-block">
                        {{ item.name }}
                        <el-rate v-model="item.grade" :max="item.grade" disabled />
                        {{ index === userInfo.skills.length - 1 ? '&nbsp;&nbsp;&nbsp;' : '、&nbsp;' }}
                      </span>
                    </template>
                    <span v-else>暂无</span>
                  </div>
                </div>
              </div>
              <div style="color: rgb(16, 16, 16); font-size: 16px; margin-top: 40px">
                <span>个人简介：</span>
                <span style='font-size: 16px;' v-html='userInfo.remarks || "暂无"' />
              </div>
            </el-col>
          </el-row>
        </el-card>
        <el-card shadow="never" style="margin-top: -1px">
          <el-form>
            <el-row>
              <el-col :md="6" :sm="24">
                <el-form-item label="累计接取项目数">
                  {{ userInfo.history_jie }}
                </el-form-item>
              </el-col>
              <el-col :md="6" :sm="24">
                <el-form-item label="已完成项目数">
                  {{ userInfo.history }}
                </el-form-item>
              </el-col>
              <el-col :md="6" :sm="24">
                <el-form-item label="累计佣金">
                  ￥{{ userInfo.money }}
                </el-form-item>
              </el-col>
              <el-col :md="6" :sm="24">
                <el-form-item label="待提现佣金">
                  ￥{{ userInfo.money_ti }}
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <div class='experience work'>
          <h2>
            <span class='title'>
              <i class='iconfont icon-gongzuo' />
              工作经历
            </span>
          </h2>
          <template v-if="workExperienceList.length">
            <div class="experience-item" v-for='(item, index) in workExperienceList' :key='index'>
              <div class='time-company'>
                <div class='time'>
                  <span>{{ item.startEndTime[0] }}</span> - <span>{{ item.startEndTime[1] }}</span>
                </div>
                <div class='company'>
                  {{ item.company || '公司名称' }}
                </div>
              </div>
              <div class='position-salary'>
                <div class='position flex items-center'>
                  <span class='cursor-pointer'>
                    {{ item.position || '职位名称' }}
                  </span>
                  <span style="margin: 0 auto;">|</span>
                  <span class='cursor-pointer'>
                    {{ item.industry || '行业' }}
                  </span>
                </div>
              </div>
              <div class='desc'>
                {{ item.desc || '暂无' }}
              </div>
            </div>
          </template>
          <el-empty v-else />
        </div>
        <div class='experience project'>
          <h2>
            <span class='title'>
              <i class='iconfont icon-project-o' />
              项目经历
            </span>
          </h2>
          <template v-if="projectExperienceList.length">
            <div class="experience-item" v-for='(item, index) in projectExperienceList' :key='index'>
              <div class='time-company'>
                <div class='time'>
                  <span>{{ item.startEndTime[0] }}</span> - <span>{{ item.startEndTime[1] }}</span>
                </div>
                <div class='company'>
                  <span class='cursor-pointer'>{{ item.project || '项目名称' }}</span>
                </div>
              </div>
              <div class='position-salary'>
                <div class='position flex items-center'>
                    <span class='cursor-pointer'>
                      {{ item.position || '职位名称' }}
                    </span>
                </div>
              </div>
              <div class='desc'>
                {{ item.desc || '暂无' }}
              </div>
            </div>
          </template>
          <el-empty v-else />
        </div>
      </div>
    </div>
  </el-card>
</template>

<style scoped lang="scss">
::v-deep .el-avatar--icon {
  border: 3px solid #fff;
}

::v-deep .el-form-item{
  margin-bottom: 0;
}

.experience {
  margin-top: 30px;
  padding: 0 20px;

  h2 {
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: bold;
    color: #444;

    .title {
      display: flex;
      justify-content: center;
      align-items: center;

      i {
        font-size: 28px;
        margin-right: 10px;

        &.icon-project-o {
          font-size: 24px;
        }
      }
    }
  }

  .experience-item {
    padding: 20px 60px 20px 20px;

    &.selected {
      background: #f5f9fa;
      margin-top: 20px;
    }

    & > div {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;

      + div {
        margin-top: 20px;
        font-size: 14px;
      }
    }

    .time-company {
      font-weight: bold;
      font-size: 16px;
    }

    .btn-group {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      margin: auto;
      display: flex;
      flex-direction: column;
      justify-content: center;

      button + button {
        margin-top: -1px;
      }
    }
  }
}
::v-deep .el-rate{
  display: inline-block;
}
</style>
