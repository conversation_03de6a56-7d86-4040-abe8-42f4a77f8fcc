<template>
  <iframe
    id="iframepage"
    name="iframepage"
    frameBorder="0"
    scrolling="no"
    width="100%"
    style="height: calc(100% - 4px)"
    :src="url"
  />
</template>
<script>
import { jianzhantongIframe } from '@/api/agent/otherProduct'
export default {
  name: 'Jianzhantong',
  data() {
    return {
      url: '',
      height: 0,
      finance_id: this.$route.query.finance_id,
      id: this.$route.query.id
    }
  },
  created() {
    this.getData()
  },
  methods: {
    getData() {
      jianzhantongIframe({ id: this.id, finance_id: this.finance_id }).then(res => {
        this.url = res.data.url

      })
    }
  }
}
</script>

<style scoped>

</style>
