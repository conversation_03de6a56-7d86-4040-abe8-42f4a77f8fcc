<script>
import SelfCard from '@/views/dashboard/admin/components/SelfCard.vue'
import CountTo from 'vue-count-to'
import * as echarts from 'echarts'

export default {
  name: 'OrderStatistics',
  components: { SelfCard, CountTo },
  data() {
    return {
      totalNum: 0,
      orderData: [
        {
          name: '开通金额',
          value: 0,
          color: '#3765FF',
          show: true
        },
        {
          name: '续费金额',
          value: 0,
          color: '#7E4EF8',
          show: true
        },
        {
          name: '订单金额',
          value: 100,
          color: '#eaeaea',
          show: false
        }
      ]
    }
  },
  mounted() {
    this.initChart()
  },
  methods: {
    //   获取当前年月
    getCurrentMonth() {
      const date = new Date()
      const year = date.getFullYear()
      const month = ('' + (date.getMonth() + 1)).padStart(2, '0')
      return `${year}-${month}`
    },
    //   统计图
    initChart() {
      const chartDom = document.getElementById('order-chart')
      const myChart = echarts.init(chartDom)
      let num = 0
      const series = this.orderData.map((item, index) => {
        num = item.value
        const a = {
          type: 'bar',
          data: [, , , num],
          coordinateSystem: 'polar',
          z: 9999 - index,
          name: item.name,
          roundCap: true,
          color: item.color,
          barGap: '-100%',
          itemStyle: {
            normal: {
              shadowBlur: 10,
              shadowColor: item.color + '60'
            }
          }
        }
        return a
      })
      const option = {
        title: [
          {
            text: '订单金额',
            x: 'center',
            top: '38%',
            textStyle: {
              color: '#3C3C3C',
              fontSize: 14
            }
          },
          {
            text: this.totalNum,
            x: 'center',
            y: '50%',
            textStyle: {
              fontSize: '16',
              color: '#111111',
              fontWeight: '800'
            }
          }
        ],
        angleAxis: {
          axisLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          min: 0,
          max: 100,
          boundaryGap: ['0', '100'],
          startAngle: 90
        },
        radiusAxis: {
          type: 'category',
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          },
          data: ['a', 'b', 'c', 'd'],
          z: 10
        },
        polar: {
          radius: ['10%', '90%']
        },
        toolbox: {
          show: false
        },
        series: series,
        tooltip: this.tooltip
      }
      option && myChart.setOption(option)
    }
  }
}
</script>

<template>
  <SelfCard title="订单统计" :subtitle="getCurrentMonth()" style="height: 100%">
    <el-row class="content-wrap" :gutter="20">
      <el-col :md="12" :sm="24" class="total-wrap">
        <el-row :gutter="20" style="display: flex; align-items: stretch;">
          <el-col :md="12" :sm="24">
            <div class="total">
              <CountTo
                class="total-num"
                :start-val="0"
                :end-val="totalNum"
                :duration="1000"
              />
              <div class="total-title">本月订单总数</div>
            </div>
          </el-col>
          <el-col :md="12" :sm="24" class="order-chart-wrap">
            <div id="order-chart" style="height: 150px" />
          </el-col>
        </el-row>
      </el-col>
      <el-col :md="12" :sm="24" class="legend-wrap">
        <el-row :gutter="20" style="display: flex; align-items: stretch;">
          <template v-for="(order, index) in orderData">
            <el-col v-if="order.show" :key="index" :md="12" :sm="24" class="legend-item">
              <div class="legend-item-wrap">
                <div class="legend-item-title">
                  <span class="legend-item-dot" :style="{ backgroundColor: order.color }" />
                  {{ order.name }}
                </div>
                <div class="legend-item-num">{{ order.value }}元</div>
              </div>
            </el-col>
          </template>
        </el-row></el-col>
    </el-row>
  </SelfCard>
</template>

<style scoped lang="scss">
.content-wrap {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: -20px;
  align-items: stretch;
  .total-wrap {
    height: 100%;
    .total {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;
      .total-num {
        font-weight: 800;
        font-size: 30px;
        color: #111111;
      }
      .total-title {
        font-size: 16px;
        color: #3C3C3C;
        margin-top: 10px;
      }
    }
  }
  .legend-wrap {
    display: flex;
    align-items: center;
    ::v-deep {
      .el-row {
        width: 100%;
        &::before, &::after {
          display: none;
        }
      }
    }
    .legend-item{
      height: 100%;
      .legend-item-wrap {
        background: #F2F6F9;
        border-radius: 0px 10px 10px 10px;
        padding: 28px 22px;
        .legend-item-title {
          font-size: 16px;
          color: #3C3C3C;
          display: flex;
          align-items: center;
          .legend-item-dot {
            width: 10px;
            height: 10px;
            border-radius: 2px;
            margin-right: 10px;
          }
        }
        .legend-item-num{
          font-weight: 800;
          font-size: 18px;
          color: #111111;
          margin-top: 18px;
        }
      }
    }
  }
}
@media screen and (max-width: 1600px) {
  .content-wrap {
    margin-bottom: 0;
    .total-wrap, .legend-wrap {
      width: 100%;
    }
  }
}
</style>
