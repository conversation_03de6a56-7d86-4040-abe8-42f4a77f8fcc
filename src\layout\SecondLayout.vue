<script>
export default {
  name: 'SecondLayout',
  data() {
    return {
      brotherRoutes: [],
      notNeedCardMap: ['/report/report/task', '/report/report/user', '/report/report/attendance', '/report/report/agent', '/report/report/site']
    }
  },
  computed: {
    routes() {
      return this.$store.getters.permission_routes
    },
    //   找到当前路由的父路由
    parentRoutes() {
      const matched = this.$route.matched
      if (matched.length < 2) return null // 没有父级路由时返回空

      // 获取倒数第二个匹配项作为父路由路径
      const parentPath = matched[matched.length - 2].path

      // 递归查找路由配置
      const findParent = (routes) => {
        for (const route of routes) {
          if (route.path === parentPath) return route
          if (route.children) {
            const found = findParent(route.children)
            if (found) return found
          }
        }
        return null
      }

      return findParent(this.routes)
    },
    //   找到当前路由的索引
    activeIndex: {
      get() {
        return this.$route.path
      },
      set(val) {
        this.$router.push(val)
      }
    },
    notNeedCard() {
      return this.notNeedCardMap.includes(this.$route.path)
    }
  },
  watch: {
    parentRoutes: {
      handler(val) {
        if (val && val.children) {
          this.brotherRoutes = val.children.filter((route) => !route.hidden && !route.showInLeft)
          //   判断当前路由是否在this.brotherRoutes中
          const currentRoute = this.$route
          const currentRoutePath = currentRoute.path
          const isCurrentRouteInBrotherRoutes = this.brotherRoutes.some(route => route.path === currentRoutePath)
          //   如果不在，就追加到this.brotherRoutes中
          if (!isCurrentRouteInBrotherRoutes) {
            this.brotherRoutes.push(currentRoute)
          }
        } else {
          this.brotherRoutes = []
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleSelect(key, keyPath) {
      this.activeIndex = key
    }
  }
}
</script>

<template>
  <div class="second-layout">
    <div class="tab-wrap el-card">
      <div class="tab-inner">
        <el-menu
          :default-active="activeIndex"
          :router="true"
          class="el-menu-demo"
          mode="horizontal"
          @select="handleSelect"
        >
          <el-menu-item v-for="(nav, index) in brotherRoutes" :key="index" :index="nav.path">{{ nav.meta.title }}</el-menu-item>
        </el-menu>
      </div>
    </div>

<div class="second-layout-card" v-if="notNeedCard">
  <router-view />
</div>
    <el-card v-else class="second-layout-card" shadow="never">
      <div class="tab-content">
        <router-view />
      </div>
    </el-card>
  </div>
</template>

<style scoped lang="scss">
.second-layout {
  padding: 15px;
  height: 100%;
  background: #EBEEF5;
  .tab-wrap {
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    .tab-inner {
      padding: 0 16px;
      background: #fff;
      ::v-deep.el-menu.el-menu--horizontal {
        border: none;
        padding: 12px 0;
        &>.el-menu-item{
          border: none;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 6px;
          &.is-active{
            background: #4D80FF;
            color: #fff;
          }
        }
      }
    }
  }
  .tab-content{
    height: 100%;
    overflow: auto;
  }
  .second-layout-card{
    height: calc(100% - 58px - 15px);
    margin-top: 15px;
    border-radius: 8px;
  }
  ::v-deep .el-card {
    & > .el-card__body {
      padding: 16px;
      height: 100%;
      overflow: hidden;
      .el-menu.el-menu--horizontal{
        display: flex;
        flex-wrap: nowrap;
      }
    }
  }
}
</style>
