import request from '@/utils/requestAgent'

export function companies(data) {
  return request({
    url: 'company/list',
    method: 'post',
    data: data
  })
}

export function store(data) {
  return request({
    url: 'company/store',
    method: 'post',
    data: data
  })
}
export function update(data) {
  return request({
    url: 'company/update',
    method: 'post',
    data: data
  })
}
export function destroy(data) {
  return request({
    url: 'company/delete',
    method: 'post',
    data: data
  })
}

export function companyStatement(data) {
  return request({
    url: 'company/share/list',
    method: 'post',
    data: data
  })
}

