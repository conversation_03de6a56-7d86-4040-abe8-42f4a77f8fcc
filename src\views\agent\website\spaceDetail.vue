<template>
  <el-card class="box-card" shadow="never" style="border: none">
    <div slot="header" class="clearfix">
      <!--<span>空间详情</span>-->
      <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
      <el-button style="float: right; padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
    </div>
    <el-form ref="ruleForm" :model="formData" label-width="150px">
      <el-form-item label="服务器IP地址: ">{{ formData.bindhoust ? formData.bindhoust.name : '未知' }}({{ formData.bindhoust ? formData.bindhoust.server_ip : '未知' }})</el-form-item>
      <el-form-item label="主机型号: ">{{ formData.name }}</el-form-item>
      <el-form-item label="空间大小: ">{{ formData.package?formData.package.size:'' }}</el-form-item>
      <el-form-item label="空间地址: ">{{ formData.domain }}</el-form-item>
      <el-form-item label="FTP用户名: ">{{ formData.ftp }}</el-form-item>
      <el-form-item label="FTP密码: ">{{ formData.ftp_pass }}</el-form-item>
      <el-form-item label="数据库用户名: ">{{ formData.ftp }}</el-form-item>
      <el-form-item label="数据库密码: ">{{ formData.mysql_pass }}</el-form-item>
      <el-form-item label="开通日期: ">{{ formData.start_at }}</el-form-item>
      <el-form-item label="到期时间: ">
        {{ formData.end_at }}
        <el-button type="text" @click="renew">【续费】</el-button>
      </el-form-item>
      <el-form-item label="运行状态: ">{{ formData.name }}</el-form-item>
    </el-form>
  </el-card>
</template>

<script>
import { mapGetters } from 'vuex'
import { detail, getFinance } from '@/api/agent/host'
export default {
  data() {
    return {
      dialogFormVisible: false,
      tableLoading: false,
      formData: {}
    }
  },
  computed: {
    // 别名处理防止冲突
    ...mapGetters({
      userType: 'agentUserType'
    })
  },
  created() {
    this.getDetail()
  },
  methods: {
    getDetail() {
      this.tableLoading = true
      detail({ id: this.$route.query.id }).then(response => {
        this.tableLoading = false
        this.formData = response.data
      }).catch(() => {
        this.tableLoading = false
      })
    },
    renew() {
      this.tableLoading = true
      getFinance({ id: this.$route.query.id }).then(res => {
        this.tableLoading = false
        this.$router.push({
          name: 'ProductRenewRouter',
          query: { id: res.data.id }
        })
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>

<style scoped>
  .el-divider--horizontal{
    width: 50%;
  }
  .box-card{
    margin: 20px;
  }
</style>
