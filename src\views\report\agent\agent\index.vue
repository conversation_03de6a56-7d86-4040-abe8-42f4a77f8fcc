<template>
  <div class="container">
<!--    <el-row>-->
<!--      <el-col :xs="24" :sm="24" :lg="6">-->
<!--        <box-card :agent="agent" />-->
<!--        <panel-group :customer="customer_count" />-->
<!--      </el-col>-->
<!--      <el-col :xs="24" :sm="24" :lg="18" style="padding-left: 20px">-->
<!--        <el-card class="box-card">-->
<!--          <div slot="header" class="clearfix">-->
<!--            <span>即将到期用户</span>-->
<!--            <el-button style="float: right; padding: 0" type="text" @click="getList">刷新</el-button>-->
<!--          </div>-->
<!--          <el-table v-loading="loading" :data="table" style="width: 100%;height: 441px" stripe>-->
<!--            <el-table-column prop="name" label="用户名称" />-->
<!--            <el-table-column prop="product" label="产品名称" />-->
<!--            <el-table-column prop="expire" label="到期时间" />-->
<!--          </el-table>-->
<!--        </el-card>-->
<!--      </el-col>-->
<!--    </el-row>-->
    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="12" style="margin-bottom: 15px;">
        <div style="background: linear-gradient(to right, #0e90d2 0%, #27a9eb 100%);padding:30px;">
          <div>
            <p style="margin-top:0px;"><span style="color:#fff;font-size: 16px">账户资产</span></p>
          </div>
          <el-row :gutter="32">
            <el-col :xs="10" :sm="10" :lg="10" >
              <div>
                  <p style="margin:0px"><span style="color:#ffffff;font-size: 30px">￥{{ agent.totle }}</span></p>
                  <p style="color:#fff;">当前账户总余额</p>
              </div>
            </el-col>
            <el-col :xs="14" :sm="14" :lg="14" >
              <div style="background-color: #30a5e0">
                <el-row :gutter="32">
                  <el-col :xs="12" :sm="12" :lg="12" style="margin-bottom: 15px;">
                    <div style="padding-left: 50px;padding-top: 15px;">
                      <p style="color:#fff;margin:0px"><span style="display: inline-block;border:2px #fff solid;height: 14px;margin-right: 12px"> </span>软件账户余额</p>
                      <p style="color:#fff;margin-bottom:0px;pxpadding-left: 8px"><span>￥{{ agent.usable }}</span></p>
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="12" :lg="12" style="margin-bottom: 15px;">
                    <div style="padding-left: 20px;padding-top: 15px;">
                      <p  style="color:#fff;margin:0px"><span style="display: inline-block;border:2px #fff solid;height: 14px;margin-right: 12px"> </span>硬件账户余额</p>
                      <p style="color:#fff;margin-bottom:0px;padding-left: 8px"><span>￥{{ agent.hardusable }}</span></p>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-col>
          </el-row>
          <div>
            <p style="color:#fff;font-size: 14px;margin:0px;"><el-button type="primary" @click="funupdate()" style="color:#0e90d2;background-color: #fff;">账户充值</el-button><i class="el-icon-warning-outline" style="padding-left: 15px"></i> <span>余额不足请尽快充值</span></p>
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="12" style="margin-bottom: 15px;">
        <div style="background: linear-gradient(to right, #6172fc 0%, #9978fd 100%);padding:30px;">
          <div>
            <p style="margin-top:0px;"><span style="color:#fff;font-size: 16px">客户概况</span></p>
          </div>
          <el-row :gutter="32">
            <el-col :xs="10" :sm="10" :lg="10" >
              <div style="text-align: center;">
                  <p><span style="color:#ffffff;font-size: 30px">{{ agent.count }}</span></p>
                  <p style="color:#fff;">生效客户数</p>
              </div>
            </el-col>
            <el-col :xs="14" :sm="14" :lg="14" >
              <div style="background-color: #9d84fd">
                <el-row :gutter="32">
                  <el-col :xs="12" :sm="12" :lg="12" style="margin-bottom: 15px;">
                    <div style="padding-left: 50px;padding-top: 15px;">
                      <p style="color:#fff;"><span style="display: inline-block;border:2px #fff solid;height: 14px;margin-right: 12px"> </span>企业用户</p>
                      <p style="color:#fff;padding-left: 8px"><span>{{ agent.count }}</span></p>
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="12" :lg="12" style="margin-bottom: 15px;">
                    <div style="padding-left: 20px;padding-top: 15px;">
                      <p  style="color:#fff;"><span style="display: inline-block;border:2px #fff solid;height: 14px;margin-right: 12px"> </span>个人用户</p>
                      <p style="color:#fff;padding-left: 8px"><span>{{ agent.grcount }}</span></p>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-col>
          </el-row>
          <div style="height:5px"></div>
        </div>
      </el-col>
    </el-row>
<!--    <el-row style="margin-top: 20px">-->
<!--      <numberOfPeople />-->
<!--      <regional />-->
<!--      <div style="width: 1650px;display: flex;margin-top: 20px">-->
<!--        <productTrend  :all-data.sync="productData" :loading.sync="activeLoading" @getActive="getDrawTendency" />-->
<!--      </div>-->
<!--    </el-row>-->
    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="12" style="margin-bottom: 15px;">
        <neworder />
      </el-col>
      <el-col :xs="24" :sm="24" :lg="12" style="margin-bottom: 15px;">
        <endorder />
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="12" style="margin-bottom: 15px;">
       <agentrecharge />
      </el-col>
      <el-col :xs="24" :sm="24" :lg="12" style="margin-bottom: 15px;">
        <activecustomer />
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="12" style="margin-bottom: 15px;">
        <agentchong />
      </el-col>
      <el-col :xs="24" :sm="24" :lg="12" style="margin-bottom: 15px;">
        <agentxiaofei />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { info } from '@/api/agent/deposit'
import { agent } from '@/api/agent/dashboard'
import neworder from './components/neworder'
import endorder from './components/endorder'
import agentrecharge from './components/agentrecharge'
import agentchong from './components/agentchong'
import agentxiaofei from './components/agentxiaofei'
import activecustomer from './components/activecustomer'
import { drawTendency } from '@/api/agent/statistic'

export default {
  name: 'DashboardEditor',
  components: { agentxiaofei,agentchong,neworder,endorder,agentrecharge,activecustomer },
  data() {
    return {
      agent: {
        name: '代理商名称',
        usable: '0.00',
        frozen: '0.00'
      },
      table: [],
      customer_count: 0,
      loading: false,
      activeData: [],
      productData: {},
      activeLoading: false
    }
  },
  computed: {
    ...mapGetters([
      'name',
      'avatar',
      'roles'
    ])
  },
  created() {
    this.getInfo()
    this.getList()
  },
  methods: {
    getInfo() {
      info().then(response => {
        this.agent = response.data
      }).catch(error => {
        console.log(error)
      })
    },
    funupdate() {
      this.$router.push({
        name: 'FinanceManageRouter',
        query: {
        }
      })
    },
    getList() {
      this.loading = true
      agent().then(response => {
        this.table = response.data.customer_expire_data
        this.customer_count = response.data.customer_count
        this.loading = false
      }).catch(error => {
        console.log(error)
        this.loading = false
      })
    },
    getDrawTendency(date, visit, client) {
      this.activeLoading = true
      drawTendency({ date: date, visit: visit, client: client }).then(res => {
        this.activeLoading = false
        this.productData = res.data
        this.activeData = res.data.data
      }).catch(() => {
        this.activeLoading = false
      })
    },
    setDrawTendency(data) {
      console.log(data)
      this.activeData = data
    }
  }
}
</script>

<style lang="scss" scoped>
  .container{
    padding: 20px;
    overflow-x: auto;
    background-color: #f0f2f5;
  }
  .emptyGif {
    display: block;
    width: 45%;
    margin: 0 auto;
  }

  .dashboard-editor-container {
    background-color: rgb(240, 242, 245);
    min-height: 100vh;
    padding: 32px;
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: auto;
    .pan-info-roles {
      font-size: 12px;
      font-weight: 700;
      color: #333;
      display: block;
    }
    .info-container {
      position: relative;
      margin-left: 190px;
      height: 150px;
      line-height: 200px;
      .display_name {
        font-size: 48px;
        line-height: 48px;
        color: #212121;
        position: absolute;
        top: 25px;
      }
    }
  }
</style>
