<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div class="filter">
        <el-form ref="form" :inline="true" :model="searchForm" label-width="90px" size="small">
          <el-row>
            <el-col :span="24">
              <el-form-item label="用户名称">
                <el-input v-model="searchForm.name" placeholder="用户名称" clearable />
              </el-form-item>
              <el-form-item label="所在地">
                <el-cascader
                  v-model="searchForm.area"
                  :options="provinceAndCityDataPlus"
                  clearable
                />
              </el-form-item>
              <el-form-item label="审核状态">
                <el-select v-model="searchForm.status" placeholder="全部" clearable>
                  <el-option label="审核中" :value="0" />
                  <el-option label="已通过" :value="1" />
                  <el-option label="已拒绝" :value="2" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" size="small" icon="el-icon-search" @click="getList">搜索</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="table">
        <el-table
          :data="tableData"
          style="width: 100%"
          height="100%"
        >
          <el-table-column
            prop="customer.id"
            label="用户编号"
            width="80"
          />
          <el-table-column
            prop="customer.name"
            label="用户名称"
          />
          <el-table-column
            v-if="userType ===1 "
            prop="agent.name"
            label="原代理商"
          />
          <el-table-column
            prop="to_agent.name"
            label="转移至"
          />
          <el-table-column label="审核状态">
            <template slot-scope="scope">
              <el-link v-if="scope.row.status === 0 && userType === 1" :underline="false" type="primary" @click="dialogShow(scope.row)">审核中</el-link>
              <el-link v-if="scope.row.status === 0 && userType === 2" :underline="false" type="primary">审核中</el-link>
              <el-link v-if="scope.row.status === 1" :underline="false" type="success">已通过</el-link>
              <el-link v-if="scope.row.status === -1" :underline="false" type="danger">已拒绝</el-link>
            </template>
          </el-table-column>
          <el-table-column v-if="userType === 1" label="操作">
            <template slot-scope="scope">
              <el-link :underline="false" type="primary" @click="seeContnet(scope.row)">查看转移原因</el-link>
            </template>
          </el-table-column>
          <el-table-column v-if="userType === 2" label="审批内容">
            <template slot-scope="scope">
              <el-link :underline="false" type="primary" @click="seeAuth(scope.row)">点击查看</el-link>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="searchForm.total>0"
          :total="searchForm.total"
          :page.sync="searchForm.page"
          :limit.sync="searchForm.perPage"
          style="text-align:center;"
          @pagination="getList"
        />
      </div>
    </el-card>
    <el-dialog
      title="转移审批"
      :visible.sync="dialogFormVisible"
      top="10vh"
    >
      <el-form :model="ruleForm" label-width="120px" :rules="rules">
        <el-form-item label="审批状态">
          <el-select v-model="ruleForm.status" placeholder="选择审批状态" clearable>
            <el-option label="审核通过" :value="1" />
            <el-option label="审核拒绝" :value="-1" />
          </el-select>
        </el-form-item>
        <el-form-item label="审批理由">
          <el-input
            v-model="ruleForm.content"
            type="textarea"
            placeholder="请输入审批理由"
            maxlength="200"
            rows="6"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogConfirm">确 定</el-button>
        <el-button @click="dialogFormVisible = false">返 回</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { provinceAndCityDataPlus, CodeToText } from 'element-china-area-data'
import { mapGetters } from 'vuex'
import { distractList, distractAudit } from '@/api/agent/customers'
export default {
  components: { Pagination },
  data() {
    return {
      provinceAndCityDataPlus: provinceAndCityDataPlus,
      ruleForm: { id: '', content: '', status: '' },
      rules: {
        id: [{ required: true, message: 'id丢失', trigger: 'blur' }],
        content: [{ required: true, message: '审批理由必须填写', trigger: 'blur' }],
        status: [{ required: true, message: '审批状态必须选择', trigger: 'blur' }]
      },
      searchForm: {
        date: '',
        name: '',
        agent: '',
        status: '',
        page: 1,
        perPage: 10,
        total: 0
      },
      dialogFormVisible: false,
      imgUpload: process.env.VUE_APP_BASE_API + '/upload/image?token=' + this.$store.getters.token,
      tableData: [],
      selectData: { bank_user: '', bank_account: '', bank_name: '', price: '' }
    }
  },
  computed: {
    // 别名处理防止冲突
    ...mapGetters({
      userType: 'type'
    })
  },
  created() {
    this.getList()
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.list-wrap > .el-card > .el-card__body')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  methods: {
    getList() {
      distractList(this.searchForm).then(response => {
        this.tableData = response.data
        this.searchForm.total = response.meta.total
      })
    },
    seeContnet(item) {
      this.$confirm(item.content, '转移原因', {
        confirmButtonText: '关闭',
        confirmButtonClass: 'el-button el-button--small',
        showCancelButton: false
      }).then(() => {
      }).catch(() => {
      })
    },
    seeAuth(item) {
      this.$confirm(item.auth, '审批内容', {
        confirmButtonText: '关闭',
        confirmButtonClass: 'el-button el-button--small',
        showCancelButton: false
      }).then(() => {
      }).catch(() => {
      })
    },
    dialogShow(item) {
      this.dialogFormVisible = true
      this.ruleForm.id = item.id
    },
    dialogConfirm() {
      distractAudit(this.ruleForm).then(response => {
        if (response.code === 200) {
          this.$message.success('审批成功')
          this.dialogFormVisible = false
          this.getList()
        }
      })
    },
    // 获取城市根据城市码
    getCityByCode(code) {
      var str = ''
      code.forEach((item) => {
        if (item !== 0) {
          str += ' ' + CodeToText[item]
        }
      })
      return str
    }
  }

}
</script>

<style scoped lang="scss">
.list-wrap{
  height: 100%;
  .filter{
    border-bottom: 1px solid #e8e8e8;
  }
  .table{
    margin-top: 20px;
  }
  ::v-deep .el-card {
    height: 100%;
    .el-card__body {
      padding: 0;
    }
  }
}
@media screen and (max-width: 996px)  {
  .list-wrap{
    .filter{
      max-height: 30vh;
      overflow: auto;
    }
  }
}
</style>
