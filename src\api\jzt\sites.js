import request from '@/utils/requestJzt'

export function sites(data) {
  return request({
    url: '/zhyman/sitelst',
    method: 'post',
    data
  })
}

export function audit(data) {
  return request({
    url: '/zhyman/audit',
    method: 'post',
    data
  })
}

export function resend(data) {
  return request({
    url: '/zhyman/resend',
    method: 'post',
    data
  })
}

export function districts(data) {
  return request({
    url: '/zhyman/catelst',
    method: 'post',
    data,
    canSwitch: false
  })
}
export function dirUpdate(data) {
  return request({
    url: '/sites/dir_update',
    method: 'post',
    data
  })
}
export function setCase(data) {
  return request({
    url: '/sites/set_case',
    method: 'post',
    data
  })
}
export function sendTemplate(data) {
  return request({
    url: '/sites/send_template',
    method: 'post',
    data
  })
}

export function domains(data) {
  return request({
    url: '/zhyman/domainlst',
    method: 'post',
    data
  })
}
export function bindDomain(data) {
  return request({
    url: '/zhyman/adddomain',
    method: 'post',
    data
  })
}

export function bindDomainDelete(data) {
  return request({
    url: '/zhyman/deldomain',
    method: 'post',
    data
  })
}

// 设置主域名
export function setMainDomains(data) {
  return request({
    url: '/zhyman/setdomain',
    method: 'post',
    data
  })
}

export function setCaseClientEdit(data) {
  return request({
    url: '/zhyman/setTemplateIsEdit',
    method: 'post',
    data
  })
}

export function unsubscribe(data) {
  return request({
    url: '/zhyman/unsubscribe',
    method: 'post',
    data
  })
}

export function checkList(data) {
  return request({
    url: '/sites/check',
    method: 'post',
    data
  })
}

export function customerList(data) {
  return request({
    url: '/zhyman/customerlist',
    method: 'post',
    data
  })
}

export function assignCustomer(data) {
  return request({
    url: '/zhyman/assignCustomer',
    method: 'post',
    data
  })
}

export function editHyType(data) {
  return request({
    url: '/zhyman/editHytype',
    method: 'post',
    data
  })
}

export function bucketDel(data) {
  return request({
    url: '/zhyman/bucketDel',
    method: 'post',
    data
  })
}

export function publishLog(data) {
  return request({
    url: '/zhyman/pushlog',
    method: 'post',
    data
  })
}

// 修改ftp
export function editFtpApi(data) {
  return request({
    url: '/zhyman/ftpUpdate',
    method: 'post',
    data
  })
}

// 自备服务器修改域名
export function editDomainApi(data) {
  return request({
    url: '/sites/domain_update',
    method: 'post',
    data
  })
}

// 同步到期时间
export function syncExpirationApi(data) {
  return request({
    url: '/zhyman/unsubscribe',
    method: 'post',
    data
  })
}

// 站点订单
export function orderListApi(data) {
  return request({
    url: '/zhyman/orderlst',
    method: 'post',
    data
  })
}

// 设置素材空间
export function setBucketApi(data) {
  return request({
    url: '/zhyman/uploadset',
    method: 'post',
    data
  })
}
// 素材空间列表
export function bucketListApi(data) {
  return request({
    url: '/zhyman/uploadsetinfo',
    method: 'post',
    data
  })
}
