<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <!--<span>工单分类管理</span>-->
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
          <el-button style="float: right; padding: 3px 0" type="text" @click="createView">新增</el-button>
        </div>
      </div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        style="width: 100%"
        height="100%"
      >
        <el-table-column
          prop="id"
          label="ID"
        />
        <el-table-column label="状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status" type="success" effect="dark" size="small">启用</el-tag>
            <el-tag v-else type="danger" effect="dark" size="small">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="分类名称"
        />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-link :underline="false" type="primary" @click="editView(scope.row)">编辑</el-link>
            <el-popconfirm
              title="确定删除当前记录吗？"
              @onConfirm="deleteHandle(scope.row)"
            >
              <el-link slot="reference" :underline="false" type="danger">删除</el-link>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!--  表单  -->
    <el-dialog
      title="文案管理"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="分类名称：" prop="name">
          <el-col :span="20">
            <el-input v-model="ruleForm.name" />
          </el-col>
        </el-form-item>
        <el-form-item label="状态：" prop="delivery">
          <el-switch
            v-model="ruleForm.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="saveHandle">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { cate, cate_store, cate_delete } from '../../api/workorder'

export default {
  name: 'RefundCauses',
  data() {
    return {
      dialogVisible: false,
      loading: false,
      tableLoading: false,
      tableData: [],
      ruleForm: {},
      rules: {
        name: [
          { required: true, message: '分类名称必填', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.list()
  },
  methods: {
    list() {
      this.tableLoading = true
      cate().then(response => {
        this.tableData = response.data
        this.tableLoading = false
      })
    },
    createView() {
      this.ruleForm = {
        status: 1
      }
      this.dialogVisible = true
    },
    editView(row) {
      this.ruleForm = row
      this.dialogVisible = true
    },
    saveHandle() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.loading = true
          cate_store(this.ruleForm).then(reponse => {
            this.dialogVisible = false
            this.loading = false
            this.list()
          })
        }
      })
    },
    deleteHandle(row) {
      cate_delete(row).then(() => {
        this.$message.success('删除成功')
        this.list()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap {
  height: 100%;

  .box-card {
    border: none;
    height: 100%;

    ::v-deep .el-card__header{
      padding-top: 0;
    }

    ::v-deep .el-card__body {
      height: calc(100% - 59px);
      overflow: auto;
      padding: 0;
      margin-top: 20px;
    }
  }
}
</style>
