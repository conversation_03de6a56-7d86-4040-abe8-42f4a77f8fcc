<script>
import SelfFormTemp from '@/views/task/components/form/SelfFormTemp.vue'
import update from '@/views/task/mixins/update'
import {
  addContractClauseOptionApi,
  updateContractClauseOptionApi
} from '@/api/egt/contract'

export default {
  name: 'OptionsUpdate',
  components: { SelfFormTemp },
  mixins: [update],
  props: {
    dialogType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formData: {
        title: '',
        content: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入条目名称', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入条目选项', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    filters() {
      if (this.dialogType === 'add') {
        return [
          {
            label: '条目名称',
            prop: 'title',
            type: 'input'
          },
          {
            label: '条目选项',
            prop: 'content',
            type: 'editor'
          }
        ]
      } else {
        return [
          {
            label: '条目选项',
            prop: 'content',
            type: 'editor'
          }
        ]
      }
    }
  },
  watch: {
    data: {
      handler(val) {
        this.formData.title = val.title
        this.formData.content = val.options
      },
      immediate: true
    }
  },
  methods: {
    async submitCallback() {
      const api = this.dialogType === 'add' ? addContractClauseOptionApi : updateContractClauseOptionApi
      this.formData.id = this.data.id
      if (!api) return
      const postData = JSON.parse(JSON.stringify(this.formData))
      delete postData.title
      try {
        this.submitLoading = true
        const res = await api(postData)
        if (res.code === 200) {
          this.$emit('submit-success')
          return Promise.resolve({ success: true })
        } else {
          return Promise.reject({ success: false, msg: this.data.id ? '编辑失败' : '新增失败' })
        }
      } catch (error) {
        return Promise.reject({ success: false, msg: this.data.id ? '编辑失败' : '新增失败' })
      } finally {
        this.submitLoading = false
      }
    }
  }
}
</script>

<template>
  <el-dialog :close-on-click-modal="false" :title="title + '选项'" :visible.sync="_visible" append-to-body>
    <self-form-temp ref="formWrap" :filters="filters" :form-data.sync="formData" :rules="rules" :inline="false" label-width="100px" />
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
