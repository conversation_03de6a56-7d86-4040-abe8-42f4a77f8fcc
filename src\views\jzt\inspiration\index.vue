<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="none" style="border: none">
      <div slot="header" class="clearfix" style="text-align: right;">
        <el-button style="float: right; padding: 3px 0; margin-left: 10px;" type="text" @click="toCate()">分类管理</el-button>
        <el-button style="float: right; padding: 3px 0; margin-left: 10px;" type="text" @click="getList">刷新
        </el-button>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="dialogVisible = true; dialogTitle = '新增'; editQuery = {state: 1}"
        >新增
        </el-button>
        <!-- <el-button icon="el-icon-circle-plus-outline" type="primary" @click="dialogVisible = true; dialogTitle = '新增'; editQuery = {state: 1}">新增</el-button>
        <el-button icon="el-icon-refresh-right" type="primary" @click="getList">刷新</el-button>
        <el-button icon="el-icon-menu" type="primary" @click="toCate()">分类管理</el-button> -->
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        height="calc(100% - 96px)"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="75"
        />
        <!-- type_title -->
        <el-table-column
          prop="type_title"
          width="120"
          label="分类"
        />
        <el-table-column
          prop="icon"
          label="图标"
          width="100"
        >
          <template slot-scope="scope">
            <img v-if="scope.row.icon" :src="scope.row.icon" style="width: 40px; height: 40px;object-fit: scale-down;">
            <span v-else>未上传</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="title"
          label="名称"
        />
        <el-table-column
          prop="link"
          label="网址"
        />
        <el-table-column
          prop="state"
          label="状态"
          width="100"
        >
          <template slot-scope="scope">
            <el-tag :type="scope.row.state === 1 ? 'success' : 'danger'">{{
              scope.row.state === 1 ? '启用' : '禁用'
            }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="create_time"
          label="创建时间"
          width="175"
        />
        <el-table-column label="操作" width="100px" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="default" @click="dataEdit(scope.row)">编辑</el-button>
            <el-button type="text" size="default" @click="dataDel(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </el-card>
    <el-dialog
      :title="`${dialogTitle}网站`"
      :visible.sync="dialogVisible"
      width="40%"
    >
      <el-form
        ref="ruleForm"
        label-position="left"
        :model="editQuery"
        :rules="editRule"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="网站分类：" prop="type">
          <el-select v-model="editQuery.type" placeholder="请选择" style="width: 100%;">
            <el-option
              v-for="item in categoryList"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="网站名称：" prop="title">
          <el-input v-model="editQuery.title" />
        </el-form-item>
        <el-form-item label="网站链接：" prop="link">
          <el-input v-model="editQuery.link" />
        </el-form-item>
        <el-form-item label="网站描述：" prop="description">
          <!-- 多行文本 -->
          <el-input v-model="editQuery.description" type="textarea" :rows="3" />
          <!-- <el-input v-model="editQuery.description" /> -->
        </el-form-item>
        <el-form-item label="网站图标：" prop="icon">
          <upload-single :value.sync="editQuery.icon" />
        </el-form-item>
        <el-form-item label="状态开关" prop="state">
          <el-col :span="20">
            <el-switch
              v-model="editQuery.state"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="2"
            />
          </el-col>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false; editQuery = {}">关 闭</el-button>
        <el-button type="primary" @click="addData">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { List, Add, Edit, Delete, Category } from '@/api/jzt/inspiration'
import Pagination from '@/components/Pagination/index.vue' // secondary package based on el-pagination
import UploadSingle from '@/components/Upload/SingleImage.vue'

export default {
  name: 'Log',
  components: { Pagination, UploadSingle },
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '',
      loading: false,
      listQuery: {
        page: 1,
        limit: 10
      },
      total: 0,
      tableData: [],
      editQuery: {
        state: 1
      },
      editRule: {
        type: [
          { required: true, message: '请选择分类', trigger: 'change' }
        ],
        title: [
          { required: true, message: '请输入网站名称', trigger: 'blur' }
        ],
        link: [
          { required: true, message: '请输入网站链接', trigger: 'blur' }
        ]
      },
      categoryList: []
    }
  },
  created() {
    this.getList()
    this.getCategory()
  },
  methods: {
    toCate() {
      this.$router.push({
        name: 'jzt-inspiration-cate'
      })
    },
    // 获取分类
    getCategory() {
      Category({ all: true }).then(response => {
        this.categoryList = response.data.data
      })
    },
    // 获取列表
    getList() {
      this.listQuery.all = false
      this.loading = true
      List(this.listQuery).then(response => {
        this.tableData = response.data.data
        this.total = response.data.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 新增/修改
    changeData(apiUrl) {
      const that = this
      that.$refs.ruleForm.validate((valid) => {
        if (valid) {
          apiUrl(this.editQuery).then(response => {
            // console.log(response)
            that.editQuery = { state: 1 }
            that.dialogVisible = false
            that.getList()
          })
        }
      })
    },
    // 点击保存按钮
    addData() {
      const dialogTitle = this.dialogTitle
      let api = Add
      switch (dialogTitle) {
        case '新增':
          api = Add
          break
        case '编辑':
          api = Edit
          break
      }
      this.changeData(api)
    },
    // 编辑
    dataEdit(row) {
      this.dialogVisible = true
      this.dialogTitle = '编辑'
      this.editQuery = {
        id: row.id,
        title: row.title,
        link: row.link,
        type: row.type,
        description: row.description,
        icon: row.icon,
        state: row.state
      }
    },
    // 删除
    dataDel(id) {
      const that = this
      this.$confirm('此操作将永久删除该该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        Delete({ id }).then(response => {
          that.$message({
            type: 'success',
            message: '删除成功!'
          })
          that.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}

.el-icon-arrow-down {
  font-size: 12px;
}
</style>

<style scoped lang="scss">
.list-wrap {
  height: 100%;

  .box-card {
    height: 100%;
    border: none;

    ::v-deep .el-card__header {
      padding-top: 0;
    }

    ::v-deep .el-card__body {
      height: calc(100% - 59px);
      overflow: auto;
      padding: 0;
      margin-top: 20px;
    }
  }
}
</style>
