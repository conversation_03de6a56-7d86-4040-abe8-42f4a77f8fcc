<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <!--<span>用户管理</span>-->
        <el-button style="float: right; padding: 0" type="text" :loading="downloadLoading" @click="exportExcel">导出</el-button>
        <!--<el-button style="float: right; padding: 0; margin-right:50px;" type="text" @click="JianZhanTong()">建站通用户</el-button>-->
        <!--<el-button style="float: right; padding: 0; margin-right:50px;" type="text" @click="distractbtn()">转移用户</el-button>-->
      </div>
      <el-form ref="form" class="filter" :inline="true" :model="searchForm" label-width="90px" size="small">
        <el-row>
          <el-col :span="24">
            <el-form-item label="客户名称">
              <el-input v-model="searchForm.user" placeholder="请输入用户名称" clearable />
            </el-form-item>
            <el-form-item label="登录账号">
              <el-input v-model="searchForm.phone" placeholder="请输入登录账号" clearable />
            </el-form-item>
            <el-form-item label="域名">
              <el-input v-model="searchForm.domain" placeholder="域名" clearable />
            </el-form-item>
            <el-form-item label="所属代理商:">
              <el-input v-model="searchForm.agent" placeholder="请输入代理商名称" clearable />
            </el-form-item>
            <el-form-item>
              <el-checkbox v-model="searchForm.agent_exact" value="1">精准</el-checkbox>
            </el-form-item>
            <el-form-item label="所在地">
              <el-cascader v-model="searchForm.area" :options="provinceAndCityDataPlus" clearable />
            </el-form-item>
            <el-form-item label="审核状态">
              <el-select v-model="searchForm.status" placeholder="全部" clearable>
                <el-option label="审核中" :value="0" />
                <el-option label="已通过" :value="1" />
                <el-option label="已拒绝" :value="-1" />
                <el-option label="未认证" :value="-2" />
              </el-select>
            </el-form-item>
            <!--            <el-form-item label="用户类型">-->
            <!--              <el-select v-model="searchForm.type" placeholder="全部" clearable>-->
            <!--                <el-option label="常规" :value="1" />-->
            <!--                <el-option label="渠道" :value="2" />-->
            <!--                <el-option label="补资质" :value="3" />-->
            <!--              </el-select>-->
            <!--            </el-form-item>-->
            <el-form-item label="注册时间">
              <el-date-picker
                v-model="searchForm.date"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
            <el-form-item>
              <el-checkbox v-model="searchForm.is_buy">已购产品</el-checkbox>
            </el-form-item>
            <el-form-item label="产品名称">
              <el-select
                v-model="searchForm.product_id"
                filterable
                multiple
                collapse-tags
                clearable
                run
                placeholder="请选择"
                style="width:400px"
              >
                <el-option v-for="item in productsList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="到期时间">
              <el-date-picker
                v-model="searchForm.end_at"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" icon="el-icon-search" @click="getList">搜索</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div v-loading="loading" class="table">
        <el-table :data="tableData" style="width: 100%">
          <el-table-column prop="id" label="客户编号" width="80" />
          <el-table-column label="客户名称">
            <template slot-scope="scope">
              <el-link :underline="false" type="primary" @click="showDetail(scope.row)">{{ scope.row.name }}</el-link>
            </template>
          </el-table-column>
          <!--        <el-table-column label="用户类型">-->
          <!--          <template slot-scope="scope">-->
          <!--            <el-link v-if="userType === 1" :underline="false" type="primary" @click="showDialogType(scope.row)">{{ ['','常规','渠道','补资质'][scope.row.type] }}</el-link>-->
          <!--            <div v-if="userType === 2">{{ ['','常规','渠道','补资质'][scope.row.type] }}</div>-->
          <!--          </template>-->
          <!--        </el-table-column>-->
          <el-table-column label="所属客服">
            <template slot-scope="scope">
              <div> 账号:{{ scope.row.service['username'] }} </div>
            <!--<div style="margin-top: 2px">密码:{{ scope.row.service['password'] }}</div>-->
            </template>
          </el-table-column>
          <el-table-column prop="agent" label="所属代理商" />
          <!--        <el-table-column-->
          <!--          v-if="userType === 1"-->
          <!--          prop="org"-->
          <!--          label="所属龙采"-->
          <!--        />-->
          <el-table-column prop="address" label="地址" />
          <el-table-column prop="reg" label="注册时间" />
          <el-table-column label="已购产品">

            <template slot-scope="scope">
              <div v-if="scope.row.products.length === 0"> - </div>
              <div v-else>
                <div v-for="item in scope.row.products" :key="item.product_id" style="margin-top: 2px">{{
                  item.product.name }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="到期时间">
            <template slot-scope="scope">
              <div v-if="scope.row.products.length === 0"> - </div>
              <div v-else>
                <div v-for="item in scope.row.products" :key="item.id" style="margin-top: 2px">{{ item.expire_end }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="复审核状态">
            <template slot-scope="scope">
              <el-link
                v-if="scope.row.statusfs === 0"
                :underline="false"
                type="primary"
              >审核中</el-link>
              <el-link
                v-if="scope.row.statusfs === 1"
                :underline="false"
                type="success"
              >已通过</el-link>
              <el-link
                v-if="scope.row.statusfs === -1"
                :underline="false"
                type="danger"
              >已拒绝</el-link>
            </template>
          </el-table-column>
          <el-table-column label="审核状态">
            <template slot-scope="scope">
              <el-link
                v-if="scope.row.status === 0"
                :underline="false"
                type="primary"
              >审核中</el-link>
              <el-link
                v-if="scope.row.status === 1"
                :underline="false"
                type="success"
              >已通过</el-link>
              <el-link
                v-if="scope.row.status === -1"
                :underline="false"
                type="danger"
              >已拒绝</el-link>
              <el-link
                v-if="scope.row.status === -2"
                :underline="false"
                type="danger"
              >未认证</el-link>
            </template>
          </el-table-column>
          <el-table-column label="审核内容" property="content" />
          <el-table-column label="操作">
            <template slot-scope="scope">
              <!--<el-button type="text" @click="companyAuth(scope.row)">企业认证</el-button>-->
              <span v-if="scope.row.status !== 1">
                <el-button type="text" @click="editView(scope.row)">编辑</el-button>
              </span>
              <span>
                <el-divider v-if="scope.row.status !== 1" direction="vertical" />
                <el-button type="text" @click="companyManager(scope.row)">企业管理</el-button>
              </span>
              <!--<span v-if="scope.row.status === 1 && scope.row.jztck === true">
                <el-divider direction="vertical" />
                <el-button type="text" @click="getSiteshow(scope.row.id)">网站编辑</el-button>
              </span>-->
              <span v-if="scope.row.status === -1 || scope.row.status === -2">
                <el-divider direction="vertical" />
                <el-button style="color: red" type="text" @click="destroy(scope.row)">删除</el-button>
              </span>
              <span v-if="scope.row.status === 1 && scope.row.distract.status === 0">
                <el-divider direction="vertical" />
                <el-button
                  style="color: red"
                  type="text"
                  @click="distractForm.customer_id = scope.row.id, distractForm.name = scope.row.name, dialogDistractVisible = true"
                >转移用户</el-button>
              </span>
              <span v-if="scope.row.status === 1 && scope.row.distract.status === 1">
                <el-divider direction="vertical" />
                <el-button style="color: red" type="text" @click="setDistractCacenlView(scope.row)">转移审核中</el-button>
              </span>
              <span v-if="scope.row.status === 1 && scope.row.distract.status === 2">
                <el-divider direction="vertical" />
                <el-button style="color: red" type="text">转移至{{ scope.row.distract.to_agent }}</el-button>
              </span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="searchForm.total > 0"
          :total="searchForm.total"
          :page.sync="searchForm.page"
          :limit.sync="searchForm.perPage"
          style="text-align:center;"
          @pagination="getList"
        />
      </div>
    </el-card>
    <el-dialog :close-on-click-modal="false" title="用户转移" :visible.sync="dialogDistractVisible" top="10vh">
      <el-form :model="distractForm" label-width="120px" :rules="distract_rules">
        <el-form-item label="用户名称">
          <span>{{ distractForm.name }}</span>
        </el-form-item>
        <el-form-item label="转移至">
          <el-autocomplete
            v-model="distractForm.to_agent_name"
            style="width: 400px"
            class="inline-input"
            :fetch-suggestions="querySearch"
            placeholder="请输入代理商名称"
            @select="handleSelect"
          />
        </el-form-item>
        <el-form-item label="转移原因">
          <el-input
            v-model="distractForm.content"
            type="textarea"
            placeholder="请输入转移原因 (必填项)"
            maxlength="200"
            rows="6"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="distract">提 交</el-button>
        <el-button @click="dialogDistractVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog :close-on-click-modal="false" title="用户类型" width="335px" :visible.sync="dialogTypeVisible" top="10vh">
      <el-form :model="typeForm" :rules="typeForm_rules">
        <p>{{ typeForm.customer }}</p>
        <el-form-item label="用户类型">
          <el-select v-model="typeForm.type" placeholder="选择一项">
            <el-option label="常规" :value="1" />
            <el-option label="渠道" :value="2" />
            <el-option label="补资质" :value="3" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="changeUserType">提 交</el-button>
        <el-button @click="dialogTypeVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { provinceAndCityDataPlus, CodeToText } from 'element-china-area-data'
import { mapGetters } from 'vuex'
import { productspx } from '@/api/agent/product'
import { index, destroy, distract, distractCancel, agentNameList, updateUserType, siteshow } from '@/api/agent/customers'
import { getToken } from '@/utils/auth'
import { userListExport } from '@/api/agent/downloads'
export default {
  components: { Pagination },
  data() {
    return {
      provinceAndCityDataPlus: provinceAndCityDataPlus,
      form: {},
      rules: {
        id: [{ required: true, message: 'id丢失', trigger: 'blur' }],
        content: [{ required: true, message: '审核原因必须填写', trigger: 'blur' }],
        status: [{ required: true, message: '审核状态必须选择', trigger: 'blur' }]
      },
      distractForm: { customer_id: '', name: '', to_agent_name: '' }, // 用户转移
      distract_rules: {
        id: [{ required: true, message: 'id丢失', trigger: 'blur' }],
        content: [{ required: true, message: '转移原因必须填写', trigger: 'blur' }],
        agent: [{ required: true, message: '代理商必须选择', trigger: 'blur' }]
      },
      typeForm: { type: '', customer_id: '' }, // 用户类型
      typeForm_rules: {
        type: [{ required: true, message: 'type至少选一个', trigger: 'blur' }],
        customer_id: [{ required: true, message: '选择用户', trigger: 'blur' }]
      },
      searchForm: {
        status: '',
        page: 1,
        perPage: 10,
        total: 0,
        is_buy: false
      },
      productsList: [],
      tableData: [],
      dialogFormVisible: false,
      dialogDistractVisible: false,
      dialogTypeVisible: false,
      restaurants: [],
      form_loading: false,
      loading: false,
      downloadLoading: false
    }
  },
  computed: {
    // 别名处理防止冲突
    ...mapGetters({
      userType: 'agentUserType',
      userRoletType: 'agentRoleType',
      username: 'name'
    })
  },
  created() {
    if (this.$route.query) {
      if (parseInt(this.$route.query['status']) > -2) {
        this.searchForm.status = parseInt(this.$route.query['status'])
      }
      if (parseInt(this.$route.query['page']) > -2) {
        this.searchForm.page = parseInt(this.$route.query['page'])
      }
    }
    this.getList()
    this.getProducts()
  },
  methods: {
    getList() {
      this.$router.push({ path: this.$route.path, query: this.searchForm })
      this.loading = true
      index(this.searchForm).then(response => {
        if (response.code === 200 && response.data) {
          this.tableData = response.data.list
          this.searchForm.total = response.data.meta.total
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 获取产品列表
    getProducts() {
      productspx({ all: true }).then(response => {
        if (response.code === 200 && response.data) {
          this.productsList = response.data
        }
      })
    },
    showDetail(row) {
      this.$router.push({
        name: 'agentCustomersDetail',
        query: {
          id: row.id
        }
      })
    },
    editView(row) {
      this.$router.push({
        name: 'agentCustomersEdit',
        query: {
          id: row.id
        }
      })
    },
    destroy(row) {
      this.$confirm('是否删除该用户，不可恢复!', '删除警告', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        destroy({ id: row.id }).then(response => {
          if (response.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          }
        })
      }).catch(() => { })
    },
    distract() {
      distract(this.distractForm).then(response => {
        if (response.code === 200) {
          this.dialogDistractVisible = false
          this.$message.success('申请转移成功')
          this.getList()
        }
      })
    },

    setDistractCacenlView(row) {
      this.$confirm('是否撤销该条申请', '审核操作', {
        confirmButtonText: '撤销',
        cancelButtonText: '返回',
        confirmButtonClass: 'el-button el-button--danger el-button--small',
        cancelButtonClass: 'el-button el-button--small',
        type: 'info'
      }).then(() => {
        this.distractCancel(row.id)
      }).catch(() => {
      })
    },
    distractCancel(customer_id) {
      distractCancel({ 'customer_id': customer_id }).then(response => {
        if (response.code === 200) {
          this.dialogDistractVisible = false
          this.$message.success('申请转移成功')
          this.getList()
        }
      })
    },
    querySearch(queryString, cb) {
      agentNameList({ 'name': queryString }).then(response => {
        if (response.code === 200) {
          // 调用 callback 返回建议列表的数据
          cb(response.data)
        }
      })
    },
    handleSelect(item) {
      console.log(item)
    },
    companyManager(row) {
      this.$router.push({
        name: 'agentCustomersCompany',
        query: {
          id: row.id
        }
      })
    },
    changeUserType() {
      updateUserType(this.typeForm).then(response => {
        if (response.code === 200) {
          this.dialogTypeVisible = false
          this.$message.success('修改用户类型成功')
          this.getList()
        }
      }).catch(() => {
        this.dialogTypeVisible = false
      })
    },
    async exportExcel() {
      try {
        this.downloadLoading = true
        const params = {
          user: this.searchForm.user,
          phone: this.searchForm.phone,
          area: this.searchForm.area,
          responsible_name: this.searchForm.responsible_name,
          status: this.searchForm.status,
          agent: this.searchForm.agent,
          domain: this.searchForm.domain,
          query: this.searchForm.query,
          date: this.searchForm.date,
          is_buy: this.searchForm.is_buy,
          type: this.searchForm.type,
          agent_exact: this.searchForm.agent_exact,
          product_id: this.searchForm.product_id,
          end_at: this.searchForm.end_at
        }
        const res = await userListExport(params)
        if (res.code === 200) {
          this.$message.success('导出成功，请到下载中心下载')
        } else {
          this.$message.error(res.msg || '导出失败')
        }
      } catch (error) {
        this.$message.error(error.message || '导出失败')
      } finally {
        this.downloadLoading = false
      }
    },
  }
}
</script>

<style scoped lang="scss">
  .list-wrap {
    .box-card {
      .filter{
       border-bottom: 1px solid #e5e5e5;
      }

      .table{
        height: calc(100% - 59px - 20px);
        margin-top: 20px;
        min-height: 200px;
      }

      ::v-deep .el-card__header {
        padding-top: 0;
      }

      ::v-deep .el-card__body {
        padding: 0;
        margin-top: 20px;
        height: calc(100% - 59px - 20px);
      }
    }
  }
</style>
