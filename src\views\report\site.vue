<!-- 站点数据统计 -->
<template>
  <div class="container">
    <div class="card-container part1">
      <div class="title">数据统计</div>
      <div class="card-content">
        <div class="tab-container">
          <div v-for="(item, index) in dataList" :key="index" class="tab-item">
            <img :src="require('@/assets/site-data/' + item.icon)" alt="">
            <div class="tab-right">
              <div class="tab-title">{{ item.title }}</div>
              <div class="tab-number">{{ item.number }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="card-container">
      <div class="title">访问数据</div>
      <visit-line :data="visitInfo" :is-show-title="false" width="100%" height="380px" />
    </div>
    <div class="card-container">
      <el-form :inline="true" :model="listQuery" class="demo-form-inline" size="small">
        <el-form-item label="建站通版本：">
          <el-select v-model="listQuery.jzt" placeholder="请选择" clearable>
            <el-option label="建站通1.1" value="1.1" />
            <el-option label="建站通2.0" value="2.0" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间筛选：">
          <el-date-picker
            v-model="listQuery.times"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getPartThree">过滤</el-button>
          <!-- <el-button type="primary" :loading="downloadLoading" icon="el-icon-download" @click="exportExcel">导出</el-button> -->
        </el-form-item>
      </el-form>
      <div class="card-content">
        <div class="tab-container">
          <div v-for="(item, index) in dataList2" :key="index" class="tab-item">
            <div class="tab-right">
              <div class="tab-title">{{ item.title }}</div>
              <div class="tab-number">{{ (data2[item.key] !== null && data2[item.key] !== undefined) ? data2[item.key] : '--' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VisitLine from '@/components/ECharts/VisitLine.vue'
import { siteDataApi, siteLineDataApi, siteTotalDataApi } from '@/api/jzt/statistics'

export default {
  components: {
    VisitLine
  },
  data() {
    return {
      listQuery: {},
      dataList: [
        { title: '已上线站点总数', icon: 'icon01.png', number: '--' },
        { title: '图片数量', icon: 'icon02.png', number: '--' },
        { title: '视频数量', icon: 'icon03.png', number: '--' },
        { title: '文件数量', icon: 'icon04.png', number: '--' },
        { title: '素材库大小', icon: 'icon05.png', number: '--' }
      ],
      visitInfo: {
        // date: [1, 2, 3, 4, 5],
        // look: [129, 392, 345, 349, 100],
        // ren: [0, 200, 400, 100, 345]
      },
      pickerOptions: {
        disabledDate(time) {
          // return time.getTime() > Date.now();
        }
      },
      dataList2: [
        { title: '已上线站点总数', key: 'sitenum' },
        { title: '图片数量', key: 'imgnum' },
        { title: '视频数量', key: 'videonum' },
        { title: '文章数量', key: 'contentnum' },
        { title: '站点PV', key: 'pvnum' },
        { title: '站点UV', key: 'uvnum' }
      ],
      data2: {}
    }
  },
  mounted() {
    this.initData()
    this.getPartThree()
  },
  methods: {
    async getPartOne() {
      const that = this
      const res = await siteDataApi()
      if (res.code == 200) {
        const { sitenum, imgnum, videonum, filenum, bucketnum } = res.data
        that.dataList[0].number = sitenum
        that.dataList[1].number = imgnum
        that.dataList[2].number = videonum
        that.dataList[3].number = filenum
        that.dataList[4].number = bucketnum
      }
    },
    async getPartTwo() {
      const that = this
      const res = await siteLineDataApi()
      if (res.code == 200) {
        const { day, pv, uv } = res
        that.visitInfo = { day, pv, uv }
      }
    },
    async getPartThree() {
      const that = this
      const res = await siteTotalDataApi(that.listQuery)
      if (res.code == 200) {
        console.log(res.data)
        that.data2 = res.data
      }
    },
    // 获取数据
    initData() {
      const that = this
      that.getPartOne()
      that.getPartTwo()
    }
  }
}
</script>
<style lang='scss' scoped>
.container {
  height: 100%;
  overflow: hidden auto;
  box-sizing: border-box;
  .card-container {
    background: #FFFFFF;
    padding: 28px 30px;
    margin-bottom: 15px;
    .title {
      font-size: 22px;
      font-weight: bold;
      color: #222222;
      margin-bottom: 30px;
    }
  }
  .part1 {
    height: 270px;
  }
  .tab-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .tab-item {
      width: calc(100% / 5 - (27px * 4 / 5));
      height: 150px;
      background: #FDFDFD;
      box-shadow: 0px 3px 20px 0px rgba(175,182,191,0.2);
      display: flex;
      align-items: center;
      padding: 0 20px 0 30px;
      img {
        width: 70px;
        height: 70px;
        margin-right: 15px;
      }
      .tab-right {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 60px;
        tab-title {
          font-size: 18px;
          color: #222222;
        }
        .tab-number {
          font-size: 34px;
          font-family: Bahnschrift;
          font-weight: 600;
          color: #222222;
          line-height: 24px;
        }
      }
    }
  }
}
</style>
