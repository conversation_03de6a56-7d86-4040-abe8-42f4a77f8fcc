import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/developerDoc/list',
    method: 'POST',
    data: data
  })
}

export function store(data) {
  return request({
    url: '/developerDoc/store',
    method: 'POST',
    data: data
  })
}

export function detail(data) {
  return request({
    url: '/developerDoc/detail',
    method: 'POST',
    data: data
  })
}

export function destory(data) {
  return request({
    url: '/developerDoc/destory',
    method: 'POST',
    data: data
  })
}
