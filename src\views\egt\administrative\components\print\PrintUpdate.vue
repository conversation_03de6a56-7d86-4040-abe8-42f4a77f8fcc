<script>
import { addPrint<PERSON><PERSON>, getPrint<PERSON>dd<PERSON>pi, getPrintUpdate<PERSON>pi, updatePrintApi } from '@/api/egt/print'
import SelfFormTemp from '@/views/task/components/form/SelfFormTemp.vue'
import update from '@/views/task/mixins/update'

export default {
  name: 'PrintUpdate',
  components: {
    SelfFormTemp
  },
  mixins: [update],
  data() {
    return {
      formData: {
        id: undefined,
        title: '',
        content: '',
        document_type: '',
        document_img: [],
        document_file: []
      },
      filters: [
        {
          label: '文件名称',
          type: 'input',
          prop: 'title'
        },
        {
          label: '文件简介',
          type: 'textarea',
          prop: 'content'
        },
        {
          label: '文件类型',
          type: 'select',
          prop: 'document_type',
          settings: {
            options: [],
            props: {
              label: 'title',
              value: 'id'
            }
          }
        },
        {
          label: '文件封面',
          type: 'upload',
          prop: 'document_img',
          settings: {
            baseUrl: 'https://hrcloud.obs.cn-north-4.myhuaweicloud.com/',
            action: process.env.VUE_APP_IHR_PROXY + '/Api_Docking/imgupload',
            accept: 'image/*',
            multiple: false,
            disabled: false,
            limit: 1,
            placeholder: '请上传1个2M以内的文件封面'
          }
        },
        {
          label: '文件',
          type: 'upload',
          prop: 'document_file',
          settings: {
            baseUrl: 'https://hrcloud.obs.cn-north-4.myhuaweicloud.com/',
            action: process.env.VUE_APP_IHR_PROXY + '/Api_Docking/fileupload',
            // 只接受doc、docx格式的文件
            accept: 'application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            multiple: false,
            limit: 1,
            placeholder: '请上传1个2M以内的文件'
          }
        }
      ],
      rules: {
        title: [
          { required: true, message: '请输入文件名称', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入文件简介', trigger: 'blur' }
        ],
        document_type: [
          { required: true, message: '请选择文件类型', trigger: 'change' }
        ],
        document_img: [
          { required: true, message: '请上传文件封面', trigger: 'change' }
        ],
        document_file: [
          { required: true, message: '请上传文件', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    _visible(val) {
      if (val) {
        this.getParams()
      }
    }
  },
  methods: {
    async getParams() {
      const api = this.data.id ? getPrintUpdateApi : getPrintAddApi
      if (!api) return
      try {
        this.loading = true
        const res = await api({ id: this.data.id })
        if (res.code === 200 && res.data) {
          if (this.data.id) {
            this.filters.find(item => item.prop === 'document_type').settings.options = res.data.category1
          } else {
            this.filters.find(item => item.prop === 'document_type').settings.options = res.data.data
          }
        }
      } catch (err) {
        console.log('🚀 ~ getParams ~ err:', err)
      } finally {
        this.loading = false
      }
    },
    handlePreview({ prop }) {
      let url = ''
      if (prop === 'document_img') {
        url = this.formData.document_img[0].url
      }
      if (prop === 'document_file') {
        url = this.formData.document_file[0].url
      }
      if (!url) return
      window.open(url)
    },
    handleRemove(file, fileList, prop) {
      this.formData[prop] = fileList
    },
    handleSuccess({ response, prop }) {
      const res = response.response
      if (res.code === 200) {
        const result = [
          {
            uid: response.file.uid || Date.now(),
            url: res.url + res.filepath,
            name: response.file.name || '',
            postUrl: res.filepath
          }
        ]
        this.formData[prop] = result
      } else {
        this.$message.error(res.msg || res.message || '上传失败')
      }
    },
    async submitCallback() {
      let api = addPrintApi
      if (this.data.id) {
        api = updatePrintApi
        this.formData.id = this.data.id
      }
      if (!api) return
      const postData = JSON.parse(JSON.stringify(this.formData))
      if (this.data.id) {
        if (postData.document_img && postData.document_img.length) {
          postData.document_img = postData.document_img[0].uid
        }
        if (postData.document_file && postData.document_file.length) {
          postData.document_file = postData.document_file[0].uid
        }
      } else {
        if (postData.document_img && postData.document_img.length) {
          postData.document_img = postData.document_img[0].postUrl
        }
        if (postData.document_file && postData.document_file.length) {
          postData.document_file = postData.document_file[0].postUrl
        }
      }
      try {
        this.submitLoading = true
        const res = await api(postData)
        if (res.code === 200) {
          this.$emit('submit-success')
          return Promise.resolve({ success: true })
        } else {
          return Promise.reject({ success: false, msg: res.msg || res.message || '操作失败' })
        }
      } catch (e) {
        console.log(e)
        return Promise.reject({ success: false, msg: '操作失败' })
      } finally {
        this.submitLoading = false
      }
    }
  }
}
</script>

<template>
  <el-dialog
    :title="title + '文件'"
    :visible.sync="_visible"
    width="50%"
  >
    <SelfFormTemp
      ref="formWrap"
      :form-data.sync="formData"
      self-key="print"
      :filters="filters"
      :inline="false"
      label-width="100px"
      :rules="rules"
      @preview="handlePreview"
      @remove="handleRemove"
      @success="handleSuccess"
    />
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel()">取消</el-button>
      <el-button :loading="submitLoading" type="primary" @click="handleSubmit()">确定</el-button>
    </div>
  </el-dialog>
</template>
