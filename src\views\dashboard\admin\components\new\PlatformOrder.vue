<script>
import SelfCard from '@/views/dashboard/admin/components/SelfCard.vue'
import Platform from '@/views/agent/product/platform.vue'
import OrderList from '@/views/orders/orders.vue'

export default {
  name: 'PlatformOrder',
  components: { OrderList, SelfCard }
}
</script>

<template>
  <SelfCard class="platform-order" title="平台订单" :show-more="true" to-more="/orders/list">
    <OrderList :simple="true" style="height: 228px;overflow: hidden;" />
  </SelfCard>
</template>

<style scoped lang="scss">
.platform-order {
  height: 100%;
  ::v-deep .el-card__body{
    padding: 0;
    height: 100%;
  }
}
</style>
