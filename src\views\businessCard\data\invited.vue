<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import update from '@/views/task/mixins/update'
import { invited_list } from '@/api/businessCard/invited'
import { objToQueryString } from '@/utils'

export default {
  name: 'Invited',
  components: { StatisticsTemplate },
  mixins: [update],
  data() {
    return {
      config: {
        key: 'invited',
        filters: [
          {
            label: '姓名',
            prop: 'name',
            type: 'input'
          },
          /* {
            label: '公司',
            prop: 'company',
            type: 'input'
          } */
        ],
        tableSettings: {
          api: invited_list,
          field: {
            page: 'page',
            limit: 'perPage',
            total: 'total'
          },
          columns: [
            {
              label: 'ID',
              prop: 'id'
            },
            {
              label: '姓名',
              prop: 'name'
            },
            {
              label: '公司',
              prop: 'company',
              minWidth: 150
            },
            {
              label: '邀请点击量',
              prop: 'num'
            },
            {
              label: '邀请成功量',
              prop: 'count'
            },
            {
              label: '邀请购买个人版',
              prop: 'grcount'
            },
            {
              label: '邀请购买企业版',
              prop: 'qycount'
            },
            {
              label: '邀请购买商城版',
              prop: 'sccount'
            }
          ]
        }
      },
    }
  },
  methods: {
    handleExport() {
      window.open(process.env.VUE_APP_BASE_API + '/card/fromexportExcel?' + objToQueryString(this.$refs.tableRef.params), '_blank')
    }
  }
}
</script>

<template>
  <div class="page-wrap">
    <StatisticsTemplate ref="tableRef" :config="config">
      <template #topActions>
        <el-button type="primary" @click="handleExport">导出</el-button>
      </template>
    </StatisticsTemplate>
  </div>
</template>

<style scoped lang="scss">
.page-wrap{
  height: 100%;
}
</style>
