import request from '@/utils/requestJzt'

export function cates(data) {
  return request({
    url: '/zhyman/catelst',
    method: 'post',
    data,
    canSwitch: false
  })
}
export function cates_store(data) {
  return request({
    url: '/lib/cate/store',
    method: 'post',
    data
  })
}

export function cates_delete(data) {
  return request({
    url: '/lib/cate/delete',
    method: 'post',
    data
  })
}

export function blocks(data) {
  return request({
    url: '/lib/block/list',
    method: 'post',
    data
  })
}

export function blocks_store(data) {
  return request({
    url: '/lib/block/store',
    method: 'post',
    data
  })
}

export function blocks_delete(data) {
  return request({
    url: '/lib/block/delete',
    method: 'post',
    data
  })
}
export function templates(data) {
  return request({
    url: '/lib/list',
    method: 'post',
    data
  })
}
export function lib_uploads(data) {
  return request({
    url: '/lib/libuploads',
    method: 'post',
    data
  })
} export function lib_update(data) {
  return request({
    url: '/lib/libupdate',
    method: 'post',
    data
  })
}
export function template_update(data) {
  return request({
    url: '/lib/update',
    method: 'post',
    data
  })
}

export function template_detail(data) {
  return request({
    url: '/lib/detail',
    method: 'post',
    data
  })
}

export function editCategory(data) {
  return request({
    url: 'lib/edit_type',
    method: 'post',
    data
  })
}
