<template>
  <div class="detail">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <!--<span>用户详情</span>-->
        <el-button style="float: right; padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
      </div>
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="150px" class="demo-ruleForm">
        <el-row>
          <el-col :span="12">
            <el-divider content-position="left">用户信息</el-divider>
            <el-form-item label="LOGO：" prop="logo">
              <el-image :src="ruleForm.logo" fit="contain" style="width: 100px;height: 100px;border-radius: 50px;border: 2px solid #f4f4f4" />
            </el-form-item>

            <el-form-item label="用户类型：" prop="legal_person">
              <el-radio-group v-model="ruleForm.type" disabled>
                <el-radio :label="1">常规</el-radio>
                <el-radio :label="2">渠道</el-radio>
                <el-radio :label="3">补资质</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="用户名称：" prop="name" style="width: 50%">
              {{ ruleForm.name }}
            </el-form-item>

            <el-form-item label="所在省市：" prop="agentArea" style="width: 50%">
              {{ getCityByCode(ruleForm.province) }}
              <el-divider v-if="ruleForm.city" direction="vertical" />
              {{ getCityByCode(ruleForm.city) }}
            </el-form-item>
            <el-form-item label="详细地址：" prop="address" style="width: 50%">
              {{ ruleForm.address }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-divider content-position="left">负责人信息</el-divider>
            <el-form-item label="负责人姓名：" prop="responsible_name" style="width: 50%">
              {{ ruleForm.responsible_name }}
            </el-form-item>
            <el-form-item label="负责人电话：" prop="responsible_phone" style="width: 50%">
              {{ ruleForm.responsible_phone }}
            </el-form-item>
            <el-form-item label="负责人邮箱：" prop="responsible_email" style="width: 50%">
              {{ ruleForm.responsible_email }}
            </el-form-item>
          </el-col>
          <!--<el-col :span="12">-->
          <!--<el-divider content-position="left">合同文件</el-divider>-->
          <!--<el-form-item label="合同：" prop="contract_id" style="width: 50%">-->
          <!--<el-link type="primary" :underline="false" :href="ruleForm.contract_id" target="_blank">点击下载</el-link>-->
          <!--</el-form-item>-->
          <!--</el-col>-->
          <el-col :span="12">
            <el-divider content-position="left">登陆信息</el-divider>
            <el-form-item label="登录账号：" prop="username" style="width: 50%">
              {{ ruleForm.username }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-divider content-position="left">公司信息</el-divider>
            <!--<el-form-item>-->
            <!--<el-radio v-model="ruleForm.legal_type" disabled label="0">法人</el-radio>-->
            <!--<el-radio v-model="ruleForm.legal_type" disabled label="1">负责人</el-radio>-->
            <!--</el-form-item>-->
            <el-form-item label="规模">{{ ruleForm.scale }}</el-form-item>
            <el-form-item label="主营项目">{{ ruleForm.operating }}</el-form-item>
            <el-form-item label="姓名：" prop="legal_person" style="width: 50%">
              {{ ruleForm.legal_person }}
            </el-form-item>
            <el-form-item label="电话：" prop="legal_phone" style="width: 50%">
              {{ ruleForm.legal_phone }}
            </el-form-item>
            <el-form-item label="身份证：" prop="legal_card" style="width: 50%">
              {{ ruleForm.legal_card }}
            </el-form-item>
            <el-form-item label="身份证正面：" prop="card_a">
              <el-image :src="ruleForm.card_a" fit="fill" :preview-src-list="[ruleForm.card_a]" style="width: 190px;height: 130px" />
            </el-form-item>
            <el-form-item label="身份证反面：" prop="card_b">
              <el-image :src="ruleForm.card_b" fit="fill" :preview-src-list="[ruleForm.card_b]" style="width: 190px;height: 130px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-divider content-position="left">证照信息</el-divider>
            <el-form-item label="营业执照号：" prop="license_number" style="width: 50%">
              {{ ruleForm.license_number }}
            </el-form-item>
            <el-form-item label="经营范围：" prop="scope" style="width: 50%">
              {{ ruleForm.scope }}
            </el-form-item>
            <el-form-item label="企业类型：" prop="person">
              <el-cascader
                  v-model="ruleForm.person"
                  disabled
                  :options="persons"
                  :props="{ value:'id',label:'name' }"
              />
            </el-form-item>
            <el-form-item label="行业类型" prop="trade_first">
              <el-cascader
                  v-model="ruleForm.trade_ids"
                  disabled
                  style="width: 100%"
                  :options="trades"
                  :props="{ value: 'id',label:'name' }"
              />
            </el-form-item>
            <el-form-item label="注册日期：" prop="registered" style="width: 50%">
              {{ ruleForm.registered }}
            </el-form-item>
            <!--<el-form-item label="企业期限：" prop="deadline" style="width: 50%">-->
            <!--{{ ruleForm.deadline }}-->
            <!--</el-form-item>-->
            <el-form-item label="营业执照：" prop="license">
              <el-image :src="ruleForm.license" fit="fill" :preview-src-list="[ruleForm.license]" style="width: 220px;height: 300px" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-button v-if="userType===1" style="width: 200px" type="primary" @click="setStatusView()">审核</el-button>
      <el-dialog
          title="认证审核"
          :visible.sync="dialogFormVisible"
          width="35%"
          top="10vh"
      >
        <el-form ref="form" :model="form" label-width="120px" :rules="rules">
          <el-form-item label="审核状态" style="width: 70%">
            <el-select v-model="form.statusfs" placeholder="请选择">
              <el-option label="审核通过" :value="1" />
              <el-option label="审核驳回" :value="-1" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="form.statusfs===-1" label="审核内容" prop="content" style="width: 70%">
            <el-input
                v-model="form.content"
                type="textarea"
                placeholder="请输入内容"
                maxlength="30"
                show-word-limit
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" :loading="form_loading" @click="setStatus">确 定</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { CodeToText } from 'element-china-area-data'
import { mapGetters } from 'vuex'
import { detail, audit, options } from '@/api/agent/customers'
export default {
  data() {
    return {
      status: -1,
      dialogFormVisible: false,
      form_loading: false,
      form: {},
      rules: {
        id: [{ required: true, message: 'id丢失', trigger: 'blur' }],
        content: [{ required: true, message: '审核原因必须填写', trigger: 'blur' }],
        statusfs: [{ required: true, message: '审核状态必须选择', trigger: 'blur' }]
      },
      ruleForm: {
        phone_type: '86',
        idcard_type: '86'
      },
      trades: [],
      persons: []
    }
  },
  computed: {
    // 别名处理防止冲突
    ...mapGetters({
      userType: 'type'
    })
  },
  created() {
    this.getOptions()
    this.getDetail()
  },
  methods: {
    getDetail() {
      detail({ id: this.$route.query.id }).then(response => {
        this.statusfs = response.data.statusfs
        response.data.legal_type = response.data.legal_type + ''
        this.ruleForm = response.data
      })
    },
    getOptions() {
      options().then(res => {
        var temp = res.data['ORGANIZATION_TYPE']
        this.removeChildren(temp)
        this.persons = temp

        temp = res.data['ORGANIZATION_TRADE']
        this.removeChildren(temp)
        this.trades = temp
      })
    },
    removeChildren(temp) {
      var that = this
      temp.forEach(function(item) {
        if (item['children'].length === 0) {
          delete (item['children'])
        } else {
          that.removeChildren(item['children'])
        }
      })
    },
    // 获取城市根据城市码
    getCityByCode(code) {
      return CodeToText[code]
    },
    setStatusView() {
      if (this.userType === 1) {
        this.form.id = this.$route.query.id
        this.dialogFormVisible = true
      }
    },
    setStatus() {
      this.form_loading = true
      if (this.form.statusfs === 1) {
        this.form.content = '审核通过'
      }

      audit(this.form).then(response => {
        if (response.code === 200) {
          this.dialogFormVisible = false
          this.form_loading = false
          this.$message.success('审核成功')
          this.getList()
        }
      }).catch(() => {
        this.form_loading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.detail{
  height: 100%;
  .box-card{
    height: 100%;
    ::v-deep .el-card__header{
      padding-top: 0;
    }
    ::v-deep.el-card__body{
      padding: 0;
      margin-top: 20px;
      height: calc(100% - 20px - 42px);
      overflow: auto;
    }
  }
}
</style>
