<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
      </div>
      <app-form :type="true" :form="formData" @transfer="sonSave" />
    </el-card>
  </div>
</template>

<script>
import AppForm from './components/app-form'
import { detail, update } from '@/api/app'

export default {
  name: 'Edit',
  components: { AppForm },
  data() {
    return {
      formData: {}
    }
  },
  created() {
    this.getAppDetail()
  },
  methods: {
    getAppDetail() {
      detail({ id: this.$route.query.id }).then(response => {
        this.formData = response.data
        this.$forceUpdate()
      })
    },
    sonSave(form) {
      update(form).then(response => {
        this.$message.success('更新成功')
        this.$router.go(-1)
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .list-wrap {
    height: 100%;

    .box-card {
      border: none;
      height: 100%;

      ::v-deep .el-card__header {
        padding-top: 0;
      }

      ::v-deep .el-card__body {
        height: calc(100% - 59px);
        overflow: auto;
      }
    }
  }
</style>
