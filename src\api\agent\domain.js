import request from '@/utils/request'

export function domainList(data) {
  return request({
    url: '/domain/domainList',
    method: 'post',
    data: data
  })
}
export function domainExcel(data) {
  return request({
    url: '/domain/domainExcel',
    method: 'post',
    data: data
  })
}

export function domainExcelshow(data) {
  return request({
    url: '/domain/domainExcelshow',
    method: 'post',
    data: data
  })
}

export function index(data) {
  return request({
    url: '/domain/index',
    method: 'post',
    data: data
  })
}

export function temindex(data) {
  return request({
    url: '/domain/temindex',
    method: 'post',
    data: data
  })
}
export function create(data) {
  return request({
    url: '/domain/create',
    method: 'post',
    data: data
  })
}

export function detail(data) {
  return request({
    url: '/domain/detail',
    method: 'post',
    data: data
  })
}

export function destroy(data) {
  return request({
    url: '/domain/destroy',
    method: 'post',
    data: data
  })
}

export function search(data) {
  return request({
    url: '/domain/search',
    method: 'post',
    data: data
  })
}

export function order(data) {
  return request({
    url: '/domain/order',
    method: 'post',
    data: data
  })
}

export function product(data) {
  return request({
    url: '/domain/product',
    method: 'post',
    data: data
  })
}

export function price(data) {
  return request({
    url: '/domain/price',
    method: 'post',
    data: data
  })
}

export function idcCity(data) {
  return request({
    url: '/idc/city',
    method: 'post',
    data: data
  })
}

export function domainFinanceId(data) {
  return request({
    url: '/domain/domainFinanceId',
    method: 'post',
    data: data
  })
}

export function certification(data) {
  return request({
    url: '/domain/certification',
    method: 'post',
    timeout: 60000,
    data: data
  })
}

export function certificationStore(data) {
  return request({
    url: '/domain/certificationStore',
    method: 'post',
    timeout: 60000,
    data: data
  })
}

export function referTo(data) {
  return request({
    url: '/domain/referTo',
    method: 'post',
    timeout: 60000,
    data: data
  })
}
// 域名查询
export function domainClaimSearch(data) {
  return request({
    url: '/domain/domainClaimSearch',
    method: 'post',
    data: data
  })
}
// 域名领取
export function domainClaim(data) {
  return request({
    url: '/domain/domainClaim',
    method: 'post',
    data: data
  })
}

// 域名设置密码
export function domainSetPwd(data) {
  return request({
    url: '/domain/setPwd',
    method: 'post',
    data: data
  })
}

// 域名查看密码
export function domainGetPwd(data) {
  return request({
    url: '/domain/getPwd',
    method: 'post',
    data: data
  })
}

export function domainDestroy(data) {
  return request({
    url: '/domain/domainDestroy',
    method: 'post',
    data: data
  })
}

export function domainCalibration(data) {
  return request({
    url: '/domain/domainCalibration',
    method: 'post',
    data: data
  })
}
export function domainRefresh(data) {
  return request({
    url: '/domain/refresh',
    method: 'post',
    data: data
  })
}
export function domainTransfer(data) {
  return request({
    url: '/domain/transfer',
    method: 'post',
    data: data
  })
}

export function domainWhite(data) {
  return request({
    url: '/domain/domainwhite',
    method: 'post',
    data: data
  })
}

export function domainWdaile(data) {
  return request({
    url: '/domain/domainWdaile',
    method: 'post',
    data: data
  })
}
