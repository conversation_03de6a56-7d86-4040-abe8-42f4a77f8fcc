<template>
  <div id="aliveTendency">
    <el-card>
      <div class="el-row--flex is-justify-space-between is-align-middle">
        <span>用户活跃趋势</span>
        <el-date-picker
          v-model="date"
          size="small"
          format="yyyy"
          value-format="yyyy"
          type="year"
          placeholder="请选择年份"
          @change="handleGetAliveTendency"
        />
      </div>
      <div id="active-tendency">
        <div class="legend clearfix">
          <el-form class="float-right">
            <el-form-item label="合计" class="bottom-0">
              {{ total }} 人
            </el-form-item>
            <el-form-item label="均值" class="bottom-0">
              {{ avarage }} 人
            </el-form-item>
          </el-form>
        </div>
        <line-chart :show-legend="false" :grid="grid" height="400px" :chart-data="chartData" :legend="legend" :color-list="colorList" :data-type="'normal'" />
      </div>
    </el-card>
  </div>
</template>

<script>
/* eslint-disable */
import LineChart from './LineChart.vue';
import {getAliveTendency} from "@/api/dashboard";

export default {
  components: { LineChart },
  name: "AliveTendency",
  data() {
    return {
      date: "",
      chartData: {
        x: [],
        y: []
      },
      legend: ["人数"],
      colorList: ["#568ee8"],
      total: 0,
      avarage: 0,
      grid: {
        left: 30,
        right: 30,
        bottom: 20,
        top: 10,
        containLabel: true
      }
    };
  },
  methods: {
    handleGetAliveTendency(){
      if (!this.date){
        this.date = new Date().getFullYear().toString();
      }
      getAliveTendency({date: this.date}).then(res => {
        if (res.code === 200 && res.data){
          if (res.data.title){
            this.chartData.x = res.data.title.map(v => v[0]);
          }
          if (res.data.arr){
            this.chartData.y = res.data.arr;
          }
          this.total = res.data.count
          this.avarage = res.data.monthcount
        }
      })
    }
  },
  mounted() {
    this.handleGetAliveTendency();
  }
};
</script>

<style lang="scss" scoped>
.bottom-0{
  margin-bottom: 0;
}
.float-right{
  float: right;
  margin-top: 10px;
  ::v-deep.el-form-item--medium .el-form-item__content{
    display: inline-block;
  }
}
</style>
