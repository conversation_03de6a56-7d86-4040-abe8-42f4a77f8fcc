<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <!--<span>修改代理商</span>-->
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
        <el-button style="float: right; padding: 3px 0" type="text" @click="submitForm">保存</el-button>
      </div>
      <!-- 新增代理商表单 -->
      <a-form ref="formRef" :rule-form.sync="ruleForm" :is-update="true" />
    </el-card>
  </div>
</template>

<script>
import { update, detail } from '@/api/agent/agent'
import AForm from './form'
export default {
  components: { AForm },
  data() {
    return {
      ruleForm: {}
    }
  },
  created() {
    this.detail()
  },
  methods: {
    detail() {
      detail({ id: this.$route.query.id }).then(response => {
        this.ruleForm = response.data
        this.ruleForm.area = [this.ruleForm.province.toString(), this.ruleForm.city.toString()]
        this.ruleForm.agentArea = [this.ruleForm.agent_province.toString(), this.ruleForm.agent_city.toString()]
        console.log(this.ruleForm)
      })
    },
    submitForm() {
      this.$refs.formRef.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.ruleForm['password'] = this.ruleForm['password'] === '1' ? this.ruleForm['principal_phone'] : ''
          update(this.ruleForm).then(response => {
            if (response.code === 200) {
              this.$message.success('添加成功')
              this.$router.push({
                name: 'AgentListRouter'
              })
            }
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap{
  .box-card {
    ::v-deep .el-card__header{
      padding-top: 0;
    }
    ::v-deep .el-card__body{
      height: calc(100% - 41px);
      padding: 0;
      margin-top: 20px;
    }
  }
}
</style>
