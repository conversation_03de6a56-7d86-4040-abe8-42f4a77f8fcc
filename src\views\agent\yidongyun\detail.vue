<template>
  <el-card class="box-card" shadow="never" style="border: none">
    <div slot="header" class="clearfix">
      <!--<span>订单详情</span>-->
      <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
    </div>
    <el-form v-if="formData.vminfo" ref="ruleForm" :model="formData" label-width="150px">
      <el-form-item label="主机名称: ">{{ formData.vminfo.ecsname }}</el-form-item>
      <el-form-item label="实例名称: ">{{ formData.vminfo.instanceName }}</el-form-item>
      <el-form-item label="初始密码: ">{{ formData.vminfo.adminPass }}</el-form-item>
      <el-form-item label="计费方式: ">{{ formData.priceinfo.type }}</el-form-item>
      <el-form-item label="地区可用区: ">{{ formData.poolinfo.poolname }} | {{ formData.poolinfo.zonename }}</el-form-item>
      <el-form-item label="实例: ">{{ formData.vminfo.FlavorType }} | {{ formData.vminfo.SpecsName }} | {{ formData.vminfo.cpu }} | {{ formData.vminfo.ram }}内存 | {{ formData.vminfo.MaxBandwidth }}</el-form-item>
      <el-form-item label="镜像: ">{{ formData.os.osName }}</el-form-item>
      <el-form-item label="系统盘: ">{{ formData.selectedSystemDisk.name }} | {{ formData.selectedSystemDisk.size }}GB</el-form-item>
      <el-form-item label="数据盘: ">{{ formData.dataDisks }}</el-form-item>
      <el-form-item label="公网ip: ">{{ formData.ip.name }} | {{ formData.IPInfo.IP
        .label }} | {{ formData.ip.type }} | {{ formData.ip.size }}Mbps</el-form-item>
      <el-form-item label="带宽: ">{{ formData.bandwidth.size }}M</el-form-item>
      <el-form-item label="网络: ">{{ formData.vpc.vpcname }}</el-form-item>
      <el-form-item label="安全组: ">{{ formData.safeInfo.securityGroup.name }}</el-form-item>
      <el-form-item label="开通日期: ">{{ formData.created_at }}</el-form-item>
      <el-form-item label="到期时间: ">
        {{ formData.endtime }}
        <!--        <el-button type="text" @click="renew">【续费】</el-button>-->
      </el-form-item>
      <!--      <el-form-item label="运行状态: ">{{ formData.name }}</el-form-item>-->
    </el-form>
    <el-form v-if="formData.ebsDiskType" ref="ruleForm" :model="formData" label-width="150px">
      <el-form-item label="数据盘名称: ">{{ formData.ebsname }}</el-form-item>
      <el-form-item label="数据盘类型: ">{{ formData.ebsDiskType }}</el-form-item>
      <el-form-item label="数据盘大小: ">{{ formData.ebsDiskSize }}</el-form-item>
      <el-form-item label="数据盘数量: ">{{ formData.num }}</el-form-item>
      <el-form-item label="开通日期: ">{{ formData.created_at }}</el-form-item>
      <el-form-item label="到期时间: ">
        {{ formData.endtime }}
      </el-form-item>
    </el-form>
    <el-form v-if="!formData.vminfo&&formData.ip" ref="ruleForm" :model="formData" label-width="150px">
      <el-form-item label="公网ip: ">{{ formData.ip.name }} | IPv4 | {{ formData.ip.type }} | {{ formData.ip.size }}Mbps</el-form-item>
      <el-form-item label="带宽: ">{{ formData.bandwidth.size }}M</el-form-item>
      <el-form-item label="到期时间: ">
        {{ formData.endtime }}
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script>
import { mapGetters } from 'vuex'
import { yidongyun_orderDetail } from '@/api/agent/product'
export default {
  data() {
    return {
      dialogFormVisible: false,
      tableLoading: false,
      formData: {}
    }
  },
  computed: {
    // 别名处理防止冲突
    ...mapGetters({
      userType: 'agentUserType'
    })
  },
  created() {
    this.getDetail()
  },
  methods: {
    getDetail() {
      this.tableLoading = true
      yidongyun_orderDetail({ id: this.$route.query.id, customer_id: this.$route.query.customer_id }).then(response => {
        this.tableLoading = false
        this.formData = response.data
        if (this.formData.priceinfo.type == 3) {
          this.formData.priceinfo.type = '包月计费'
        }
        if (this.formData.priceinfo.type == 1) {
          this.formData.priceinfo.type = '包年计费'
        }
        if (this.formData.priceinfo.type == 4) {
          this.formData.priceinfo.type = '按量计费'
        }
        this.formData.dataDisks = this.dataDisk()
      }).catch(() => {
        this.tableLoading = false
      })
    },
    dataDisk() {
      let str = ''
      if (this.formData.dataDisks && this.formData.dataDisks.length) {
        str = this.formData.dataDisks
          .map(v => {
            return v.num + '块' + '  |  ' + v.name + '  |  ' + v.size + 'GB'
          })
          .join(',')
      }
      return str
    }
    // renew() {
    //   this.tableLoading = true
    //   getFinance({ id: this.$route.query.id }).then(res => {
    //     this.tableLoading = false
    //     this.$router.push({
    //       name: 'ProductRenewRouter',
    //       query: { id: res.data.id }
    //     })
    //   }).catch(() => {
    //     this.tableLoading = false
    //   })
    // }
  }
}
</script>

<style scoped>
  .el-divider--horizontal{
    width: 50%;
  }
  .box-card{
    margin: 20px;
  }
</style>
