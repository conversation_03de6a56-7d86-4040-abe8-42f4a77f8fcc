<!-- 站点下拉选择 -->
<template>
  <el-select-more
    :api="sites"
    :value.sync="siteIds"
    :is-multiple="isMultiple"
    :is-customer-label="true"
    label-key="title"
    search-key="template_name"
    value-key="site_id"
  >
    <template slot="label" slot-scope="scope">
      <template v-if="from === 'limit'">{{ scope.row.title }}</template>
      <template v-else-if="scope.row.host">{{ scope.row.host.web_name }} --- {{ scope.row.host.domain ? scope.row.host.domain : '未绑定' }}</template>
      <template v-else>{{ scope.row.title }} --- 未绑定</template>
    </template>
  </el-select-more>
</template>

<script>
import { sites } from '@/api/jzt/sites'
import ElSelectMore from '@/views/jzt/components/ElSelectMore/index.vue'

export default {
  name: 'SiteSelect',
  components: { ElSelectMore },
  props: {
    siteId: {
      type: String,
      default: ''
    },
    from: {
      type: String,
      default: ''
    },
    isMultiple: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      sites: sites,
      siteIds: this.siteId
    }
  },
  watch: {
    siteIds(e) {
      this.$emit('update:siteId', e)
    },
    siteId() {
      this.siteIds = this.siteId
    }
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  // 方法集合
  methods: { }
}
</script>
<style lang='scss' scoped>
//@import url(); 引入公共css类
</style>
