<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import OtherCate from './OtherCate.vue'
import OtherUpdate from './OtherUpdate.vue'
import { deleteOtherTestListApi, getOtherTestListApi } from '@/api/egt/recruitment'

export default {
  name: 'Other',
  components: {
    StatisticsTemplate,
    OtherCate,
    OtherUpdate
  },
  data() {
    return {
      config: {
        key: 'other',
        tableSettings: {
          api: getOtherTestListApi,
          field: {
            page: 'pageIndex',
            limit: 'pageSize',
            total: 'total'
          },
          columns: [
            {
              label: 'ID',
              prop: 'id',
              width: 60
            },
            {
              label: '题目',
              prop: 'title'
            },
            {
              label: '题目分类',
              prop: 'category'
            },
            {
              label: '选项',
              prop: 'option',
              isSlot: true
            },
            {
              label: '操作',
              prop: 'action',
              isSlot: true,
              align: 'center',
              width: '200'
            }
          ]
        }
      },
      updateVisible: false,
      row: {},
      cateSettingsVisible: false
    }
  },
  methods: {
    handleAdd() {
      this.updateVisible = true
      this.row = {}
    },
    handleUpdate(row) {
      this.updateVisible = true
      this.row = row
    },
    async handleDelete(row) {
      if (!row.id) return
      try {
        this.$refs.listRef.setLoading(row.id, true)
        const res = await deleteOtherTestListApi({ id: row.id })
        if (res.code === 200) {
          this.$message.success('删除成功')
          await this.$refs.listRef.handleGetData()
        } else {
          this.$message.error(res.msg || res.message || '删除失败')
        }
      } catch (e) {
        console.log(e)
        this.$message.error('删除失败')
      } finally {
        this.$refs.listRef.setLoading(row.id, false)
      }
    },
    toCateSettings() {
      this.cateSettingsVisible = true
    },
    submitSuccess() {
      this.$refs.listRef.handleGetData()
    }
  }
}
</script>

<template>
  <div class="list">
    <statistics-template ref="listRef" :config="config">
      <template #topActions>
        <div style="margin-bottom: 10px;text-align: right;width: 100%;">
          <el-button type="primary" @click="toCateSettings">设置分类</el-button>
          <el-button type="primary" @click="handleAdd">添加</el-button>
        </div>
      </template>
      <template #other_option="{row}">
        <div v-html="row.option" />
      </template>
      <template #other_action="{row}">
        <el-button size="mini" @click="handleUpdate(row)">修改</el-button>
        <el-popconfirm
          title="确定删除吗？"
          @onConfirm="handleDelete(row)"
        >
          <el-button slot="reference" size="mini" type="danger">删除</el-button>
        </el-popconfirm>
      </template>
    </statistics-template>
    <other-cate :visible.sync="cateSettingsVisible" :row="row" />
    <other-update
      :visible.sync="updateVisible"
      :data="row"
      @submitSuccess="submitSuccess"
    />
  </div>
</template>

<style scoped lang="scss">
.list{
  height: 100%;
}
</style>
