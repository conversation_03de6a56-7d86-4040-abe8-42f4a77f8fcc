<!-- 下拉 分页 -->
<template>
  <el-select style="width: 100%;" :value="c_value" placeholder="请选择" clearable :multiple="isMultiple"
    v-select-load-more="loadMore" @change="valueChange" filterable remote reserve-keyword :remote-method="remoteMethod">
  <!-- <el-option v-for="item in list" :key="item[valueKey]" :label="customerLabel(item, labelKey)"
        :value="item[valueKey]" /> -->
    <el-option v-for="item in list" :key="item[valueKey]" :label="item[labelKey]" :value="item[valueKey]">
      <template v-if="isCustomerLabel">
        <slot name="label" :row="item"></slot>
      </template>
      <template v-else>{{ item[labelKey] }}</template>
    </el-option>
  </el-select>
</template>

<script>

export default {
  name: 'ElSelectMore',
  props: {
    api: {
      type: Function,
      default: () => { }
    },
    value: {
      type: String,
      default: ''
    },
    labelKey: {
      type: String,
      default: 'name'
    },
    valueKey: {
      type: String,
      default: 'id'
    },
    searchKey: {
      type: String,
      default: 'name'
    },
    isMultiple: {
      type: Boolean,
      default: false
    },
    isCustomerLabel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      c_value: this.value,
      list: [],
      hasMore: true,
      query: {
        page: 1,
        limit: 10
      }
    }
  },
  //方法集合
  methods: {
    // 获取列表
    getList() {
      this.api(this.query).then(response => {
        this.list = this.list.concat(response.data.data)
        if (response.data.last_page == this.query.page) {
          this.hasMore = false
        }
      })
    },
    valueChange(e) {
      this.c_value = e
      this.$emit('update:value', e.toString())
    },
    // 触底加载
    loadMore() {
      if (this.hasMore) {
        this.query.page++
        this.getList()
      }
    },
    // 远程搜索
    remoteMethod(query) {
      if (query != undefined) {
        this.hasMore = true
        this.query.page = 1
        this.query[this.searchKey] = query
        this.list = []
        this.getList()
      }
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {
    this.getList()
  }
}
</script>
<style lang='scss' scoped>
//@import url(); 引入公共css类
</style>