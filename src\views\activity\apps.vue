<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>活动产品管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="saveHandle">保存</el-button>
      </div>
      <el-table
        ref="multipleTable"
        v-loading="tableLoading"
        :data="tableData"
        tooltip-effect="dark"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
        />
        <el-table-column label="产品图标" width="100">
          <template slot-scope="scope">
            <el-image fit="contain" style="height: 48px;width: 48px" :src="scope.row.icon" />
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="产品名称"
          width="180"
        />
        <el-table-column label="产品状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status" type="success" size="small" effect="dark">启用</el-tag>
            <el-tag v-else type="danger" effect="dark" size="small">禁用</el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { all } from '../../api/app'
import { set_app, get_app } from '../../api/activity'
export default {
  name: 'Apps',
  data() {
    return {
      tableLoading: false,
      ruleForm: {
        id: this.$route.query.id,
        app_id: []
      },
      tableData: []
    }
  },
  created() {
    this.getAppsList()
  },
  mounted() {
  },
  methods: {
    getAppsList() {
      this.tableLoading = true
      all().then(response => {
        this.tableData = response.data
        get_app(this.$route.query).then(response => {
          const apps = response.data
          apps.forEach(item => {
            this.$refs.multipleTable.toggleRowSelection(this.tableData.find(app => app.id === item))
          })
          this.tableLoading = false
        })
      })
    },
    saveHandle() {
      set_app(this.ruleForm).then(response => {
        this.$confirm('设置成功', '提示', {
          confirmButtonText: '返回上一页',
          cancelButtonText: '留在本页',
          type: 'success'
        }).then(() => {
          this.$router.go(-1)
        }).catch(() => {
        })
      })
    },
    handleSelectionChange(val) {
      const ids = []
      val.forEach(function(item) {
        ids.push(item.id)
      })
      this.ruleForm.app_id = ids
    }
  }
}
</script>

<style scoped>

</style>
