<template>
  <div class="app-container" style="height: 100%; padding-bottom: 0;padding-top: 0">
    <div class="list-wrap">
      <div class="clearfix">
        <!--<span>产品分类</span>-->
        <el-button style="float: right; padding: 3px 0" type="text" @click="createdView">新增分类</el-button>
      </div>
      <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%;margin-top: 20px;"
      >
        <el-table-column
            prop="id"
            label="ID"
            width="100"
        />
        <el-table-column
            prop="name"
            label="分类名称"
        />
        <el-table-column label="图标">
          <template slot-scope="scope">
            <el-image
                style="width: 32px; height: 32px"
                :src="scope.row.icon"
                fit="fill"
            />
          </template>
        </el-table-column>
        <el-table-column label="图标激活">
          <template slot-scope="scope">
            <el-image
                style="width: 32px; height: 32px"
                :src="scope.row.icon_active"
                fit="fill"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-link :underline="false" type="primary" @click="editDialog(scope.row)">编辑</el-link>
            <el-popconfirm
                title="确定删除当前记录吗？"
                @onConfirm="deleteHandle(scope.row)"
            >
              <el-link slot="reference" :underline="false" type="danger">删除</el-link>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-form ref="ruleForm" :model="ruleForm" :rules="rules" size="small" label-width="100px" class="demo-ruleForm">
      <el-dialog
          title=""
          :visible.sync="dialogVisible"
          width="40%"
      >
        <el-form-item label="分类名称：" prop="name">
          <el-input v-model="ruleForm.name" style="width: 70%" />
        </el-form-item>
        <el-form-item label="分类图标:" prop="icon">
          <div class="category-upload ">
            <el-upload
                class="avatar-uploader"
                :action="upload_url"
                :headers="headers"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
            >
              <img v-if="ruleForm.icon" :src="ruleForm.icon" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon" />
            </el-upload>
          </div>
        </el-form-item>
        <el-form-item label="分类激活图标:" prop="icon_active">
          <div class="category-upload ">
            <el-upload
                class="avatar-uploader"
                :action="upload_url"
                :headers="headers"
                :show-file-list="false"
                :on-success="handleAvataractiveSuccess"
                :before-upload="beforeAvatarUpload"
            >
              <img v-if="ruleForm.icon_active" :src="ruleForm.icon_active" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon" />
            </el-upload>
          </div>
        </el-form-item>
        <span slot="footer" class="dialog-footer">
            <el-button size="small" @click="dialogVisible = false">取 消</el-button>
            <el-button size="small" type="primary" :loading="b_loading" @click="saveHandle">确 定</el-button>
          </span>
      </el-dialog>
    </el-form>
  </div>
</template>

<script>
import { getCategory, destroy, store } from '../../api/shop_category'
export default {
  name: 'Category',
  data() {
    return {
      upload_url: process.env.VUE_APP_BASE_API + '/upload',
      headers: {
        Authorization: 'Bearer ' + this.token
      },
      b_loading: false,
      loading: false,
      ruleForm: {
        icon: '',
        icon_active: ''
      },
      dialogVisible: false,
      tableData: [],
      rules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' }
        ],
        icon: [
          { required: true, message: '分类图标必须上传', trigger: 'blur' }
        ],
        icon_active: [
          { required: true, message: '分类激活图标必须上传', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      getCategory().then(response => {
        if (response.code === 200) {
          this.tableData = response.data
          this.loading = false
        }
      })
    },
    createdView() {
      this.b_loading = false
      this.ruleForm = {}
      this.dialogVisible = true
    },
    saveHandle() {
      this.$refs.ruleForm.validate((valid) => {
        this.b_loading = true
        if (valid) {
          store(this.ruleForm).then(response => {
            if (response.code === 200) {
              this.dialogVisible = false
              this.$message.success('操作成功')
              this.getList()
            }
          }).catch(() => {
            this.b_loading = false
          })
        } else {
          this.b_loading = false
          return false
        }
      })
    },
    editDialog(row) {
      this.b_loading = false
      this.ruleForm = row
      this.dialogVisible = true
    },
    deleteHandle(row) {
      destroy({ id: row.id }).then(response => {
        if (response.code === 200) {
          this.dialogVisible = false
          this.$message.success('操作成功')
          this.getList()
        }
      })
    },
    handleAvatarSuccess(res, file) {
      this.ruleForm.icon = res.data.url
    },
    handleAvataractiveSuccess(res, file) {
      this.ruleForm.icon_active = res.data.url
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG && !isPNG) {
        this.$message.error('上传的图标只能是 JPG 或 PNG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传的图片大小不能超过 2MB!')
      }
      return (isJPG || isPNG) && isLt2M
    }
  }
}
</script>

<style scoped lang="scss">
.card-wrap ::v-deep .el-card__body {
  height: 100%;
}
.category-upload {
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    &:hover {
      border-color: #409EFF;
    }
  }
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>

<style scoped lang="scss">
.list-wrap {
  height: 100%;

  .box-card {
    border: none;
    height: 100%;

    ::v-deep .el-card__header {
      padding-top: 0;
    }

    ::v-deep .el-card__body {
      height: calc(100% - 59px);
      overflow: auto;
      padding: 0;
      margin-top: 20px;
    }
  }
}
</style>
