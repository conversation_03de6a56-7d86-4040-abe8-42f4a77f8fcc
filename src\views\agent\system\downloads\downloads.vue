<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none;">
      <div class="filter">
        <el-form :inline="true" :model="queryList" class="demo-form-inline" size="small">
          <el-form-item label="标题">
            <el-input v-model="queryList.title" placeholder="请输入标题" clearable />
          </el-form-item>
          <el-form-item label="日期">
            <el-date-picker
              v-model="queryList.date"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="table mt20">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          stripe
          style="width: 100%"
          height="calc(100% - 96px)"
        >
          <el-table-column
            prop="title"
            label="标题"
          />
          <el-table-column label="创建时间" prop="created_at" />
          <el-table-column align="center" label="状态">
            <template slot-scope="scope">
              <el-link v-if="scope.row.status === 0" type="warning" :underline="false">生成中</el-link>
              <el-link v-if="scope.row.status === 1" type="success" :underline="false">成功</el-link>
              <el-link v-if="scope.row.status === -1" type="danger" :underline="false">失败</el-link>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button v-if="scope.row.status === 1" type="text" @click="openManager(scope.row)">下载</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryList.page"
          :limit.sync="queryList.perPage"
          style="text-align:center;"
          @pagination="getList"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { list } from '@/api/agent/downloads'
import { add, update, delect, show } from '@/api/agent/agentPoint'
import Pagination from '@/components/Pagination'

export default {
  name: 'Notice',
  components: { Pagination },
  data() {
    return {
      height: '500',
      dialogVisible: false,
      isUpdate: false,
      tableData: [],
      ruleForm: {},
      html_text: '',
      agentlist: [],
      rules: {
        title: [{ required: true, message: '标题必填', trigger: 'blur' }],
        content: [{ required: true, message: '标题必填', trigger: 'blur' }]
      },
      queryList: {
        page: 1,
        perPage: 10
      },
      total: 0,
      multipleSelection: [],
      tableLoading: false
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.list-wrap>.box-card>.el-card__body')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  methods: {
    getList() {
      console.log(this.queryList)
      this.tableLoading = true
      list(this.queryList).then(res => {
        if (res.code === 200 && res.data) {
          this.total = res.data.meta.total
          this.tableData = res.data.list
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    createView() {
      this.isUpdate = false
      this.ruleForm = {}
      this.dialogVisible = true
    },
    editView(row) {
      this.isUpdate = true
      this.dialogVisible = true
      show({ id: row.id }).then(response => {
        this.ruleForm = response.data
        console.log(this.ruleForm)
      })
    },
    saveHandle() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          add(this.ruleForm).then(response => {
            this.dialogVisible = false
            this.$message.success('新增成功')
            this.getList()
          })
        } else {
          return false
        }
      })
    },
    updateHandle() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          update(this.ruleForm).then(response => {
            this.dialogVisible = false
            this.$message.success('更新成功')
            this.getList()
          })
        } else {
          return false
        }
      })
    },
    del(row) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delect({ id: row.id }).then(response => {
          if (response.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          }
        })
      })
    },
    openManager(row) {
      window.open(row.file)
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap {
  height: 100%;
  .box-card {
    height: 100%;
    ::v-deep .el-card__header {
      padding-top: 0;
    }
    ::v-deep .el-card__body {
      padding: 0;
      height: 100%;
      overflow: auto;
    }
  }
  .filter {
    border-bottom: 1px solid #E5E5E5;
  }
  .table {
    margin-top: 20px;
    min-height: 200px;
  }
}
</style>
