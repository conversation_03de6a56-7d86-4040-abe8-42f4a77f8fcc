<script>
import * as echarts from 'echarts'
import resize from '@/views/dashboard/admin/components/mixins/resize'

export default {
  name: 'LineEchart',
  mixins: [resize],
  props: {
    color: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => ({
        x: [],
        y: []
      })
    }
  },
  data() {
    return {
      id: this.generateRandomId()
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  methods: {
    //   生成随机div的id
    generateRandomId() {
      const characters = 'abcdefghijklmnopqrstuvwxyz0123456789'
      let id = ''
      for (let i = 0; i < 10; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length)
        id += characters.charAt(randomIndex)
      }
      return id
    },
    initChart() {
      this.$nextTick(() => {
        const chart = echarts.init(document.getElementById(this.id))
        const option = {
          title: '',
          grid: {
            left: '1%',
            right: '4%',
            bottom: '10%',
            top: '10%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: this.data.x,
            boundaryGap: false,
            axisLine: {
              lineStyle: {
                color: '#A7ABBB'
              }
            }
          },
          yAxis: {
            type: 'value',
            axisLine: {
              show: true,
              lineStyle: {
                color: '#A7ABBB'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#EEEFF7'
              }
            }
          },
          series: [{
            type: 'line',
            smooth: true, // 是否平滑曲线显示
            showAllSymbol: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              normal: {
                color: this.color // 线条颜色
              }
            },
            label: {
              show: true,
              position: 'top',
              textStyle: {
                color: '#fff'
              }
            },
            itemStyle: {
              color: 'transparent',
              borderColor: 'transparent',
              borderWidth: 0
            },
            tooltip: {
              show: false
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: this.color + '60'
                },
                  {
                    offset: 1,
                    color: 'rgba(255,255,255,0.2)'
                  }
                ], false)
              }
            },
            data: this.data.y
          }]
        }
        chart.setOption(option)
      })
    }
  }
}
</script>

<template>
  <div :id="id" style="height: 200px" />
</template>

<style scoped lang="scss">

</style>
