<template>
  <div id="rank">
    <div v-if="data.length" class="rank-wrap">
      <div v-for="(item, index) in data" :key="item.id" class="rank-item">
        <div class="index-title">
          <img
            v-if="index < 3"
            :src="require(`@/assets/img/top${index + 1}.png`)"
            alt=""
          >
          <span v-else>{{ index.toString().padStart(2, "0") }}</span>
          <span class="name">{{ item[finallyFields.name] }}</span>
        </div>

        <div class="value">
          <div v-for="(v, i) in options" :key="i">
            <div v-if="v" class="progress-wrap">
              <div
                v-if="item[v.field] || item[v.field] === 0"
                :style="`width: ${getProcess(
                  item[v.field],
                  findMax(v.field)
                )}%; background: ${v[finallyFields.color]}`"
                class="progress-bar"
              />
              <div
                v-else
                :style="`width: 100%; background: #f1efef`"
                class="progress-bar"
              />

              <span
                :style="item[v.field] || item[v.field] === 0 ? `left: calc(${getProcess(
                  item[v.field],
                  findMax(v.field)
                )}% + 6px)` : `left: calc(100% + 5px)`"
              >
                <i v-if="v.unit && v.unitPosition === 'left'">{{ v.unit }}</i>

                {{ item[v.field] || 0 }}

                <i v-if="v.unit && v.unitPosition === 'right'">{{ v.unit }}</i>
              </span>
            </div>
          </div>
        </div>
      </div>
      <div v-if="showMoreBtn" class="more-wrap">
        <el-button type="text" @click="handleShowMore">更多</el-button>
      </div>
    </div>

    <el-empty v-else />

    <div v-if="data.length && options.length" class="legend">
      <div v-for="(item, index) in options" :key="index" class="legend-item">
        <div class="icon" :style="`background: ${item[finallyFields.color]}`" />
        <div class="label">{{ item[finallyFields.label] }}</div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable */

import ElEmpty from '@/components/ElEmpty/index.vue'

export default {
  name: "Rank",
  components: { ElEmpty },
  props: {
    data: {
      type: Array,
      default: [],
    },
    options: {
      type: Array,
      default: () => [
        {
          field: "num",
          color: "#2C6DF2",
          unit: "",
          unitPosition: "left",
          label: "项目发布数量",
        },
        {
          field: "money",
          color: "#00DEA2",
          unit: "￥",
          unitPosition: "left",
          label: "项目发布金额",
        },
      ],
    },
    replaceProps: {
      type: Object,
      default: () => {}
    },
    showMoreBtn: {
      type: Boolean,
      default: false
    }
  },
  data(){
    return {
      fields: {
        name: 'name',
        label: 'label',
        id: 'id',
        color: 'color'
      },
      show: false
    }
  },
  computed: {
    finallyFields(){
      if(this.replaceProps){
        return {...this.fields, ...this.replaceProps}
      }else{
        return this.fields;
      }
    }
  },
  methods: {
    findMax(field) {
      if (field) {
        const numList = this.data.map((v) => v[field]);
        const maxNum = numList.length ? Math.max(...numList) : [];
        return maxNum;
      }
    },
    getProcess(value, max) {
      if(!value){
        return 0
      }
      if (max === 0) {
        return 0;
      }
      if (value && max) {
        return ((value / max) * 100).toFixed(2);
      }
    },
    handleShowMore(){
      this.$listeners.showMore && this.$listeners.showMore();
    }
  },
};
</script>

<style lang="scss" scoped>
#rank {
  height: 100%;
  display: flex;
  flex-direction: column;
  .rank-wrap {
    flex-shrink: 0;
    overflow: auto;
    height: calc(100% - 62px);
    .rank-item {
      & + .rank-item {
        margin-top: 24px;
      }
      .index-title {
        margin-bottom: 7px;
        display: flex;
        align-items: center;
        .name {
          margin-left: 8px;
          font-size: 14px;
          font-family: PingFang SC;
          font-weight: 500;
          color: #111111;
          line-height: 35px;
        }
      }
      .value {
        margin-top: 7px;
        & > div {
          height: 18px;
          .progress-wrap {
            width: 70%;
            position: relative;
            .progress-bar {
              height: 7px;
              background: #111111;
            }
            span {
              position: absolute;
              top: 0;
              bottom: 0;
              margin: auto;
              line-height: 7px;
              font-size: 14px;
              font-family: DIN Medium;
              font-weight: 400;
              color: #111111;
              white-space: nowrap;
              i {
                font-style: normal;
              }
            }
          }
        }
      }
    }
    .more-wrap{
      text-align: center;
      margin-top: 30px;
    }
  }
  .legend {
    flex-shrink: 0;
    width: 100%;
    height: 62px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    .legend-item{
      display: flex;
      justify-content: center;
      align-items: center;
      .icon{
        width: 6px;
        height: 6px;
        margin-right: 4px;
      }
      .label{
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #999999;
        line-height: 68px;
      }
    }
  }
}
</style>
