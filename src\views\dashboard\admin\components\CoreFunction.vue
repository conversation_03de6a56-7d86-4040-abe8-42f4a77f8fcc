<template>
  <div id="coreFunction">
    <el-card>
      <div class="el-row--flex is-justify-space-between is-align-middle">
        <span>核心功能使用次数（访问量）</span>
        <el-date-picker
          v-model="date"
          size="small"
          format="yyyy-MM"
          value-format="yyyy-MM"
          type="month"
          placeholder="请选择月份"
          @change="handleGetCoreFunction"
        />
      </div>
      <div id="core-function">
        <bar-chart :show-legend="false" :grid="grid" height="500px" :chart-data="chartData" :legend="legend" :color-list="colorList" :data-type="'normal'" :rotate="-45" />
      </div>
    </el-card>
  </div>
</template>

<script>
/* eslint-disable */
import BarChart from './BarChart.vue';
import {getCoreFunction} from "@/api/dashboard";

export default {
  name: "coreFunction",
  components: { BarChart },
  data() {
    return {
      date: "",
      chartData: {
        x: [],
        y: []
      },
      legend: ["访问量"],
      colorList: ["#31d0be"],
      total: 0,
      avarage: 0,
      grid: {
        left: 30,
        right: 100,
        bottom: 60,
        top: 30,
        containLabel: true
      }
    };
  },
  methods: {
    handleGetCoreFunction(){
      if(!this.date){
        this.date = new Date().getFullYear().toString() + '-' + (new Date().getMonth() + 1).toString().padStart(2, '0');
      }
      getCoreFunction({date: this.date}).then(res => {
        if (res.code === 200 && res.data){
          if (res.data.title){
            this.chartData.x = res.data.title;
          }
          if (res.data.count){
            this.chartData.y = res.data.count;
          }
        }
      })
    }
  },
  mounted() {
    this.handleGetCoreFunction();
  }
};
</script>

<style lang="scss" scoped>
.bottom-0{
  margin-bottom: 0;
}
.float-right{
  float: right;
  margin-top: 10px;
  ::v-deep.el-form-item--medium .el-form-item__content{
    display: inline-block;
  }
}
</style>
