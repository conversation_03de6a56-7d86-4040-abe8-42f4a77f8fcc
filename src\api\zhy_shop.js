import request from '@/utils/request'

// 获取传统电商列表
export function traditionListApi(data) {
  return request({
    url: '/tradition/list',
    method: 'POST',
    data: data
  })
}

// 添加/修改传统电商
export function traditionSaveApi(data) {
  return request({
    url: '/tradition/store',
    method: 'POST',
    data: data
  })
}

// 删除传统电商
export function traditionDeleteApi(id) {
  return request({
    url: '/tradition/destory',
    method: 'POST',
    data: { id }
  })
}

// 传统电商 分类
export function traditionTypeApi() {
  return request({
    url: '/tradition/categories/list',
    method: 'POST'
  })
}

// 传统电商 分类 添加、修改
export function traditionTypeSaveApi(data) {
  return request({
    url: '/tradition/categories/store',
    method: 'POST',
    data: data
  })
}

// 传统电商 分类 删除
export function traditionTypeDeleteApi(id) {
  return request({
    url: '/tradition/categories/destory',
    method: 'POST',
    data: { id }
  })
}

// 获取私域、带货电商
export function privateListApi(data) {
  return request({
    url: '/ecommerce/list',
    method: 'POST',
    data: data
  })
}

// 添加/修改私域、带货电商
export function privateSaveApi(data) {
  return request({
    url: '/ecommerce/store',
    method: 'POST',
    data: data
  })
}

// 删除私域、带货电商
export function privateDeleteApi(id) {
  return request({
    url: '/ecommerce/destory',
    method: 'POST',
    data: { id }
  })
}