<template>
  <div class="app-container" style="height: 100%; padding-bottom: 0">
    <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        height="calc(100% - 96px)"
    >
      <el-table-column
          prop="id"
          label="ID"
          width="100"
      />
      <el-table-column
          prop="order_no"
          label="订单编号"
      />
      <el-table-column label="类型" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.type === 1" size="small" type="success" effect="dark">福利币</el-tag>
          <el-tag v-if="scope.row.type === 2" size="small" type="danger" effect="dark">福利劵</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="amount"
          label="充值金额"
      />
      <el-table-column
          prop="lc_amount"
          label="福利币"
      />
      <el-table-column
          prop="num"
          label="张数"
      />
      <el-table-column
          prop="user"
          label="用户名称"
      />
      <el-table-column label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 0" size="small" type="warning" effect="dark">待支付</el-tag>
          <el-tag v-if="scope.row.status === 1" size="small" type="success" effect="dark">已支付</el-tag>
          <el-tag v-if="scope.row.status === 2" size="small" type="info" effect="dark">取消订单</el-tag>
          <el-tag v-if="scope.row.status === 3" size="small" type="info" effect="dark">订单超时</el-tag>
          <el-tag v-if="scope.row.status === 4" size="small" type="danger" effect="dark">交易关闭</el-tag>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
  </div>
</template>

<script>
import { getOrders } from '../../api/shop_wallet_orders'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
export default {
  name: 'WalletOrders',
  components: { Pagination },
  data() {
    return {
      loading: false,
      tableData: [],
      listQuery: {
        page: 1,
        limit: 10
      },
      total: 1
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      getOrders(this.listQuery).then(response => {
        this.tableData = response.data.data
        this.total = response.data.total
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.card-wrap ::v-deep .el-card__body {
  height: 100%;
}
.list-wrap {
  height: 100%;

  ::v-deep .card-wrap {
    height: 100%;
    border: none;

    .el-card__body {
      height: 100%;
      padding: 0;
    }

    .box-card {
      height: 100%;
      border: none;

      .el-card__body {
        height: calc(100% - 59px);
        overflow: auto;
      }
    }
  }
}
</style>
