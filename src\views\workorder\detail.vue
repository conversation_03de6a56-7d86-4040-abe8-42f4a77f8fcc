<template>
  <div class="app-container">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>问题描述</span>
        <el-button :loading="loading" style="float: right; padding: 3px 0" type="text" @click="$router.back(-1)">返回上一页</el-button>
      </div>
      <el-form label-width="120px">
        <el-form-item label="联系人：">
          {{ detailData.username }}
        </el-form-item>
        <el-form-item label="联系电话：">
          {{ detailData.phone }}
        </el-form-item>
        <el-form-item label="提交时间：">
          {{ detailData.created_at }}
        </el-form-item>
        <el-form-item label="问题描述：">
          {{ detailData.question }}
        </el-form-item>
        <el-form-item v-if="detailData.images.length > 0" label="图片附件：">
          <el-row v-if="detailData.images.length > 0" :gutter="20">
            <el-col v-for="item in detailData.images" :key="item" :span="4">
              <div>
                <el-image
                  style="width: 150px;height: 100px"
                  :src="item"
                  :preview-src-list="detailData.images"
                  fit="fill"
                />
              </div>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
    </el-card>
    <div v-if="detailData.comments">
      <el-card class="box-card" v-for="item in detailData.comments" shadow="never">
        <div slot="header" class="clearfix">
          <span v-if="item.type === 1">追问</span>
          <span v-if="item.type === 2">答复</span>
        </div>
        <el-form label-width="120px">
          <el-form-item label="提交时间：">
            {{ item.created_at }}
          </el-form-item>
          <el-form-item label="问题描述：">
            <div v-if="item.type === 1">
              {{ item.question }}
            </div>
            <div v-else v-html="item.question"></div>
          </el-form-item>
          <el-form-item v-if="item.images.length" label="图片附件：">
            <el-row v-if="item.images.length > 0" :gutter="20">
              <el-col v-for="pic in item.images" :key="pic" :span="4">
                <div>
                  <el-image
                    style="width: 150px;height: 100px"
                    :src="pic"
                    :preview-src-list="item.images"
                    fit="fill"
                  />
                </div>
              </el-col>
            </el-row>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
    <!--回复-->
    <el-card v-if="detailData.status !== 3" class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>回复</span>
        <el-button :loading="loading" style="float: right; padding: 3px 0" type="text" @click="reComment">提交</el-button>
      </div>
      <Editor :content.sync="formData.content" />
    </el-card>
  </div>
</template>

<script>
import Editor from '../../components/editor'
import { detail, comment } from '../../api/workorder'
export default {
  name: 'Detail',
  components: { Editor },
  data() {
    return {
      loading: false,
      activeCollapse: ['1', '2', '3'],
      formData: {},
      detailData: {
        images: []
      }
    }
  },
  created() {
    this.getDetail()
  },
  methods: {
    getDetail() {
      detail({ id: this.$route.query.id }).then(response => {
        this.detailData = response.data
      })
    },
    reComment() {
      this.loading = true
      this.formData.id = this.detailData.id
      comment(this.formData).then(response => {
        this.$message.success('回复成功')
        this.getDetail()
        this.$router.push({
          path: '/workorder/list'
        })
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped>

</style>
