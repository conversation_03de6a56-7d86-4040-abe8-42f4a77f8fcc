<script>
import WebsiteUpdate from '@/views/egt/hr/components/recruitment/WebsiteUpdate.vue'
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'

export default {
  name: 'Website',
  components: { WebsiteUpdate, StatisticsTemplate },
  data() {
    return {
      config: {
        key: 'website',
        filters: [
          {
            label: '关键字',
            prop: 'keyword',
            type: 'input',
            value: ''
          }
        ],
        tableSettings: {
          selection: true,
          index: true,
          columns: [
            {
              label: '网站',
              prop: 'website',
              sortable: true
            },
            {
              label: '网址',
              prop: 'url',
              sortable: true
            },
            {
              label: '状态',
              prop: 'status',
              sortable: true
            },
            {
              label: '操作',
              prop: 'action',
              align: 'center',
              isSlot: true
            }
          ],
          data: [
            {
              id: 1,
              website: '百度',
              url: 'https://www.baidu.com',
              status: 1
            }
          ]
        }
      },
      row: {},
      updateVisible: false,
      selection: []
    }
  },
  methods: {
    getList() {
      console.log(this.listQuery)
    },
    handleAdd() {
      this.updateVisible = true
      this.row = {}
    },
    handleUpdate(row) {
      this.updateVisible = true
      this.row = row
    },
    handleRefresh(data) {
      this.config.tableSettings.data.map(item => {
        if (item.id === data.id) {
          item.year = data.year
          item.dates = data.dates
        }
        return item
      })
      this.getList()
    },
    handleSelectionChange(val) {
      this.selection = val
    },
    handleDelete() {
      const ids = this.selection.map(item => item.id)
      const data = this.config.tableSettings.data.filter(item => !ids.includes(item.id))
      this.$set(this.config.tableSettings, 'data', data)
      this.getList()
    }
  }
}
</script>

<template>
  <div class="list">
    <statistics-template :config="config" @selection-change="handleSelectionChange">
      <template #topActions>
        <div class="operations">
          <el-button type="primary" @click="handleAdd">添加</el-button>
          <el-popconfirm
            title="确定删除吗？"
            @onConfirm="handleDelete"
          >
            <el-button slot="reference" style="margin-left: 10px;" :disabled="selection.length === 0" type="danger">删除</el-button>
          </el-popconfirm>
        </div>
      </template>
      <template #website_action="{row}">
        <el-button type="text" @click="handleUpdate(row)">编辑</el-button>
      </template>
    </statistics-template>
    <website-update :visible.sync="updateVisible" :data="row" @refresh="handleRefresh" />
  </div>
</template>

<style scoped lang="scss">
.list{
  height: 100%;
  .operations{
    display: flex;
    align-items: center;
  }
}
</style>
