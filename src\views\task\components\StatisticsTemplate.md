# StatisticsTemplate 统计模板组件

## 组件说明
StatisticsTemplate是一个通用的列表统计模板组件，集成了搜索、表格展示、分页等功能，支持自定义列、排序、多选等特性。

## 基础用法

```vue
<template>
  <statistics-template
    :config="config"
    @selection-change="handleSelectionChange"
    @sort-change="handleSortChange"
  >
    <!-- 自定义顶部操作按钮 -->
    <template #topActions>
      <el-button type="primary" @click="handleAdd">添加</el-button>
    </template>

    <!-- 自定义列内容 -->
    <template #column_action="{ row }">
      <el-button type="text" @click="handleEdit(row)">编辑</el-button>
    </template>
  </statistics-template>
</template>

<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'

export default {
  components: { StatisticsTemplate },
  data() {
    return {
      config: {
        key: 'uniqueKey',  // 组件唯一标识
        filters: [         // 搜索条件配置
          {
            label: '关键字',
            prop: 'keyword',
            type: 'input'
          }
        ],
        tableSettings: {   // 表格配置
          selection: true, // 是否显示多选
          index: true,    // 是否显示序号
          columns: [      // 列配置
            {
              label: '标题',
              prop: 'title',
              sortable: true
            }
          ]
        }
      }
    }
  }
}
</script>
```

## Props

### config 配置对象

| 参数 | 说明 | 类型 | 必填 | 默认值 |
|------|------|------|------|--------|
| key | 组件唯一标识，用于插槽命名 | String | 是 | 'page' |
| filters | 搜索条件配置 | Array | 否 | [] |
| tableSettings | 表格配置 | Object | 是 | - |

### filters 搜索条件配置

```javascript
filters: [
  {
    label: '显示的标签名',
    prop: '字段名',
    type: '输入类型', // input/select/datePicker/cascader等
    settings: {  // 针对不同type的特殊配置
      options: [], // select的选项
      props: {},   // 配置项
      placeholder: '提示文字'
    }
  }
]
```

### tableSettings 表格配置

```javascript
tableSettings: {
  api: Function,           // 获取数据的接口方法
  params: Object,          // 额外的请求参数
  selection: Boolean,      // 是否显示多选框
  index: Boolean,          // 是否显示序号列
  height: String,          // 表格高度
  showPage: Boolean,       // 是否显示分页
  columns: [              // 表格列配置
    {
      label: '列标题',
      prop: '字段名',
      width: '宽度',
      minWidth: '最小宽度',
      align: '对齐方式',
      sortable: true,     // 是否可排序
      formatter: Function, // 格式化函数
      isSlot: Boolean,    // 是否使用插槽
      fixed: 'right'      // 固定列
    }
  ],
  field: {               // 接口返回数据的字段映射
    total: 'count',     // 总数的字段名
    list: 'data',      // 列表数据的字段名
    page: 'page',      // 页码的字段名
    limit: 'limit'     // 每页条数的字段名
  }
}
```

## Events

| 事件名 | 说明 | 回调参数 |
|------|------|------|
| selection-change | 当选择项发生变化时触发 | selection: 已选择的行数据数组 |
| sort-change | 当表格的排序条件发生变化时触发 | { column, prop, order } |
| get-data | 获取数据成功后触发 | response: 接口返回的原始数据 |

## Slots

| 插槽名 | 说明 | 作用域参数 |
|------|------|------|
| topActions | 表格顶部的操作按钮区域 | - |
| [key]_[prop] | 自定义列的内容，key为config.key，prop为列的prop | { row } |

## 使用示例

```vue
<template>
  <statistics-template
    :config="config"
    @selection-change="handleSelectionChange"
  >
    <template #topActions>
      <el-button type="primary" @click="handleAdd">新增</el-button>
      <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
    </template>

    <template #page_action="{ row }">
      <el-button type="text" @click="handleEdit(row)">编辑</el-button>
      <el-button type="text" @click="handleDelete(row)">删除</el-button>
    </template>
  </statistics-template>
</template>

<script>
export default {
  data() {
    return {
      config: {
        key: 'page',
        filters: [
          {
            label: '关键字',
            prop: 'keyword',
            type: 'input'
          },
          {
            label: '状态',
            prop: 'status',
            type: 'select',
            settings: {
              options: [
                { label: '启用', value: 1 },
                { label: '禁用', value: 0 }
              ]
            }
          }
        ],
        tableSettings: {
          api: this.getList,
          selection: true,
          index: true,
          columns: [
            {
              label: '标题',
              prop: 'title',
              sortable: true
            },
            {
              label: '状态',
              prop: 'status'
            },
            {
              label: '操作',
              prop: 'action',
              fixed: 'right',
              width: '150px',
              isSlot: true
            }
          ]
        }
      }
    }
  },
  methods: {
    getList(params) {
      // 实现获取数据的接口调用
      return api.getList(params)
    }
  }
}
</script>
```

## 注意事项

1. 使用自定义列时，插槽名需要按照 `[config.key]_[column.prop]` 的格式命名
2. 表格高度默认为100%，需要在父容器中设置具体高度
3. 接口返回的数据格式需要与field配置对应
4. 如果需要自定义搜索条件的组件，可以通过filters配置的type来指定
