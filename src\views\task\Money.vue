<template>
  <div id="money" class="app-container">
    <el-card class="box-card" style="margin-bottom: 15px" shadow="hover">
      <div slot="header" class="clearfix">
        <span>搜索栏</span>
      </div>
      <el-form :inline="true" :model="query" class="demo-form-inline" method="get">
        <el-row>
          <el-col :span="20">
            <el-form-item label="姓名">
              <el-input v-model="query.username" placeholder="请输入申请人姓名" clearable />
            </el-form-item>
            <el-form-item>
              <el-select v-model="query.status" placeholder="审批状态" clearable>
                <el-option v-for="item in statusList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-input v-model="query.name" placeholder="请输入项目编号或项目名称" clearable />
            </el-form-item>
            <el-form-item>
              <el-date-picker
                v-model="query.createtime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="请输入申请时间"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-date-picker
                v-model="query.statustime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="请输入审批时间"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="getList(1)">搜索</el-button>
            </el-form-item>
          </el-col>

          <el-col :span="4">
            <el-button type="primary" @click="batchApproval(1)">批量通过</el-button>
            <el-button type="danger" @click="batchApproval(2)">批量驳回</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card class="box-card" shadow="hover">
      <div slot="header" class="clearfix">
        <span>列表</span>
      </div>
      <div v-loading="loading" class="list-wrap">
        <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%;margin-top: 40px;"
          :row-class-name="setRowClass"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" :selectable="selectable" />
          <el-table-column prop="username" label="申请人" align="center">
            <template #default="{ row }">
              <span v-if="row.userinfo">{{ row.userinfo.name }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="project" label="项目名称" align="center">
            <template #default="{ row }">
              <span v-if="row.taskinfo">{{ row.taskinfo.name }}（{{ row.taskinfo.hall_orderNumber }}）</span>
            </template>
          </el-table-column>
          <el-table-column prop="project" label="项目/任务类型" align="center">
            <template #default="{ row }">
              <span v-if="row.taskinfo">{{ row.taskinfo.hallTypeTitle }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="statusInfo" label="审批状态" align="center">
            <template #default="{ row }">
              <el-tag :type="statusMap[row.statusInfo]">{{ row.statusInfo }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="createtime" label="申请时间" />
          <el-table-column align="center" prop="statustime" label="审核时间" />
          <el-table-column align="center" fixed="right" label="操作" width="300px">
            <template #default="{ row }">
              <el-button type="default" @click="showDetail(row)">查看详情</el-button>
              <el-button v-if="row.statusInfo === '审核中'" type="primary" size="small" @click="approvalApi(1, row.id)">通过
              </el-button>
              <el-button v-if="row.statusInfo === '审核中'" type="danger" size="small" @click="approvalApi(2, row.id)">驳回
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="page" style="text-align: center;margin-top: 20px;">
          <el-pagination
            v-show="total > 0"
            :total="total"
            :page.sync="page.page"
            :limit.sync="page.limit"
            @current-change="getList"
          />
        </div>
      </div>
    </el-card>

    <!-- 审批备注 -->
    <el-dialog
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :title="approvalTitle"
      :visible.sync="approvalDialogVisible"
      width="500px"
    >
      <el-form v-if="approvalDialogVisible" ref="approvalForm" :model="approvalForm" :rules="approvalFormRules">
        <el-form-item prop="remarks" label="驳回原因">
          <el-input v-model="approvalForm.remarks" type="textarea" placeholder="请输入驳回原因" />
        </el-form-item>
        <div style="text-align: center; margin-top: 30px">
          <el-button type="primary" @click="approval(2, approvalData)">确定</el-button>
          <el-button @click="closeApprovalDialogDetail">取消</el-button>
        </div>
      </el-form>
    </el-dialog>

    <!-- 申请详情 -->
    <el-dialog
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :title="detailTitle"
      :visible.sync="detailDialogVisible"
      width="560px"
    >
      <div v-if="detailDialogVisible" style="display: flex; justify-content: space-between; align-items: center">
        <div class="before" style="flex-grow: 1">
          <el-form>
            <h3 style="margin-top: 0;margin-bottom: 30px">修改前</h3>
            <el-form-item label="基础佣金">
              {{ detailData.beforInfo.bountymoney }}
            </el-form-item>
            <el-form-item label="加价佣金">
              {{ detailData.beforInfo.hall_money }}
            </el-form-item>
            <el-form-item label="实际佣金">
              <span v-if="Number(detailData.beforInfo.hall_user_money)">{{
                detailData.beforInfo.hall_user_money
              }}</span>
              <span v-else>
                {{ (Number(detailData.beforInfo.bountymoney) + Number(detailData.beforInfo.hall_money)).toFixed(2) }}
              </span>
            </el-form-item>
          </el-form>
        </div>
        <i style="flex-shrink: 0;padding-right: 50px;font-size: 30px">→</i>
        <div class="after" style="flex-grow: 1">
          <el-form>
            <h3 style="margin-top: 0;margin-bottom: 30px">修改后</h3>
            <el-form-item label="基础佣金">
              {{ detailData.bountymoney }}
            </el-form-item>
            <el-form-item label="加价佣金">
              {{ detailData.hall_money }}
            </el-form-item>
            <el-form-item label="实际佣金">
              <span v-if="Number(detailData.hall_user_money)">{{ detailData.hall_user_money }}</span>
              <span v-else>
                {{ (Number(detailData.bountymoney) + Number(detailData.hall_money)).toFixed(2) }}
              </span>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <div style="margin-top: 30px;text-align: center;">
        <el-button v-if="detailData.statusInfo === '审核中'" type="primary" size="small" @click="approvalApi(1, detailId)">通过
        </el-button>
        <el-button v-if="detailData.statusInfo === '审核中'" type="danger" size="small" @click="approvalApi(2, detailId)">驳回
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import dialog from '@/mixins/dialog'
import form from '@/mixins/form'
import selfRequest from '@/mixins/selfRequest'
import setRowClass from '@/mixins/setRowClass'

export default {
  name: 'Money',
  mixins: [dialog, selfRequest, form, setRowClass],
  data() {
    return {
      query: {
        username: '',
        name: '',
        createtime: '',
        statustime: '',
        status: ''
      },
      statusList: [
        { id: 0, name: '审核中' }, { id: 1, name: '已通过' }, { id: 2, name: '已驳回' }
      ],
      loading: false,
      tableData: [],
      selectRows: [],
      approvalTitle: '',
      approvalDialogVisible: false,
      approvalId: '',
      approvalData: {},
      approvalForm: {
        remarks: ''
      },
      approvalFormRules: {
        remarks: [{ required: true, message: '请输入驳回原因', trigger: 'blur' }]
      },
      approvalLoading: false,
      page: {
        page: 1,
        limit: 10
      },
      total: 0,
      statusMap: {
        '审核中': '',
        '已通过': 'success',
        '已驳回': 'danger'
      },
      detailTitle: '',
      detailDialogVisible: false,
      detailId: '',
      detailData: {}
    }
  },
  mounted() {
    this.getList(1)
  },
  methods: {
    // 过滤已审核
    selectable(row, index) {
      return row.statusInfo === '审核中'
    },

    // 获取列表
    getList(page) {
      if (page) this.page.page = page
      const data = { ...this.query, ...this.page }
      this.requestApi({
        apiName: 'updateMoneyList',
        data,
        loadingField: 'loading',
        success: ({ data }) => {
          this.total = data.count
          data.data ? this.tableData = data.data : []
        }
      })
    },

    // 表格选中
    handleSelectionChange(rows) {
      this.selectRows = rows
    },

    // 关闭弹窗
    closeApprovalDialogDetail() {
      this.changeDialogVisible({
        name: 'approvalDialogVisible',
        id: '',
        idField: 'approvalId',
        title: '驳回原因',
        titleField: 'approvalTitle',
        data: {},
        dataField: 'approvalData',
        visible: false
      })
    },

    // 审批
    approvalApi(type, id) {
      const typeMap = {
        1: '通过',
        2: '驳回'
      }
      this.showConfirm({
        tips: '确定要' + typeMap[type] + '该申请吗？',
        loadingField: 'approvalLoading',
        apiName: 'approvalUpdateMoney',
        data: {
          id,
          status: type
        },
        successMsg: '审核成功',
        errMsg: '审核失败',
        success: () => {
          this.getList()
          if (this.detailDialogVisible) {
            this.closeDetail()
          }
        }
      })
    },
    // 驳回意见
    approval() {
      this.validateForm('approvalForm', this.reject)
    },
    reject() {
      this.approvalApi(2, this.approvalData.id)
    },
    batchApproval(type) {
      if (this.selectRows.length === 0) {
        return this.$message.warning('请选择要操作的数据')
      }
      this.approvalApi(type, this.selectRows.map(item => item.id).join(','), true)
    },
    // 申请详情
    showDetail(data) {
      this.changeDialogVisible({
        name: 'detailDialogVisible',
        idField: 'detailId',
        id: data.id,
        dataField: 'detailData',
        data,
        title: '申请详情',
        titleField: 'detailTitle',
        visible: true
      })
    },
    closeDetail() {
      this.changeDialogVisible({
        name: 'detailDialogVisible',
        idField: 'detailId',
        id: '',
        dataField: 'detailData',
        data: {},
        title: '申请详情',
        titleField: 'detailTitle',
        visible: false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-table tr.task td {
  background: #e4e5e7;
}
</style>
