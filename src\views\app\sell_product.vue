<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>售卖产品</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="createView">新增分类</el-button>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="180"
        />
        <el-table-column
          prop="name"
          label="产品名称"
          width="180"
        />
        <el-table-column
          prop="name"
          label="分类名称"
          width="180"
        />
        <el-table-column
          prop="first_price"
          label="购买价格"
          width="180"
        />
        <el-table-column
          prop="renew_price"
          label="续费价格"
          width="180"
        />
        <el-table-column
          prop="indexnum"
          label="排序"
          width="180"
        />
        <el-table-column label="产品状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status" type="success" size="small" effect="dark">启用</el-tag>
            <el-tag v-else type="danger" effect="dark" size="small">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-link :underline="false" type="primary" @click="editView(scope.row)">编辑</el-link>
            <el-popconfirm
              title="确定删除当前记录吗？"
              @onConfirm="destoryApp(scope.row)"
            >
              <el-link slot="reference" :underline="false" type="danger">删除</el-link>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </el-card>

    <el-dialog
      title="售卖产品管理"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-form ref="form" :model="ruleForm" :rules="rules" label-width="120px">
        <el-form-item label="产品分类:" prop="category_id">
          <el-select v-model="ruleForm.category_id" placeholder="请选择">
            <el-option
              v-for="category in appCategory"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="产品:" prop="app_id">
          <el-select v-model="ruleForm.app_id" placeholder="请选择"  @change="getPackage(ruleForm.app_id)">
            <el-option label="选择产品" :value="0" />
            <el-option
              v-for="app in apps"
              :key="app.id"
              :label="app.name"
              :value="app.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="套餐:" prop="package_id">
          <el-select v-model="ruleForm.package_id" placeholder="请选择" @click.native="getPackage(ruleForm.app_id)">
            <el-option label="选择套餐" :value="0" />
            <el-option
              v-for="packagelist in packages"
              :key="packagelist.id"
              :label="packagelist.name"
              :value="packagelist.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="产品名称：" prop="name">
          <el-input v-model="ruleForm.name" />
        </el-form-item>
        <el-form-item label="购买价格：" prop="first_price">
          <el-col :span="10">
            <el-input-number v-model="ruleForm.first_price" :precision="2" :min="0" :max="999999999" label="请输入购买价格" />
          </el-col>
        </el-form-item>
        <el-form-item label="续费价格：" prop="renew_price">
          <el-col :span="10">
            <el-input-number v-model="ruleForm.renew_price" :precision="2" :min="0" :max="999999999" label="请输入续费价格" />
          </el-col>
        </el-form-item>
        <el-form-item label="交易状态:" prop="status" style="width: 70%">
          <el-checkbox v-model="ruleForm.is_buy" label="购买" :true-label="1" :false-label="0" ></el-checkbox>
          <el-checkbox v-model="ruleForm.is_renew" label="续费" :true-label="1" :false-label="0"></el-checkbox>
          <el-checkbox v-model="ruleForm.is_update" label="升级" :true-label="1" :false-label="0"></el-checkbox>
        </el-form-item>
        <el-form-item label="排序：" prop="indexnum">
          <el-input-number v-model="ruleForm.indexnum" :min="0" :max="999" controls-position="right" />
        </el-form-item>
        <el-form-item label="产品状态：" prop="status">
          <el-switch
            v-model="ruleForm.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item label="产品类型：" prop="status">
          <el-select v-model="ruleForm.type" placeholder="请选择产品类型">
            <el-option label="软件" :value="1" />
            <el-option label="硬件" :value="2" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="b_loading" @click="saveHandle">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import { list, store, selldelete, getpackageapps} from '../../api/sell'
import { parent_apps } from '@/api/app'
import { list as categorylist } from '@/api/category'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
export default {
  components: { Pagination },
  name: 'Category',
  data() {
    return {
      total: 0,
      listQuery: {
        page: 1,
        limit: 10
      },
      b_loading: false,
      loading: false,
      dialogVisible: false,
      tableData: [],
      appCategory:[],
      apps:[],
      packages:[],
      ruleForm: {},
      rules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择分类状态', trigger: 'change' }
        ]
      }
    }
  },

  created() {
    this.getList()
    this.getCategory()
    this.getApps()
  },
  methods: {
    getCategory() {
      categorylist().then(response => {
        this.appCategory = response.data
      })
    },
     getApps() {
      parent_apps({ id: this.$route.query.id }).then(response => {
        this.apps = response.data
      })
    },
    getList() {
      this.loading = true
      list(this.listQuery).then(response => {
        this.total = response.data.total
        this.tableData = response.data.data
        this.loading = false
      })
    },
    getPackage(id) {
      this.packages = [];
      if(id){
        getpackageapps({ app_id: id }).then(response => {
          this.packages = response.data
        })
      }

    },
    createView() {
      this.dialogVisible = true
      this.ruleForm = {
        status: 1,
        level: 0
      }
    },
    editView(row) {
      this.dialogVisible = true
      this.ruleForm = row
    },
    saveHandle() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.b_loading = true
          store(this.ruleForm).then(response => {
            this.dialogVisible = false
            this.b_loading = false
            this.$message.success('更新成功')
            this.getList()
          })
        } else {
          return false
        }
      })
    },
    destoryApp(row) {
      selldelete(row).then(() => {
        this.$message.success('删除成功')
        this.getList()
      })
    }
  }
}
</script>

