<template>
  <div id="auth">
    <div class="filter">
      <el-form :inline="true" :model="query" class="demo-form-inline" method="get">
        <el-form-item>
          <el-input v-model="query.company_name" placeholder="请输入公司名称" clearable />
        </el-form-item>
        <el-form-item>
          <el-input v-model="query.phone" placeholder="请输入手机号" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList(1)">搜索</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table" shadow="hover">
      <div v-loading="loading" class="list-wrap">
        <el-table :data="tableData" height="calc(100% - 52px)">
          <el-table-column prop="company_name" align="center" label="公司名称" />
          <el-table-column prop="phone" align="center" label="手机号" />
          <el-table-column label="权限" align="center">
            <template #default="{ row }">
              <el-button type="text" @click="handleAuth(row)">授权</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="page" style="text-align: center;margin-top: 20px;">
          <el-pagination
            v-show="total > 0"
            :total="total"
            :page.sync="page.page"
            :limit.sync="page.limit"
            style="display: flex; justify-content: center; align-items: center"
            @current-change="getList"
          />
        </div>
      </div>
    </div>

    <el-dialog
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :title="detailTitle"
      :visible.sync="detailDialogVisible"
      width="560px"
    >
      <el-input v-model="filterText" style="margin-bottom: 20px;" placeholder="输入关键字进行过滤" @change="treeFilter" />
      <div v-loading="treeLoading">
        <el-form label-width="130px">
          <el-form-item label="权限列表">
            <div style="max-height: 400px;overflow: auto">
              <el-tree
                ref="tree"
                :data="treeData"
                show-checkbox
                node-key="id"
                :default-expanded-keys="expanded"
                :default-checked-keys="checked"
                :props="defaultProps"
                :filter-node-method="filterNode"
              />
            </div>
          </el-form-item>
          <el-form-item label="是否集团版">
            <el-switch v-model="isGroup" :active-value="1" :inactive-value="0" />
          </el-form-item>
        </el-form>
      </div>

      <div style="margin-top: 30px; text-align: center;">
        <el-button type="primary" @click="checkAll">全选</el-button>
        <el-button type="primary" @click="toAuth">授权</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import selfRequest from '@/mixins/selfRequest'
import dialog from '@/mixins/dialog'

export default {
  name: 'ProductAuth',
  mixins: [selfRequest, dialog],
  data() {
    return {
      query: {
        company_name: '',
        phone: ''
      },
      page: {
        page: 1,
        limit: 10
      },
      total: 0,
      tableData: [],
      detailTitle: '',
      detailDialogVisible: false,
      detailId: '',
      detailData: {},
      treeData: [],
      expanded: [],
      checked: [],
      defaultProps: { value: 'id', label: 'label' },
      filterText: '',
      loading: false,
      treeLoading: false,

      // 20240129 是否集团版
      isGroup: false
    }
  },
  mounted() {
    this.getList(1)
    this.getAuthTree()
  },
  methods: {
    getChecked() {
      const checked = this.$refs.tree.getCheckedKeys()
      const halfChecked = this.$refs.tree.getHalfCheckedKeys()
      return [...checked, ...halfChecked]
    },
    // 获取公司列表
    getList(page) {
      if (page) {
        this.page.page = page
      }
      const params = { ...this.query, ...this.page }
      this.requestApi({
        apiName: 'getCompanyAuthList',
        data: params,
        loadingField: 'loading',
        success: ({ data }) => {
          this.total = data.count
          if (data.data) {
            this.tableData = data.data
          }
        }
      })
    },
    // 获取权限树
    getAuthTree() {
      this.requestApi({
        apiName: 'getCompanyAuthTree',
        data: {},
        loadingField: 'treeLoading',
        success: ({ data }) => {
          if (data.items) {
            this.treeData = data.items
          }
        }
      })
    },
    handleAuth(data) {
      this.changeDialogVisible({
        name: 'detailDialogVisible',
        idField: 'detailId',
        id: data.id,
        titleField: 'detailTitle',
        title: '授权',
        dataField: 'detailData',
        data,
        visible: true,
        callback: () => {
          if (this.detailData) {
            this.requestApi({
              apiName: 'getCompanyAuth',
              data: { id: this.detailData.id },
              loadingField: 'treeLoading',
              success: ({ data }) => {
                this.$nextTick(() => {
                  if (data.power) {
                    data.power.forEach((i) => {
                      const node = this.$refs.tree.getNode(parseInt(i))
                      if (node && node.isLeaf) {
                        this.$refs.tree.setChecked(node, true)
                      }
                    })
                  }
                  if ('account_type' in data) {
                    this.isGroup = data.account_type
                  }
                })
              }
            })
          }
        }
      })
    },
    treeFilter(val) {
      this.$refs.tree.filter(val)
    },
    filterNode(value, data) {
      if (!value) return true
      return data[this.defaultProps['label']].indexOf(value) !== -1
    },
    cancel() {
      this.detailDialogVisible = false
    },
    // 保存授权
    toAuth() {
      this.requestApi({
        apiName: 'saveCompanyAuth',
        data: {
          id: this.detailData.id,
          power: this.getChecked(),
          account_type: this.isGroup
        },
        loading: 'treeLoading',
        successMsg: '保存成功',
        errMsg: '保存失败',
        success: () => {
          this.changeDialogVisible({
            name: 'detailDialogVisible',
            idField: 'detailId',
            id: '',
            titleField: 'detailTitle',
            title: '授权',
            dataField: 'detailData',
            data: {},
            visible: false,
            callback: () => {
              this.getList()
            }
          })
        }
      })
    },
    // 全选
    checkAll() {
      this.$refs.tree.setCheckedNodes(this.treeData)
    }
  }
}
</script>

<style scoped lang="scss">
  #auth {
    height: 100%;

    .filter {
      border-bottom: 1px solid #e5e5e5;
    }

    .table {
      height: calc(100% - 59px - 20px);
      margin-top: 20px;

      .list-wrap {
        height: 100%;
      }
    }
  }
</style>
