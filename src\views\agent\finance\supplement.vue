<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <!--<span>补开订单</span>-->
        <el-button type="primary" style="float: right" @click="pushOpen">补开产品</el-button>
      </div>
      <div class="filter">
        <el-form :inline="true" :model="query" class="demo-form-inline">
          <el-form-item label="套餐订单号">
            <el-input v-model="query.no" placeholder="请输入搜索的订单号" clearable />
          </el-form-item>
          <el-form-item label="产品名称">
            <el-input v-model="query.product" placeholder="请输入产品名称" clearable />
          </el-form-item>
          <el-form-item label="审核状态">
            <el-select v-model="query.status" clearable>
              <el-option label="审核中" value="0" />
              <el-option label="已通过" value="1" />
              <el-option label="已拒绝" value="-1" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="userType === 1" label="代理商">
            <el-input v-model="query.agent" placeholder="请输入搜索的代理商" clearable />
          </el-form-item>
          <!-- <el-form-item label="用户名称">
            <el-input v-model="query.user" placeholder="请输入搜索的用户名称" clearable />
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="table">
        <el-table v-loading="tableLoading" :data="table" stripe style="width: 100%;" height="calc(100% - 96px)">
          <el-table-column label="套餐订单号" prop="number" width="160" />
          <el-table-column label="套餐名称" prop="finance" width="160" />
          <el-table-column label="补开产品">
            <template slot-scope="scope">
              <span v-if="scope.row.product">{{ scope.row.product }}</span>
              <span v-else style="color: red">产品已删除</span>
            </template>
          </el-table-column>
          <el-table-column label="域名" prop="domain" width="160" />
          <el-table-column v-if="userType === 1" label="代理商" width="160">
            <template slot-scope="scope">
              <span v-if="scope.row.agent">{{ scope.row.agent }}</span>
              <span v-else style="color: red">代理商已删除</span>
            </template>
          </el-table-column>
          <el-table-column label="用户名称" width="160">
            <template slot-scope="scope">
              <span v-if="scope.row.customer">{{ scope.row.customer }}</span>
              <span v-else style="color: red">客户已删除</span>
            </template>
          </el-table-column>
          <el-table-column label="原因" prop="content" width="160" show-overflow-tooltip />
          <el-table-column label="开始时间" prop="created_at" width="160" />
          <el-table-column label="到期时间" prop="expired_at" width="160" />
          <el-table-column label="处理时间" prop="updated_at" width="160" />
          <el-table-column align="center" label="审核状态" width="100">
            <template slot-scope="scope">
              <el-link v-if="scope.row.status === 0" type="warning" :underline="false" @click="auth(scope.row)">审核中</el-link>
              <el-link v-if="scope.row.status === 1" type="success" :underline="false">已通过</el-link>
              <el-link v-if="scope.row.status === -1" type="danger" :underline="false">已拒绝</el-link>
            </template>
          </el-table-column>
          <el-table-column label="审批人" prop="admin" />
          <el-table-column label="审批内容" show-overflow-tooltip width="200" prop="audit" />
          <el-table-column v-if="userType===2" label="操作" width="80">
            <template slot-scope="scope">
              <el-link v-if="scope.row.status !== 1" type="danger" :underline="false" @click="dialogDestory(scope.row.id)">删除</el-link>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="query.page"
          :limit.sync="query.perPage"
          @pagination="getList"
        />
      </div>
    </el-card>
    <el-dialog
      title="审核"
      :visible.sync="dialogFormVisible"
      width="35%"
      top="10vh"
    >
      <el-form ref="form" :model="form" label-width="120px" :rules="rules">
        <el-form-item label="审核状态" style="width: 70%">
          <el-select v-model="form.status" placeholder="请选择">
            <el-option label="审核通过" :value="1" />
            <el-option label="审核驳回" :value="-1" />
          </el-select>
        </el-form-item>
        <el-form-item label="审核内容" prop="content" style="width: 70%">
          <el-input
            v-model="form.audit"
            type="textarea"
            placeholder="请输入内容"
            maxlength="30"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="dialogConfirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { index, destroy, auth } from '@/api/agent/supplement'
import Pagination from '@/components/Pagination'
export default {
  name: 'Supplement',
  components: { Pagination },
  data() {
    return {
      tableLoading: false,
      dialogLoading: false,
      dialogFormVisible: false,
      form: {},
      query: {
        page: 1,
        perPage: 10
      },
      rules: {
        customer_id: [{ required: true, message: '用户必选', trigger: 'blur' }],
        audit: [{ required: true, message: '审核理由必填', trigger: 'blur' }],
        status: [{ required: true, message: '状态必选', trigger: 'blur' }]
      },
      total: 0,
      table: []
    }
  },
  computed: {
    // 别名处理防止冲突
    ...mapGetters({
      userType: 'type'
    })
  },
  created() {
    this.getList()
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.list-wrap > .el-card > .el-card__body')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  methods: {
    getList() {
      this.tableLoading = true
      index(this.query).then(res => {
        if (res.code === 200 && res.data) {
          this.table = res.data.list
          this.total = res.data.meta.total
          this.query.page = res.data.meta.page
          this.query.perPage = res.data.meta.perPage
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    // 删除订单
    dialogDestory(id) {
      this.$confirm('确定要删除订单嘛?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        destroy({ id: id }).then(res => {
          this.$message.success('操作成功')
          this.getList()
        }).catch(error => {
          console.log(error)
        })
      }).catch(error => {
        console.log(error)
      })
    },
    auth(row) {
      if (this.userType === 1) {
        this.form.id = row.id
        this.dialogFormVisible = true
      }
    },
    dialogConfirm() {
      this.dialogLoading = true
      auth(this.form).then(res => {
        this.dialogLoading = false
        this.dialogFormVisible = false
        this.$message.success(res.message)
        this.getList()
      }).catch(() => {
        this.dialogLoading = false
      })
    },
    pushOpen() {
      this.$router.push({
        name: 'agentFinanceReopen'
      })
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap {
  height: 100%;
  .box-card {
    height: 100%;
    ::v-deep .el-card__header {
      padding-top: 0;
    }
    ::v-deep .el-card__body {
      padding: 0;
      margin-top: 20px;
      height: calc(100% - 59px);
      overflow: auto;
    }
  }
  .filter {
    border-bottom: 1px solid #E5E5E5;
  }
  .statistic{
    margin-top: 20px;

    /* 方案2：强调数字风格 */
    font-size: 14px;
    color: #333;
    span {
      color: #409EFF;
      font-weight: 500;
      font-size: 16px;
    }

    /* 通用增强效果 */
    transition: all 0.3s;
    font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
  }

  .table {
    margin-top: 20px;
    min-height: 200px;
  }
}
</style>
