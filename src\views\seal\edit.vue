<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>编辑</span>
      </div>
      <el-col :span="12">
        <version-form :type="true" :form="formData" @transfer="sonSave" />
      </el-col>
    </el-card>
  </div>
</template>
<script>
import VersionForm from './components/version-form'
import { store, detail } from '@/api/seal'
import { TextToCode } from 'element-china-area-data'
export default {
  name: 'Edit',
  components: { VersionForm },
  data() {
    return {
      formData: {
        policy: {
          area: []
        },
        md5var: ''
      }
    }
  },
  created() {
    this.getDetail()
  },
  methods: {
    getDetail() {
      detail(this.$route.query).then(response => {
        this.formData = response.data
        // 处理文件上传信息
        this.formData.fileList = [{
          name: this.formData.download_url,
          url: this.formData.download_url
        }]
        // 处理城市信息
        if (this.formData.policy.area.length > 0) {
          this.formData.selectedOptions = [
            TextToCode[this.formData.policy.area[0]].code,
            TextToCode[this.formData.policy.area[0]][this.formData.policy.area[1]].code
          ]
        }
        console.log(this.formData)
        this.$forceUpdate()
      })
    },
    sonSave(form) {
      store(form).then(response => {
        this.$message.success('更新成功')
        this.$router.go(-1)
      })
    }
  }
}
</script>

<style scoped>

</style>
