export const resultUpdateFormSettings = [
  {
    label: '特征标签',
    prop: 'title',
    type: 'input',
    settings: {
      placeholder: '请输入特征标签，用顿号分隔'
    }
  },
  {
    label: '关键词',
    prop: 'keyword',
    type: 'input',
    settings: {
      placeholder: '请输入关键词，用顿号分隔'
    }
  },
  {
    label: '通用注释',
    prop: 'currency',
    type: 'textarea',
    settings: {
      placeholder: '请输入通用注释'
    }
  },
  {
    label: '特殊注释A',
    prop: 'ABannotation',
    type: 'textarea',
    settings: {
      placeholder: '请输入特殊注释A'
    }
  },
  {
    label: '推荐职位A',
    prop: 'ABposition',
    type: 'textarea',
    settings: {
      placeholder: '请输入推荐职位A'
    }
  },
  {
    label: '特殊注释B',
    prop: 'BAannotation',
    type: 'textarea',
    settings: {
      placeholder: '请输入特殊注释B'
    }
  },
  {
    label: '推荐职位B',
    prop: 'BAposition',
    type: 'textarea',
    settings: {
      placeholder: '请输入推荐职位B'
    }
  }
]

export const resultUpdateFormData = {
  title: '',
  keyword: '',
  currency: '',
  ABannotation: '',
  ABposition: '',
  BAannotation: '',
  BAposition: ''
}

export const resultUpdateFormRules = {
  title: [
    { required: true, message: '请输入特征标签', trigger: 'blur' }
  ],
  keyword: [
    { required: true, message: '请输入关键词', trigger: 'blur' }
  ],
  currency: [
    { required: true, message: '请输入通用注释', trigger: 'blur' }
  ],
  ABannotation: [
    { required: true, message: '请输入特殊注释A', trigger: 'blur' }
  ],
  ABposition: [
    { required: true, message: '请输入推荐职位A', trigger: 'blur' }
  ],
  BAannotation: [
    { required: true, message: '请输入特殊注释B', trigger: 'blur' }
  ],
  BAposition: [
    { required: true, message: '请输入推荐职位B', trigger: 'blur' }
  ]
}
