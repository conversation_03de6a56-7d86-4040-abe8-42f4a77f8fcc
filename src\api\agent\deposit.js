import request from '@/utils/requestAgent'

export function index(data) {
  return request({
    url: '/agent/deposit/index',
    method: 'post',
    data: data
  })
}

export function info() {
  return request({
    url: '/agent/deposit/info',
    method: 'post'
  })
}

export function recharge(data) {
  return request({
    url: '/agent/deposit/recharge',
    method: 'post',
    data: data
  })
}

export function extract(data) {
  return request({
    url: '/agent/deposit/extract',
    method: 'post',
    data: data
  })
}

export function audit(data) {
  return request({
    url: '/agent/deposit/audit',
    method: 'post',
    data: data
  })
}

export function destroy(data) {
  return request({
    url: '/agent/deposit/destroy',
    method: 'post',
    data: data
  })
}

// history
export function historyIndex(data) {
  return request({
    url: '/agent/deposit/history/index',
    method: 'post',
    data: data
  })
}

// 余额转移
export function balanceTransfer(data) {
  return request({
    url: '/agent/deposit/balanceTransfer/index',
    method: 'post',
    data: data
  })
}

export function balanceTransferStore(data) {
  return request({
    url: '/agent/deposit/balanceTransfer/store',
    method: 'post',
    data: data
  })
}

export function balanceTransferAudit(data) {
  return request({
    url: '/agent/deposit/balanceTransfer/audit',
    method: 'post',
    data: data
  })
}

export function balanceTransferCancel(data) {
  return request({
    url: '/agent/deposit/balanceTransfer/cancel',
    method: 'post',
    data: data
  })
}

export function deductionStore(data) {
  return request({
    url: '/agent/deposit/deduction/store',
    method: 'post',
    data: data
  })
}

export function deductionCancel(data) {
  return request({
    url: '/agent/deposit/deduction/cancel',
    method: 'post',
    data: data
  })
}

export function quotaDetail(data) {
  return request({
    url: '/agent/deposit/quotaDetail',
    method: 'get'
  })
}
