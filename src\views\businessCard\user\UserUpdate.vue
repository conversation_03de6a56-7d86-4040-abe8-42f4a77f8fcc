<script>
import update from '@/views/task/mixins/update'
import SelfFormTemp from '@/views/task/components/form/SelfFormTemp.vue'

export default {
  name: 'UserUpdate',
  components: { SelfFormTemp },
  mixins: [update],
  data() {
    return {
      formData: {
        name: '',
        phone: '',
        company: '',
        position: '',
        social: '',
        personal: '',
        wechat: '',
        email: '',
        address: '',
        desc: '',
        url: '',
        tel: '',
        companyImg: [],
        companyVideo: [],
        status: '',
        license: [],
        proof: []
      },
      filters: [
        {
          label: '姓名',
          prop: 'name',
          type: 'input'
        },
        {
          label: '手机号',
          prop: 'phone',
          type: 'input'
        },
        {
          label: '公司',
          prop: 'company',
          type: 'input'
        },
        {
          label: '职位',
          prop: 'position',
          type: 'input'
        },
        {
          label: '社交',
          prop: 'social',
          type: 'input'
        },
        {
          label: '个人荣誉',
          prop: 'personal',
          type: 'input'
        },
        {
          label: '微信',
          prop: 'wechat',
          type: 'input'
        },
        {
          label: '微信二维码',
          prop: 'wechat',
          type: 'upload',
          settings: {
            accept: 'image/*',
            action: 'https://img.yzcdn.cn/vant/cat.jpeg'
          }
        },
        {
          label: '邮箱',
          prop: 'email',
          type: 'input'
        },
        {
          label: '地址',
          prop: 'address',
          type: 'input'
        },
        {
          label: '简介',
          prop: 'desc',
          type: 'input'
        },
        {
          label: '公司网址',
          prop: 'url',
          type: 'input'
        },
        {
          label: '公司电话',
          prop: 'tel',
          type: 'input'
        },
        {
          label: '公司图片',
          prop: 'companyImg',
          type: 'upload',
          settings: {
            accept: 'image/*',
            action: 'https://img.yzcdn.cn/vant/cat.jpeg'
          }
        },
        {
          label: '公司视频',
          prop: 'companyVideo',
          type: 'upload',
          settings: {
            accept: 'video/*',
            action: 'https://img.yzcdn.cn/vant/cat.jpeg'
          }
        },
        {
          label: '状态',
          prop: 'status',
          type: 'select',
          options: [
            {
              label: '启用',
              value: '1'
            },
            {
              label: '禁用',
              value: '0'
            }
          ]
        },
        {
          label: '营业执照',
          prop: 'license',
          type: 'upload',
          settings: {
            accept: 'image/*',
            action: 'https://img.yzcdn.cn/vant/cat.jpeg'
          }
        },
        {
          label: '在职证明',
          prop: 'proof',
          type: 'upload',
          settings: {
            accept: 'image/*',
            action: 'https://img.yzcdn.cn/vant/cat.jpeg'
          }
        }
      ]
    }
  }
}
</script>

<template>
  <el-dialog :close-on-click-modal="false" :title="'名片管理' + title" width="50vw" :visible.sync="_visible">
    <SelfFormTemp ref="formWrap" :form-data.sync="formData" :filters="filters" :inline="false" label-width="130px" />
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
