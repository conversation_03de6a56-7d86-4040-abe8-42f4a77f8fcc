<template>
  <div id="area">
    <el-card shadow="never" class="border-transparent">
      <div class="el-row--flex is-justify-space-between is-align-middle">
        <span>活跃用户地域分布</span>
        <el-date-picker
          v-model="date"
          size="small"
          format="yyyy-MM"
          value-format="yyyy-MM"
          type="month"
          placeholder="请选择月份"
          @change="handleGetList(1)"
        />
      </div>
      <div id="area">
        <el-row style="padding-top: 30px">
          <el-col :md="12" :sm="24" :xs="24" class="s-w-full">
            <map-chart height="400px" :chart-data="chartData" />
          </el-col>
          <el-col :md="8" :sm="24" :xs="24" class="s-w-full">
            <el-table stripe style="width: 100" :data="tableData">
              <el-table-column
                type="index"
                label="序号"
                align="center"
                width="50"
              />
              <el-table-column
                prop="name"
                label="省份"
                align="center"
              />
              <el-table-column
                prop="value"
                label="启动次数"
                align="center"
              />
            </el-table>
            <div class="page" style="text-align: center">
              <el-pagination
                :current-page="page.page"
                :page-size="page.limit"
                layout="prev, pager, next"
                :total="total"
                @current-change="handleCurrentChange"
              />
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
/* eslint-disable */
import MapChart from './MapChart.vue';
import {getUserArea} from "@/api/dashboard";

export default {
  name: "AreaCard",
  components: { MapChart },
  data() {
    return {
      date: "",
      chartData: [],
      tableData: [],
      legend: ["活跃用户地域分布"],
      colorList: ["#fb726c"],
      total: 0,
      avarage: 0,
      grid: {
        left: 30,
        right: 30,
        bottom: 20,
        top: 30,
        containLabel: true
      },
      page: {
        page: 1,
        limit: 5
      }
    };
  },
  methods: {
    handleCurrentChange(val){
      this.page.page = val;
      this.tableData = this.chartData.slice((this.page.page - 1) * this.page.limit, this.page.page * this.page.limit);
    },
    handleGetList(page = this.page.page){
      if(page){
        this.page.page = page;
      }
      if(!this.date){
        this.date = new Date().getFullYear().toString() + '-' + (new Date().getMonth() + 1).toString().padStart(2, '0');
      }
      getUserArea({date: this.date}).then(res => {
        if (res.code === 200 && res.data){
          this.chartData = [];
          if(res.data.title && res.data.arr){
            res.data.title.forEach((v, i) => {
              if(v){
                this.chartData.push({
                  name: v[0],
                  value: res.data.arr[i] || 0
                })
              }
            });
          }
          this.total = this.chartData.length;
          this.tableData = this.chartData.slice((this.page.page - 1) * this.page.limit, this.page.page * this.page.limit);
        }
      })
    }
  },
  mounted() {
    this.handleGetList(1);
  }
};
</script>

<style lang="scss" scoped>
.bottom-0{
  margin-bottom: 0;
}
.float-right{
  float: right;
  margin-top: 10px;
  ::v-deep.el-form-item--medium .el-form-item__content{
    display: inline-block;
  }
}
.border-transparent{
  border: none;
}
.page{
  margin-top: 20px;
}
</style>
