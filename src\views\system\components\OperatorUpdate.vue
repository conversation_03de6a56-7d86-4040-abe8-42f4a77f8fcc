<script>
import update from '@/views/task/mixins/update'
import SelfFormTemp from '@/views/task/components/form/SelfFormTemp.vue'

export default {
  name: 'OperatorUpdate',
  components: { SelfFormTemp },
  mixins: [update],
  data() {
    return {
      formData: {
        name: '',
        desc: '',
        cover: [],
        file: [],
        status: 1
      },
      rules: {
        name: [
          { required: true, message: '请输入手册名称', trigger: 'blur' }
        ],
        desc: [
          { required: true, message: '请输入手册简介', trigger: 'blur' }
        ],
        cover: [
          { required: true, message: '请上传手册封面', trigger: 'change' }
        ],
        file: [
          { required: true, message: '请上传手册文件', trigger: 'change' }
        ]
      },
      filters: [
        {
          label: '手册名称',
          prop: 'name',
          type: 'input'
        },
        {
          label: '手册简介',
          prop: 'desc',
          type: 'textarea'
        },
        {
          label: '手册封面',
          prop: 'cover',
          type: 'upload',
          settings: {
            action: '/upload',
            accept: 'image/*'
          }
        },
        {
          label: '手册文件',
          prop: 'file',
          type: 'upload',
          settings: {
            action: '/upload',
            accept: 'application/pdf'
          }
        },
        {
          label: '手册状态',
          prop: 'status',
          type: 'switch'
        }
      ]
    }
  }
}
</script>

<template>
  <el-dialog :close-on-click-modal="false" :title="title + '操作手册'" :visible.sync="_visible">
    <SelfFormTemp ref="formWrap" :form-data.sync="formData" :inline="false" :rules="rules" :filters="filters" label-width="100px" />
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
