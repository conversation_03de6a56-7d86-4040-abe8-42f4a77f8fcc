<template>
  <div class="list-wrap">
    <el-card v-loading="loading" class="box-card" style="border: none" shadow="never">
      <div slot="header" class="clearfix">
        <!--<span>用户认证信息</span>-->
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
        <el-button style="float: right; padding: 3px 0" type="text" @click="saveHandle">保存</el-button>
      </div>
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="150px" class="demo-ruleForm">
        <el-row>
          <el-col v-loading="licenseloading" :span="12" style="max-width: 400px">
            <!--<el-form-item label="企业logo：" prop="logo">-->
            <!--<upload-img-->
            <!--:url.sync="ruleForm.logo"-->
            <!--:size="300"-->
            <!--accept="image/png, image/jpeg"-->
            <!--tip="请选择不大于300K的JPG、PNG、图片"-->
            <!--@success="logoSuccess"-->
            <!--/>-->
            <!--</el-form-item>-->
            <!--<el-divider content-position="left">工商信息</el-divider>-->
            <el-divider content-position="left">工商执照认证</el-divider>
            <el-form-item label="营业执照：" prop="license">
              <upload-img
                :url.sync="ruleForm.license"
                :size="2000"
                accept="image/png, image/jpeg"
                tip="请选择不大于2M的JPG、PNG、图片"
                @success="licenseSuccess"
              />
            </el-form-item>
            <el-form-item label="营业执照号：" prop="license_number">
              <el-input v-model="ruleForm.license_number" />
            </el-form-item>
            <el-form-item label="注册资本：" prop="capital">
              <el-input v-model="ruleForm.capital" />
            </el-form-item>
            <el-form-item label="企业类型：" prop="person">
              <el-cascader
                v-model="ruleForm.person"
                :options="persons"
                :props="{ value:'id',label:'name' }"
              />
            </el-form-item>
            <el-form-item label="证件类型：" prop="person">
              <el-select v-model="ruleForm.icType">
                <el-option value="1" label="组织机构代码证" />
                <el-option value="2" label="营业执照" />
                <el-option value="3" label="登记证书" />
                <el-option value="4" label="单位证明" />
                <el-option value="5" label="办事机构注册证" />
                <el-option value="6" label="身份证" />
                <el-option value="7" label="户口本" />
                <el-option value="8" label="军官证" />
                <el-option value="9" label="台港澳通行证" />
                <el-option value="10" label="护照" />
                <el-option value="11" label="台湾通行证" />
                <el-option value="12" label="临时居民身份证" />
                <el-option value="13" label="武装警察身份证件" />
                <el-option value="14" label="其他" />
                <el-option value="15" label="三证合一营业执照" />
                <el-option value="16" label="税务登记证" />
                <el-option value="17" label="事业单位法人证书" />
                <el-option value="18" label="社会团体法人证书" />
                <el-option value="19" label="军队代码" />
                <el-option value="20" label="有偿服务许可证" />
                <el-option value="21" label="统—社会信用代码证书" />
                <el-option value="22" label="宗教活动场所登记证" />
                <el-option value="23" label="民办非企业单位登记证书" />
                <el-option value="24" label="基金会法人登记证书" />
                <el-option value="25" label="律师事务所执业许可证" />
              </el-select>
            </el-form-item>
            <el-form-item label="登记机关：" prop="authority">
              <el-input v-model="ruleForm.authority" />
            </el-form-item>
            <el-form-item label="成立日期：" prop="registered">
              <el-date-picker
                v-model="ruleForm.registered"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
                clearable
              />
            </el-form-item>
            <el-form-item label="经营范围：" prop="scope">
              <el-input
                v-model="ruleForm.scope"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
          </el-col>
          <el-col v-loading="cardloading" :span="12" style="margin-left: 50px">
            <el-divider content-position="left">法人认证</el-divider>
            <el-row>
              <el-row>
                <el-col :span="11">
                  <el-form-item label="身份证正面：" prop="card_a">
                    <upload-img
                      :url.sync="ruleForm.card_a"
                      :size="300"
                      accept="image/png, image/jpeg"
                      tip="请选择不大于300K的JPG、PNG、图片"
                      @success="cardASuccess"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11" style="margin-left: 20px">
                  <el-form-item label="身份证背面：" prop="card_b">
                    <upload-img
                      :url.sync="ruleForm.card_b"
                      :size="300"
                      accept="image/png, image/jpeg"
                      tip="请选择不大于300K的JPG、PNG、图片"
                      @success="cardBSuccess"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="法人姓名：" prop="legal_person">
                <el-input v-model="ruleForm.legal_person" />
              </el-form-item>
              <el-form-item label="身份证号码：" prop="legal_card">
                <el-input v-model="ruleForm.legal_card" />
              </el-form-item>
              <el-form-item label="法人手机号码：" prop="legal_phone">
                <el-input v-model="ruleForm.legal_phone" />
              </el-form-item>
            </el-row>
            <el-divider content-position="left">负责人信息</el-divider>
            <el-form-item label="负责人姓名：" prop="responsible_name">
              <el-input v-model="ruleForm.responsible_name" />
            </el-form-item>
            <el-form-item label="负责人手机号码：" prop="responsible_phone">
              <el-input v-model="ruleForm.responsible_phone" />
            </el-form-item>
            <el-form-item label="负责人邮箱：" prop="responsible_email">
              <el-input v-model="ruleForm.responsible_email" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import axios from 'axios'
import UploadImg from '@/components/Upload/uploadImg'
import { options, auth, detail, huaweiImg } from '@/api/agent/customers'
export default {
  name: 'CustomerForm',
  components: { UploadImg },
  data() {
    return {
      loading: false,
      cardloading: false,
      licenseloading: false,
      persons: [],
      yun_api: '',
      fileid: 0,
      action: process.env.VUE_APP_API_API_PROXY + '/api/upload',
      ruleForm: { id: '', logo: '', license_certificate_path: '', legal_person_certificate_a: '', legal_person_certificate_b: '' },
      rules: {
        logo: [{ required: true, message: '必选', trigger: 'blur' }],
        capital: [{ required: true, message: '必选', trigger: 'blur' }],
        person: [{ required: true, message: '必选', trigger: 'blur' }],
        authority: [{ required: false, message: '必填', trigger: 'blur' }],
        registered: [{ required: true, message: '必选', trigger: 'blur' }],
        scope: [{ required: true, message: '必填', trigger: 'blur' }],
        license: [{ required: true, message: '必传', trigger: 'blur' }],
        license_number: [{ required: true, message: '必填', trigger: 'change' }],
        legal_person: [{ required: true, message: '必填', trigger: 'blur' }],
        legal_card: [{ required: true, message: '必填', trigger: 'blur' }],
        legal_phone: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        card_a: [{ required: true, message: '必传', trigger: ['blur', 'change'] }],
        card_b: [{ required: true, message: '必传', trigger: ['blur', 'change'] }],
        responsible_name: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        responsible_phone: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        responsible_email: [{ required: true, message: '必填', trigger: ['blur', 'change'] }]
      }
    }
  },
  created() {
    this.yun_api = process.env.VUE_APP_API_API_PROXY + '/api/upload'
    this.ruleForm.id = this.$route.query['id']
    this.getOptions()
    if (this.$route.query.id) {
      this.getDetail()
    }
  },
  methods: {
    getDetail() {
      this.loading = true
      detail({ id: this.$route.query.id }).then(res => {
        this.loading = false
        this.ruleForm = res.data
      }).catch(() => {
        this.loading = false
      })
    },
    getOptions() {
      options({ key: 'ORGANIZATION_TYPE' }).then(res => {
        var temp = res.data['ORGANIZATION_TYPE']
        this.removeChildren(temp)
        this.persons = temp
      })
    },
    removeChildren(temp) {
      var that = this
      temp.forEach(function(item) {
        if (item['children'].length === 0) {
          delete (item['children'])
        } else {
          that.removeChildren(item['children'])
        }
      })
    },
    logoSuccess(file) {
      var that = this
      this.getYunImage(file, function(id) {
        that.ruleForm['logo_id'] = id
      })
    },
    licenseSuccess(file) {
      this.licenseloading = true
      var fileid = file.response.data.id
      var that = this
      this.getYunImage(file, function(id) {
        that.ruleForm['license_certificate_path'] = id
        huaweiImg({ id: fileid, type: 2 }).then(res => {
          if (res.code) {
            if (res.data) {
              that.ruleForm['license_number'] = res.data.registration_number
              that.ruleForm['capital'] = res.data.registered_capital
              that.ruleForm['registered'] = res.data.found_date
              that.ruleForm['scope'] = res.data.business_scope
              that.ruleForm['responsible_name'] = res.data.legal_representative
              that.licenseloading = false
            }
          }
          that.licenseloading = false
        }).catch(() => {
          this.licenseloading = false
        })
      })
    },
    cardASuccess(file) {
      console.log('file', file)
      this.cardloading = true
      var fileid = file.response.data.id
      var that = this
      this.getYunImage(file, function(id) {
        that.ruleForm['legal_person_certificate_a'] = id
        huaweiImg({ id: fileid, type: 1 }).then(res => {
          if (res.code) {
            if (res.data) {
              that.ruleForm['legal_person'] = res.data.name
              that.ruleForm['legal_card'] = res.data.number
            }
          }
          that.cardloading = false
        }).catch(() => {
          this.cardloading = false
        })
      })
    },
    cardBSuccess(file) {
      var that = this
      this.getYunImage(file, function(id) {
        that.ruleForm['legal_person_certificate_b'] = id
      })
    },
    getYunImage(file, callback) {
      const formData = new FormData()
      formData.append('files[]', file.raw)
      axios.post(this.yun_api, formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      }).then(res => {
        callback(res.data.data[0])
      })
    },
    saveHandle() {
      // console.log(this.ruleForm)
      this.$refs['ruleForm'].validate((valida) => {
        if (valida) {
          this.loading = true
          auth(this.ruleForm).then(res => {
            this.loading = false
            if (res.code === 200) {
              this.$message.success('认证信息提交成功')
              this.$router.push({
                name: 'CustomersListRouter'
              })
            }
          }).catch(() => {
            this.loading = false
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped>
  .el-input{
    width: 250px;
  }
  ::v-deep.upload-img{
    width: 400px;
  }
  .el-divider--horizontal{
    width: 50%;
  }
</style>
