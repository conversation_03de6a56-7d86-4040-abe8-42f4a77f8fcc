<template>
  <div class="list-wrap">
    <div class="filter">
      <el-form :inline="true" :model="listQuery" class="demo-form-inline" size="small">
        <el-form-item label="站点名称：">
          <el-input v-model="listQuery.web_name" placeholder="站点名称" clearable />
        </el-form-item>
        <el-form-item label="素材链接：">
          <el-input v-model="listQuery.img_url" placeholder="素材链接" clearable />
        </el-form-item>
        <el-form-item label="操作时间：">
          <el-date-picker
            v-model="listQuery.times"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getList">过滤</el-button>
          <!-- <el-button type="primary" :loading="downloadLoading" icon="el-icon-download" @click="exportExcel">导出</el-button> -->
        </el-form-item>
        <switch-package-type @switchPackageCallback="switchPackageCallback" />
        <el-form-item>
          <el-button type="primary" icon="el-icon-refresh-right" @click="getList">刷新</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table mt20">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        height="calc(100% - 96px)"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="75"
        />
        <el-table-column
          prop="img_title"
          label="文件名称"
          width="200"
        />
        <el-table-column
          prop="web_name"
          label="站点名称"
          width="200"
        />
        <el-table-column
          prop="user_name"
          label="操作人"
          width="100"
        />
        <el-table-column label="IP地址" width="135">
          <template slot-scope="scope">
            <el-tag effect="dark" size="small">{{ scope.row.user_ip }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作类型" width="80">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.f_type === 1" effect="dark" size="small">文件</el-tag>
            <el-tag v-if="scope.row.f_type === 2" effect="dark" type="success" size="small">文件夹</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="文件类型" width="80">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.type === 1" effect="dark" size="small" type="success">图片</el-tag>
            <el-tag v-if="scope.row.type === 2" effect="dark" type="info" size="small">视频</el-tag>
            <el-tag v-if="scope.row.type === 3" effect="dark" type="warning" size="small">文件</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="img_url"
          label="素材链接"
          width="300"
        />
        <el-table-column
          prop="create_time"
          label="上传时间"
          width="180"
        />
        <el-table-column
          prop="del_time"
          label="操作时间"
          width="180"
        />
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>
  </div>
</template>

<script>
import { parseTime } from '@/utils'
import { bucketDel } from '@/api/jzt/sites'
import Pagination from '@/components/Pagination'
import SwitchPackageType from '@/views/jzt/components/SwitchPackageType.vue' // secondary package based on el-pagination
export default {
  name: 'Material',
  components: { SwitchPackageType, Pagination },
  data() {
    return {
      tableData: [],
      loading: false,
      listQuery: {
        page: 1,
        limit: 10
      },
      total: 0,
      /* 导出相关 */
      downloadLoading: false,
      autoWidth: true,
      bookType: 'xlsx',
      filename: '素材库操作记录'
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.tab-content')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      console.log(tabContentHeight, filterHeight)
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  methods: {
    switchPackageCallback() {
      this.getList()
    },
    getList() {
      this.listQuery.all = false
      this.loading = true
      bucketDel(this.listQuery).then(response => {
        const list = response.data.data
        const page = this.listQuery.page
        list.forEach((item, index) => {
          item.id = index + ((page - 1) * 10) + 1
        })
        this.tableData = list
        this.total = response.data.total
        this.loading = false
      })
    },
    exportExcel() {
      this.listQuery.all = true
      this.downloadLoading = true
      bucketDel(this.listQuery).then(response => {
        import('@/vendor/Export2Excel').then(excel => {
          const tHeader = ['ID', '公司名称', '服务器ip', '开通时间', '到期时间', '域名', '审核时间']
          const filterVal = ['id', 'company_name', 'server_ip', 'create_time', 'expiration_at', 'domain', 'type']
          const list = response.data
          list.forEach((item, index) => {
            item.id = index + 1
          })
          const data = this.formatJson(filterVal, list)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.filename,
            autoWidth: this.autoWidth,
            bookType: this.bookType
          })
          this.downloadLoading = false
        })
      })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        if (j === 'create_time') {
          return parseTime(v[j])
        } else if (j === 'type') {
          switch (v.type) {
            case 1:
              return v.audit_at
            default:
              return v.create_time
          }
        } else {
          return v[j]
        }
      }))
    }
  }
}
</script>

<style scoped>
  .el-dropdown-link {
    cursor: pointer;
    color: #409EFF;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
</style>

<style lang="scss" scoped>
.list-wrap{
  .filter{
    border-bottom: 1px solid #e5e5e5;
  }
  .table{
    min-height: 200px;
  }
}
</style>
