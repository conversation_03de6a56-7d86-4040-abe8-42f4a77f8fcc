<script>
import update from '@/views/task/mixins/update'
import { company_check, company_detail } from '@/api/agent/certificate'

export default {
  name: 'Detail',
  mixins: [update],
  data() {
    return {
      id: null,
      form: {
        baseInfo: {},
        authInfo: {},
        reason: ''
      },
      selected: {},
      trades:{},
      persons:{},
      provinceAndCityData:{},
      loading: false,
      disabled: false,
      rulesFrom: {
        status: [
          { required: true, message: '审核选项不能为空', trigger: 'blur' }
        ],
        reason: [
          { required: true, message: '请填写原因', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    _visible(val) {
      if (val) {
        this.getDetail(this.data.unique_id)
      }
    }
  },
  methods: {
    getDetail(id) {
      this.loading = true
      company_detail({ id: id }).then(response => {
        this.form = response.data
        this.disabled = response.data.status !== 0
        if (!this.disabled) {
          this.form.status = 1
        }

        this.loading = false
      }).catch(() => {
        this.loading = false
        // this.goBack()
      })
    },
    removeChildren(temp) {
      var that = this
      temp.forEach(function(item) {
        if (item['children'].length === 0) {
          delete (item['children'])
        } else {
          that.removeChildren(item['children'])
        }
      })
    },
    onSubmit() {
      this.$refs['ruleForm'].validate((valid) => {
        if (!valid) {
          console.log(this.form)
          return false
        }
        this.loading = true
        const data = {
          id: this.form.baseInfo.unique_id,
          reason: this.form.reason,
          status: this.form.status
        }
        company_check(data).then(response => {
          if (response.code === 200) {
            this.$message.success('操作成功')
          }

          this.loading = false
        }).catch(() => {
          this.loading = false
        })
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<template>
  <el-drawer
    title="资料审核"
    :visible.sync="_visible"
  >
    <el-form ref="ruleForm" v-loading="loading" label-width="180px" :rules="rulesFrom" :model="form">
      <el-form-item label="名称">
        {{ form.baseInfo.name || '' }}
        <span v-if="form.baseInfo.name !== form.baseInfo.name" style="color: red">( 认证前：{{ form.baseInfo.name }} ) </span>
      </el-form-item>
      <el-form-item label="Logo">
        <el-avatar shape="square" :size="100" :src="form.baseInfo.logo" />
      </el-form-item>

      <el-divider content-position="left">基础信息</el-divider>
      <el-form-item label="行业类型">{{ form.baseInfo.industryInfo?form.baseInfo.industryInfo:'' }}
      </el-form-item>
      <el-form-item label="规模">{{ form.baseInfo.scale }}</el-form-item>
      <el-form-item label="省份">
        <span>{{ form.baseInfo.locatedInfo }}</span>
      </el-form-item>
      <el-form-item label="地址">{{ form.baseInfo.address }}</el-form-item>

      <el-divider content-position="left">工商信息</el-divider>
      <el-form-item label="企业类型">{{ form.authInfo.natureInfo }}</el-form-item>
      <el-form-item label="注册资本">{{ form.authInfo.registered_capital }}</el-form-item>
      <el-form-item label="统一代码证">{{ form.authInfo.cods }}</el-form-item>
      <el-form-item label="法人姓名">{{ form.authInfo.linkman }}</el-form-item>
      <el-form-item label="签发地">{{ form.authInfo.registration }}</el-form-item>
      <el-form-item label="成立日期">{{ form.authInfo.date_of_incorporation }}</el-form-item>
      <el-form-item label="经营范围">{{ form.authInfo.business }}</el-form-item>
      <el-divider content-position="left">法人信息</el-divider>
      <el-form-item label="姓名">{{ form.authInfo.legalPerson }}</el-form-item>
      <el-form-item label="证件类型">{{ form.authInfo.legal_person_type }}</el-form-item>
      <el-form-item label="证件号码">{{ form.authInfo.legalPersonNumber }}</el-form-item>
      <el-form-item label="证件A面文件">
        <el-image :src="form.authInfo.legalPersonIdCardFront">
          <div slot="placeholder" class="image-slot">
            加载中
            <span class="dot">...</span>
          </div>
        </el-image>
      </el-form-item>
      <el-form-item label="证件B面文件">
        <el-image :src="form.authInfo.legalPersonIdCardBack">
          <div slot="placeholder" class="image-slot">
            加载中
            <span class="dot">...</span>
          </div>
        </el-image>
      </el-form-item>

      <el-divider content-position="left">营业执照</el-divider>
      <el-form-item label="执照类型">{{ form.authInfo.license_type }}</el-form-item>
      <el-form-item label="执照编码">{{ form.authInfo.cods }}</el-form-item>
      <el-form-item label="执照文件">
        <el-image :src="form.authInfo.license">
          <div slot="placeholder" class="image-slot">
            加载中
            <span class="dot">...</span>
          </div>
        </el-image>
      </el-form-item>

      <el-divider content-position="left">企业负责人</el-divider>
      <el-form-item label="负责人姓名">{{ form.authInfo.linkman }}</el-form-item>
      <el-form-item label="联系方式">{{ form.authInfo.linkmanphone }}</el-form-item>
      <el-form-item label="邮箱地址">{{ form.authInfo.linkmanemail }}</el-form-item>

      <el-divider content-position="left">审核操作</el-divider>
      <el-form-item label="审核选项" prop="status">
        <el-select v-model="form.status" placeholder="请选择">
          <el-option label="通过" :value="1" />
          <el-option label="驳回" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.status && form.status === 2" label="原因" prop="reason">
        <el-input
            v-model="form.reason"
            type="textarea"
            :rows="5"
            :maxlength="200"
            placeholder="请输入您驳回的原因，提示用户及时改正"
            show-word-limit
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">立即提交</el-button>
        <el-button @click="goBack">返回</el-button>
      </el-form-item>
    </el-form>
  </el-drawer>
</template>

<style scoped lang="scss">
::v-deep .el-drawer__body{
  height: calc(100% - 45px - 32px);
  overflow: auto;
}
</style>
