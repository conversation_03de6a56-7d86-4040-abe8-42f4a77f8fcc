<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
      </div>
      <el-col :span="12">
        <version-form :type="true" @transfer="sonSave" />
      </el-col>
    </el-card>
  </div>
</template>

<script>
import VersionForm from './components/version-form'
import { store } from '@/api/version'
export default {
  name: 'Add',
  components: { VersionForm },
  data() {
    return {
    }
  },
  methods: {
    sonSave(form) {
      store(form).then(response => {
        this.$message.success('添加成功')
        this.$router.go(-1)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap {
  height: 100%;

  .box-card {
    border: none;
    height: 100%;

    ::v-deep .el-card__header {
      padding-top: 0;
    }

    ::v-deep .el-card__body {
      height: calc(100% - 59px);
      overflow: auto;
      padding: 0;
      margin-top: 20px
    }
  }
}
</style>
