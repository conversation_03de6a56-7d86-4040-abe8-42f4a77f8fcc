/* eslint-disable */
import requestMap from "@/api/requestMap";

export default {
  methods: {
    /**
     * 接口返回提示
     * @param {Object} res 接口结果
     * @param {String} successMsg 消息内容
     * @param {Function} success 回调
     * @param {Function} error 失败回调
     * @param errorMsg
     * @param successCode
     * @param showCustomMsg
     * @param showCustomErrorMsg
     */
    apiTips({
      res = {},
      successMsg = "",
      success = null,
      error = null,
      errMsg = "",
      successCode = 1,
      showCustomMsg = false,
      showCustomErrorMsg = false,
    }) {
      if (res.code === successCode) {
        if (successMsg) {
          if (showCustomMsg) {
            this.$message.success(successMsg);
          } else {
            this.$message.success(res.msg || res.message || successMsg);
          }
        }
        if (success) success(res);
      } else {
        if (errMsg) {
          if (showCustomErrorMsg) {
            this.$message.error(errMsg);
          } else {
            this.$message.error(res.msg || res.message || errMsg);
          }
        }
        if (error) error(res);
      }
    },
    /**
     * 确认弹窗
     * @param data
     * @param {String} data.tips 提示的消息内容
     * @param {String} data.loadingField 加载loading名
     * @param {String} data.apiName 接口名
     * @param {String} data.msg 接口完成后的提示内容
     * @param {String} data.errMsg 失败提示
     * @param {String | Number} data.successCode 成功码
     * @param {String} data.confirmButtonText 确认按钮文字
     * @param {String} data.cancelButtonText 取消按钮文字
     * @param {Boolean} data.showCustomMsg 是否显示传入的成功提示
     * @param {Boolean} data.showCustomErrorMsg 是否显示传入的失败提示
     * @param {Function} data.success 成功回调
     * @param {Function} data.error 失败回调
     */
    showConfirm({
      tips = "",
      loadingField = "",
      apiName = "",
      data = {},
      successMsg = "",
      success = null,
      error = null,
      complete = null,
      errMsg = "",
      successCode = 1,
      confirmButtonText = "确定",
      cancelButtonText = "取消",
      showCustomMsg = false,
      showCustomErrorMsg = false,
    }) {
      this.$confirm(tips, "提示", {
        confirmButtonText: confirmButtonText,
        cancelButtonText: cancelButtonText,
        type: "warning",
      })
        .then(() => {
          if (apiName) {
            this.requestApi({
              apiName,
              data,
              loadingField,
              success,
              error,
              successMsg,
              errMsg,
              successCode,
              showCustomMsg,
              showCustomErrorMsg,
            });
          }
        })
        .catch((e) => {
          console.log(e, 'e');
          if (loadingField in this) {
            this[loadingField] = false;
          }
        }).finally(() => {
          if (complete) {
            complete();
          }
        });
    },
    /**
     * 请求接口
     * @param {String} apiName 接口名
     * @param {Object} data 发送的数据
     * @param {String} loadingField loading名
     * @param {String} successMsg 成功提示
     * @param {Function} success 回调函数
     * @param {Function} error 失败函数
     * @param {Function} complete 完成函数
     * @param {String} errMsg 失败提示
     * @param {Number} successCode 成功码
     * @param showCustomMsg 是否强制展示传入成功提示
     * @param showCustomErrorMsg 是否强制展示传入错误提示
     * @param {Function} complete 结束回调
     */
    requestApi({
      apiName,
      data = {},
      loadingField = "",
      success = null,
      error = null,
      successMsg = "",
      errMsg = "",
      successCode = 1,
      showCustomMsg = false,
      showCustomErrorMsg = false,
      complete = null,
    }) {
      if (apiName) {
        if (loadingField in this) {
          this[loadingField] = true;
        }
        requestMap[apiName](data)
          .then((res) => {
            this.apiTips({
              res,
              successMsg,
              success,
              error,
              errMsg,
              successCode,
              showCustomMsg,
              showCustomErrorMsg,
            });
          })
          .catch((err) => {
            console.log(err, 'err');
          })
          .finally(() => {
            if (loadingField in this) {
              this[loadingField] = false;
            }
            if (complete) {
              complete();
            }
          });
      }
    },
  },
};
