<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import OptionsUpdate from '@/views/egt/legal/components/OptionsUpdate.vue'
import { deleteContractClauseOptionApi, getContractClauseOptionListApi } from '@/api/egt/contract'

export default {
  name: 'List',
  components: { OptionsUpdate, StatisticsTemplate },
  filters: {
    formatDate(value) {
      if (value) {
        return new Date(value * 1000).toLocaleString()
      }
      return ''
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      row: {},
      updateVisible: false
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    config() {
      return {
        key: 'optionsList',
        tableSettings: {
          index: true,
          api: getContractClauseOptionListApi,
          params: {
            id: this.data.id
          },
          field: {
            page: 'pageIndex',
            limit: 'pageSize',
            total: 'total'
          },
          columns: [
            {
              label: '合同条款内容',
              prop: 'options',
              isSlot: true
            },
            {
              label: '添加时间',
              prop: 'add_time',
              width: 160,
              isSlot: true
            },
            {
              label: '操作',
              prop: 'action',
              width: 180,
              fixed: 'right',
              align: 'center',
              isSlot: true
            }
          ]
        }
      }
    }
  },
  watch: {
    _visible(val) {
      if (val) {
        this.$nextTick(() => {
          if (this.$refs.optionsList) {
            this.$refs.optionsList.$refs.tableRef.doLayout()
          }
        })
      }
    }
  },
  methods: {
    handleUpdate(row) {
      this.row = row
      this.updateVisible = true
    },
    async handleDelete(row) {
      if (!row.id) return
      try {
        this.$refs.optionsList.setLoading(row, true)
        const res = await deleteContractClauseOptionApi({ id: row.id })
        if (res.code === 200) {
          this.$message.success('删除成功')
          await this.$refs.optionsList.handleGetData()
        } else {
          this.$message.error(res.msg || res.message || '删除失败')
        }
      } catch (error) {
        console.log(error)
        this.$message.error('删除失败')
      } finally {
        this.$refs.optionsList.setLoading(row, false)
      }
    },
    handleSubmitSuccess() {
      this.$refs.optionsList.handleGetData()
    }
  }
}
</script>

<template>
  <el-dialog
    title="选项列表"
    :visible.sync="_visible"
    width="80vw"
    append-to-body
  >
    <StatisticsTemplate ref="optionsList" :config="config" self-key="optionsList">
      <template v-slot:optionsList_action="{row}">
        <el-button size="mini" @click="handleUpdate(row)">修改</el-button>
        <el-popconfirm
          title="确定删除吗？"
          style="margin-left: 10px;"
          @onConfirm="handleDelete(row)"
        >
          <el-button slot="reference" type="danger" size="mini">删除</el-button>
        </el-popconfirm>
      </template>
      <template v-slot:optionsList_options="{row}">
        <div v-html="row.options" />
      </template>
      <template v-slot:optionsList_add_time="{row}">
        <span>{{ row.add_time | formatDate }}</span>
      </template>
    </StatisticsTemplate>
    <OptionsUpdate :visible.sync="updateVisible" :data="row" @submit-success="handleSubmitSuccess" />
  </el-dialog>
</template>

<style scoped lang="scss">
::v-deep #statistics-template {
  height: 500px;
  .filter.block {
    display: none;
  }
}
</style>
