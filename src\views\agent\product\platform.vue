<template>
  <div class="list-wrap">
    <el-card :class="['box-card', {simple: simple}]" shadow="never" :style="`border: none;${simple ? 'height: 100%' : ''}`">
      <div v-if="!simple" class="filter">
        <el-form v-if="!simple" :inline="true" :model="queryList" class="demo-form-inline" size="small">
          <el-form-item label="产品名称">
            <el-input v-model="queryList.product" placeholder="请输入产品名称" />
          </el-form-item>
          <!--<el-form-item v-if="userType === 1" label="代理商名称">-->
          <!--<el-input v-model="queryList.agent" placeholder="代理商名称" />-->
          <!--</el-form-item>-->
          <el-form-item label="公司名称">
            <el-input v-model="queryList.company" placeholder="公司名称" />
          </el-form-item>
          <!--<el-form-item label="购买时间">-->
          <!--<el-date-picker-->
          <!--v-model="queryList.start_at"-->
          <!--type="date"-->
          <!--value-format="yyyy-MM-dd"-->
          <!--placeholder="选择日期"-->
          <!--clearable-->
          <!--/>-->
          <!--</el-form-item>-->
          <!--<el-form-item label="到期时间">-->
          <!--<el-date-picker-->
          <!--v-model="queryList.end_at"-->
          <!--type="date"-->
          <!--value-format="yyyy-MM-dd"-->
          <!--placeholder="选择日期"-->
          <!--clearable-->
          <!--/>-->
          <!--</el-form-item>-->
          <el-form-item label="支付状态">
            <el-select v-model="queryList.status" placeholder="全部" clearable>
              <el-option label="待付款" :value="1" />
              <el-option label="已付款" :value="2" />
              <el-option label="已取消" :value="3" />
              <el-option label="已超时" :value="4" />
              <el-option label="待退款" :value="5" />
              <el-option label="已退款" :value="6" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="table" :style="!simple ? 'margin-top: 20px;' : 'height: 100%;margin-top: 0'">
        <el-table
          :data="tableData"
          stripe
          style="width: 100%"
          :height="simple ? '100%' : ''"
        >
          <el-table-column
            prop="order_no"
            label="订单号"
          />
          <el-table-column
            prop="intro"
            label="产品名称"
          />
          <el-table-column
            v-if="!simple"
            prop="order_type"
            label="类型"
          />
          <el-table-column
            v-if="!simple"
            prop="agent"
            label="代理名称"
          />
          <el-table-column
            prop="company"
            label="公司名称"
          />
          <el-table-column
            v-if="!simple"
            prop="customer"
            label="用户名称"
          />
          <el-table-column
            prop="total_amount"
            label="价格"
          />
          <el-table-column
            prop="created_at"
            label="购买时间"
          />
          <el-table-column
            v-if="!simple"
            prop="expire_end"
            label="到期时间"
          />
          <el-table-column
            v-if="!simple"
            show-overflow-tooltip
            prop="pay_type"
            label="支付方式"
          />
          <el-table-column
            show-overflow-tooltip
            prop="status"
            label="支付状态"
          />
        </el-table>
        <pagination
          v-show="total>0 && showPage"
          :total="total"
          :page.sync="queryList.page"
          :limit.sync="queryList.perPage"
          style="text-align:center;"
          @pagination="getList"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { yunPlatform } from '@/api/agent/product'
import { mapGetters } from 'vuex'
import Pagination from '@/components/Pagination'
import { getToken } from '@/utils/auth'

export default {
  name: 'Platform',
  components: { Pagination },
  props: {
    showPage: {
      type: Boolean,
      default: true
    },
    simple: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogLoading: false,
      tableData: [],
      queryList: {
        page: 1,
        perPage: 10
      },
      total: 0,
      history_list: []
    }
  },
  computed: {
    // 别名处理防止冲突
    ...mapGetters({
      userType: 'type'
    })
  },
  created() {
    this.getList()
  },
  mounted() {
    if (!this.simple) {
      this.$nextTick(() => {
        // 获取.tab-content的高度
        const tabContent = document.querySelector('.list-wrap > .el-card > .el-card__body')
        const tabContentHeight = tabContent.clientHeight
        // 获取.filter的高度
        const filter = document.querySelector('.filter')
        const filterHeight = filter.clientHeight
        // 计算.table的高度
        const tableHeight = tabContentHeight - filterHeight - 21
        // 设置.table的高度
        const table = document.querySelector('.table')
        table.style.height = tableHeight + 'px'
      })
    }
  },
  methods: {
    getList() {
      yunPlatform(this.queryList).then(response => {
        if (response.code === 200 && response.data) {
          this.tableData = response.data.list
          this.total = response.data.meta.total
        }
      })
    },
    exportExcel() {
      console.log(this.queryList)
      var agent = this.queryList.agent !== undefined ? this.queryList.agent : ''
      var user = this.queryList.user !== undefined ? this.queryList.user : ''
      var product = this.queryList.product !== undefined ? this.queryList.product : ''

      window.open(process.env.VUE_APP_BASE_API + '/finance/history/exportExcel?agent=' + agent + '&user=' + user + '&product=' + product + '&token=' + getToken())
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap {
  height: 100%;
  .box-card {
    height: 100%;
    ::v-deep .el-card__header {
      padding-top: 0;
    }
    ::v-deep .el-card__body {
      padding: 0;
      margin-top: 20px;
      height: 100%;
      overflow: auto;
    }
    &.simple ::v-deep .el-card__body{
      margin-top: 0;
    }
  }
  .filter {
    border-bottom: 1px solid #E5E5E5;
  }
  .table {
    margin-top: 20px;
    min-height: 200px;
  }
}
</style>
