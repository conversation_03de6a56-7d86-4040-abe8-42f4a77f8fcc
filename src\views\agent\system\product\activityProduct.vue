<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <!--<span>优惠活动</span>-->
        <el-button style="float: right; padding: 3px 0" type="text" @click="createView">新增</el-button>
      </div>
      <div class="filter">
        <el-form ref="form" :model="searchForm" :inline="true" class="demo-form-inline" size="small">
          <el-form-item label="活动名称">
            <el-input v-model="searchForm.name" placeholder="活动名称" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="table mt20">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          style="width: 100%"
        >
          <el-table-column
            prop="id"
            label="ID"
            width="75"
          />
          <el-table-column
            prop="name"
            label="活动名称"
          />
          <el-table-column
            prop="content"
            label="优惠原因"
          />
          <el-table-column
            prop="discount"
            label="折扣"
          />
          <el-table-column
            prop="first_price"
            label="首购价格"
          />
          <el-table-column
            prop="renew_price"
            label="续费价格"
          />
          <el-table-column label="是否启用">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.status"
                active-color="#13ce66"
                inactive-color="#ff4949"
                :active-value="1"
                :inactive-value="0"
                @change="setStatus(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="editView(scope.row)">编辑</el-button>
              <el-button type="text" style="color: #FFBA00" @click="detail(scope.row)">产品</el-button>
              <el-button type="text" style="color: red" @click="destory(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="searchForm.total>0"
          :total="searchForm.total"
          :page.sync="searchForm.page"
          :limit.sync="searchForm.perPage"
          style="text-align:center;"
          @pagination="getList"
        />
      </div>
    </el-card>
    <el-dialog
      :title="isUpdate ? '编辑活动名称' : '新增活动名称'"
      :visible.sync="dialogVisible"
    >
      <el-form ref="ruleForm" :model="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-form-item label="活动名称：" prop="name" style="width: 65%">
          <el-input v-model="ruleForm.name" placeholder="请输入活动名称" />
        </el-form-item>
        <el-form-item label="优惠原因:" prop="content" style="width: 70%">
          <el-input v-model="ruleForm.content" placeholder="请输入活动名称" />
        </el-form-item>
        <el-form-item label="优惠政策:" prop="renew_price" style="width: 70%">
          <el-select v-model="ruleForm.type" placeholder="请选择">
            <el-option label="折扣" :value="1" />
            <el-option label="首购价格" :value="2" />
            <el-option label="年限价格" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="ruleForm.type === 1" label="折扣:" prop="discount" style="width: 70%">
          <el-input-number v-model="ruleForm.discount" placeholder="请输入折扣" />
        </el-form-item>
        <el-form-item v-if="ruleForm.type === 2" label="首购价格" prop="first_price" style="width: 70%">
          <el-input-number v-model="ruleForm.first_price" placeholder="请输入首购价格" />
        </el-form-item>
        <el-form-item v-if="ruleForm.type === 2" label="续费价格:" prop="renew_price" style="width: 70%">
          <el-input-number v-model="ruleForm.renew_price" placeholder="请输入续费价格" />
        </el-form-item>
        <el-form-item v-if="ruleForm.type === 3" label="年限价格:" prop="renew_price" style="width: 70%">
          <el-button type="text" @click="addGivingProduct">添加年限价格</el-button>
          <el-form-item
            v-for="(item,index) in year_price"
            :key="index"
            label="年限："
            style="margin-bottom: 10px"
          >
            <el-input-number v-model="item.year" size="mini" :min="1" :max="10" label="年" />
            <span>价格：</span>
            <el-input v-model="item.price" style="width: 180px" type="number" placeholder="请输入价格" />
            <el-button type="text" style="color: red;margin-left: 40px" @click="deleteGivingProduct(index)">删除</el-button>
          </el-form-item>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button :loading="buttonLoading" type="primary" @click="saveHandle()">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { list, store, destory, setStatus } from '@/api/agent/activity'
import Pagination from '@/components/Pagination'
export default {
  name: 'ActivityProduct',
  components: { Pagination },
  data() {
    return {
      dialogVisible: false,
      tableLoading: false,
      buttonLoading: false,
      searchForm: {
        total: 0,
        page: 1,
        perPage: 10
      },
      tableData: [],
      isUpdate: false,
      ruleForm: {},
      year_price: []
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.list-wrap > .el-card > .el-card__body')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  methods: {
    getList() {
      this.tableLoading = true
      list(this.searchForm).then(response => {
        if (response.code === 200 && response.data) {
          this.tableData = response.data.list
          this.searchForm.total = response.data.meta.total
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    createView() {
      this.isUpdate = false
      this.dialogVisible = true
      this.ruleForm = {}
      this.year_price = []
    },
    editView(item) {
      this.isUpdate = true
      this.dialogVisible = true
      this.ruleForm = item
      this.year_price = item['year_price']
    },
    saveHandle() {
      this.buttonLoading = true
      console.log(typeof this.year_price)
      if (typeof this.year_price === 'string') {
        this.year_price = JSON.parse(this.year_price)
      }

      this.ruleForm['year_price'] = this.year_price
      store(this.ruleForm).then(res => {
        this.buttonLoading = false
        this.dialogVisible = false
        this.$message.success(res.message)
        this.getList()
      }).catch(() => {
        this.buttonLoading = false
      })
    },
    destory(item) {
      this.$confirm('此操作将永久删除该活动, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        destory(item).then(res => {
          this.$message.success(res.message)
          this.getList()
        })
      })
    },
    // 添加年限价格
    addGivingProduct() {
      this.year_price = this.year_price ? this.year_price : []
      this.year_price.push({ year: '1', price: '' })
    },
    // 删除年限价格
    deleteGivingProduct(index) {
      this.year_price.splice(index, 1)
    },
    detail(item) {
      this.$router.push({
        query: { id: item.id },
        name: 'agentSystemProductActivityDetail'
      })
    },
    setStatus(item) {
      console.log(item)
      setStatus({ id: item.id, status: item.status }).then(res => {
        this.$message.success(res.message)
      }).catch(() => {
      })
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap {
  height: 100%;
  .box-card {
    height: 100%;
    ::v-deep .el-card__header {
      padding-top: 0;
    }
    ::v-deep .el-card__body {
      padding: 0;
      margin-top: 20px;
      height: 100%;
      overflow: auto;
    }
  }
  .filter {
    border-bottom: 1px solid #E5E5E5;
  }
  .table {
    margin-top: 20px;
    min-height: 200px;
  }
}
</style>
