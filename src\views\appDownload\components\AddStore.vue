<script>
/*eslint-disable*/
import {addStore} from "@/api/appDownload";

export default {
  name: "AddStore",
  props: ['id', 'data'],
  watch: {
    data: {
      handler(v){
        if (v){
          this.form = JSON.parse(JSON.stringify(v));
        }
      },
      deep: true,
      immediate: true
    }
  },
  data(){
    return {
      loading: false,
      form: {
        name: '',
        status: 1
      },
      rules: {
        name: [{required: true, message: '请输入市场名称', trigger: 'blur'}],
        status: [{required: true, message: '请选择启用状态', trigger: 'change'}],
      }
    }
  },
  methods: {
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = false;
          addStore(this.form).then(res => {
            if (res.code === 200){
              this.$message.success(res.msg || res.message || '保存成功');
              this.close(true);
            }else{
              this.$message.error(res.msg || res.message || '保存失败');
            }
          }).finally(() => {
            this.loading = false;
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    close(refresh){
      if (typeof refresh !== 'boolean'){
        refresh = false;
      }
      this.$refs.form.resetFields();
      this.$emit("closeDialog", refresh);
    }
  }
}
</script>

<template>
<div id="addStore" v-loading="loading">
  <el-form :model="form" ref="form" :rules="rules" label-width="100px">
    <el-form-item label="市场名称" prop="name">
      <el-input v-model="form.name"></el-input>
    </el-form-item>
    <el-form-item label="是否启用" prop="name">
      <el-switch :active-value="1" :inactive-value="0" v-model="form.status"></el-switch>
    </el-form-item>
  </el-form>
  <el-row slot="footer" style="text-align: center;margin-top: 24px;">
    <el-button type="primary" @click="submitForm">保存</el-button>
    <el-button type="default" @click="close(false)">取消</el-button>
  </el-row>
</div>
</template>

<style scoped>

</style>
