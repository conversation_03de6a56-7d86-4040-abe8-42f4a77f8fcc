<template>
  <div class="dashboard-editor-container">
    <!--<panel-group class="bottom-20" />-->
    <use-data class="bottom-20" />

    <div style="overflow:hidden;">
      <el-row class="bottom-20" style="display: flex" :gutter="20">
        <el-col :md="12" :sm="24" :xs="24" class="s-w-full">
          <active-trends style="height: 100%" />
        </el-col>
        <el-col :md="12" :sm="24" :xs="24" class="s-w-full">
          <time-trends style="height: 100%" />
        </el-col>
      </el-row>
    </div>

    <CoreFunc class="bottom-20" />

    <ActiveArea class="bottom-20" />

    <el-card>
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <div class="buttons">
          <el-button
            type="primary"
            size="small"
            @click="toStore"
          >手机市场管理
          </el-button>
          <el-button
            type="primary"
            size="small"
            @click="toDownload"
          >下载量管理
          </el-button>
        </div>
        <el-date-picker
          v-model="date"
          size="small"
          format="yyyy-MM"
          value-format="yyyy-MM"
          type="month"
          placeholder="请选择月份"
        />
      </div>
      <el-row :gutter="20">
        <el-col :md="12" :sm="24" :xs="24" class="s-w-full">
          <download ref="download" :date="date" />
        </el-col>
        <el-col :md="12" :sm="24" :xs="24" class="s-w-full">
          <download-percent ref="downloadPercent" :date="date" />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import PanelGroup from './components/PanelGroup'
import AliveTendency from './components/AliveTendency.vue'
import AliveTime from './components/AliveTime.vue'
import CoreFunction from './components/CoreFunction.vue'
import Download from './components/Download.vue'
import downloadPercent from './components/DownloadPercent.vue'
import AreaCard from './components/AreaCard.vue'
import UseData from '@/views/dashboard/admin/components/new/UseData.vue'
import ActiveTrends from '@/views/dashboard/admin/components/new/ActiveTrends.vue'
import TimeTrends from '@/views/dashboard/admin/components/new/TimeTrends.vue'
import CoreFunc from '@/views/dashboard/admin/components/new/CoreFunc.vue'
import ActiveArea from '@/views/dashboard/admin/components/new/ActiveArea.vue'

const lineChartData = {
  newVisitis: {
    expectedData: [100, 120, 161, 134, 105, 160, 165],
    actualData: [120, 82, 91, 154, 162, 140, 145]
  },
  messages: {
    expectedData: [200, 192, 120, 144, 160, 130, 140],
    actualData: [180, 160, 151, 106, 145, 150, 130]
  },
  purchases: {
    expectedData: [80, 100, 121, 104, 105, 90, 100],
    actualData: [120, 90, 100, 138, 142, 130, 130]
  },
  shoppings: {
    expectedData: [130, 140, 141, 142, 145, 150, 160],
    actualData: [120, 82, 91, 154, 162, 140, 130]
  }
}

export default {
  name: 'DashboardAdmin',
  components: {
    ActiveArea,
    CoreFunc,
    TimeTrends,
    ActiveTrends,
    UseData,
    PanelGroup,
    AliveTendency,
    AliveTime,
    CoreFunction,
    Download,
    downloadPercent,
    AreaCard
  },
  data() {
    return {
      lineChartData: lineChartData.newVisitis,
      date: ''
    }
  },
  mounted() {
    this.date =
        new Date().getFullYear().toString() +
        '-' +
        (new Date().getMonth() + 1).toString().padStart(2, '0')
  },
  methods: {
    toStore() {
      this.$router.push('/appDownload/list')
    },
    toDownload() {
      this.$router.push('/appDownload/count')
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  position: relative;
  height: 100%;
  overflow: auto;
  .github-corner {
    position: absolute;
    top: 0px;
    border: 0;
    right: 0;
  }
  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}
@media (max-width: 1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
.bottom-20 {
  margin-bottom: 20px;
}
@media (max-width: 1500px) {
  .s-w-full {
    width: 100%;
  }
}
</style>
