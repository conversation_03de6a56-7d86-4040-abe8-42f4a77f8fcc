import request from '@/utils/request'

// 用户使用数据
export function getUsageData(data) {
  return request({
    url: '/statistics/UsagedataCount',
    method: 'POST',
    data: data
  })
}

// 用户活跃趋势
export function getAliveTendency(data) {
  return request({
    url: '/statistics/monthlyUserCount',
    method: 'POST',
    data: data
  })
}

// 分时段用户活跃数
export function getAliveTime(data) {
  return request({
    url: '/statistics/hourUserCount',
    method: 'POST',
    data: data
  })
}

// 核心功能统计
export function getCoreFunction(data) {
  return request({
    url: '/statistics/UsageCoreCount',
    method: 'POST',
    data: data
  })
}

// 活跃用户地域分布
export function getUserArea(data) {
  return request({
    url: '/statistics/provinceUserCount',
    method: 'POST',
    data: data
  })
}
// 下载量
export function getDownloadData(data) {
  return request({
    url: '/statistics/app_downloads_totale',
    method: 'POST',
    data: data
  })
}
