<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <!-- <span>小程序管理</span> -->
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
          <el-button style="padding: 3px 0" type="text" @click="addMiniApp">新增程序</el-button>
        </div>
      </div>
      <el-table :data="tableData">
        <el-table-column property="version" label="版本" width="150" />
        <el-table-column property="url" label="url" width="300" />
        <el-table-column label="类型">
          <template slot-scope="scope">
            <span>{{ scope.row.type===1?'小程序':'H5' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <span v-if="scope.row.is_default===1" style="color: palevioletred">默认</span>
            <el-button v-if="scope.row.is_default===0" type="text" @click="setDefault(scope.row.id)">设为默认</el-button>
            <el-button v-if="scope.row.is_default===0" type="text" style="color: red" @click="destroy(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <appMiniUpload :dialog-visible="appMiniUploadShow" :app-id="appId" @transfer="getList" />
  </div>
</template>

<script>
import appMiniUpload from './components/app-miniUpload'
import { miniAppList, miniAppDefault, miniAppDestory } from '../../api/app'

export default {
  name: 'AppMiniList',
  appId: null,
  components: { appMiniUpload },
  data() {
    return {
      is_default: 0,
      tableData: [],
      appMiniUploadShow: false
    }
  },
  watch: {
    appId: {
      handler(newValue, oldValue) {
        this.appId = newValue
      }
    }
  },
  created() {
    this.appId = this.$route.query.id
    this.getList()
  },
  methods: {
    getList() {
      this.appMiniUploadShow = false
      miniAppList({ id: this.appId }).then(response => {
        this.tableData = response.data
      })
    },
    setDefault(id) {
      this.$alert('设置为默认包么', '', {
        confirmButtonText: '确定',
        callback: action => {
          if (action === 'confirm') {
            miniAppDefault({ id: id }).then(response => {
              this.$message.success('设置成功')
              this.getList()
            })
          }
        }
      })
    },
    destroy(id) {
      this.$alert('删除后不可恢复', '确定删除么', {
        confirmButtonText: '确定',
        callback: action => {
          if (action === 'confirm') {
            miniAppDestory({ id: id }).then(response => {
              this.$message.success('删除成功')
              this.getList()
            })
          }
        }
      })
    },
    addMiniApp() {
      this.appMiniUploadShow = true
      this.$forceUpdate()
    }

  }

}
</script>

<style scoped lang="scss">
  .list-wrap {
    height: 100%;

    .box-card {
      border: none;
      height: 100%;

      ::v-deep .el-card__body {
        height: calc(100% - 59px);
        overflow: auto;
      }
    }
  }
</style>
