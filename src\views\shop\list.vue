<template>
  <div class="app-container" style="height: 100%; padding-bottom: 0;">
    <div class="list-wrap">
      <div class="filter">
        <el-form ref="form" :inline="true" :model="listQuery" label-width="100px">
          <el-form-item label="商品名称：">
            <el-input v-model="listQuery.name" placeholder="商品名称" size="small" clearable />
          </el-form-item>
          <el-form-item label="商品分类：">
            <el-select v-model="listQuery.category_id" placeholder="请选择" clearable size="small">
              <el-option v-for="item in categories" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="审批状态：">
            <el-select v-model="listQuery.isstatus" placeholder="请选择" clearable>
              <el-option label="待审批" :value="0" />
              <el-option label="通过" :value="1" />
              <el-option label="驳回" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button-group>
              <el-button type="primary" size="small" icon="el-icon-search" @click="getList">筛选</el-button>
            </el-button-group>
          </el-form-item>
        </el-form>
      </div>
      <div class="table mt20">
        <el-table
            v-loading="loading"
            :data="tableData"
            style="width: 100%"
            height="calc(100% - 96px)"
        >
          <el-table-column
              prop="id"
              label="ID"
              width="75"
          />
          <el-table-column label="图片" width="50">
            <template slot-scope="scope">
              <el-image
                  style="width: 32px; height: 32px"
                  :src="scope.row.pic"
                  :preview-src-list="[scope.row.pic]"
                  fit="fill"
              />
            </template>
          </el-table-column>
          <el-table-column
              prop="company.name"
              label="公司名称"
              width="150"
          />
          <el-table-column label="产品名称" width="350">
            <template slot-scope="scope">
              <el-link :underline="false" type="primary" @click="editView(scope.row)">{{ scope.row.name }}</el-link>
            </template>
          </el-table-column>

          <el-table-column
              prop="category.name"
              label="产品分类"
              width="150"
          />
          <el-table-column
              prop="price"
              label="售卖积分"
              width="100"
          />
          <el-table-column
              prop="money"
              label="售卖价格"
              width="100"
          />
          <el-table-column
              prop="flmoney"
              label="返利福利币"
              width="100"
          />
          <el-table-column label="售卖模式" width="100">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.mode === 1" size="small" type="success" effect="dark">全积分</el-tag>
              <el-tag v-if="scope.row.mode === 2" size="small" type="danger" effect="dark">全福利币</el-tag>
              <el-tag v-if="scope.row.mode === 3" size="small" type="warning" effect="dark">积分+福利币</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="显示状态" width="100">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.ck_status === 1" size="small" type="success" effect="dark">本公司</el-tag>
              <el-tag v-if="scope.row.ck_status === 2" size="small" type="danger" effect="dark">全局</el-tag>
            </template>
          </el-table-column>
          <el-table-column
              prop="number"
              label="库存"
              width="100"
          />
          <el-table-column label="状态" width="100">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.status" size="small" type="success" effect="dark">上架</el-tag>
              <el-tag v-else type="danger" size="small" effect="dark">下架</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="推荐" width="100">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.is_hot" size="small" type="success" effect="dark">是</el-tag>
              <el-tag v-else type="danger" size="small" effect="dark">否</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="首页" width="100">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.is_index" size="small" type="success" effect="dark">是</el-tag>
              <el-tag v-else type="danger" size="small" effect="dark">否</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="审核" width="100">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.isstatus === 0" size="small" type="warning" effect="dark">未审核</el-tag>
              <el-tag v-if="scope.row.isstatus === 1" size="small" type="success" effect="dark">通过</el-tag>
              <el-tag v-if="scope.row.isstatus === 2" size="small" type="danger" effect="dark">驳回</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right">
            <template slot-scope="scope">
              <el-link :underline="false" type="primary" @click="editView(scope.row)">编辑</el-link>
              <el-popconfirm
                  title="确定删除当前记录吗？"
                  @onConfirm="deleteHandle(scope.row)"
              >
                <el-link slot="reference" :underline="false" type="danger">删除</el-link>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
      </div>
    </div>
  </div>
</template>

<script>
import { getCategory } from '../../api/shop_category'
import { getGoods, destroy } from '../../api/shop_goods'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
export default {
  name: 'List',
  components: { Pagination },
  data() {
    return {
      loading: false,
      total: 1,
      tableData: [],
      categories: [],
      listQuery: {
        page: 1,
        limit: 10
      }
    }
  },
  created() {
    this.getList()
    this.getCategories()
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.list-wrap')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      console.log(tabContentHeight, filterHeight)
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  methods: {
    getCategories() {
      getCategory().then(response => {
        this.categories = response.data
      })
    },
    getList() {
      this.loading = true
      getGoods(this.listQuery).then(response => {
        this.tableData = response.data.data
        this.total = response.data.total
        this.loading = false
      })
    },
    createView() {
      this.$router.push({
        name: 'goods-add'
      })
    },
    editView(row) {
      this.$router.push({
        name: 'goods-edit',
        query: { id: row.id }
      })
    },
    deleteHandle(row) {
      destroy({ id: row.id }).then(response => {
        this.$message.success('删除成功')
        this.getList()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.card-wrap ::v-deep .el-card__body {
  height: 100%;
}
.list-wrap{
  height: 100%;
  overflow: auto;
  .filter{
    border-bottom: 1px solid #e5e5e5;
  }
  .table{
    height: 100%;
  }
}
@media screen and (max-width: 996px)  {
  .filter{
    max-height: 30vh;
    overflow: auto;
  }
}
</style>
