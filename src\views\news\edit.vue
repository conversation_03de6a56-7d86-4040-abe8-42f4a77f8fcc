<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <!--<span>{{ title }}</span>-->
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="back">返回</el-button>
      </div>
      <el-form ref="ruleForm" :model="formData" :rules="rules" label-width="140px" class="demo-ruleForm">
        <el-row>
          <el-col :span="18">
            <el-form-item label="新闻分类:" prop="news_category_id">
              <el-select v-model="formData.news_category_id" placeholder="请选择">
                <el-option
                  v-for="app in apps"
                  :key="app.id"
                  :label="app.name"
                  :value="app.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="新闻标题:" prop="title" label-width="140px">
              <el-col :span="10">
                <el-input v-model="formData.title" />
              </el-col>
            </el-form-item>
            <el-form-item label="置顶:" prop="is_top">
              <el-switch
                v-model="formData.is_top"
                active-color="#13ce66"
                inactive-color="#ff4949"
                :active-value="1"
                :inactive-value="0"
              />
            </el-form-item>
            <el-form-item label="推荐:" prop="is_recommend">
              <el-switch
                v-model="formData.is_recommend"
                active-color="#13ce66"
                inactive-color="#ff4949"
                :active-value="1"
                :inactive-value="0"
              />
            </el-form-item>
            <el-form-item label="发布时间:" prop="publish_at">
              <el-date-picker
                v-model="formData.publish_at"
                type="datetime"
                placeholder="选择日期时间"
                default-time="12:00:00"
              />
            </el-form-item>
            <el-form-item label="是否跳转" prop="is_skip">
              <el-radio-group v-model="formData.is_skip">
                <el-radio :label="0">不跳转</el-radio>
                <el-radio :label="1">跳转原生</el-radio>
                <el-radio :label="2">跳转链接</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="跳转地址" prop="skip_url">
              <el-input
                v-model="formData.skip_url"
                placeholder="请输入跳转地址"
                clearable
              />
            </el-form-item>
            <el-form-item label="简述" prop="desc">
              <el-input
                v-model="formData.desc"
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-upload
              class="news-avatar-uploader"
              :action="upload_url"
              :headers="headers"
              :data="upload_data"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="formData.cover" :src="formData.cover" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon" />
              <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过2MB</div>
            </el-upload>
          </el-col>
          <el-col :span="24">
            <el-form-item label="内容" prop="content">
              <Editor :content.sync="formData.content" path="news" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="commit">保存</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import Editor from '../../components/editor'
import { store, detail } from '../../api/news'
import { list as cateList } from '../../api/news_category'
export default {
  name: 'Edit',
  components: { Editor },
  data() {
    /* var validateSkipUrl = (rule, value, callback) => {
      if (value === '' && this.formData.is_skip > 0) {
        callback(new Error('请输入跳转地址'))
      } else {
        callback()
      }
    } */
    return {
      upload_url: process.env.VUE_APP_BASE_API + '/upload',
      headers: {
        Authorization: 'Bearer ' + this.token
      },
      title: '创建新闻',
      id: null,
      apps: [],
      formData: {
        cover: '',
        is_skip: 0,
        skip_url: '',
        desc: ''
      },
      upload_data: {
        path: 'news'
      },
      rules: {
        title: [
          { required: true, message: '请输新闻标题', trigger: 'blur' }
        ],
        is_top: [
          { required: true, message: '请选择是否置顶', trigger: 'blur' }
        ],
        is_recommend: [
          { required: true, message: '请选择是否推荐', trigger: 'blur' }
        ],
        publish_at: [
          { required: true, message: '请选择发布时间', trigger: 'blur' }
        ],
        news_category_id: [
          { required: true, message: '请选择新闻分类', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入新闻内容', trigger: 'blur' }
        ]
        /* skip_url: [
          { validator: validateSkipUrl }
        ] */
      }
    }
  },
  created() {
    this.id = this.$route.query.id
    if (this.id) {
      this.title = '编辑新闻'
      this.getDetail()
    }
    this.getCateList()
  },
  methods: {

    handleAvatarSuccess(res, file) {
      this.formData.cover = res.data.url
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG && !isPNG) {
        this.$message.error('上传的图片只能是 JPG 或 PNG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传的图片大小不能超过 2MB!')
      }
      return (isJPG || isPNG) && isLt2M
    },
    getDetail() {
      detail({ id: this.id, ...this.query }).then(response => {
        console.log(response.data)
        this.dialogVisible = false
        this.formData = response.data
      }).catch(error => {
        console.log(error)
        this.dialogVisible = false
      })
    },
    getCateList() {
      cateList().then(response => {
        this.apps = response.data
      })
    },
    commit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          store(this.formData).then(response => {
            this.$message.success('操作成功')
            this.back()
          })
        }
      })
    },
    back() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
  .news-avatar-uploader {
    ::v-deep .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      &:hover {
        border-color: #409EFF;
      }
    }
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>

<style scoped lang="scss">
.list-wrap {
  height: 100%;

  .box-card {
    border: none;
    height: 100%;

    ::v-deep .el-card__header {
      padding-top: 0;
    }

    ::v-deep .el-card__body {
      height: calc(100% - 59px);
      overflow: auto;
    }
  }
}
</style>
