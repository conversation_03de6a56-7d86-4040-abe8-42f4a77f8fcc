import request from '@/utils/request'

// 获取合同分类列表
export function getContractCategoryListApi(data) {
  return request({
    url: '/lcontract/lcontractCategory',
    method: 'POST',
    data
  })
}
// 获取修改合同分类需要参数
export function getContractCategoryUpdateApi(data) {
  return request({
    url: '/lcontract/getLcontractCategoryEdit',
    method: 'POST',
    data
  })
}
// 添加合同分类
export function addContractCategoryApi(data) {
  return request({
    url: '/lcontract/lcontractCategoryCreate',
    method: 'POST',
    data
  })
}
// 更新合同分类
export function updateContractCategoryApi(data) {
  return request({
    url: '/lcontract/lcontractCategoryEdit',
    method: 'POST',
    data
  })
}
// 删除合同分类
export function deleteContractCategoryApi(data) {
  return request({
    url: '/lcontract/lcontractCategoryDelete',
    method: 'POST',
    data
  })
}
// 获取合同范本列表
export function getContractTemplateListApi(data) {
  return request({
    url: '/lcontract/lcontractList',
    method: 'POST',
    data
  })
}
// 获取合同分类列表-范本用
export function getContractCategoryListApiForTemplate(data) {
  return request({
    url: '/lcontract/getCategoryByLcontractList',
    method: 'POST',
    data
  })
}
// 获取添加合同范本需要参数
export function getContractTemplateAddApi(data) {
  return request({
    url: '/lcontract/getLcontractAdd',
    method: 'POST',
    data
  })
}
// 添加合同范本
export function addContractTemplateApi(data) {
  return request({
    url: '/lcontract/lcontractCreate',
    method: 'POST',
    data
  })
}
// 获取修改合同范本需要参数
export function getContractTemplateUpdateApi(data) {
  return request({
    url: '/lcontract/getLcontractEdit',
    method: 'POST',
    data
  })
}
// 修改合同范本
export function updateContractTemplateApi(data) {
  return request({
    url: '/lcontract/lcontractEdit',
    method: 'POST',
    data
  })
}
// 删除合同范本
export function deleteContractTemplateApi(data) {
  return request({
    url: '/lcontract/lcontractDelete',
    method: 'POST',
    data
  })
}
// 获取合同条款列表
export function getContractClauseListApi(data) {
  return request({
    url: '/lcontract/lcontractModelList',
    method: 'POST',
    data
  })
}
// 获取修改合同条款需要参数
export function getContractClauseUpdateApi(data) {
  return request({
    url: '/lcontract/getLcontractModelEdit',
    method: 'POST',
    data
  })
}
// 添加合同条款
export function addContractClauseApi(data) {
  return request({
    url: '/lcontract/lcontractModelCreate',
    method: 'POST',
    data
  })
}
// 修改合同条款
export function updateContractClauseApi(data) {
  return request({
    url: '/lcontract/lcontractModelEdit',
    method: 'POST',
    data
  })
}
// 删除合同条款
export function deleteContractClauseApi(data) {
  return request({
    url: '/lcontract/lcontractModelDelete',
    method: 'POST',
    data
  })
}
// 获取合同条款选项列表
export function getContractClauseOptionListApi(data) {
  return request({
    url: '/lcontract/lcontractModelOptionList',
    method: 'POST',
    data
  })
}
// 添加合同条款选项
export function addContractClauseOptionApi(data) {
  return request({
    url: '/lcontract/lcontractModelOptionCreate',
    method: 'POST',
    data
  })
}
// 修改合同条款选项
export function updateContractClauseOptionApi(data) {
  return request({
    url: '/lcontract/lcontractModelOptionEdit',
    method: 'POST',
    data
  })
}
// 删除合同条款选项
export function deleteContractClauseOptionApi(data) {
  return request({
    url: '/lcontract/lcontractModelOptionDelete',
    method: 'POST',
    data
  })
}
