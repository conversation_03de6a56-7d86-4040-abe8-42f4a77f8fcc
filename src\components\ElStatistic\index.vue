<script>
/* eslint-disable */
export default {
  name: 'ElStatistic',
  props: {
    title: {
      type: String,
      default: ""
    },
    unit: {
      type: String,
      default: ""
    },
    value: {
      type: [String, Number],
      default: 0
    },
    valueStyle: {
      type: String,
      default: ""
    },
    unitStyle: {
      type: String,
      default: ""
    }
  }
}
</script>

<template>
  <div class="el-statistic">
    <div class="head">
      <span class="title">
        {{ title }}
      </span>
    </div>
    <div class="con">
      <span class="number">
        <i class="value" :style="valueStyle">{{ value }}</i>
        <i class="unit" :style="unitStyle">{{ unit }}</i>
      </span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.el-statistic {
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: #000;
  font-variant: tabular-nums;
  list-style: none;
  font-feature-settings: "tnum";
  text-align: center;
  .head {
    margin-bottom: 4px;
    color: #606266;
    font-size: 13px;
  }
  .con {
    font-family: Sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #303133;
    span {
      display: inline-block;
      margin: 0;
      line-height: 100%;
      i{
        font-style: normal;
        &.unit{
          font-size: 16px;
        }
      }
    }
    .number {
      font-size: 20px;
      padding: 0 4px;
    }
  }
}
</style>
