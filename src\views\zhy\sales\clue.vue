<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import Update from './Update.vue'
import { getClueList } from '@/api/zhy/clue'
import ChangeStatus from '@/views/zhy/sales/ChangeStatus.vue'

export default {
  name: 'Clue',
  components: { ChangeStatus, StatisticsTemplate, Update },
  data() {
    return {
      config: {
        key: 'clue',
        filters: [
          {
            label: '企业名称',
            prop: 'name',
            type: 'input'
          },
          {
            label: '联系电话',
            prop: 'mobile',
            type: 'input'
          },
          {
            label: '信息来源',
            prop: 'typetitle',
            type: 'select',
            settings: {
              options: [
                {
                  label: '选项1',
                  value: '1'
                },
                {
                  label: '选项2',
                  value: '2'
                }
              ]
            }
          },
          {
            label: '提交时间',
            prop: 'date',
            type: 'datePicker',
            settings: {
              type: 'datetime',
              format: 'yyyy-MM-dd HH:mm:ss',
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
              placeholder: ['选择日期']
            }
          },
          {
            label: '是否成单',
            prop: 'status',
            type: 'select',
            settings: {
              options: [
                {
                  label: '是',
                  value: '1'
                },
                {
                  label: '否',
                  value: '0'
                }
              ]
            }
          }
        ],
        tableSettings: {
          api: getClueList,
          field: {
            page: 'page',
            limit: 'perPage',
            total: 'total'
          },
          columns: [
            {
              label: 'ID',
              prop: 'id',
              width: 60
            },
            {
              label: '企业名称',
              prop: 'name'
            },
            {
              label: '联系电话',
              prop: 'mobile',
              width: 150
            },
            {
              label: '产品',
              prop: 'producttitle',
              width: 100
            },
            {
              label: '信息来源',
              prop: 'typetitle'
            },
            {
              label: '咨询时间',
              prop: 'date'
            },
            {
              label: '是否成单',
              prop: 'status',
              width: 100,
              isSlot: true
            },
            {
              label: '成单金额',
              prop: 'price',
              width: 100
            },
            {
              label: '成单日期',
              prop: 'orderdate'
            },
            {
              label: '客户意向',
              prop: 'intention'
            },
            {
              label: '跟进情况',
              prop: 'followup'
            },
            {
              label: '操作',
              prop: 'operation',
              width: 100,
              isSlot: true,
              align: 'center',
              fixed: 'right'
            }
          ]
        }
      },
      row: {},
      updateVisible: false,
      loading: false
    }
  },
  methods: {
    handleEdit(row) {
      this.row = row
      this.updateVisible = true
    },
    setLoading(row, loading) {
      this.$refs.listWrapRef.setLoading(row, loading)
    },
    refresh() {
      this.$refs.listWrapRef.handleGetData()
    }
  }
}
</script>

<template>
  <div class="page-wrap">
    <StatisticsTemplate ref="listWrapRef" :config="config">
      <template #clue_status="{row}">
        <change-status :data.sync="row" :loading="row.loading" @setLoading="setLoading" @refresh="refresh" />
      </template>
      <template #clue_operation="{row}">
        <el-button type="text" size="small" @click="handleEdit(row)">详情</el-button>
      </template>
    </StatisticsTemplate>
    <Update :visible.sync="updateVisible" :data="row" @refresh="refresh" />
  </div>
</template>

<style scoped lang="scss">
.page-wrap{
  height: 100%;
}
</style>
