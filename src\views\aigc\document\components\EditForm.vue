<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="60%">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" label-position="left">
      <el-form-item label="表单项名称" prop="title">
        <el-input v-model="form.title" placeholder="请输入表单项名称"></el-input>
      </el-form-item>
      <el-form-item label="表单项英文名" prop="field">
        <el-input v-model="form.field" placeholder="请输入表单项英文名"></el-input>
      </el-form-item>
      <el-form-item label="表单项类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择表单项类型">
          <el-option
            v-for="item in formTypes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.type == 'select'" label="下拉框选项" prop="options">
        <el-input v-model="form.options" placeholder="请输入下拉框选项，用逗号分隔"></el-input>
      </el-form-item>
      <el-form-item label="提示词" prop="placeholder">
        <el-input type="textarea" v-model="form.placeholder" placeholder="请输入提示词" rows="4"></el-input>
      </el-form-item>
      <el-form-item v-if="form.type == 'select'" label="是否多选" prop="multiple">
        <el-switch v-model="form.multiple" :active-value="1" :inactive-value="0" in></el-switch>
      </el-form-item>
      <el-form-item label="是否必填" prop="required">
        <el-switch v-model="form.required" :active-value="1" :inactive-value="0" in></el-switch>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex';
import { formStoreApi } from '@/api/aigc';

export default {
  name: 'EditForm',
  props: {
    typeId: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {
      dialogTitle: '',
      dialogVisible: false,
      form: {
        id: '',
        title: '',
        field: '',
        type: 'text',
        options: '',
        placeholder: '',
        required: 0,
        multiple: 0
      },
      rules: {
        title: [{ required: true, message: '请输入表单项名称', trigger: 'blur' }],
        field: [{ required: true, message: '请输入表单项英文名', trigger: 'blur' }],
        type: [{ required: true, message: '请选择表单项类型', trigger: 'change' }],
        options: [{ required: false, message: '请输入下拉框选项', trigger: 'blur' }],
        placeholder: [{ required: false, message: '请输入提示词', trigger: 'blur' }],
      },
    };
  },
  computed: {
    ...mapGetters(['formTypes']),
  },
  methods: {
    openDialog(row = null) {
      if (row) {
        this.dialogTitle = '编辑表单项';
        const { id, title, field, type, options, placeholder, required, multiple } = row;
        this.form = {
          id,
          title,
          field,
          type,
          options,
          placeholder,
          required,
          multiple
        };
      } else {
        this.dialogTitle = '添加表单项';
        this.form = {
          title: '',
          field: '',
          type: 'text',
          options: '',
          placeholder: '',
          required: 0,
          multiple: 0
        };
      }
      this.dialogVisible = true;
    },
    submitForm() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return;
        let params = JSON.parse(JSON.stringify(this.form));
        params.type_id = this.typeId
        formStoreApi(params).then(() => {
          this.$message.success('操作成功');
          this.$emit('refresh'); // 通知父组件刷新数据
          this.dialogVisible = false;
        }).catch(error => {
          console.log('操作失败:', error);
          // this.$message.error(`操作失败: ${error.message}`);
        });
      });
    }
  }
};
</script>