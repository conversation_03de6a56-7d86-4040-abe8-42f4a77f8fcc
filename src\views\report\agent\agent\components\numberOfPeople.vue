<template>
  <el-card v-loading="formLoading" class="box-card">
    <div style="height:20px;" />
    <div class="btn-day">
      <span
        v-for="(item,index) in ['昨日','最近7日','最近30日']"
        :key="index"
        :class="indexOfPeopleNumDate===index?'sel':''"
        @click="peopleNumDay(index)"
      >{{ item }}</span>
    </div>
    <div v-if="type===1" class="view-people-num">
      <div :style="`background-url: ${require('@/assets/agent/p1.png')}`">
        <p>注册 用户总数</p>
        <div>
          <h1>{{ agent_data.yun_user }}</h1>
          <span>家</span>
        </div>
      </div>
      <div>
        <p>注册代理商总数</p>
        <div>
          <h1>{{ agent_data.agent_all }}</h1>
          <span>家</span>
        </div>
      </div>
      <div>
        <p>注册企业总数</p>
        <div>
          <h1>{{ agent_data.company_count }}</h1>
          <span>家</span>
        </div>
        <div>
          <span>已实名: {{ agent_data.company_real_count }}家</span>
          <span style="margin-left: 30px">未实名: {{ agent_data.company_unreal_count }}家</span>
        </div>
      </div>
    </div>
    <div style="height:20px;" />
    <div v-if="type===1" class="view-people-num">
      <div :style="`background-url: ${require('@/assets/agent/p1.png')}`">
        <p>
          <span>
            资海云开通产品生效用户数
            <el-tooltip class="item" effect="dark" content="开通过资海云产品的用户，用户有未过期产品，产品包括易管通，建站通专业版，建站通营销版，建站通商城版等产品，除idc域名空间外的产品" placement="bottom">
              <i class="el-icon-question" />
            </el-tooltip>
          </span>
        </p>
        <div>
          <h1 style="cursor: pointer;" @click="showAgentCustomers(3,1)">{{ agent_data.customerzhycount }}</h1>
          <span>家</span>
        </div>
        <div>
          <span style="cursor: pointer;" @click="showAgentCustomers(3,0)">资海云开通产品用户数（仅限产品）: {{ agent_data.customerzhyallcount }}家
            <el-tooltip class="item" effect="dark" content="开通过资海云产品的用户，产品包括易管通，建站通专业版，建站通营销版，建站通商城版等产品，除idc域名空间外的产品" placement="bottom">
              <i class="el-icon-question" />
            </el-tooltip>
          </span>
        </div>
      </div>
      <div>
        <p>
          <span>
            资海云开通IDC生效用户数
            <el-tooltip class="item" effect="dark" content="开通idc产品的有效用户，用户有未过期产品，产品包括域名，空间等产品" placement="bottom">
              <i class="el-icon-question" />
            </el-tooltip>
          </span>
        </p>
        <div>
          <h1 style="cursor: pointer;" @click="showAgentCustomers(2,1)">{{ agent_data.customeridccount }}</h1>
          <span>家</span>
        </div>
        <div>
          <span style="cursor: pointer;" @click="showAgentCustomers(2,0)">
            资海云开通IDC用户数: {{ agent_data.customeridcallcount }}家
            <el-tooltip class="item" effect="dark" content="开通过idc产品的用户，产品包括域名，空间等产品" placement="bottom">
              <i class="el-icon-question" />
            </el-tooltip>
          </span>
        </div>
      </div>
      <div>
        <p>
          <span>资海云代理商用户生效用户数</span>
          <el-tooltip class="item" effect="dark" content="开通全部产品中任意一个产品的用户，用户有未过期产品，产品包括idc，硬件，软件产品" placement="bottom">
            <i class="el-icon-question" />
          </el-tooltip>
        </p>
        <div>
          <h1 style="cursor: pointer;" @click="showAgentCustomers(1,1)">{{ agent_data.customecount }}</h1>
          <span>家</span>
        </div>
        <div>
          <span style="cursor: pointer;" @click="showAgentCustomers(1,0)">
            资海云代理商用户数: {{ agent_data.customerallcount }}家
            <el-tooltip class="item" effect="dark" content="开通全部产品中任意一个产品的用户，产品包括idc，硬件，软件产品" placement="bottom">
              <i class="el-icon-question" />
            </el-tooltip>
          </span>
        </div>
      </div>
    </div>
    <div class="view-num">
      <div @click="pushUserRegister(1)">
        <p>昨日用户注册 ></p>
        <p>{{ visit_data.register }}家</p>
      </div>
      <div @click="pushUserRegister(2)">
        <p>昨日用户登录 ></p>
        <p>{{ visit_data.login }}</p>
      </div>
      <div @click="pushUserRegister(3)">
        <p>IP访问量 ></p>
        <p>{{ visit_data.user_ip }}</p>
      </div>
      <div>
        <p>浏览次数(PV) ></p>
        <p>{{ visit_data.user_pv }}</p>
      </div>
      <div>
        <p>独立访问(UV)</p>
        <p>{{ visit_data.user_uv }}</p>
      </div>
    </div>
    <div v-if="type===10" class="view-num">
      <div @click="pushAgentList(1)">
        <p>昨日代理商注册 ></p>
        <p>{{ agent_data.register }}人</p>
      </div>
      <div @click="pushAgentList(2)">
        <p>昨日代理商登录 ></p>
        <p>{{ agent_data.login }}人</p>
      </div>
      <div @click="pushUserRegister(3)">
        <p>IP访问量></p>
        <p>{{ visit_data.agent_ip }}</p>
      </div>
      <div>
        <p>浏览次数(PV) ></p>
        <p>{{ visit_data.agent_pv }}</p>
      </div>
      <div>
        <p>独立访问(UV)</p>
        <p>{{ visit_data.agent_uv }}</p>
      </div>
    </div>
  </el-card>
</template>

<script>
import { mapGetters } from 'vuex'
import { agentLoginAndRegisterCount, userLoginAndRegisterCount } from '@/api/agent/statistic'

export default {
  name: 'NumberOfPeople',
  data() {
    return {
      formLoading: false,
      indexOfPeopleNumDate: 0,
      days: 1,
      agent_id: null,
      visit_data: {},
      agent_data: {}
    }
  },
  computed: {
    ...mapGetters([
      'type'
    ])
  },
  created() {
    this.getData()
  },
  methods: {
    peopleNumDay(index) {
      this.indexOfPeopleNumDate = index
      this.days = [1, 7, 30][index]
      this.getData()
    },
    getData() {
      this.getUserCount()
      // 看代理商的统计数据
      if (this.type === 1) {
        this.getAgentCount()
      }
    },
    getAgentCount() {
      agentLoginAndRegisterCount({ days: this.days }).then(res => {
        this.agent_data = res.data
      })
    },
    getUserCount() {
      userLoginAndRegisterCount({ days: this.days }).then(res => {
        this.visit_data = res.data
      })
    },
    showAgentCustomers(type, status) {
      this.$router.push({
        name: 'AgentCustomerRouter',
        query: { type: type, status: status }
      })
    },
    pushUserRegister(type) {
      var temp = 'register'
      if (type === 2) {
        temp = 'login'
      } else if (type === 3) {
        temp = 'ip'
      }

      this.$router.push({
        query: { type: temp, days: this.days },
        name: 'statisticsRegisterListRouter'
      })
    },
    pushAgentList(type) {
      var temp = 'register'
      if (type === 2) {
        temp = 'login'
      }

      this.$router.push({
        query: { type: temp, days: this.days },
        name: 'statisticsAgentListRouter'
      })
    }
  }
}
</script>

<style lang="scss" scoped>

  .box-card {
    width: 1650px;
  .el-icon-question{
        box-sizing: border-box;

    word-wrap: normal;
    white-space: normal;
    word-break: break-all;

    user-select: none;

    text-align: left;

    text-indent: initial;
    border-spacing: 2px;

    font-size: 14px;
    color: #909399;
    font-family: element-icons!important;
    speak: none;
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    vertical-align: baseline;
    display: inline-block;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;}
    .el-icon-question:before {
    content: "\e7a4";
      color:#fff;
}
    ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background: rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 0 1px 0 rgba(0, 0, 0, 0.15);
    padding: 0;
    border: none;
    overflow-x: visible;
}
    ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.25);
    padding: 0;
    border: none;
    width: 20px;
}
    p {
      margin: 0;
    }
    h1 {
      margin: 0;
    }
    .btn-day {
      margin-top: 10px;
      margin-bottom: 40px;
      span {
        padding: 10px 25px;
        font-size: 10px;
        cursor: pointer;
      }
      .sel {
        background-color: #0e90d2;
        color: #ffffff;
      }
    }
    .view-people-num {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      > div {
        position: relative;
        margin-right: 45px;
        padding: 35px 24px 27px;
        width: 505px;
        height: 195px;
        background: no-repeat center;
        color: white;
        > p {
          font-size: 18px;
          color: white;
        }
        > div:nth-child(2) {
          display: flex;
          flex-direction: row;
          align-items: baseline;
          margin: 20px 0;
          h1 {
            font-size: 61px;
            font-weight: normal;
          }
          span {
            font-size: 27px;
          }
        }
        > div:nth-child(3) {
          span {
            font-size: 16px;
            color: #ffffff;
          }
        }
      }
      > div:nth-child(2) {
        //background: url("../../../../static/p2.png") no-repeat center;
      }
      > div:nth-child(3) {
        margin-right: 0;
        //background: url("../../../../static/p3.png") no-repeat center;
      }
    }
    .view-num {
      display: flex;
      margin-top: 52px;
      > div {
        width: 329px;
        height: 54px;
        border-right: 1px solid #eeeeee;
        text-align: center;
        p {
          font-size: 16px;
        }
        > p:nth-child(2) {
          margin-top: 20px;
        }
      }
      > div {
        cursor: pointer;
      }
      > div:last-child {
        border-right: 0px solid #eeeeee;
      }
    }
  }

</style>
