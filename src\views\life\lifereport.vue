<template>
  <div class="app-container integral">
    <el-card class="box-card" shadow="hover">
      <div slot="header" class="clearfix">
        <span>列表</span>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        :default-sort="{prop: 'created_at', order: 'descending'}"
      >
        <el-table-column label="ID" prop="id" width="80" />
        <el-table-column label="举报话题" prop="ctitle" />
        <el-table-column label="举报理由" prop="title" />
        <el-table-column label="举报者" prop="user" />
        <el-table-column label="举报内容" prop="comments" />
        <el-table-column label="举报图片" width="200">
          <template slot-scope="scope">
            <el-row v-if="scope.row.comimgurllist.length > 0" :gutter="0">
              <el-col v-for="item in scope.row.comimgurllist" :key="item" :span="6">
                <div>
                  <el-image
                    style="width: 100%;height: 50px"
                    :src="item"
                    :preview-src-list="scope.row.comimgurllist"
                    fit="fill"
                  />
                </div>
              </el-col>
            </el-row>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="created_at" />
        <el-table-column label="显示状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 0" size="small" type="danger" effect="dark">审核中</el-tag>
            <el-tag v-if="scope.row.status === 1" size="small" type="success" effect="dark">通过</el-tag>
            <el-tag v-if="scope.row.status === 2" size="small" type="danger" effect="dark">拒绝</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="handelEdit(scope.row)">审核</el-button>
            <el-divider direction="vertical" />
            <el-popconfirm title="确定删除吗并且无法恢复？" @onConfirm="destroy(scope.row)">
              <el-button slot="reference" type="text">删除</el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="query.page"
        :limit.sync="query.limit"
        @pagination="getList"
      />
    </el-card>

    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form" :rules="rules">
        <el-form-item label="举报话题" :label-width="formLabelWidth" prop="title">
          {{ form.ctitle }}
        </el-form-item>
        <el-form-item label="话题内容" :label-width="formLabelWidth" prop="title">
          {{ form.circles['comments'] }}
        </el-form-item>
        <el-form-item v-if="form.circles.comimgurllist&&form.circles.category==1" label="话题图片：" :label-width="formLabelWidth">
          <div style="width:350px;">
            <el-row v-if="form.circles.comimgurllist" :gutter="10">
              <el-col v-for="item in form.circles.comimgurllist" :key="item" :span="4" justify="center">
                <div>
                  <el-image
                    style="width: 100%;"
                    :src="item"
                    :preview-src-list="form.circles.comimgurllist"
                    fit="fill"
                  />
                </div>
              </el-col>
            </el-row>
          </div>
        </el-form-item>
        <el-form-item v-if="form.circles.video&&form.circles.category==2" label="话题视频" :label-width="formLabelWidth">
          <div>
            <el-image
              v-show="show1!=form.id&&form.circles.video"
              :id="'myimg'+form.id"
              style="width: 100px; max-height: 100px"
              :src="form.circles.videoimg"
              @click="handelVideo(form)"
            />
            <div v-show="show==form.id" class="test_two_box" :class="'myVideo'+form.id">
              <video :id="'myVideo'+form.id" class="video-js">
                <source :src="form.circles.video" type="video/mp4">
              </video>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="举报理由" :label-width="formLabelWidth" prop="title">
          {{ form.title }}
        </el-form-item>
        <el-form-item label="举报者" :label-width="formLabelWidth" prop="user">
          {{ form.user }}
        </el-form-item>
        <el-form-item label="举报内容" :label-width="formLabelWidth" prop="comments">
          {{ form.comments }}
        </el-form-item>
        <el-form-item v-if="form.comimgurllist" label="举报图片：" :label-width="formLabelWidth">
          <div style="width:350px;">
            <el-row v-if="form.comimgurllist" :gutter="10">
              <el-col v-for="item in form.comimgurllist" :key="item" :span="4" justify="center">
                <div>
                  <el-image
                    style="width: 100%;"
                    :src="item"
                    :preview-src-list="form.comimgurllist"
                    fit="fill"
                  />
                </div>
              </el-col>
            </el-row>
          </div>
        </el-form-item>
        <el-form-item label="审核状态：" :label-width="formLabelWidth">
          <el-select v-model="form.status" prop="status" placeholder="请选择">
            <el-option label="待审核" :value="0" />
            <el-option label="同意" :value="1" />
            <el-option label="驳回" :value="2" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { fetchList, store, remove } from '@/api/lifereport'
import { mapGetters } from 'vuex'

export default {
  name: 'Personal',
  components: { Pagination },
  data() {
    return {
      total: 0,
      query: {
        page: 1,
        limit: 10,
        name: null,
        number: null,
        status: null
      },
      show: 0,
      show1: 0,
      options: [],
      tableData: [],
      myPlayer: null,
      loading: true,
      imgshow: true,
      videoshow: false,
      form: {
        circles: []
      },
      rules: {
        reply: [
          { required: false, message: '规则描述不能为空', max: 100, trigger: 'blur' }
        ]
      },
      dialogFormVisible: false,
      formLabelWidth: '150px',
      title: '新增规则',
      circulation_disabled: false,
      upload_url: process.env.VUE_APP_BASE_API + '/uploadReturnId',
      fileList: []
    }
  },
  computed: {
    ...mapGetters([
      'token'
    ])
  },
  created() {
    this.getList()
  },
  methods: {

    getList() {
      this.loading = true
      fetchList(this.query).then(response => {
        this.total = response.data.total
        this.tableData = response.data.data
        console.log(this.tableData, 'error submit!!')
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    initVideo(id) {
      // 初始化视频方法
      const myPlayer = this.$video('myVideo' + id, { // eslint-disable-line no-unused-vars
        // 确定播放器是否具有用户可以与之交互的控件。没有控件，启动视频播放的唯一方法是使用autoplay属性或通过Player API。
        controls: true,
        // 自动播放属性,muted:静音播放
        autoplay: 'muted',
        // 建议浏览器是否应在<video>加载元素后立即开始下载视频数据。
        preload: 'auto',
        // 设置视频播放器的显示宽度（以像素为单位）
        width: '300px',
        // 设置视频播放器的显示高度（以像素为单位）
        height: '150px'
      })
    },

    handelUnit() {
      if (this.form.unit === 'forever') {
        this.form.circulation = 0
        this.circulation_disabled = true
      } else {
        if (this.form.circulation <= 0) {
          this.form.circulation = 1
        }
        this.circulation_disabled = false
      }
    },
    handelSwitch(row) {
      store(row)
    },
    handelCreate() {
      this.title = '新增规则'
      delete this.form.id
      this.dialogFormVisible = true
      this.fileList = []
    },
    handelEdit(row) {
      this.title = '审核'
      this.fileList = []
      this.form = row
      console.log('row', row)
      this.dialogFormVisible = true
      if (this.form.icon) {
        this.form.icon.name = this.form.icon.url.split('/').pop()
        this.fileList.push(this.form.icon)
      }
    },
    handelVideo(row) {
      this.show = row.id
      this.show1 = row.id
      console.log(this.tableData, 'error submit!!')
      this.initVideo(row.id)
    },
    beforeIconUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG && !isPNG) {
        this.$message.error('上传的图标只能是 JPG 或 PNG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传的图片大小不能超过 2MB!')
      }
      return (isJPG || isPNG) && isLt2M
    },
    handleIconSuccess(res, file) {
      if (res && res.code === 200) {
        this.form.att_id = res.data[0]
      }
    },
    handleIconRemove(file, fileList) {
      this.form.att_id = null
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          store(this.form).then(_ => {
            this.dialogFormVisible = false
            this.getList()
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    destroy(row) {
      remove({ id: row.id }).then(_ => {
        this.getList()
      })
    }
  }
}
</script>

<style type="text/css" scoped>
    .integral ::v-deep .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .integral ::v-deep .avatar-uploader .el-upload:hover {
        border-color: #409EFF;
    }

    .integral ::v-deep .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 100px;
        height: 100px;
        line-height: 100px;
        text-align: center;
    }

    .integral ::v-deep .avatar {
        width: 100px;
        height: 100px;
        display: block;
    }
</style>
