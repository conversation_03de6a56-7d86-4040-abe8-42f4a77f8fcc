<template>
  <div class="app-container" style="height: 100%; padding-bottom: 0;padding-top: 0">
    <div class="list-wrap integral">
      <el-card class="card-wrap" shadow="hover">
        <div slot="header" class="clearfix">
          <!--<span>列表</span>-->
          <el-button style="float: right; padding: 3px 0" type="text" @click="handelCreate">新增规则</el-button>
        </div>
        <el-table
            v-loading="loading"
            :data="tableData"
            stripe
            style="width: 100%"
            :default-sort="{prop: 'created_at', order: 'descending'}"
            height="calc(100% - 96px)"
        >
          <el-table-column label="ID" prop="id" width="80" />
          <el-table-column label="公司名称" prop="company" />
          <el-table-column label="名称" prop="user" width="200" />
          <el-table-column label="积分" prop="money" width="120" />
          <el-table-column label="剩余积分" prop="price" width="120" />
          <el-table-column label="商品名称" prop="title" />
          <el-table-column label="创建时间" prop="created_at" />
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-popconfirm title="确定删除吗并且无法恢复？" @onConfirm="destroy(scope.row)">
                <el-button slot="reference" type="text">删除</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="query.page"
            :limit.sync="query.limit"
            @pagination="getList"
        />
      </el-card>
      <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="dialogFormVisible" width="1050px">
        <el-form ref="form" :model="form" :rules="rules">
          <el-form-item label="所属公司" :label-width="formLabelWidth" prop="company_id">
            <el-autocomplete
                v-model="form.companyname"
                :fetch-suggestions="querySearchGroup"
                placeholder="请选择"
                clearable
                class="filter-item"
                style="width: 300px;margin-top:0px"
                @select="selectGroup"
                @focus="groupListMe"
            />
          </el-form-item>
          <el-form-item label="商品名称" :label-width="formLabelWidth" prop="goods_id">
            <el-autocomplete
                v-model="form.goodsname"
                :fetch-suggestions="goodsSearchGroup"
                placeholder="请选择"
                clearable
                class="filter-item"
                style="width: 300px;margin-top:0px"
                @select="goodsselectGroup"
                @focus="goodsListMe"
            />
          </el-form-item>
          <el-form-item label="积分：" :label-width="formLabelWidth" prop="integral">
            <el-input-number v-model="form.integral" :min="0" :max="99999999" label="积分" />
          </el-form-item>
          <el-form-item label="选择员工：" :label-width="formLabelWidth" prop="userid">
            <el-transfer
                v-model="form.userid"
                filterable
                :filter-method="filterMethod"
                filter-placeholder="请输入用户名称"
                :titles="['未选择','已选择']"
                :data="memberList"
                :props="{
                  key: 'id',
                  label: 'name'
                }"
                @change="transfer"
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { getCompay, membersList } from '../../api/user'
import { givingindex, givingstore, givingremove, goodsindex } from '@/api/integral'
import { mapGetters } from 'vuex'

export default {
  name: 'Personal',
  components: { Pagination },
  data() {
    return {
      total: 0,
      query: {
        page: 1,
        limit: 10,
        name: null,
        number: null,
        status: null
      },
      options: [],
      tableData: [],
      company: [],
      groupArr: [],
      groupList: [],
      memberList: [],
      groupgoodsArr: [],
      groupgoodsList: [],
      loading: true,
      form: {
        goods_id: null,
        unique_id: '',
        integral: 1,
        company_id: 0,
        userid: []
      },
      rules: {
        company_id: [
          { required: true, message: '公司不能为空', trigger: 'blur' }
        ],
        goods_id: [
          { required: false, message: '商品不能为空', trigger: 'blur' }
        ],
        integral: [
          { required: false, message: '奖励积分数不能为空', trigger: 'blur' }
        ],
        userid: [
          { required: true, message: '员工不能为空', trigger: 'blur' }
        ]
      },
      filterMethod(query, item) {
        return item.name.indexOf(query) > -1
      },
      dialogFormVisible: false,
      formLabelWidth: '150px',
      title: '发放积分',
      circulation_disabled: false,
      upload_url: process.env.VUE_APP_BASE_API + '/uploadReturnId',
      fileList: []
    }
  },
  computed: {
    ...mapGetters([
      'token'
    ])
  },
  watch: {
    'form.companyname': {
      deep: true,
      handler: function(newVal, oldVal) {
        this.groupArr = [] // 这是定义好的用于存放下拉提醒框中数据的数组
        var arr = []
        var datafrom = []
        datafrom = {
          name: this.form.companyname
        }
        getCompay(datafrom).then(res => { // getDictInfo()这里是调用后台接口
          if (res.data) {
            this.groupList = res.data
            for (const item of this.groupList) {
              arr.push({
                value: item.name, // 这里一定是value: '值'
                id: item.id,
                unique_id: item.unique_id
              })
            }
          }
        })
        this.groupArr = arr
      }
    },
    'form.goodsname': {
      deep: true,
      handler: function(newVal, oldVal) {
        this.groupgoodsArr = [] // 这是定义好的用于存放下拉提醒框中数据的数组
        var arr = []
        var datafrom = []
        datafrom = {
          name: this.form.goodsname,
          company_id: this.form.company_id
        }
        goodsindex(datafrom).then(res => { // getDictInfo()这里是调用后台接口
          if (res.data) {
            this.groupgoodsList = res.data
            for (const item of this.groupgoodsList) {
              arr.push({
                value: item.name, // 这里一定是value: '值'
                id: item.id
              })
            }
          }
        })
        this.groupgoodsArr = arr
      }
    }
  },
  created() {
    this.getList()
    this.groupListMe()
  },
  methods: {
    groupListMe() {
      getCompay().then(res => { // getDictInfo()这里是调用后台接口
        if (res.data) {
          this.groupList = []
          this.groupArr = []
          this.groupList = res.data
          for (const item of this.groupList) {
            this.groupArr.push({
              value: item.name, // 这里一定是value: '值'
              id: item.id,
              unique_id: item.unique_id
            })
          }
        }
      })
        .catch(err => {
          console.log(err)
        })
    },
    goodsListMe() {
      var datafrom = []
      datafrom = {
        company_id: this.form.company_id
      }
      goodsindex(datafrom).then(res => { // getDictInfo()这里是调用后台接口
        if (res.data) {
          this.groupgoodsList = []
          this.groupgoodsArr = []
          this.groupgoodsList = res.data
          for (const item of this.groupgoodsList) {
            this.groupgoodsArr.push({
              value: item.name, // 这里一定是value: '值'
              id: item.id
            })
          }
        }
      })
        .catch(err => {
          console.log(err)
        })
    },
    memberListMe() {
      var datafrom = []
      datafrom = {
        unique_id: this.form.unique_id
      }
      membersList(datafrom).then(res => { // getDictInfo()这里是调用后台接口
        if (res.data) {
          this.memberList = []
          for (const item of res.data) {
            this.memberList.push({
              name: item.name + '(' + item.phone + ')' + '-' + item.department, // 这里一定是value: '值'
              id: item.user_unique_id
            })
          }
        }
      })
        .catch(err => {
          console.log(err)
        })
    },
    transfer(value, direction, movedKeys) {
    },
    querySearchGroup(queryString, cb) {
      var groupArr = this.groupArr
      cb(groupArr)
    },
    selectGroup(val) {
      this.groupId = val.code
      this.form.company_id = val.id
      this.form.unique_id = val.unique_id
      this.goodsListMe()
      this.memberListMe()
    },
    goodsSearchGroup(queryString, cb) {
      var groupgoodsArr = this.groupgoodsArr
      cb(groupgoodsArr)
    },
    goodsselectGroup(val) {
      this.groupgoodsId = val.code
      this.form.goods_id = val.id
    },
    getList() {
      this.loading = true
      givingindex(this.query).then(response => {
        this.total = response.data.total
        this.tableData = response.data.data
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    handelCreate() {
      this.title = '发放积分'
      delete this.form.id
      this.dialogFormVisible = true
      this.fileList = []
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          givingstore(this.form).then(res => {
            this.dialogFormVisible = false
            this.getList()
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    destroy(row) {
      givingremove({ id: row.id }).then(res => {
        this.getList()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.card-wrap ::v-deep .el-card__body {
  height: 100%;
}
.list-wrap {
  height: 100%;

  ::v-deep .card-wrap {
    height: 100%;
    border: none;

    .el-card__header {
      padding-top: 0;
    }

    .el-card__body {
      height: calc(100% - 59px);
      padding: 0;
      margin-top: 20px;
    }

    .box-card {
      height: 100%;
      border: none;

      .el-card__body {
        height: calc(100% - 59px);
        overflow: auto;
      }
    }
  }
}
</style>
<style type="text/css" scoped>
 .el-transfer ::v-deep .el-transfer-panel{
    width:300px!important;
  }
    .integral ::v-deep .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .integral ::v-deep .avatar-uploader .el-upload:hover {
        border-color: #409EFF;
    }

    .integral ::v-deep .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 100px;
        height: 100px;
        line-height: 100px;
        text-align: center;
    }

    .integral ::v-deep .avatar {
        width: 100px;
        height: 100px;
        display: block;
    }
</style>
