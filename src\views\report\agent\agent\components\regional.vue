<template>
  <SelfCard title="用户地域分布" class="box-card">
    <template v-slot:right>
      <div style="display: flex; align-items: center;">
        <el-select v-model="keys" class="selectright" placeholder="请选择" @change="selectType">
          <el-option
            v-for="(item,index) in options_user"
            :key="index"
            :label="item.name"
            :value="item.key"
          />
        </el-select>
        <el-radio-group v-model="indexOfDate" style="margin-left: 10px" @change="selectDay">
          <el-radio-button v-for="charge in radios" :key="charge.value" :label="charge.value">
            {{ charge.label }}
          </el-radio-button>
        </el-radio-group>
      </div>
    </template>

    <div v-loading="mapLoading" class="view-body">
      <div class="left">
        <div id="chartMap" style="width: 100%;height: 100%;" />
      </div>
      <div class="right">
        <div v-if="keys==='user_visit_all_number'">
          <table border="1">
            <tr>
              <th>省份</th>
              <th>浏览量(PV)</th>
              <th>占比</th>
            </tr>
            <tr v-for="(item,index) in lists" :key="index">
              <td>{{ item.name }}</td>
              <td>{{ item.count }}</td>
              <td>{{ item.proportion }}%</td>
            </tr>
          </table>
          <div v-if="lists.length===0" class="null">暂无数据</div>
        </div>
        <div v-if="keys==='user_visit_ip_number'">
          <table border="1">
            <tr>
              <th>省份</th>
              <th>ip量</th>
            </tr>
            <tr v-for="(item,index) in lists" :key="index">
              <td>{{ item.name }}</td>
              <td>{{ item.count }}</td>
            </tr>
          </table>
          <div v-if="lists.length===0" class="null">暂无数据</div>
        </div>
        <div v-if="keys==='user_visit_number'">
          <table border="1">
            <tr>
              <th>省份</th>
              <th>访客数(UV)</th>
            </tr>
            <tr v-for="(item,index) in lists" :key="index">
              <td> {{ item.name }}</td>
              <td>{{ item.count }}</td>
            </tr>
          </table>
          <div v-if="lists.length===0" class="null">暂无数据</div>
        </div>
      </div>

    </div>
  </selfcard>
</template>

<script>
import { drawGeographical } from '@/api/agent/statistic'
import * as echarts from 'echarts'
require('echarts/theme/macarons')
import mapJson from '@/assets/map/map2.json'
import SelfCard from '@/views/dashboard/admin/components/SelfCard.vue'

export default {
  name: 'Regional',
  components: { SelfCard },
  data() {
    return {
      chartMap: null,
      mapLoading: false,
      allData: {},
      lists: [],
      graph: {},
      max: 0,
      indexOfDate: 0,
      indexOfCate: 0,
      options_user: [
        // { name: '用户注册', key: 'user_register_number' },
        // { name: '用户登录', key: 'user_login_number' },s
        { name: 'IP访问量', key: 'user_visit_ip_number' },
        { name: '浏览次数', key: 'user_visit_all_number' },
        { name: '独立访问', key: 'user_visit_number' }
      ],
      options_agent: ['代理商注册', '代理商登录', 'IP访问量', '浏览次数', '独立访问'],
      days: 0,
      keys: 'user_visit_all_number',
      client: 'cloud',
      radios: [
        { label: '今天', value: 0 },
        { label: '昨天', value: 1 },
        { label: '最近7日', value: 7 },
        { label: '最近30日', value: 30 }
      ]
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      this.mapLoading = true
      await this.getSystem()
    },
    selectDay(index) {
      this.indexOfDate = index
      this.days = [0, 1, 7, 30][index]
      this.getSystem()
    },
    selectType(e) {
      this.keys = e
      this.getSystem()
    },
    selectCate(index) {
      this.indexOfCate = index
      this.client = ['cloud', 'agent'][index]
      this.getSystem()
    },
    async getSystem() {
      this.mapLoading = true
      await drawGeographical({ days: this.days, keys: this.keys, client: this.client }).then(response => {
        this.mapLoading = false
        this.allData = response.data
        this.lists = this.allData.data
        this.updateMapData()
      }).catch(error => {
        this.mapLoading = false
        console.log(error)
      })
    },
    updateMapData() {
      // 地图数据
      var arr = []
      this.lists.forEach(function(item) {
        arr.push({ 'name': item['name'], 'value': item['count'] })
      })
      this.graph = arr

      // 最大值
      var temp_max = 0
      arr.forEach(function(item) {
        temp_max = item['value'] > temp_max ? item['value'] : temp_max
      })
      this.max = temp_max === 0 ? 20 : temp_max

      this.setOptions()
    },
    setOptions() {
      echarts.registerMap('map', mapJson)
      this.chartMap = echarts.init(document.getElementById('chartMap'))
      this.chartMap.setOption({
        tooltip: {},
        legend: {
          orient: 'vertical',
          left: 'left',
          data: ['']
        },
        visualMap: {
          min: 0,
          max: this.max,
          left: '10%',
          top: 'bottom',
          text: ['高', '低'],
          calculable: false,
          orient: 'horizontal',
          color: ['#2a52be', '#4d80ff', '#8ebce6']
        },
        selectedMode: 'single',
        aspectScale: 0.75,
        zoom: 2,
        series: [
          {
            name: '',
            type: 'map',
            map: 'map',
            itemStyle: {
              normal: {
                borderColor: 'rgba(0, 0, 0, 0.2)'
              },
              emphasis: {
                shadowOffsetX: 0,
                shadowOffsetY: 0,
                shadowBlur: 20,
                borderWidth: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            showLegendSymbol: false,
            label: {
              normal: {
                show: true
              },
              emphasis: {
                show: true
              }
            },
            data: this.graph
          }
        ]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.box-card{
  .view-body{
    display: flex;
    pading-top:10px;
    .left{
      flex: 1;
    }
    .right{
      position: relative;
      width: 41%;
      padding-top: 10px;
      height: 360px;
      table{
        display:inline-block;
        height: 323px;
        border-collapse: collapse;
        overflow: auto;
        border: 0 solid transparent;
        tr{
          height: 40px;
          th{
            width: 191px;
          }
          td{
            width: 191px;
            font-size: 14px;
            text-align: center;
          }
        }
        tr:nth-child(1){
          background-color: #eff4f7;
          th{
            color: #3c3c3c;
            font-weight: 100;
            font-size: 14px;
          }
        }
        tr:nth-child(2n){
          background-color: #f9f9f9;
        }
      }
      .null{
        position: absolute;
        top: 50px;
        width: 100%;
        line-height: 200px;
        text-align: center;
        color: #8c939d;
        font-size: 30px;
        font-weight: bold;
      }
    }
  }
}
</style>
