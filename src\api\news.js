import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/news/list',
    method: 'POST',
    data: data
  })
}

export function store(data) {
  return request({
    url: '/news/store',
    method: 'POST',
    data: data
  })
}

export function detail(data) {
  return request({
    url: '/news/detail',
    method: 'POST',
    data: data
  })
}

export function destory(data) {
  return request({
    url: '/news/destory',
    method: 'POST',
    data: data
  })
}
