<script>
import SelfCard from '@/views/dashboard/admin/components/SelfCard.vue'
import MapChart from '@/views/dashboard/admin/components/MapChart.vue'
import { getUserArea } from '@/api/dashboard'

export default {
  name: 'ActiveArea',
  components: { MapChart, SelfCard },
  data() {
    return {
      date: '',
      chartData: [],
      tableData: [],
      legend: ['活跃用户地域分布'],
      colorList: ['#fb726c'],
      total: 0,
      avarage: 0,
      grid: {
        left: 30,
        right: 30,
        bottom: 20,
        top: 30,
        containLabel: true
      },
      page: {
        page: 1,
        limit: 5
      }
    }
  },
  mounted() {
    this.handleGetList(1)
  },
  methods: {
    handleCurrentChange(val) {
      this.page.page = val
      this.tableData = this.chartData.slice((this.page.page - 1) * this.page.limit, this.page.page * this.page.limit)
    },
    handleGetList(page = this.page.page) {
      if (page) {
        this.page.page = page
      }
      if (!this.date) {
        this.date = new Date().getFullYear().toString() + '-' + (new Date().getMonth() + 1).toString().padStart(2, '0')
      }
      getUserArea({ date: this.date }).then(res => {
        if (res.code === 200 && res.data) {
          this.chartData = []
          if (res.data.title && res.data.arr) {
            res.data.title.forEach((v, i) => {
              if (v) {
                this.chartData.push({
                  name: v[0],
                  value: res.data.arr[i] || 0
                })
              }
            })
          }
          this.total = this.chartData.length
          this.tableData = this.chartData.slice((this.page.page - 1) * this.page.limit, this.page.page * this.page.limit)
        }
      })
    }
  }
}
</script>

<template>
  <SelfCard title="活跃用户地域分布">
    <template v-slot:right>
      <el-date-picker
        v-model="date"
        type="year"
        value-format="yyyy-MM"
        format="yyyy-MM"
        placeholder="选择日期"
        style="width: 160px;margin-left: 26px;"
      />
    </template>
    <div id="area">
      <el-row style="display: flex;align-items: stretch">
        <el-col :md="12" :sm="24" :xs="24" class="s-w-full">
          <map-chart height="400px" :chart-data="chartData" />
        </el-col>
        <el-col :md="8" :sm="24" :xs="24" class="s-w-full" style="display: flex;flex-direction: column;justify-content: center;align-items: center">
          <el-table :data="tableData">
            <el-table-column
              type="index"
              label="序号"
              align="center"
              width="50"
            />
            <el-table-column
              prop="name"
              label="省份"
              align="center"
            />
            <el-table-column
              prop="value"
              label="启动次数"
              align="center"
            />
          </el-table>
          <div class="page" style="text-align: center">
            <el-pagination
              :current-page="page.page"
              :page-size="page.limit"
              layout="prev, pager, next"
              :total="total"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-col>
      </el-row>
    </div>
  </SelfCard>
</template>

<style scoped lang="scss">

</style>
