import request from '@/utils/requestAPI'

// 获取认证列表
export function getCertificateList(query) {
  return request({
    url: '/agent/company/list',
    method: 'post',
    data: query
  })
}
// 失效企业
export function invalidCertificate(query) {
  return request({
    url: '/agent/company/companyInvalid',
    method: 'post',
    data: query
  })
}
// 获取认证企业详情
export function company_detail(data) {
  return request({
    url: '/agent/company/details',
    method: 'post',
    data: data
  })
}
// 企业认证
export function company_check(data) {
  return request({
    url: '/certificate/company/check',
    method: 'post',
    data: data
  })
}

