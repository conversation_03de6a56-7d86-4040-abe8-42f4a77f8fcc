<script>
export default {
  name: 'SelfCard',
  props: {
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    showMore: {
      type: Boolean,
      default: false
    },
    toMore: {
      type: String,
      default: ''
    }
  }
}
</script>

<template>
  <div class="self-card">
    <div class="top">
      <div class="left">
        <h2 class="title">{{ title }}</h2>
        <p class="subtitle">{{ subtitle }}</p>
      </div>
      <div class="right">
        <router-link v-if="showMore" :to="toMore" class="more">
          更多
          <el-icon name="arrow-right" />
        </router-link>
        <template v-if="$slots.right">
          <slot name="right" />
        </template>
      </div>
    </div>
    <div class="bottom">
      <slot />
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .self-card{
    padding: 26px;
    background: #FFFFFF;
    border-radius: 10px;
    .top{
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left{
        display: flex;
        align-items: center;
        .title{
          font-size: 20px;
          color: #000000;
          margin: 0;
        }
        .subtitle {
          font-size: 16px;
          color: #3C3C3C;
          margin: 0 0 0 12px;
        }
      }
      .right{
        .more{
          font-size: 14px;
          color: #8F8F8F;
        }
      }
    }
    .bottom{
      margin-top: 26px;
    }
  }
</style>
