<template>
  <div class="app-container" style="height: 100%;padding-bottom: 0;">
    <div class="list-wrap">
      <div class="filter">
        <el-form ref="form" :inline="true" :model="listQuery" label-width="100px" size="small">
          <el-form-item label="订单编号：">
            <el-input v-model="listQuery.no" placeholder="订单编号" clearable />
          </el-form-item>
          <el-form-item label="收件人：">
            <el-input v-model="listQuery.user" placeholder="收件人" clearable />
          </el-form-item>
          <el-form-item label="电话：">
            <el-input v-model="listQuery.phone" placeholder="电话" clearable />
          </el-form-item>
          <el-form-item label="商品名称：">
            <el-input v-model="listQuery.goods_name" placeholder="商品名称" clearable />
          </el-form-item>
          <el-form-item label="订单状态：">
            <el-select v-model="listQuery.status" placeholder="请选择" clearable>
              <el-option label="待发货" :value="0" />
              <el-option label="已发货" :value="1" />
              <el-option label="已完成" :value="2" />
              <el-option label="待退款" :value="4" />
              <el-option label="已退款" :value="5" />
            </el-select>
          </el-form-item>
          <el-form-item label="下单时间：">
            <el-date-picker v-model="listQuery.created_at" type="date" placeholder="选择日期" value-format="yyyy-MM-dd" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" icon="el-icon-search" @click="getList">确定</el-button>
            <el-button :loading="downloadLoading" type="primary" size="small" icon="el-icon-link" @click="handleDownload">导出</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="table">
        <el-table
            :data="tableData"
            style="width: 100%"
            height="calc(100% - 96px)"
        >
          <el-table-column
              prop="id"
              label="ID"
              width="100"
              fixed
          />
          <el-table-column label="订单编号" width="175">
            <template slot-scope="scope">
              <el-link :underline="false" type="primary" @click="detailView(scope.row)">{{ scope.row.no }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="图片">
            <template slot-scope="scope">
              <el-image
                  style="width: 32px; height: 32px"
                  :src="scope.row.pic"
                  :preview-src-list="[scope.row.pic]"
                  fit="fill"
              />
            </template>
          </el-table-column>
          <el-table-column
              prop="name"
              show-overflow-tooltip
              label="商品名称"
          />
          <el-table-column
              prop="price"
              label="积分"
          />
          <el-table-column
              prop="total_price"
              label="总积分"
          />
          <el-table-column
              prop="money"
              label="福利币单价"
          />
          <el-table-column
              prop="total_money"
              label="福利币总价"
          />
          <el-table-column
              prop="flmoney"
              label="返利福利币"
          />
          <el-table-column
              prop="number"
              label="数量"
          />
          <el-table-column
              prop="user"
              label="收件人"
          />
          <el-table-column
              prop="phone"
              width="150"
              label="电话"
          />
          <el-table-column label="状态">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.status === 0" size="small" type="primary" effect="dark">待发货</el-tag>
              <el-tag v-if="scope.row.status === 1" size="small" type="success" effect="dark">已发货</el-tag>
              <el-tag v-if="scope.row.status === 2" size="small" type="danger" effect="dark">已完成</el-tag>
              <el-tag v-if="scope.row.status === 4" size="small" type="danger" effect="dark">待退款</el-tag>
              <el-tag v-if="scope.row.status === 5" size="small" type="danger" effect="dark">已退款</el-tag>
            </template>
          </el-table-column>
          <el-table-column
              prop="created_at"
              label="创建时间"
              width="170"
          />
          <el-table-column label="操作" fixed="right">
            <template slot-scope="scope">
              <el-link :underline="false" type="primary" @click="detailView(scope.row)">详情</el-link>
              <el-popconfirm
                  title="确定删除当前记录吗？"
                  @onConfirm="deleteHandle(scope.row)"
              >
                <!--              <el-link slot="reference" :underline="false" type="danger">删除</el-link>-->
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
      </div>
    </div>
  </div>
</template>

<script>
import { parseTime } from '@/utils'
import { getOrders } from '../../api/shop_orders'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
export default {
  name: 'Orders',
  components: { Pagination },
  data() {
    return {
      downloadLoading: false,
      tableData: [],
      exportData: [],
      listQuery: {
        page: 1,
        limit: 10
      },
      total: 1,
      autoWidth: true,
      bookType: 'xlsx',
      filename: '积分商城订单列表'
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    this.$nextTick(() => {
      // 获取.tab-content的高度
      const tabContent = document.querySelector('.list-wrap')
      const tabContentHeight = tabContent.clientHeight
      // 获取.filter的高度
      const filter = document.querySelector('.filter')
      const filterHeight = filter.clientHeight
      // 计算.table的高度
      console.log(tabContentHeight, filterHeight)
      const tableHeight = tabContentHeight - filterHeight - 21
      // 设置.table的高度
      const table = document.querySelector('.table')
      table.style.height = tableHeight + 'px'
    })
  },
  methods: {
    getList() {
      getOrders(this.listQuery).then(response => {
        this.tableData = response.data.data
        this.total = response.data.total
      })
    },
    detailView(row) {
      this.$router.push({
        name: 'order-detail',
        query: { no: row.no }
      })
    },
    handleDownload() {
      this.listQuery.export = true
      getOrders(this.listQuery).then(response => {
        // this.exportData = response.data
        this.downloadLoading = true
        import('@/vendor/Export2Excel').then(excel => {
          const tHeader = ['订单编号', '商品名称', '商品数量', '积分', '总积分', '福利币单价', '福利币总价', '返利福利币', '售卖模式', '收件人', '收件人电话', '收货地址', '下单时间', '发货状态', '快递公司', '快递单号', '发货时间']
          const filterVal = ['no', 'name', 'number', 'price', 'total_price', 'money', 'total_money', 'flmoney', 'mode', 'user', 'phone', 'address', 'created_at', 'status', 'express_name', 'express_number', 'express_time']
          const list = response.data
          const data = this.formatJson(filterVal, list)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.filename,
            autoWidth: this.autoWidth,
            bookType: this.bookType
          })
          this.downloadLoading = false
        })
      })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    }
  }
}
</script>

<style scoped lang="scss">
.card-wrap ::v-deep .el-card__body {
  height: 100%;
}
.list-wrap {
 height: 100%;
 .filter {
  border-bottom: 1px solid #e5e5e5;
 }
 .table{
  margin-top: 20px;
 }
}
</style>
