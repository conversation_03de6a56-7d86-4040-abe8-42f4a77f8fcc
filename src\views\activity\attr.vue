<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>活动属性</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="createView">添加属性</el-button>
      </div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="65"
        />
        <el-table-column
          prop="name"
          label="属性名称"
        />
        <el-table-column label="状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status" type="success" effect="dark" size="small">启用</el-tag>
            <el-tag v-else type="danger" effect="dark" size="small">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="配置规则">
          <template slot-scope="scope">
            <el-button type="text" @click="addRules(scope.row)">规则管理</el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-link :underline="false" type="primary" @click="editDialog(scope.row)">编辑</el-link>
            <el-popconfirm
              title="确定删除当前记录吗？"
              @onConfirm="deleteHandle(scope.row)"
            >
              <el-link slot="reference" :underline="false" type="danger">删除</el-link>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!--  表单  -->
    <el-dialog
      title="活动属性管理"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="属性名称：" prop="name">
          <el-col :span="20">
            <el-input v-model="ruleForm.name" />
          </el-col>
        </el-form-item>
        <el-form-item label="状态开关：" prop="status">
          <el-col :span="20">
            <el-switch
              v-model="ruleForm.status"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
            />
          </el-col>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="saveHandle">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { attr_list, attr_store, attr_delete } from '../../api/activity_attr'
export default {
  name: 'Category',
  data() {
    return {
      tableLoading: false,
      loading: false,
      dialogVisible: false,
      tableData: [],
      ruleForm: {},
      rules: {
        name: [
          { required: true, message: '分类名称必填', trigger: 'blur' }
        ],
        level: [
          { required: true, message: '分类顺序必填', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getAttrList()
  },
  methods: {
    getAttrList() {
      this.tableLoading = true
      attr_list().then(response => {
        this.tableData = response.data
        this.tableLoading = false
      })
    },
    createView() {
      this.$confirm('每创建一条属性，需要后端配置相应的规则类型才能使用，请确认了解该功能。', '友情提示', {
        confirmButtonText: '我已经了解，继续创建',
        cancelButtonText: '关闭,我只是看看',
        type: 'warning'
      }).then(() => {
        this.ruleForm = {
          status: 1
        }
        this.dialogVisible = true
      })
    },
    editDialog(row) {
      this.$confirm('修改属性后可能会导致，属性与规则不匹配，导致功能无法正常使用！', '友情提示', {
        confirmButtonText: '我已经了解，继续编辑',
        cancelButtonText: '关闭,我只是看看',
        type: 'warning'
      }).then(() => {
        this.ruleForm = row
        this.dialogVisible = true
      })
    },
    saveHandle() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.loading = true
          attr_store(this.ruleForm).then(response => {
            this.dialogVisible = false
            this.loading = false
            this.getAttrList()
          }).catch(() => {
            this.loading = false
          })
        }
      })
    },
    deleteHandle(row) {
      attr_delete(row).then(response => {
        this.$message.success('删除成功')
        this.getAttrList()
      })
    },
    addRules(row) {
      this.$router.push({
        path: '/activity/rules',
        query: {
          id: row.id
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
