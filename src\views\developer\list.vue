<template>
  <div class="list-wrap">
    <el-table
      :data="tableData"
      stripe
      style="width: 100%"
      height="calc(100% - 96px)"
    >
      <el-table-column label="ID" prop="id" width="65" />
      <el-table-column label="公司名称" prop="name" />
      <el-table-column label="账号" prop="phone" />
      <el-table-column label="认证状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 1" type="warning" effect="dark">申请认证中</el-tag>
          <el-tag v-if="scope.row.status === 2" type="success" effect="dark">认证通过</el-tag>
          <el-tag v-if="scope.row.status === 3" type="info" effect="dark">认证拒绝</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" prop="created_at" />
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="detail(scope.row.id)">编辑</el-button>
          <!--            <el-button type="text" @click="destory(scope.row.id)">删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="query.page" :limit.sync="query.perPage" @pagination="getList" />
  </div>
</template>

<script>
import { list } from '../../api/developer'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
export default {
  components: { Pagination },
  data() {
    return {
      total: 0,
      tableData: [],
      query: { page: 1, perPage: 10 },
      dialogVisible: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.dialogVisible = true
      list(this.query).then(response => {
        this.dialogVisible = false
        this.tableData = response.data.data
        this.total = response.data.total
      }).catch(error => {
        console.log(error)
        this.dialogVisible = false
      })
    },
    detail(id) {
      this.$router.push({
        'path': '/developer/edit',
        'query': { 'id': id }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .list-wrap {
    height: 100%;

    ::v-deep .el-tabs{
      height: 100%;
      .el-tabs__content{
        height: calc(100% - 41px);
        .el-tab-pane{
          height: 100%;
        }
      }
    }

    ::v-deep .card-wrap {
      height: 100%;
      border: none;

      .el-card__body {
        height: 100%;
      }

      .box-card {
        height: 100%;
        border: none;

        .el-card__body {
          height: calc(100% - 59px);
          overflow: auto;
        }
      }
    }
  }
</style>
