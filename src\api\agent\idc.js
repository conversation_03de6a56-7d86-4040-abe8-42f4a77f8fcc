import request from '@/utils/request'

export function index(data) {
  return request({
    url: '/erp/index',
    method: 'post',
    data: data
  })
}

export function websiteCreate(data) {
  return request({
    url: '/erp/website/create',
    method: 'post',
    data: data
  })
}

export function websiteDestory(data) {
  return request({
    url: '/erp/website/destory',
    method: 'post',
    data: data
  })
}

export function domainCreate(data) {
  return request({
    url: '/erp/domain/create',
    method: 'post',
    data: data
  })
}

export function domainDestory(data) {
  return request({
    url: '/erp/domain/destory',
    method: 'post',
    data: data
  })
}

export function hostCreate(data) {
  return request({
    url: '/erp/host/create',
    method: 'post',
    data: data
  })
}

export function hostDestory(data) {
  return request({
    url: '/erp/host/destory',
    method: 'post',
    data: data
  })
}
