<script>
import update from '@/views/task/mixins/update'
import SelfFormTemp from '@/views/task/components/form/SelfFormTemp.vue'
import { editAppealApi, getAppealEditApi } from '@/api/egt/appeal'

export default {
  name: 'Update',
  components: { SelfFormTemp },
  mixins: [update],
  data() {
    return {
      formData: {
        enterprise_name: '',
        old_info: '',
        numberid: '',
        name: '',
        sex: '',
        content: '',
        img: '',
        act: '',
        refuse: ''
      }
    }
  },
  computed: {
    filters() {
      const filters = [
        {
          label: '企业名称',
          prop: 'enterprise_name',
          type: 'text'
        },
        {
          label: '申诉信息',
          prop: 'old_info',
          type: 'text'
        },
        {
          label: '变更身份证号码',
          prop: 'numberid',
          type: 'text'
        },
        {
          label: '变更身份证姓名',
          prop: 'name',
          type: 'text'
        },
        {
          label: '变更身份证性别',
          prop: 'sex',
          type: 'text'
        },
        {
          label: '备注',
          prop: 'content',
          type: 'text'
        }
      ]
      if (this.formData.picurl) {
        filters.push({
          label: '附件',
          prop: 'img',
          type: 'img'
        })
      }
      if (+this.data.status === 0) {
        filters.push({
          label: '审核',
          prop: 'act',
          type: 'select',
          settings: {
            options: [
              {
                label: '通过',
                value: 'agree'
              },
              {
                label: '驳回',
                value: 'refuse'
              }
            ]
          }
        })
      }
      if (this.formData.act === 'refuse') {
        filters.push({
          label: '驳回原因',
          prop: 'refuse',
          type: 'textarea'
        })
      }
      return filters
    }
  },
  watch: {
    _visible(val) {
      if (val) {
        this.getParams()
      }
    }
  },
  methods: {
    async getParams() {
      const api = this.data.id ? getAppealEditApi : undefined
      if (!api) return
      try {
        this.loading = true
        const res = await api({ id: this.data.id })
        if (res.code === 200) {
          this.formData = res.data.data
          this.formData.img = 'https://hrcloud.obs.cn-north-4.myhuaweicloud.com/' + res.data.data.picurl
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    },
    async submitCallback() {
      if (!this.data.id) return
      try {
        this.submitLoading = true
        const postData = {
          id: this.data.id,
          act: this.formData.act,
          refuse: this.formData.refuse
        }
        if (postData.act === 'refuse') {
          if (!postData.refuse) {
            this.$message.error('请填写驳回原因')
            return Promise.reject({ success: false, msg: '请填写驳回原因' })
          }
        }
        const res = await editAppealApi(postData)
        if (res.code === 200) {
          this.$emit('submit-success')
          return Promise.resolve({ success: true })
        } else {
          return Promise.reject({ success: false, msg: res.msg || res.message || '编辑失败' })
        }
      } catch (e) {
        console.log(e)
        return Promise.reject({ success: false, msg: e.msg || e.message || '编辑失败' })
      } finally {
        this.submitLoading = false
      }
    }
  }
}
</script>

<template>
  <el-dialog title="编辑申诉" :visible.sync="_visible" width="800px">
    <self-form-temp ref="formWrap" :filters="filters" :form-data.sync="formData" :inline="false" label-width="120px" />
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel()">取消</el-button>
      <el-button type="primary" @click="handleSubmit()">保存</el-button>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
