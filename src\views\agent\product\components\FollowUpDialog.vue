<template>
  <el-dialog
    :title="finance.product"
    :visible.sync="dialogVisible"
    :before-close="close"
  >
    <el-form
      ref="dataForm"
      v-loading="loading"
      :model="formData"
      :rules="rules"
    >
      <h4>跟进记录</h4>
      <el-form-item>
        <ul class="list">
          <li v-for="(item,index) in list" :key="index">
            <p>跟进人：{{ item.nickname }}</p>
            <p>跟进时间：{{ item.created_at }}</p>
            <p>跟进内容：{{ item.content }}</p>
          </li>
        </ul>
      </el-form-item>
      <el-form-item label="跟进内容" prop="content">
        <el-input
          v-model="formData.content"
          type="textarea"
          :rows="4"
          placeholder="请输入跟进内容"
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">关 闭</el-button>
      <el-button type="primary" :loading="btn_loading" @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { followUpIndex, followUpStore } from '@/api/agent/finance'
export default {
  name: 'FollowUpDialog',
  props: {
    finance: {
      type: Object,
      required: true
    },
    dialogVisible: Boolean
  },
  data() {
    return {
      loading: false,
      btn_loading: false,
      list: [],
      formData: {},
      rules: {
        content: [
          { required: true, message: '请输入跟进内容', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    dialogVisible: function(indexVal, oldVal) {
      if (indexVal === true) {
        this.formData = {
          finance_id: this.finance.id
        }
        this.getData()
      }
    }
  },
  methods: {
    getData() {
      this.loading = true
      var that = this
      followUpIndex({ finance_id: this.formData.finance_id }).then(function(res) {
        if (res.code === 200 && res.data) {
          that.list = res.data
        }
      }).catch((e) => {
        console.log(e)
      }).finally(() => {
        that.loading = false
      })
    },
    submit() {
      var that = this
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          that.btn_loading = true
          followUpStore(this.formData).then(function(res) {
            that.btn_loading = false
            that.close()
          }).catch(() => {
            that.btn_loading = false
          })
        }
      })
    },
    close() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep.el-dialog__body{
    padding-top: 0;
  }
.list{
  margin: 0;
  padding: 0;
  max-height: 300px;
  overflow-y: scroll;
  list-style: none;
  li{
    margin-bottom: 10px;
    border-bottom: 1px dotted #dedede;
    p{
      margin: 7px 0;
      line-height: 20px;
    }
  }
}
</style>
