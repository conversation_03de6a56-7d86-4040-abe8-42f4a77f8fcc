<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'

export default {
  name: 'AttendanceCompanyMonthlyRank',
  components: { StatisticsTemplate },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      config: {
        key: 'attendance_company_monthly_rank',
        filters: [
          {
            label: '选择时间范围',
            prop: 'time_range',
            type: 'datePicker',
            settings: {
              type: 'daterange',
              placeholder: ['开始日期', '结束日期']
            }
          }
        ],
        tableSettings: {
          columns: [
            {
              label: 'ID',
              prop: 'id',
              width: 60,
              sortable: true
            },
            {
              label: '公司名称',
              prop: 'company_name',
              sortable: true
            },
            {
              label: '考勤使用员工数',
              prop: 'attendance_employee_count',
              sortable: true
            },
            {
              label: '考勤次数',
              prop: 'attendance_count',
              sortable: true
            }
          ]
        }
      }
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  }
}
</script>

<template>
  <el-dialog :close-on-click-modal="false" :visible.sync="_visible" title="月活跃企业排行" append-to-body>
    <StatisticsTemplate :config="config">
      <template v-slot:topActions>
        <el-button type="primary">导出</el-button>
      </template>
    </StatisticsTemplate>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
