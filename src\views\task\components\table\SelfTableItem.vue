<script>
export default {
  name: 'SelfTableItem',
  props: {
    column: {
      type: Object,
      default: () => {}
    },
    configKey: {
      type: String,
      default: ''
    },
    tableWidth: {
      type: [Number, String],
      default: ''
    }
  },
  methods: {
    getWidth(width) {
      if (!width) return ''
      if (typeof width === 'string' && width.indexOf('%') !== -1) {
        width = width.replace('%', '')
        return this.tableWidth * width / 100 + 'px'
      }
      return width
    }
  }
}
</script>

<template>
  <el-table-column :label="column.label" :prop="column.prop" :width="getWidth(column.width)" :min-width="getWidth(column.minWidth)" :align="column.align" :sortable="column.sortable" :show-overflow-tooltip="true">
    <template v-if="column.children && column.children.length">
      <self-table-item v-for="(child, childIndex) in column.children" :key="childIndex + '_c_' + Math.random()" :columns="child.children" />
    </template>
    <template v-if="column.isSlot" slot-scope="{row}">
      <slot :name="configKey + '_' + column.prop" :row="row[column.prop]" />
    </template>
    <template v-else-if="column.type === 'img'">
      <el-image v-if="column.settings" style="width: 100px; height: 100px" :src="column.settings.baseUrl ? (column.settings.baseUrl + row[item.prop]) : row[item.prop]" :fit="'fit'" />
    </template>
    <template v-else slot-scope="{row}">
      {{ row[column.prop] }}
    </template>
  </el-table-column>
</template>

<style scoped>

</style>
