<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import CateUpdate from '@/views/egt/legal/components/CateUpdate.vue'
import Terms from '@/views/egt/legal/components/Terms.vue'
import { deleteContractTemplateApi, getContractCategoryListApiForTemplate, getContractTemplateListApi } from '@/api/egt/contract'

export default {
  name: 'Contract',
  components: { Terms, CateUpdate, StatisticsTemplate },
  filters: {
    formatDate(value) {
      if (value) {
        return new Date(value * 1000).toLocaleString()
      }
      return ''
    }
  },
  data() {
    return {
      config: {
        key: 'contract',
        filters: [
          {
            label: '合同名称',
            prop: 'name',
            type: 'input'
          },
          {
            label: '合同类型',
            prop: 'category',
            type: 'select',
            settings: {
              options: []
            }
          }
        ],
        tableSettings: {
          api: getContractTemplateListApi,
          index: true,
          field: {
            page: 'pageIndex',
            limit: 'pageSize',
            total: 'total'
          },
          columns: [
            {
              label: '合同类型名称',
              prop: 'model_name',
              sortable: true
            },
            {
              label: '合同类型简介',
              prop: 'model_des',
              sortable: true
            },
            {
              label: '所属分类',
              prop: 'cate_name',
              sortable: true
            },
            {
              label: '创建时间',
              prop: 'add_time',
              isSlot: true,
              sortable: true
            },
            {
              label: '操作',
              prop: 'action',
              width: 300,
              fixed: 'right',
              align: 'center',
              isSlot: true
            }
          ]
        }
      },
      updateVisible: false,
      row: {},
      termUpdateVisible: false
    }
  },
  created() {
    this.handleGetCategory()
  },
  methods: {
    async handleGetCategory() {
      const res = await getContractCategoryListApiForTemplate()
      if (res.code === 200) {
        const cate = this.config.filters.find(item => item.prop === 'category')
        if (!cate) return
        cate.settings = cate.settings || {}
        cate.settings.options = []
        if (res.data.data && res.data.data.length) {
          cate.settings.options = res.data.data.map(item => ({
            label: item.cate_name,
            value: item.id
          }))
        }
        cate.settings.options.unshift({
          label: '全部',
          value: ''
        })
      }
    },
    handleAdd() {
      this.updateVisible = true
      this.row = {}
    },
    handleUpdate(row) {
      this.updateVisible = true
      this.row = row
    },
    async handleDelete(row) {
      if (!row.id) return
      try {
        this.$refs.statisticsTemplate.setLoading(row, true)
        const res = await deleteContractTemplateApi({ id: row.id })
        if (res.code === 200) {
          this.$message.success('删除成功')
          await this.$refs.statisticsTemplate.handleGetData()
        } else {
          this.$message.error(res.msg || res.message || '删除失败')
        }
      } catch (error) {
        console.log('[ error ] >', error)
        this.$message.error(error.msg || error.message || '删除失败')
      } finally {
        this.$refs.statisticsTemplate.setLoading(row, false)
      }
    },
    handleUpdateTerm(row) {
      this.termUpdateVisible = true
      this.row = row
    },
    toCate() {
      this.$router.push({ path: '/egt/legal/contractCate' })
    },
    handleSubmitSuccess() {
      this.$refs.statisticsTemplate.handleGetData()
    }
  }
}
</script>

<template>
  <div class="page-wrap">
    <StatisticsTemplate ref="statisticsTemplate" self-key="contract" :config="config">
      <template #topActions>
        <div>
          <el-button type="primary" @click="toCate">合同分类</el-button>
          <el-button type="primary" @click="handleAdd">新增合同类型</el-button>
        </div>
      </template>
      <template #contract_add_time="{row}">
        {{ row.add_time | formatDate }}
      </template>
      <template #contract_action="{row}">
        <el-button size="mini" type="primary" @click="handleUpdateTerm(row)">编辑合同条款</el-button>
        <el-button size="mini" @click="handleUpdate(row)">编辑</el-button>
        <el-popconfirm
          title="确定删除吗？"
          style="margin-left: 10px;"
          @onConfirm="handleDelete(row)"
        >
          <el-button slot="reference" type="danger" size="mini">删除</el-button>
        </el-popconfirm>
      </template>
    </StatisticsTemplate>
    <CateUpdate :visible.sync="updateVisible" :data="row" @submit-success="handleSubmitSuccess" />
    <Terms :visible.sync="termUpdateVisible" :data="row" />
  </div>
</template>

<style lang="scss" scoped>
.page-wrap {
  height: calc(100% - 20px);
}
</style>
