<!-- 媒体投放 -->
<template>
  <div class="list-wrap">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="列表" name="list">
        <media-list />
      </el-tab-pane>
      <el-tab-pane label="分类" name="cate">
        <media-cate />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import MediaList from './components/media/list.vue'
import MediaCate from './components/media/cate.vue'

export default {
  components: {
    MediaList, MediaCate
  },
  data() {
    return {
      activeName: 'list'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event)
    }
  }
}
</script>

<style scoped lang="scss">
  .list-wrap {
    height: 100%;

    ::v-deep .el-tabs{
      height: 100%;
      overflow: auto;
      .el-tabs__content{
        height: calc(100% - 41px - 15px);
      }
    }

    ::v-deep .card-wrap {
      height: 100%;
      border: none;

      .el-card__body {
        height: 100%;
      }

      .box-card {
        height: 100%;
        border: none;

        .el-card__body {
          height: calc(100% - 59px);
          overflow: auto;
        }
      }
    }
  }
</style>
