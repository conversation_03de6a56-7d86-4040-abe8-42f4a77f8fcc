(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6018924f"],{"0317":function(e,t,a){},"3cbc":function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"pan-item",style:{zIndex:e.zIndex,height:e.height,width:e.width}},[a("div",{staticClass:"pan-info"},[a("div",{staticClass:"pan-info-roles-container"},[e._t("default")],2)]),e._v(" "),a("div",{staticClass:"pan-thumb",style:{backgroundImage:"url("+e.image+")"}})])},s=[],l=(a("c5f6"),{name:"PanThumb",props:{image:{type:String,required:!0},zIndex:{type:Number,default:1},width:{type:String,default:"150px"},height:{type:String,default:"150px"}}}),n=l,i=(a("c0c1"),a("2877")),o=Object(i["a"])(n,r,s,!1,null,"799537af",null);t["a"]=o.exports},"7faf":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-card",{staticStyle:{"margin-bottom":"20px"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("关于我")])]),e._v(" "),a("div",{staticClass:"user-profile"},[a("div",{staticClass:"box-center"},[a("pan-thumb",{attrs:{image:e.user.avatar,height:"100px",width:"100px",hoverable:!1}},[a("div",[e._v("您好")]),e._v("\n        "+e._s(e.user.role)+"\n      ")])],1),e._v(" "),a("div",{staticClass:"box-center"},[a("div",{staticClass:"user-name text-center"},[e._v("用户名："+e._s(e.user.name))]),e._v(" "),a("div",{staticClass:"user-role text-center text-muted"},[e._v("角色："+e._s(e._f("uppercaseFirst")(e.user.role)))])])])])},s=[],l=a("3cbc"),n={components:{PanThumb:l["a"]},props:{user:{type:Object,default:function(){return{name:"",email:"",avatar:"",role:""}}}}},i=n,o=(a("b0d0"),a("2877")),c=Object(o["a"])(i,r,s,!1,null,"f6e9c326",null);t["default"]=c.exports},8118:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"请输入原密码：",prop:"original_password"}},[a("el-input",{attrs:{type:"password"},model:{value:e.ruleForm.original_password,callback:function(t){e.$set(e.ruleForm,"original_password",t)},expression:"ruleForm.original_password"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"请输入新密码：",prop:"password"}},[a("el-input",{attrs:{type:"password"},model:{value:e.ruleForm.password,callback:function(t){e.$set(e.ruleForm,"password",t)},expression:"ruleForm.password"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"请确认新密码：",prop:"password_confirmation"}},[a("el-input",{attrs:{type:"password"},model:{value:e.ruleForm.password_confirmation,callback:function(t){e.$set(e.ruleForm,"password_confirmation",t)},expression:"ruleForm.password_confirmation"}})],1),e._v(" "),a("el-form-item",[a("el-row",{attrs:{type:"flex",justify:"center"}},[a("el-col",{attrs:{span:12}},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.changeProfilePassword}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{type:"warning",size:"small"},on:{click:function(t){e.ruleForm={}}}},[e._v("重置")])],1)],1)],1)],1)],1)},s=[],l=a("c24f"),n={name:"ChangePassword",data:function(){var e=this,t=function(t,a,r){a!==e.ruleForm.password&&r(new Error("两次输入密码不一致")),a.length<6?r(new Error("密码不能小于6位")):r()};return{ruleForm:{type:1},rules:{original_password:[{required:!0,message:"原始密码必填",trigger:"blur"}],password:[{required:!0,message:"新密码必填",trigger:"blur"}],password_confirmation:[{required:!0,trigger:"blur",validator:t}]}}},methods:{changeProfilePassword:function(){var e=this;this.$refs.ruleForm.validate((function(t){t&&Object(l["a"])(e.ruleForm).then((function(t){e.$message.success("修改成功")}))}))}}},i=n,o=a("2877"),c=Object(o["a"])(i,r,s,!1,null,"816038be",null);t["default"]=c.exports},"972a":function(e,t,a){"use strict";a("0317")},a010:function(e,t,a){},b0d0:function(e,t,a){"use strict";a("a010")},c0c1:function(e,t,a){"use strict";a("c288")},c288:function(e,t,a){},ecac:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[e.user?a("div",[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:6,xs:24}},[a("user-card",{attrs:{user:e.user}})],1),e._v(" "),a("el-col",{attrs:{span:18,xs:24}},[a("el-card",[a("el-tabs",{model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"修改密码",name:"activity"}},[a("change-password")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"修改头像",name:"timeline"}},[a("change-avatar",{attrs:{user:e.user}})],1)],1)],1)],1)],1)],1):e._e()])},s=[],l=(a("7f7f"),a("5530")),n=a("2f62"),i=a("7faf"),o=a("8118"),c=a("fed2"),u={name:"Profile",components:{UserCard:i["default"],ChangePassword:o["default"],ChangeAvatar:c["default"]},data:function(){return{user:{},activeTab:"activity"}},computed:Object(l["a"])({},Object(n["b"])(["name","avatar","roles"])),created:function(){this.getUser()},methods:{getUser:function(){this.user={name:this.name,role:this.roles.join(" | "),email:"<EMAIL>",avatar:this.avatar}}}},p=u,d=a("2877"),m=Object(d["a"])(p,r,s,!1,null,null,null);t["default"]=m.exports},fed2:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",[a("el-form-item",[a("el-row",{attrs:{type:"flex",justify:"center"}},[a("el-col",{attrs:{span:12}},[a("el-upload",{staticClass:"avatar-uploader",attrs:{action:e.upload_url,headers:e.headers,"show-file-list":!1,"on-success":e.handleAvatarSuccess,"before-upload":e.beforeAvatarUpload}},[e.ruleForm.avatar?a("img",{staticClass:"avatar",attrs:{src:e.ruleForm.avatar}}):a("i",{staticClass:"el-icon-plus avatar-uploader-icon"}),e._v(" "),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("只能上传jpg/png文件，且不超过2M")])])],1)],1)],1),e._v(" "),a("el-form-item",[a("el-row",{attrs:{type:"flex",justify:"center"}},[a("el-col",{attrs:{span:8}},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.saveAvatar}},[e._v("保存")])],1)],1)],1)],1)],1)},s=[],l=a("c24f"),n={name:"ChangeAvatar",props:{user:{type:Object,default:function(){return{avatar:""}}}},data:function(){return{ruleForm:{avatar:this.user.avatar},upload_url:"https://api.china9.cn/manager/upload",headers:{Authorization:"Bearer "+this.token}}},methods:{handleAvatarSuccess:function(e,t){this.ruleForm.avatar=e.data.url},beforeAvatarUpload:function(e){var t="image/jpeg"===e.type,a="image/png"===e.type,r=e.size/1024/1024<2;return t||a||this.$message.error("上传头像图片只能是 JPG 或 PNG 格式!"),r||this.$message.error("上传头像图片大小不能超过 2MB!"),(t||a)&&r},saveAvatar:function(){var e=this;this.ruleForm.type=2,Object(l["a"])(this.ruleForm).then((function(t){e.$message.success("更新成功，刷新页面后生效")}))}}},i=n,o=(a("972a"),a("2877")),c=Object(o["a"])(i,r,s,!1,null,null,null);t["default"]=c.exports}}]);