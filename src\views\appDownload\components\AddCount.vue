<script>
/*eslint-disable*/
import {addCount, countDetail, getStoreList} from "@/api/appDownload";

export default {
  name: "AddCount",
  props: ['id', 'data'],
  watch: {
    id: {
      handler(v){
        if (v){
          this.handleGetDetail(v);
        }
      },
      deep: true,
      immediate: true
    }
  },
  data(){
    return {
      loading: false,
      form: {
        market: undefined,
        apptype: undefined,
        downloads: 0,
        totalday: ''
      },
      rules: {
        market: [{required: true, message: '请选择应用市场', trigger: 'change'}],
        apptype: [{required: true, message: '请选择操作系统', trigger: 'change'}],
        downloads: [{required: true, message: '请输入下载量', trigger: 'blur'}],
        totalday: [{required: true, message: '请选择统计日期', trigger: 'change'}],
      },
      storeList: [],
      appTypeList: [
        {
          id: 1,
          name: "Android"
        },
        {
          id: 2,
          name: "I<PERSON>"
        },
      ]
    }
  },
  methods: {
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          addCount(this.form).then(res => {
            if (res.code === 200){
              this.$message.success(res.msg || res.message || '保存成功');
              this.close(true);
            }else{
              this.$message.error(res.msg || res.message || '保存失败');
            }
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    close(refresh){
      if (typeof refresh !== 'boolean'){
        refresh = false;
      }
      this.$refs.form.resetFields();
      this.$emit("closeDialog", refresh);
    },
    handleGetStoreList() {
      this.loading = true;
      getStoreList().then(res => {
        if (res.code === 200 && res.data) {
          this.storeList = res.data;
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    handleGetDetail(id){
      this.loading = true;
      countDetail({id}).then(res => {
        if (res.code && res.data){
          this.form = res.data;
        }
      }).finally(() => {
        this.loading = false;
      })
    }
  },
  mounted() {
    this.handleGetStoreList();
  }
}
</script>

<template>
<div id="addCount" v-loading="loading">
  <el-form :model="form" ref="form" :rules="rules" label-width="100px">
    <el-form-item label="应用市场" prop="market">
      <el-select v-model="form.market" placeholder="请选择">
        <el-option
          v-for="item in storeList"
          :key="item.id"
          :label="item.name"
          :value="item.id">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="操作系统" prop="apptype">
      <el-select v-model="form.apptype" placeholder="请选择">
        <el-option
          v-for="item in appTypeList"
          :key="item.id"
          :label="item.name"
          :value="item.id">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="下载量" prop="downloads">
      <el-input-number v-model="form.downloads" />
    </el-form-item>
    <el-form-item label="统计日期" prop="totalday">
      <el-date-picker
        v-model="form.totalday"
        format="yyyy-MM"
        value-format="yyyy-MM"
        type="month"
        placeholder="统计日期"
      />
    </el-form-item>
  </el-form>
  <el-row slot="footer" style="text-align: center;margin-top: 24px;">
    <el-button type="primary" @click="submitForm">保存</el-button>
    <el-button type="default" @click="close(false)">取消</el-button>
  </el-row>
</div>
</template>

<style scoped>

</style>
