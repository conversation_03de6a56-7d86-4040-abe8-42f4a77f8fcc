<template>
  <div class="app-container" style="height: 100%; padding-bottom: 0;padding-top: 0">
    <div class="clearfix">
      <!--<span>banner图</span>-->
      <el-button style="float: right; padding: 3px 0" type="text" @click="createdView">新增banner图</el-button>
    </div>
    <div class="table">
      <el-table v-loading="loading" :data="tableData" style="width: 100%" height="100%">
        <el-table-column prop="id" label="ID" width="100" />
        <el-table-column prop="title" label="名称" />
        <el-table-column label="图片">
          <template slot-scope="scope">
            <el-image style="width: 32px; height: 32px" :src="scope.row.imgfile" fit="fill" />
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-link :underline="false" type="primary" @click="editDialog(scope.row)">编辑</el-link>
            <el-popconfirm title="确定删除当前记录吗？" @onConfirm="deleteHandle(scope.row)">
              <el-link slot="reference" :underline="false" type="danger">删除</el-link>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog :close-on-click-modal="false" title="" :visible.sync="dialogVisible" width="50%">
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" size="small" label-width="120px" class="demo-ruleForm">
        <el-form-item label="banner名称：" prop="title">
          <el-input v-model="ruleForm.title" style="width: 70%" />
        </el-form-item>
        <el-form-item label="图片:" prop="imgfile">
          <div class="category-upload ">
            <el-upload
              class="avatar-uploader"
              :action="upload_url"
              :headers="headers"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="ruleForm.imgfile" :src="ruleForm.imgfile" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon" />
            </el-upload>
          </div>
        </el-form-item>
        <el-form-item label="跳转方式：" prop="type">
          <el-select v-model="ruleForm.type" placeholder="请选择">
            <el-option label="无跳转链接" :value="0" />
            <el-option label="外链" :value="1" />
            <el-option label="商品详情" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="跳转链接：" prop="url">
          <el-input v-model="ruleForm.url" style="width: 70%" />
          <el-link :underline="false" type="danger">跳转方式为商品详情时，跳转链接为商品id</el-link>
        </el-form-item>
        <span slot="footer" class="dialog-footer">
          <el-button size="small" @click="dialogVisible = false">取 消</el-button>
          <el-button size="small" type="primary" :loading="b_loading" @click="saveHandle">确 定</el-button>
        </span>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getAdvertising, del, store } from '../../api/advertising'
export default {
  name: 'Advertising',
  data() {
    return {
      upload_url: process.env.VUE_APP_BASE_API + '/upload',
      headers: {
        Authorization: 'Bearer ' + this.token
      },
      b_loading: false,
      loading: false,
      ruleForm: {
        imgfile: ''
      },
      dialogVisible: false,
      tableData: [],
      rules: {
        title: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        imgfile: [
          { required: true, message: '图片必须上传', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      getAdvertising().then(response => {
        if (response.code === 200) {
          this.tableData = response.data
          this.loading = false
        }
      })
    },
    createdView() {
      this.b_loading = false
      this.ruleForm = {}
      this.dialogVisible = true
    },
    saveHandle() {
      this.$refs.ruleForm.validate((valid) => {
        this.b_loading = true
        if (valid) {
          store(this.ruleForm).then(response => {
            if (response.code === 200) {
              this.dialogVisible = false
              this.$message.success('操作成功')
              this.getList()
            }
          }).catch(() => {
            this.b_loading = false
          })
        } else {
          this.b_loading = false
          return false
        }
      })
    },
    editDialog(row) {
      this.b_loading = false
      this.ruleForm = row
      this.dialogVisible = true
    },
    deleteHandle(row) {
      del({ id: row.id }).then(response => {
        if (response.code === 200) {
          this.dialogVisible = false
          this.$message.success('操作成功')
          this.getList()
        }
      })
    },
    handleAvatarSuccess(res, file) {
      this.ruleForm.imgfile = res.data.url
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG && !isPNG) {
        this.$message.error('上传的图标只能是 JPG 或 PNG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传的图片大小不能超过 2MB!')
      }
      return (isJPG || isPNG) && isLt2M
    }
  }
}
</script>

<style scoped lang="scss">
.card-wrap ::v-deep .el-card__body {
  height: 100%;
}
.table {
  margin-top: 20px;
  height: calc(100% - 20px - 22px);
}
.category-upload {
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &:hover {
      border-color: #409EFF;
    }
  }
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
