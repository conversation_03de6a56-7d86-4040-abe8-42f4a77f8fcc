<template>
  <el-card class="box-card-component">
    <div slot="header" class="box-card-header">
      <img src="https://wpimg.wallstcn.com/e7d23d71-cf19-4b90-a1cc-f56af8c0903d.png">
    </div>
    <div style="position:relative;">
      <pan-thumb :image="avatar" class="panThumb" />
      <mallki class-name="mallki-text" :text="agent.name" />
      <div style="padding-top:40px;display: flex;flex-direction:row;">
        <div style="flex: 1;text-align: center; border-right: 1px solid #e8e8e8">
          <div style="padding: 5px 0">
            <span style="font-size: 14px;">￥</span><span style="font-size: 20px;font-weight: bold">{{ agent.usable }}</span>
            <p style="font-size: 16px;margin: 5px 0 0 0;">软件预存款</p>
            <div style="height:10px;" />
<!--            <span style="font-size: 14px;">￥</span><span style="font-size: 20px;font-weight: bold">{{ agent.shopusable }}</span>-->
<!--            <p style="font-size: 16px;margin: 5px 0 0 0;">商城版可用预存款</p>-->
            <span style="font-size: 14px;">￥</span><span style="font-size: 20px;font-weight: bold">{{ agent.hardusable }}</span>
            <p style="font-size: 16px;margin: 5px 0 0 0;">非软件预存款</p>
          </div>
        </div>
        <div style="flex: 1;text-align: center">
          <div style="padding: 5px 0">
            <span style="font-size: 14px;">￥</span><span style="font-size: 20px;font-weight: bold">{{ agent.frozen }}</span>
            <p style="font-size: 16px;margin: 5px 0 0 0;">保证金</p>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
import { mapGetters } from 'vuex'
import PanThumb from '@/components/PanThumb'
import Mallki from '@/components/TextHoverEffect/Mallki'

export default {
  components: { PanThumb, Mallki },

  filters: {
    statusFilter(status) {
      const statusMap = {
        success: 'success',
        pending: 'danger'
      }
      return statusMap[status]
    }
  },
  props: {
    agent: {
      // eslint-disable-next-line vue/require-valid-default-prop
      default: { name: '代理商名称', usable: '0.00', frozen: '0.00' },
      type: Object
    }
  },
  data() {
    return {
      statisticsData: {
        article_count: 1024,
        pageviews_count: 1024
      }
    }
  },
  computed: {
    ...mapGetters([
      'name',
      'avatar',
      'nickname',
      'roles'
    ])
  }
}
</script>

<style lang="scss" >
.box-card-component{
  .el-card__header {
    padding: 0px!important;
  }
}
</style>
<style lang="scss" scoped>
.box-card-component {
  .box-card-header {
    position: relative;
    height: 220px;
    img {
      width: 100%;
      height: 100%;
      transition: all 0.2s linear;
      &:hover {
        transform: scale(1.1, 1.1);
        filter: contrast(130%);
      }
    }
  }
  .mallki-text {
    position: absolute;
    top: 0px;
    right: 0px;
    font-size: 20px;
    font-weight: bold;
  }
  .panThumb {
    z-index: 100;
    height: 70px!important;
    width: 70px!important;
    position: absolute!important;
    top: -45px;
    left: 0px;
    border: 5px solid #ffffff;
    background-color: #fff;
    margin: auto;
    box-shadow: none!important;
    ::v-deep .pan-info {
      box-shadow: none!important;
    }
  }
  .progress-item {
    margin-bottom: 10px;
    font-size: 14px;
  }
  @media only screen and (max-width: 1510px){
    .mallki-text{
      display: none;
    }
  }
}
</style>
