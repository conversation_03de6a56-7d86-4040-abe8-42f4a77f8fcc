<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style scoped lang="scss">
::v-deep .el-button--primary,
::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active,
::v-deep .el-radio-button__orig-radio:checked+.el-radio-button__inner,
::v-deep .el-radio__input.is-checked .el-radio__inner{
  background-color: #4d80ff;
  border-color: #4d80ff;
}
::v-deep .el-radio__input.is-checked .el-radio__inner{
  border-color: #4d80ff;
}
::v-deep .el-tabs__item.is-active,
::v-deep .el-link.el-link--primary,
::v-deep .el-button--text,
::v-deep .el-radio__input.is-checked+.el-radio__label{
  color: #4d80ff;
}
::v-deep .el-table th.is-leaf{
  background: #f2f6f9;
}
</style>
