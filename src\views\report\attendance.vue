<script>
import * as echarts from 'echarts'
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import AiAttendanceCompanyMonthlyRank from '@/views/report/components/AIAttendanceCompanyMonthlyRank.vue'
import AttendanceCompanyMonthlyRank from '@/views/report/components/AttendanceCompanyMonthlyRank.vue'
import SelfCard from '@/views/dashboard/admin/components/SelfCard.vue'
import CountTo from 'vue-count-to'

export default {
  name: 'Attendance',
  components: { SelfCard, AttendanceCompanyMonthlyRank, AiAttendanceCompanyMonthlyRank, StatisticsTemplate, CountTo },
  data() {
    return {
      cards: [
        {
          title: '无感知考勤应用企业数',
          unit: '个',
          num: 100,
          color: '#00DEA2',
          icon: 'el-icon-user-solid',
          desc: '员工数 26662'
        },
        {
          title: '无感知考勤当日考勤总人数',
          unit: '人',
          num: 100,
          color: '#00DEA2',
          icon: 'el-icon-user-solid',
          desc: '2025-03-07'
        },
        {
          title: '无感知考勤当日总次数',
          unit: '次',
          num: 100,
          color: '#00DEA2',
          icon: 'el-icon-user-solid',
          desc: '2025-03-07'
        },
        {
          title: '无感知考勤活跃企业数',
          unit: '个',
          num: 100,
          color: '#00DEA2',
          icon: 'el-icon-user-solid',
          desc: '最近一个月'
        }
      ],
      config: {
        key: 'attendance',
        tableSettings: {
          columns: [
            {
              label: '企业名称',
              prop: 'name'
            },
            {
              label: '无感知考勤使用员工数',
              prop: 'per'
            },
            {
              label: '无感知考勤次数',
              prop: 'num'
            }
          ]
        }
      },
      aiVisible: false,
      visible: false,
    }
  },
  mounted() {
    this.initChart('todayMax', ['9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00'], [820, 932, 901, 934, 1290, 1330, 1320, 800, 1200, 900])
    this.initChart('monthNum', ['9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00'], [820, 932, 901, 934, 1290, 1330, 1320, 800, 1200, 900])
    this.initChart('monthPer', ['9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00'], [820, 932, 901, 934, 1290, 1330, 1320, 800, 1200, 900])
  },
  methods: {
    initChart(id, xData, yData) {
      const chart = echarts.init(document.getElementById(id))
      const option = {
        grid: {
          top: 40,
          bottom: 30,
          left: 40,
          right: 20
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xData,
          axisLine: {
            lineStyle: {
              color: '#409eff'
            }
          },
          axisLabel: {
            color: '#666'
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: '#409eff'
            }
          },
          axisLabel: {
            color: '#666'
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(64,158,255,0.1)'
            }
          }
        },
        series: [{
          data: yData,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#409eff',
            borderWidth: 2
          },
          lineStyle: {
            width: 2,
            shadowColor: 'rgba(64,158,255,0.3)',
            shadowBlur: 10,
            shadowOffsetY: 8
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(64,158,255,0.3)' },
              { offset: 1, color: 'rgba(64,158,255,0.01)' }
            ])
          }
        }],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(255,255,255,0.95)',
          borderColor: '#409eff',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          }
        }
      }
      chart.setOption(option)

      // 窗口调整时自适应
      window.addEventListener('resize', () => chart.resize())
      this.$once('hook:beforeDestroy', () => {
        window.removeEventListener('resize', () => chart.resize())
        chart.dispose()
      })
    },
    showAi() {
      this.aiVisible = true
      this.visible = false
    },
    showMonthly() {
      this.aiVisible = false
      this.visible = true
    }
  }
}
</script>

<template>
  <div class="page-wrap">
    <div style="width: 100%;overflow:hidden;">
      <el-row :gutter="20">
        <el-col v-for="(card, index) in cards" :key="index" :md="6" :sm="12" style="margin-bottom: 20px;">
          <SelfCard :title="card.title">
            <div class="card">
              <div class="content">
                <CountTo :start-val="0" :end-val="card.num" :duration="1000" />
                <span>{{ card.unit }}</span>
              </div>
              <div class="desc">{{ card.desc }}</div>
            </div>
          </SelfCard>
        </el-col>
      </el-row>
    </div>
    <el-alert
      title="数据统计中（首次加载可能需要30s左右）"
      type="warning"
    />
    <SelfCard title="当日无感知考勤峰值" class="mt20">
      <div class="echart">
        <div id="todayMax" style="height: 300px;" />
      </div>
    </SelfCard>
    <SelfCard title="当月无感知考勤人数" class="mt20">
      <div class="echart">
        <div id="monthNum" style="height: 300px;" />
      </div>
    </SelfCard>
    <SelfCard title="当月无感知考勤人数" class="mt20">
      <div class="echart">
        <div id="monthPer" style="height: 300px;" />
      </div>
    </SelfCard>
    <SelfCard title="AI考勤月活跃企业TOP10" class="mt20">
      <template v-slot:right>
        <div class="action">
          <el-button type="text" @click="showAi()">AI考勤月活跃企业排行</el-button>
          <el-button type="text" @click="showMonthly()">考勤月活跃企业排行</el-button>
        </div>
      </template>
      <div>
        <StatisticsTemplate :config="config" />
        <AiAttendanceCompanyMonthlyRank :visible.sync="aiVisible " />
        <AttendanceCompanyMonthlyRank :visible.sync="visible " />
      </div>
    </SelfCard>
  </div>
</template>

<style scoped lang="scss">
$primary-color: #4d80ff; // 在此处修改主色
$primary-hover: rgba($primary-color, 0.1);
// 后续颜色值使用这些变量
.page-wrap {
  height: 100%;
  overflow: auto;
  .el-card {
    &:hover {
      box-shadow: 0 10px 20px rgba(64, 158, 255, 0.1); // 修改阴影颜色
    }

    ::v-deep .el-card__header {
      background: linear-gradient(90deg, rgba(64, 158, 255, 0.06), rgba(64, 158, 255, 0.03)); // 蓝
      border-bottom: 1px solid rgba(64, 158, 255, 0.3); // 蓝
    }
  }

  .card {
    padding: 12px 0;

    .content {
      display: flex;
      align-items: baseline;
      margin-bottom: 12px;

      span:first-child {
        font-size: 32px;
        font-weight: 700;
        color: $primary-color; // Element UI 蓝
        margin-right: 8px;
      }

      span:last-child {
        font-size: 14px;
        color: #666;
      }
    }

    .el-icon-user-solid {
      background: $primary-color; // 图标背景
      color: white;
      padding: 8px;
      border-radius: 50%;
      margin-right: 10px;
    }

    .el-icon-user-solid {
      background: $primary-color;
      color: white;
      padding: 8px;
      border-radius: 50%;
      margin-right: 10px;
    }

    .desc {
      color: #999;
      font-size: 14px;
      padding-top: 8px;
      text-align: right;
      border-top: 1px dashed #eee;
    }
  }

  .action {
    ::v-deep .el-button {
      background: rgba(64, 158, 255, 0.08); // 按钮背景
      border: 1px solid rgba(64, 158, 255, 0.3); // 按钮边框
      padding-left: 10px;
      padding-right: 10px;

      &:hover {
        background: rgba(64, 158, 255, 0.2);
        color: $primary-color;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
      }

      &.is-active,
      &:active {
        background: rgba(64, 158, 255, 0.2); // 激活状态
        border-color: $primary-color;
      }
    }
  }
}
</style>

