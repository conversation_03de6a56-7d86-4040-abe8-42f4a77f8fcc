<template>
  <div class="app-container" style="height: 100%;padding-bottom: 0">
    <div class="list-wrap integral">
      <el-table
          v-loading="loading"
          :data="tableData"
          stripe
          style="width: 100%"
          :default-sort="{prop: 'created_at', order: 'descending'}"
          height="calc(100% - 96px)"
      >
        <el-table-column label="评价ID" prop="id" width="80" />
        <el-table-column label="商品封面" width="100">
          <template slot-scope="scope">
            <el-image
                style="width: 100px; max-height: 100px"
                :src="scope.row.imgurl"
                :preview-src-list="[scope.row.imgurl]"
                fit="fill"
            />
          </template>
        </el-table-column>
        <el-table-column label="商品名称" prop="title" />
        <el-table-column label="评价者" prop="user" />
        <el-table-column label="评价内容" prop="comments" />
        <el-table-column label="评价图片" width="200">
          <template slot-scope="scope">
            <el-row v-if="scope.row.comimgurllist.length > 0" :gutter="0">
              <el-col v-for="item in scope.row.comimgurllist" :key="item" :span="6">
                <div>
                  <el-image
                      style="width: 100%;height: 50px"
                      :src="item"
                      :preview-src-list="scope.row.comimgurllist"
                      fit="fill"
                  />
                </div>
              </el-col>
            </el-row>
          </template>
        </el-table-column>
        <el-table-column label="评价视频" width="300">
          <template slot-scope="scope">
            <el-image
                v-show="show1!=scope.row.id&&scope.row.comvideo"
                :id="'myimg'+scope.row.id"
                style="width: 100px; max-height: 100px"
                :src="scope.row.videoimg"
                @click="handelVideo(scope.row)"
            />
            <div v-show="show==scope.row.id" class="test_two_box" :class="'myVideo'+scope.row.id">
              <video :id="'myVideo'+scope.row.id" class="video-js">
                <source :src="scope.row.comvideo" type="video/mp4">
              </video>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="评分" prop="score" />
        <el-table-column label="创建时间" prop="created_at" />
        <el-table-column label="显示状态">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.status" active-color="#13ce66" inactive-color="#ff4949" @change="handelSwitch(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="handelEdit(scope.row)">回复</el-button>
            <el-divider direction="vertical" />
            <el-popconfirm title="确定删除吗并且无法恢复？" @onConfirm="destroy(scope.row)">
              <el-button slot="reference" type="text">删除</el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <pagination
          v-show="total>0"
          :total="total"
          :page.sync="query.page"
          :limit.sync="query.limit"
          @pagination="getList"
      />

      <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="dialogFormVisible">
        <el-form ref="form" :model="form" :rules="rules">
          <el-form-item label="商品名称" :label-width="formLabelWidth" prop="title">
            {{ form.title }}
          </el-form-item>
          <el-form-item label="评价者" :label-width="formLabelWidth" prop="user">
            {{ form.user }}
          </el-form-item>
          <el-form-item label="评价内容" :label-width="formLabelWidth" prop="comments">
            {{ form.comments }}
          </el-form-item>
          <el-form-item v-if="form.comimgurllist" label="评价图片：" :label-width="formLabelWidth">
            <div style="width:350px;">
              <el-row v-if="form.comimgurllist" :gutter="10">
                <el-col v-for="item in form.comimgurllist" :key="item" :span="4" justify="center">
                  <div>
                    <el-image
                        style="width: 100%;"
                        :src="item"
                        :preview-src-list="form.comimgurllist"
                        fit="fill"
                    />
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-form-item>
          <el-form-item label="评分" :label-width="formLabelWidth" prop="score">
            {{ form.score }}
            {{ form.wuliu }}
          </el-form-item>
          <el-form-item label="状态" :label-width="formLabelWidth">
            <el-switch
                v-model="form.status"
                active-color="#13ce66"
                inactive-color="#ff4949"
            />
          </el-form-item>
          <el-form-item label="回复" :label-width="formLabelWidth" prop="reply">
            <el-input v-model="form.reply" type="textarea" :rows="5" maxlength="100" show-word-limit placeholder="请输入回复" style="width: 400px" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </div>
      </el-dialog>

    </div>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { fetchList, store, remove } from '@/api/evaluation'
import { mapGetters } from 'vuex'

export default {
  name: 'Personal',
  components: { Pagination },
  data() {
    return {
      total: 0,
      query: {
        page: 1,
        limit: 10,
        name: null,
        number: null,
        status: null
      },
      show: 0,
      show1: 0,
      options: [],
      tableData: [],
      myPlayer: null,
      loading: true,
      imgshow: true,
      videoshow: false,
      form: {
        reply: ''
      },
      rules: {
        reply: [
          { required: false, message: '规则描述不能为空', max: 100, trigger: 'blur' }
        ]
      },
      dialogFormVisible: false,
      formLabelWidth: '150px',
      title: '新增规则',
      circulation_disabled: false,
      upload_url: process.env.VUE_APP_BASE_API + '/uploadReturnId',
      fileList: []
    }
  },
  computed: {
    ...mapGetters([
      'token'
    ])
  },
  created() {
    this.getList()
  },
  methods: {

    getList() {
      this.loading = true
      fetchList(this.query).then(response => {
        this.total = response.data.total
        this.tableData = response.data.data
        console.log(this.tableData, 'error submit!!')
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    initVideo(id) {
      // 初始化视频方法
      const myPlayer = this.$video('myVideo' + id, { // eslint-disable-line no-unused-vars
        // 确定播放器是否具有用户可以与之交互的控件。没有控件，启动视频播放的唯一方法是使用autoplay属性或通过Player API。
        controls: true,
        // 自动播放属性,muted:静音播放
        autoplay: 'muted',
        // 建议浏览器是否应在<video>加载元素后立即开始下载视频数据。
        preload: 'auto',
        // 设置视频播放器的显示宽度（以像素为单位）
        width: '300px',
        // 设置视频播放器的显示高度（以像素为单位）
        height: '150px'
      })
    },

    handelUnit() {
      if (this.form.unit === 'forever') {
        this.form.circulation = 0
        this.circulation_disabled = true
      } else {
        if (this.form.circulation <= 0) {
          this.form.circulation = 1
        }
        this.circulation_disabled = false
      }
    },
    handelSwitch(row) {
      store(row)
    },
    handelCreate() {
      this.title = '新增规则'
      delete this.form.id
      this.dialogFormVisible = true
      this.fileList = []
    },
    handelEdit(row) {
      this.title = '回复'
      this.fileList = []
      this.form = row
      this.dialogFormVisible = true
      if (this.form.icon) {
        this.form.icon.name = this.form.icon.url.split('/').pop()
        this.fileList.push(this.form.icon)
      }
    },
    handelVideo(row) {
      this.show = row.id
      this.show1 = row.id
      console.log(this.tableData, 'error submit!!')
      this.initVideo(row.id)
    },
    beforeIconUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG && !isPNG) {
        this.$message.error('上传的图标只能是 JPG 或 PNG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传的图片大小不能超过 2MB!')
      }
      return (isJPG || isPNG) && isLt2M
    },
    handleIconSuccess(res, file) {
      if (res && res.code === 200) {
        this.form.att_id = res.data[0]
      }
    },
    handleIconRemove(file, fileList) {
      this.form.att_id = null
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          store(this.form).then(_ => {
            this.dialogFormVisible = false
            this.getList()
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    destroy(row) {
      remove({ id: row.id }).then(_ => {
        this.getList()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.card-wrap ::v-deep .el-card__body {
  height: 100%;
}
.list-wrap {
  height: 100%;

  ::v-deep .card-wrap {
    height: 100%;
    border: none;

    .el-card__body {
      height: 100%;
    }

    .box-card {
      height: 100%;
      border: none;

      .el-card__body {
        height: calc(100% - 59px);
        overflow: auto;
      }
    }
  }
}
</style>

<style scoped lang="scss">
.integral {
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    &:hover {
      border-color: #409EFF;
    }
  }
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
