import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/workorder/list',
    method: 'POST',
    data
  })
}

export function detail(data) {
  return request({
    url: '/workorder/detail',
    method: 'POST',
    data
  })
}

export function comment(data) {
  return request({
    url: '/workorder/comment',
    method: 'POST',
    data
  })
}
export function closeOrder(data) {
  return request({
    url: '/workorder/closeOrder',
    method: 'POST',
    data
  })
}

export function cate() {
  return request({
    url: '/workorder/cate',
    method: 'POST'
  })
}

export function cate_store(data) {
  return request({
    url: '/workorder/cate_store',
    method: 'POST',
    data
  })
}
export function cate_delete(data) {
  return request({
    url: '/workorder/cate_delete',
    method: 'POST',
    data
  })
}

