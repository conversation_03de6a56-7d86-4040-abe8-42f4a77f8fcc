export default {
  methods: {
    /**
     * 打开关闭弹窗
     * @param name
     * @param idField
     * @param id
     * @param titleField
     * @param title
     * @param dataField
     * @param {Object} data 配置参数
     * @param visible
     * @param callback
     * @param {String} data.name 对话框字段名
     * @param {String} data.idField 数据id字段名
     * @param {String | Number} data.id 数据id值
     * @param {String} data.titleField 标题字段名
     * @param {String} data.title 标题
     * @param {String} data.dataField 数据字段名
     * @param {Object} data.data 数据
     * @param {Boolean} data.visible 打开关闭状态
     * @param {Function} data.callback 回调函数
     */
    changeDialogVisible({
      name = '',
      idField = '',
      id = undefined,
      titleField = '',
      title = '',
      dataField = '',
      data = {},
      visible = false,
      callback = null
    }) {
      try {
        this[name] = visible
        this[titleField] = title
        this[idField] = id
        this[dataField] = data
        if (callback) {
          callback()
        }
      } catch (err) {
        console.log(err)
      }
    }
  }
}
