<template>
  <el-col :span="18">
    <el-form ref="ruleForm" :model="formData" :rules="rules" label-width="120px">
      <div class="mb64">
        <el-divider content-position="left"><h3>基本信息</h3></el-divider>
        <el-form-item label="代理商名称：" prop="name" style="width: 70%">
          <el-input v-model="formData.name" />
        </el-form-item>
        <el-form-item label="负责人姓名：" prop="principal_name" style="width: 70%">
          <el-input v-model="formData.principal_name" />
        </el-form-item>
        <el-form-item label="负责人电话：" prop="principal_phone" style="width: 70%">
          <el-input v-model="formData.principal_phone" />
          <span :style="isUpdate?'display:block':'display:none'">
            <span>同时更新密码</span>
            <el-checkbox v-model="formData.password" true-label="1" false-label="" />
          </span>

        </el-form-item>
        <el-form-item label="登录用户名：" prop="username" style="width: 70%">
          <el-tooltip class="item" effect="dark" content="密码默认为负责人手机号" placement="right">
            <el-input v-model="formData.username" :disabled="isUpdate" />
          </el-tooltip>
        </el-form-item>
        <el-form-item label="所在区域：" prop="area" style="width: 70%">
          <el-cascader
            v-model="formData.area"
            :options="provinceAndCityData"
            filterable
            clearable
            placeholder="可搜索"
          />
        </el-form-item>
        <el-form-item label="代理区域：" prop="agentArea" style="width: 70%">
          <el-cascader
            v-model="formData.agentArea"
            :options="provinceAndCityData"
            filterable
            clearable
            placeholder="可搜索"
          />
        </el-form-item>
      </div>
      <div class="mb64">
        <el-divider content-position="left"><h3>完善信息</h3></el-divider>
        <el-form-item label="公司LOGO：" prop="logo" style="width: 70%">
          <upload-img
            :url.sync="formData.logo"
            :size="300"
            accept="image/png, image/jpeg"
            tip="请选择不大于300K的JPG、PNG、图片，建议图片大小为100*100像素"
          />
        </el-form-item>
        <el-form-item label="法人姓名：" prop="legal_person" style="width: 70%">
          <el-input v-model="formData.legal_person" />
        </el-form-item>
        <el-form-item label="法人电话：" prop="legal_phone" style="width: 70%">
          <el-input v-model="formData.legal_phone" />
        </el-form-item>
        <el-form-item label="法人身份证号：" prop="card" style="width: 70%">
          <el-input v-model="formData.card" />
        </el-form-item>
        <el-form-item label="年限：" prop="years" style="width: 70%">
          <el-input v-model="formData.years" />
        </el-form-item>
        <el-form-item label="规模：" prop="scale" style="width: 70%">
          <el-input v-model="formData.scale" />
        </el-form-item>
        <el-form-item label="营业执照号：" prop="license_number" style="width: 70%">
          <el-input v-model="formData.license_number" />
        </el-form-item>
        <el-form-item label="经营范围：" prop="scope" style="width: 90%">
          <el-input v-model="formData.scope" />
        </el-form-item>
        <el-form-item label="详细地址：" prop="address" style="width: 90%">
          <el-input v-model="formData.address" />
        </el-form-item>
        <el-form-item label="身份证正面：" prop="card_a">
          <upload-img
            :url.sync="formData.card_a"
            :size="500"
            :image="cardA"
            accept="image/png, image/jpeg"
            width="190px"
            height="130px"
            tip="请选择不大于500K的JPG、PNG、图片"
          />
        </el-form-item>
        <el-form-item label="身份证反面：" prop="card_b">
          <upload-img
            :url.sync="formData.card_b"
            :size="500"
            accept="image/png, image/jpeg"
            :image="cardB"
            width="190px"
            height="130px"
            tip="请选择不大于500K的JPG、PNG、图片"
          />
        </el-form-item>
        <el-form-item label="营业执照：" prop="license">
          <upload-img
            :url.sync="formData.license"
            :size="500"
            accept="image/png, image/jpeg"
            :image="licenseImg"
            width="220px"
            height="300px"
            tip="请选择不大于500K的JPG、PNG、图片"
          />
        </el-form-item>
        <el-form-item label="选择用户：" prop="customer_id">
          <el-select v-model="formData.customer_id" placeholder="请选择" :filter-method="filterValue" filterable clearable @change="selectCustomer">
            <el-option v-for="customer in customers" :key="customer.id" :label="customer.name" :value="customer.id" />
          </el-select>
        </el-form-item>
      </div>
    </el-form>
  </el-col>
</template>

<script>
import { index } from '@/api/agent/customers'
import { provinceAndCityData } from 'element-china-area-data'
import UploadImg from '@/components/Upload/uploadImg'
import cardA from '@/assets/agent/cardA.png'
import cardB from '@/assets/agent/cardB.png'
import licenseImg from '@/assets/agent/license.png'
export default {
  name: 'AForm',
  components: { UploadImg },
  props: {
    ruleForm: {
      type: Object,
      default: () => {}
    },
    isUpdate: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      customers: [],
      cardA,
      cardB,
      licenseImg,
      provinceAndCityData: provinceAndCityData,
      formData: this.ruleForm,
      queryParams: { type: '', user: '' },
      rules: {
        name: [{ required: true, message: '代理商名称必须填写', trigger: 'blur' }],
        principal_name: [{ required: true, message: '负责人姓名必须填写', trigger: 'blur' }],
        principal_phone: [{ required: true, message: '负责人电话必须填写', trigger: 'blur' }],
        username: [{ required: true, message: '代理商账号必须填写', trigger: 'blur' }],
        area: [{ required: true, message: '所在地区必须选择', trigger: 'change' }],
        agentArea: [{ required: true, message: '代理地区必须选择', trigger: 'change' }],
        logo: [{ required: true, message: '公司LOGO必须上传', trigger: 'blur' }],
        legal_person: [{ required: true, message: '法人姓名必须填写', trigger: 'blur' }],
        legal_phone: [{ required: true, message: '法人电话必须填写', trigger: 'blur' }],
        card: [
          { required: true, message: '法人身份证必须填写', trigger: 'blur' },
          { min: 15, max: 18, message: '身份证号长度在15-18位之间', trigger: 'blur' }
        ],
        years: [{ required: true, message: '年限必须填写', trigger: 'blur' }],
        scale: [{ required: true, message: '规模必须填写', trigger: 'blur' }],
        license_number: [{ required: true, message: '营业执照编号必须填写', trigger: 'blur' }],
        scope: [{ required: true, message: '经营范围必须填写', trigger: 'blur' }],
        address: [{ required: true, message: '详细地址必须填写', trigger: 'blur' }],
        card_a: [{ required: true, message: '身份证图片未上传', trigger: 'blur' }],
        card_b: [{ required: true, message: '身份证图片未上传', trigger: 'blur' }],
        license: [{ required: true, message: '营业执照图片未上传', trigger: 'blur' }]
      }
    }
  },
  watch: {
    ruleForm(value) {
      this.formData = value
    }
  },
  created() {
    // this.getCustomers()
    this.provinceAndCityData.forEach(function($item) {
      $item.children.unshift({ label: '全部', value: '0' })
    })
  },
  methods: {
    // 获取要开通产品的客户
    getCustomers() {
      // index({ all: true, type: this.ruleForm.type }).then(response => {
      index(this.queryParams).then(response => {
        this.customers = response.data
      })
    },
    filterValue(value) {
      this.queryParams.user = value
      this.getCustomers()
    },
    selectCustomer(v) {
      console.log(v)
    }
  }
}
</script>

<style scoped lang="scss">
.mb64 {
  margin-bottom: 64px;
  ::v-deep .el-divider{
    margin-bottom: 50px;
  }
}
</style>
