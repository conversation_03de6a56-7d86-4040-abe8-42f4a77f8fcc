<script>
import SelfFormTemp from '@/views/task/components/form/SelfFormTemp.vue'
import update from '@/views/task/mixins/update'
import ElDescriptions from '@/components/ElDescription/descriptions'
import ElDescriptionsItem from '@/components/ElDescription/descriptions-item'
import History from './History.vue'
import { getClueDetail, getClueFollowList, saveClueFollow } from '@/api/zhy/clue'
import ChangeStatus from '@/views/zhy/sales/ChangeStatus.vue'

export default {
  name: 'Update',
  components: {
    ChangeStatus,
    SelfFormTemp,
    ElDescriptions,
    ElDescriptionsItem,
    History
  },
  mixins: [update],
  data() {
    return {
      history: [
        {
          time: '2024-10-30 14:25:37',
          content: '4'
        }
      ],
      filters: [
        {
          label: '成单金额',
          prop: 'price',
          type: 'number',
          settings: {
            min: 0,
            max: 1000000.99,
            class: 'half-w'
          }
        },
        {
          label: '成单日期',
          prop: 'date',
          type: 'datePicker',
          settings: {
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            placeholder: ['请选择成单日期'],
            class: 'half-w'
          }
        },
        {
          label: '客户意向',
          prop: 'intention',
          type: 'textarea',
          settings: {
            class: 'full-w'
          }
        },
        {
          label: '跟进情况',
          prop: 'followup',
          type: 'textarea',
          settings: {
            class: 'full-w'
          }
        }
      ],
      formData: {
        status: 0,
        price: 0,
        date: '',
        intention: '',
        followup: ''
      },
      statusLoading: false,
      historyLoading: false
    }
  },
  watch: {
    _visible(val) {
      if (val) {
        this.getDetail()
        this.getHistory()
      }
    }
  },
  methods: {
    async getDetail() {
      if (!this.data.id) return
      try {
        this.loading = true
        const res = await getClueDetail({ id: this.data.id })
        if (res.code === 200) {
          res.data.price = Number(res.data.price)
          this.formData = res.data
        }
      } catch (error) {
        console.log(error.message || '获取详情失败')
      } finally {
        this.loading = false
      }
    },
    async getHistory() {
      if (!this.data.id) return
      try {
        this.loading = true
        const res = await getClueFollowList({ id: this.data.id })
        if (res.code === 200) {
          this.history = res.data
        }
      } catch (error) {
        console.log(error.message || '获取历史失败')
      } finally {
        this.loading = false
      }
    },
    handleSetLoading(row, loading) {
      this.statusLoading = loading
    },
    handleRefresh() {
      this.getDetail()
      this.$emit('refresh')
    },
    async submitCallback() {
      try {
        this.loading = true
        this.formData.id = this.data.id
        const postData = {
          id: this.data.id,
          price: this.formData.price,
          date: this.formData.date,
          intention: this.formData.intention,
          followup: this.formData.followup
        }
        const res = await saveClueFollow(postData)
        if (res.code === 200) {
          this.$emit('refresh')
          return Promise.resolve({ success: true })
        } else {
          return Promise.resolve({ success: false, msg: res.msg || res.message || '保存失败' })
        }
      } catch (error) {
        return Promise.resolve({ success: false, msg: error.message || '保存失败' })
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<template>
  <el-drawer title="线索详情" :visible.sync="_visible" size="1000px">
    <div v-loading="loading" class="update-drawer">
      <el-descriptions title="咨询信息" border>
        <el-descriptions-item label="企业名称">{{ data.name }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ data.mobile }}</el-descriptions-item>
        <el-descriptions-item label="信息来源">{{ data.typetitle }}</el-descriptions-item>
        <el-descriptions-item label="咨询时间">{{ data.date }}</el-descriptions-item>
        <el-descriptions-item label="是否成单">
          <change-status :data.sync="formData" :loading="statusLoading" @setLoading="handleSetLoading" @refresh="handleRefresh" />
        </el-descriptions-item>
      </el-descriptions>
      <div class="history mt20">
        <div class="el-descriptions__header">修改历史</div>
        <History :list="history" />
      </div>
      <div class="info">
        <div class="el-descriptions__header">跟进信息</div>
        <SelfFormTemp ref="formWrap" :filters="filters" :form-data.sync="formData" label-width="100px" />
        <div class="btn-box">
          <el-button @click="handleCancel()">取消</el-button>
          <el-button type="primary" @click="handleSubmit()">保存</el-button>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<style lang="scss" scoped>
.update-drawer {
  padding: 0 20px;
  .el-descriptions__header{
    font-size: 16px;
    font-weight: 700;
  }
  .info {
    margin-top: 40px;
  }
  .btn-box {
    margin-top: 20px;
    text-align: right;
  }
}
::v-deep #el-drawer__title {
  span {
    outline: none;
  }
}
::v-deep .el-drawer__body {
  height: calc(100vh - 45px - 32px);
  overflow: auto;
}
::v-deep .half-w {
  width: calc(50% - 10px);
}
::v-deep .full-w {
  width: 100%;
  .el-form-item__content{
    width: calc(100% - 100px);
  }
}
</style>
