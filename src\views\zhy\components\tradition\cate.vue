<!-- 媒体投放列表 -->
<template>
  <div class=''>
    <el-table :data="tableData" style="width: 100%" height="calc(100vh - 280px)" row-key="id" highlight-current-row
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <el-table-column label="ID" prop="id" width="80" />
      <el-table-column prop="name" label="名称" />
      <el-table-column label="操作" width="180">
        <template slot="header" slot-scope="scope">
          <el-button type="primary" size="small" @click="addDialog">新增分类</el-button>
        </template>
        <template slot-scope="scope">
          <el-link :underline="false" type="primary" @click="editDialog(scope.row)">编辑</el-link>
          <el-popconfirm title="确定删除当前记录吗？" @onConfirm="deleteHandle(scope.row)">
            <el-link slot="reference" :underline="false" type="danger">删除</el-link>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination style="margin-top: 0;" v-show="total > 0" :total="total" :page.sync="listQuery.page"
      :limit.sync="listQuery.limit" @pagination="getList" /> -->
    <!-- 新增 修改 -->
    <cate-edit-dialog ref="cateEditDialogRef" @getList="getList" :editApi="editApi" from="tradition" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import CateEditDialog from '../media/CateEditDialog.vue'
import { traditionTypeApi, traditionTypeSaveApi, traditionTypeDeleteApi } from '@/api/zhy_shop'

export default {
  components: { Pagination, CateEditDialog },
  data() {
    return {
      listQuery: {
        page: 1,
        limit: 10
      },
      total: 0,
      tableData: [],
      editApi: traditionTypeSaveApi,
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    // 获取列表
    async getList() {
      const that = this
      const result = await traditionTypeApi(that.listQuery)
      const { code, data } = result
      that.tableData = data
    },
    // 添加
    addDialog() {
      this.$refs.cateEditDialogRef.openDialog(null)
    },
    // 编辑
    editDialog(row) {
      this.$refs.cateEditDialogRef.openDialog(row)
    },
    // 删除电商平台
    async deleteHandle(row) {
      const that = this
      const result = await traditionTypeDeleteApi(row.id)
      const { code, data } = result
      if (code == 200) {
        that.$message.success('删除成功')
        that.getList()
      }
    }
  }
}
</script>
<style lang='scss' scoped></style>