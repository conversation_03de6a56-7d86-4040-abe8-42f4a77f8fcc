<script>
import { addPrintCateApi, getPrintCateAddApi, getPrintCateUpdateApi, updatePrintCateApi } from '@/api/egt/print'
import SelfFormTemp from '@/views/task/components/form/SelfFormTemp.vue'
import update from '@/views/task/mixins/update'

export default {
  name: 'PrintCateUpdate',
  components: { SelfFormTemp },
  mixins: [update],
  data() {
    return {
      filters: [
        {
          prop: 'title',
          label: '名称',
          type: 'input'
        },
        {
          prop: 'p_id',
          label: '分类',
          type: 'select',
          settings: {
            options: [
              {
                value: 0,
                label: '顶级分类'
              }
            ],
            props: {
              label: 'title',
              value: 'id'
            }
          }
        }
      ],
      formData: {
        title: '',
        p_id: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入文件名称', trigger: 'blur' }
        ],
        p_id: [
          { required: true, message: '请选择文件类型', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    _visible(val) {
      if (val) {
        this.getParams()
      }
    }
  },
  methods: {
    async getParams() {
      const api = this.data.id ? getPrintCateUpdateApi : getPrintCateAddApi
      if (!api) return
      try {
        this.loading = true
        const res = await api({ id: this.data.id })
        if (res.code === 200 && res.data) {
          const cate = this.filters.find(item => item.prop === 'p_id')
          if (this.data.id) {
            cate.settings.options = res.data.category1
          } else {
            cate.settings.options = res.data.data
          }
          cate.settings.options.unshift({
            id: 0,
            title: '顶级分类'
          })
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false
      }
    },
    async submitCallback() {
      let api = addPrintCateApi
      if (this.data.id) {
        api = updatePrintCateApi
        this.formData.id = this.data.id
      }
      try {
        this.loading = true
        const res = await api(this.formData)
        if (res.code === 200) {
          this.$emit('submit-success')
          return Promise.resolve({ success: true })
        } else {
          return Promise.reject({ success: false, msg: res.msg || res.message || '操作失败' })
        }
      } catch (error) {
        console.log(error)
        return Promise.reject({ success: false, msg: '操作失败' })
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<template>
  <el-dialog :close-on-click-modal="false" :title="title + '文件类型'" :visible.sync="_visible" width="500px">
    <SelfFormTemp ref="formWrap" :form-data.sync="formData" :filters="filters" self-key="printCate" :rules="rules" :inline="false" label-width="80px" />
    <div slot="footer">
      <el-button @click="handleCancel()">取 消</el-button>
      <el-button type="primary" @click="handleSubmit()">确 定</el-button>
    </div>
  </el-dialog>
</template>
