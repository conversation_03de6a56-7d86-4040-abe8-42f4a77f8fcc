import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/sealversion/list',
    method: 'POST',
    data
  })
}

export function detail(data) {
  return request({
    url: '/sealversion/detail',
    method: 'POST',
    data
  })
}

export function store(data) {
  return request({
    url: '/sealversion/store',
    method: 'POST',
    data
  })
}

export function del(data) {
  return request({
    url: '/sealversion/delete',
    method: 'POST',
    data
  })
}
export function phoneModels() {
  return request({
    url: '/sealversion/phone',
    method: 'POST'
  })
}
