<script>
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import { getTalentStudyApi } from '@/api/egt/study'

export default {
  name: 'Study',
  components: { StatisticsTemplate },
  data() {
    return {
      config: {
        key: 'study',
        filters: [
          {
            prop: 'company_name',
            label: '公司名称',
            type: 'input'
          },
          {
            prop: 'time',
            label: '公司名称',
            type: 'datePicker',
            settings: {
              type: 'daterange',
              placeholder: ['请选择开始日期', '请选择结束日期']
            }
          }
        ],
        tableSettings: {
          api: getTalentStudyApi,
          params: {},
          index: true,
          columns: [
            {
              label: '公司名称',
              prop: 'company_name'
            },
            {
              label: '上传课程数',
              prop: 'course_num'
            },
            {
              label: '学习人次',
              prop: 'study_num'
            },
            {
              label: '学习时长',
              prop: 'study_time'
            }
          ],
          formmat: res => {
            return res.data
          },
          showPage: false,
          field: {}
        }
      }
    }
  }
}
</script>

<template>
  <div class="page-wrap">
    <statistics-template :config="config" />
  </div>
</template>

<style scoped lang="scss">
.page-wrap{
  height: 100%;
}
</style>
