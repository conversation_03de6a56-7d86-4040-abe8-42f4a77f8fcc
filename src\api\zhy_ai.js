import request from '@/utils/request'

// 获取AI工具列表
export function aiListApi(data) {
  return request({
    url: '/aitools/list',
    method: 'POST',
    data: data
  })
}

// 获取AI工具添加、修改
export function aiAddApi(data) {
  return request({
    url: '/aitools/store',
    method: 'POST',
    data: data
  })
}

// 删除AI工具
export function aiDelApi(id) {
  return request({
    url: '/aitools/destory/',
    method: 'POST',
    data: { id }
  })
}

// 获取AI工具分类 带分页
export function aiTypeApi(data) {
  return request({
    url: '/aitoolstypes/list',
    method: 'POST',
    data: data
  })
}

// 获取AI工具分类 不分页
export function aiTypeAllApi(data) {
  return request({
    url: '/aitoolstypes/nonePageList',
    method: 'POST',
    data: data
  })
}

// 获取AI工具分类添加、修改
export function aiTypeAddApi(data) {
  return request({
    url: '/aitoolstypes/store',
    method: 'POST',
    data: data
  })
}

// 删除AI工具分类
export function aiTypeDelApi(id) {
  return request({
    url: '/aitoolstypes/destory',
    method: 'POST',
    data: { id }
  })
}