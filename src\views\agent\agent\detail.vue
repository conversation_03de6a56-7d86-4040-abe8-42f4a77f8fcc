<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <!--<span>代理商详情</span>-->
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
      </div>
      <el-form ref="ruleForm" :model="formData" label-width="120px">
        <div class="mb64">
          <el-divider content-position="left"><h3>基本信息</h3></el-divider>
          <el-form-item label="代理商名称：" prop="name" style="width: 70%">
            {{ formData.name }}
          </el-form-item>
          <el-form-item label="负责人姓名：" prop="principal_name" style="width: 70%">
            {{ formData.principal_name }}
          </el-form-item>
          <el-form-item label="负责人电话：" prop="principal_phone" style="width: 70%">
            {{ formData.principal_phone }}
          </el-form-item>
          <el-form-item label="登录用户名：" prop="username" style="width: 70%">
            {{ formData.username }}
          </el-form-item>
          <el-form-item label="所在区域：" prop="area" style="width: 70%">
            {{ codeToText(formData.province,formData.city) }}
          </el-form-item>
          <el-form-item label="代理区域：" prop="agentArea" style="width: 70%">
            {{ codeToText(formData.agent_province,formData.agent_city) }}
          </el-form-item>
        </div>
        <div class="mb64">
          <el-divider content-position="left"><h3>完善信息</h3></el-divider>
          <el-form-item prop="logo" style="width: 70%">
            <el-avatar :size="100" :src="formData.logo" />
          </el-form-item>
          <el-form-item label="法人姓名：" prop="legal_person" style="width: 70%">
            {{ formData.legal_person }}
          </el-form-item>
          <el-form-item label="法人电话：" prop="legal_phone" style="width: 70%">
            {{ formData.legal_phone }}
          </el-form-item>
          <el-form-item label="法人身份证号：" prop="card" style="width: 70%">
            {{ formData.card }}
          </el-form-item>
          <el-form-item label="年限：" prop="years" style="width: 70%">
            {{ formData.years }}
          </el-form-item>
          <el-form-item label="规模：" prop="scale" style="width: 70%">
            {{ formData.scale }} 人
          </el-form-item>
          <el-form-item label="营业执照号：" prop="license_number" style="width: 70%">
            {{ formData.license_number }}
          </el-form-item>
          <el-form-item label="经营范围：" prop="scope" style="width: 90%">
            {{ formData.scope }}
          </el-form-item>
          <el-form-item label="详细地址：" prop="address" style="width: 90%">
            {{ formData.address }}
          </el-form-item>
          <el-form-item label="身份证正面：" prop="card_a">
            <img :src="formData.card_a" alt="身份证正面" style="max-width: 290px">
          </el-form-item>
          <el-form-item label="身份证反面：" prop="card_b">
            <img :src="formData.card_b" alt="身份证反面" style="max-width: 290px">
          </el-form-item>
          <el-form-item label="营业执照：" prop="license">
            <img :src="formData.license" alt="营业执照" style="max-width: 400px">
          </el-form-item>
          <el-form-item v-if="formData.status > 0 " label="审核状态：">
            <el-tag v-if="formData.status === 1" type="success">审核中</el-tag>
            <el-tag v-if="formData.status === 2" type="success">已通过</el-tag>
            <el-tag v-if="formData.status === 3" type="danger">已驳回</el-tag>
          </el-form-item>
          <el-form-item v-if="formData.status === 3 " label="驳回理由：">
            {{ formData.remark }}
          </el-form-item>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { detail } from '@/api/agent/agent'
import { CodeToText } from 'element-china-area-data'
export default {
  data() {
    return {
      formData: {}
    }
  },
  created() {
    this.detail()
  },
  methods: {
    detail() {
      detail({ id: this.$route.query.id }).then(response => {
        if (response.code === 200) {
          this.$message.success('获取成功')
          this.formData = response.data
        }
      })
    },
    codeToText(p, c) {
      // eslint-disable-next-line no-unused-vars
      let str
      if (c !== 0) {
        str = CodeToText[p] + CodeToText[c]
      } else {
        str = CodeToText[p]
      }
      return str
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap{
  height: 100%;
  .box-card{
   height: 100%;
    ::v-deep .el-card__header{
      padding-top: 0;
    }
    ::v-deep.el-card__body{
      height: calc(100% - 41px);
      padding: 0;
      margin-top: 20px;
    }
    .mb64 {
      margin-bottom: 64px;
      ::v-deep .el-divider{
        margin-bottom: 50px;
      }
    }
  }
}
</style>
