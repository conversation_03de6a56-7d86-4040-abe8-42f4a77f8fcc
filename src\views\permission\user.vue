<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>用户管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="createUser">添加用户</el-button>
      </div>
      <el-table
        :data="tableData"
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="65"
        />
        <el-table-column label="头像">
          <template slot-scope="scope">
            <el-avatar shape="circle" :size="32" :src="scope.row.avatar">{{ scope.row.name }}</el-avatar>
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="账号"
        />
        <el-table-column
          prop="description"
          label="描述"
          show-overflow-tooltip
        />
        <el-table-column label="状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status" type="success" size="small" effect="dark">启用</el-tag>
            <el-tag v-else type="danger" effect="dark" size="small">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="角色"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.roles.length">
              <el-tag v-for="role in scope.row.roles" :key="role.id" size="small" effect="dark" style="margin: 3px">{{ role.name }}</el-tag>
            </div>
            <el-tag v-else type="info" size="small" effect="dark">未设置</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <el-link :underline="false" type="warning" @click="resetPasswordHandle(scope.row)">重置密码</el-link>
            <el-link :underline="false" type="primary" @click="editUser(scope.row)">编辑</el-link>
            <el-popconfirm
              title="确定删除当前记录吗？"
              @onConfirm="deleteHandle(scope.row)"
            >
              <el-link slot="reference" :underline="false" type="danger">删除</el-link>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getUserList" />
    </el-card>
    <!--表单-->
    <el-dialog
      title="用户管理"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="120px" class="demo-ruleForm">
        <el-form-item label="用户名:" prop="name">
          <el-col :span="12">
            <el-input v-model="ruleForm.name" type="text" />
          </el-col>
        </el-form-item>
        <el-form-item v-show="!ruleForm.id" label="密码:" prop="password">
          <el-col :span="12">
            <el-input v-model="ruleForm.password" type="text" />
          </el-col>
        </el-form-item>
        <el-form-item label="所属公司" prop="company_id">
          <el-autocomplete
            v-model="ruleForm.companyname"
            :fetch-suggestions="querySearchGroup"
            placeholder="请选择"
            clearable
            class="filter-item"
            style="width: 300px;margin-top:0px"
            @select="selectGroup"
            @focus="groupListMe"
          />
        </el-form-item>

        <el-form-item label="描述:" prop="description">
          <el-col :span="12">
            <el-input v-model="ruleForm.description" type="textarea" />
          </el-col>
        </el-form-item>
        <el-form-item label="状态:" prop="status">
          <el-switch
            v-model="ruleForm.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item label="用户组:" prop="name">
          <el-select v-model="ruleForm.roles_id" multiple placeholder="请选择">
            <el-option
              v-for="role in roles"
              :key="role.id"
              :label="role.name"
              :value="role.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="saveHandle">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="密码重置成功"
      :visible.sync="dialogVisiblePassword"
      width="30%"
    >
      <el-input v-model="inputPassword" readonly size="small">
        <el-button slot="append" type="primary" icon="el-icon-document" size="small" @click="handleCopy(inputPassword,$event)">复制密码</el-button>
      </el-input>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisiblePassword = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getUsers, updateUser, storeUser, resetPassword, deleteUser, getCompay } from '../../api/user'
import clip from '@/utils/clipboard' // use clipboard directly
import { getRoles } from '../../api/role'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
export default {
  components: { Pagination },
  data() {
    const validatePassword = (rule, value, callback) => {
      if (this.ruleForm.id) {
        callback()
      } else {
        if (value.length < 6) {
          callback(new Error('密码不能小于6位'))
        } else {
          callback()
        }
      }
    }
    return {
      inputPassword: '',
      dialogVisiblePassword: false,
      loading: false,
      ruleForm: {},
      dialogVisible: false,
      tableData: [],
      roles: [],
      company: [],
      groupArr: [],
      groupList: [],
      listQuery: {
        page: 1,
        limit: 50
      },
      total: 0,
      rules: {
        name: [
          { required: true, message: '用户名必填', trigger: 'blur' }
        ],
        password: [
          { required: true, validator: validatePassword, trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    'ruleForm.companyname': {
      deep: true,
      handler: function(newVal, oldVal) {
        this.groupArr = [] // 这是定义好的用于存放下拉提醒框中数据的数组
        var arr = []
        var datafrom = []
        datafrom = {
          name: this.ruleForm.companyname
        }
        getCompay(datafrom).then(res => { // getDictInfo()这里是调用后台接口
          if (res.data) {
            this.groupList = res.data
            for (const item of this.groupList) {
              arr.push({
                value: item.name, // 这里一定是value: '值'
                id: item.id
              })
            }
          }
        })
        this.groupArr = arr
      }
    }
  },
  created() {
    this.getUserList()
    this.getRolesList()
  },
  methods: {
    groupListMe() {
      getCompay().then(res => { // getDictInfo()这里是调用后台接口
        if (res.data) {
          this.groupList = []
          this.groupArr = []
          this.groupList = res.data
          for (const item of this.groupList) {
            this.groupArr.push({
              value: item.name, // 这里一定是value: '值'
              id: item.id
            })
          }
        }
      })
        .catch(err => {
          console.log(err)
        })
    },
    querySearchGroup(queryString, cb) {
      var groupArr = this.groupArr
      cb(groupArr)
    },
    selectGroup(val) {
      this.groupId = val.code
      this.ruleForm.company_id = val.id
    },
    getUserList() {
      getUsers(this.listQuery).then(response => {
        this.total = response.data.total
        this.tableData = response.data.data
      })
    },
    getRolesList() {
      getRoles().then(response => {
        this.roles = response.data
      })
    },
    getCompaylist() {
      getCompay().then(response => {
        this.company = response.data
      })
    },
    createUser() {
      this.ruleForm = {
        status: 1
      }
      this.dialogVisible = true
    },
    editUser(row) {
      this.ruleForm = row
      this.dialogVisible = true
    },
    saveHandle() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.loading = true
          if (this.ruleForm.id) {
            updateUser(this.ruleForm).then(response => {
              this.dialogVisible = false
              this.loading = false
              this.getUserList()
            }).catch(error => {
              this.loading = false
              console.log(error)
            })
          } else {
            storeUser(this.ruleForm).then(response => {
              this.dialogVisible = false
              this.loading = false
              this.getUserList()
            }).catch(() => {
              this.loading = false
            })
          }
        }
      })
    },
    resetPasswordHandle(row) {
      resetPassword(row).then(response => {
        this.inputPassword = response.data.password
        this.dialogVisiblePassword = true
      })
    },
    handleCopy(text, event) {
      clip(text, event)
    },
    deleteHandle(row) {
      deleteUser(row).then(response => {
        this.$message.success('删除成功')
        this.getUserList()
      })
    }
  }
}
</script>

<style scoped>

</style>
