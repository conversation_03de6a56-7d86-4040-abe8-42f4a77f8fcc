<template>
  <el-upload
    class="avatar-uploader"
    :action="action"
    :headers="headers"
    :show-file-list="false"
    :on-success="handleAvatarSuccess"
    :before-upload="beforeAvatarUpload"
    :accept="accept"
    :data="data"
    :multiple="multiple"
    :disabled="disabled"
  >
    <img v-if="url" :src="url" class="avatar" :style="{ width:width,height:height }">
    <i v-else class="el-icon-plus avatar-uploader-icon" :style="{ background:'url('+ image +')',width:width,height:height,lineHeight:height }" />
    <div slot="tip" class="el-upload__tip">{{ tip }}</div>
  </el-upload>
</template>

<script>
export default {
  name: 'UploadImg',
  props: {
    action: {
      type: String,
      default: () => process.env.VUE_APP_AGENT_API_PROXY + '/upload/image'
    },
    url: {
      type: String,
      default: () => ''
    },
    info: {
      type: Object,
      default: () => {}
    },
    tip: {
      type: String,
      default: () => '建议尺寸 750 x 750 且不超过2M 的jpg/png图片'
    },
    accept: {
      type: String,
      default: () => ''
    },
    size: {
      type: Number,
      default: () => 2000
    },
    image: {
      type: String,
      default: () => ''
    },
    width: {
      type: String,
      default: () => '178px'
    },
    height: {
      type: String,
      default: () => '178px'
    },
    data: {
      type: Object,
      default: () => {}
    },
    multiple: {
      type: Boolean,
      default: () => false
    },
    disabled: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      imageUrl: '',
      headers: { Authorization: 'Bearer ' + this.$store.getters.token }
    }
  },
  methods: {
    handleAvatarSuccess(res, file) {
      if (res.code === 200) {
        // this.imageUrl = URL.createObjectURL(file.raw)
        // this.imageUrl = res.data.path
        this.$emit('update:url', res.data.path)
        this.$emit('update:info', res.data)
        this.$emit('success', file, res)
      } else {
        this.$message.error(res.message)
      }
    },
    beforeAvatarUpload(file) {
      // const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      // // 增加一个图片判断方法
      const isLt2M = file.size / 1024 < this.size

      // if (!isJPG) {
      //   this.$message.error('上传头像图片只能是 JPG 或 PNG 格式!')
      // }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过' + this.size + 'KB')
      }
      return isLt2M
    }
  }
}
</script>
<style>
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 294px;
    height: 200px;
    line-height: 200px;
    text-align: center;
  }

  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>
