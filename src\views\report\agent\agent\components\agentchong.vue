<template>
  <AgentCard
    title="代理商充值数据"
    :statistics="statistics"
    :charge-radio="chargeRadio"
    :chart-data="{x: trend_keys, y: trend_data1}"
    :color="{card: '#fff1ef', chart: '#ec7b6b'}"
    :table-data="lists"
    chart-id="charge"
    :form.sync="searchForm"
    :date.sync="value2"
    :map-loading="mapLoading"
    @get-active="getActive"
  />
</template>

<script>
import { rechargedata } from '@/api/agent/dashboard'
import AgentCard from './AgentCard.vue'

export default {
  name: 'Agentchong',
  components: { AgentCard },
  props: {
    allData: {
      type: Object,
      default() {
        return {}
      }
    },
    loading: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  data() {
    return {
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      value2: '',
      mapLoading: false,
      statistics: [
        {
          title: '代理商充值总额',
          value: 0,
          img: 'https://scrm-api.china9.cn/web/images/dlscz.png'
        },
        {
          title: '软件充值金额',
          value: 0,
          img: 'https://scrm-api.china9.cn/web/images/rjcz.png'
        },
        {
          title: '硬件充值金额',
          value: 0,
          img: 'https://scrm-api.china9.cn/web/images/yjcz.png'
        }
      ],
      searchForm: {
        status: '',
        page: 1,
        perPage: 5,
        total: 0,
        is_buy: false
      },
      lists: [],
      indexOfDate: 0,
      indexOfType: 0,
      chargeRadio: [
        {
          label: '充值趋势',
          value: 0
        },
        {
          label: '充值排行',
          value: 1
        }
      ],
      indexOfCate: 0,
      chartLine: null,
      dataSource: [],
      trend_keys: [],
      trend_data1: []
    }
  },
  watch: {
    allData(value) {
      this.allData = value
    }
  },
  created() {
  },
  mounted() {
    this.init()
    this.getActive()
  },
  methods: {
    selectType(index) {
      this.indexOfType = index
      this.init()
    },
    changeDay() {
      this.init()
    },
    getActive() {
      // 产品活跃度排行
      this.$emit('getActive')
    },
    async init() {
      this.getRechargedata()
    },
    async getRechargedata(page, limit) {
      this.mapLoading = true
      if (typeof page === 'number') {
        this.searchForm.page = page
      }
      await rechargedata({ date: this.value2, page: this.searchForm.page }).then(response => {
        // this.mapLoading = false
        this.value2 = response.data.date
        this.trend_data1 = response.data.allprice
        this.trend_keys = response.data.detelist
        this.statistics[0].value = +response.data.alltotalprice
        this.statistics[1].value = +response.data.alltotalrjprice
        this.statistics[2].value = +response.data.alltotalyjprice
        this.lists = response.data.agentprice
        this.searchForm.total = response.data.agentallprice.total
        // this.allData = response.data
        // this.lists = this.allData.data
      }).catch(error => {
        this.mapLoading = false
        console.log(error)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
