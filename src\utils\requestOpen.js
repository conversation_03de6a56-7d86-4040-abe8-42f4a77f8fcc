import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_OPEN_API_PROXY, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 50000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    // do something before request is sent
    config.headers['Accept'] = 'application/json'

    if (store.getters.token) {
      // let each request carry token
      // please modify it according to the actual situation
      config.headers['Authorization'] = 'Bearer' + ' ' + getToken()
    }
    return config
  },
  error => {
    // do something with request error
    // console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
     * If you want to get http information such as headers or status
     * Please return  response => response
     */

  /**
     * Determine the request status by custom code
     * Here is just an example
     * You can also judge the status by HTTP Status Code
     */
  response => {
    if (response.status !== 200 || !response.data || (typeof response.data.code !== 'undefined' && response.data.code !== 200)) {
      let error
      switch (response.status) {
        case 200:
          error = response.data.error || response.data.message || response.data.msg
          break
        case 400:
          error = response.data.error || response.data.message || response.data.msg
          break
        case 401:
          error = '你需要重新登陆'
          break
        case 403:
          error = '拒绝访问'
          break
        case 415:
          error = '请求方式错误'
          break
        default:
          error = '未知错误'
          break
      }

      Message({
        message: error,
        type: 'error',
        duration: 5 * 1000
      })
      return Promise.reject(new Error(error))
    }
    return response.data
  },
  error => {
    let message = error.message
    if (error.response) {
      switch (error.response.status) {
        case 422:
          message = error.response.data.errors[Object.keys(error.response.data.errors)[0]][0]
          break
        case 404:
          message = 'Not Found ( 404 )'
          break
        case 500:
          message = error.response.data.message
          break
      }
    }
    Message({
      message: message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
