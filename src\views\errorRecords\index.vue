<template>
  <div class="app-container feedback">
    <el-card class="box-card" style="margin-bottom: 15px;" shadow="hover">
      <el-form :inline="true" :model="query" class="demo-form-inline" method="get">
        <el-form-item label="手机号">
          <el-input v-model="query.phone" clearable style="width: 140px" />
        </el-form-item>
        <el-form-item label="用户ID">
          <el-input v-model="query.user_id" clearable style="width: 180px" />
        </el-form-item>
        <el-form-item label="错误消息">
          <el-input v-model="query.error_msg" clearable />
        </el-form-item>
        <el-form-item label="错误url">
          <el-input v-model="query.error_url" clearable />
        </el-form-item>
        <el-form-item label="错误码">
          <el-input v-model="query.error_code" clearable />
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker v-model="query.created_at" type="date" placeholder="选择日期" value-format="yyyy-MM-dd" clearable style="width: 150px" />
        </el-form-item>
        <el-form-item label="操作系统">
          <el-select v-model="query.type" placeholder="请选择" :clearable="true" style="width: 120px">
            <el-option label="android" value="0" />
            <el-option label="ios" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getList" :loading="loading">搜索</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card class="box-card" shadow="hover">
      <div slot="header" class="clearfix">
        <span>日志列表</span>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        :default-sort="{prop: 'created_at', order: 'descending'}"
      >
        <el-table-column label="序列" align="center" width="80px">
          <template slot-scope="scope">{{ scope.$index + 1 }}</template>
        </el-table-column>
        <el-table-column label="手机号" prop="phone" />
        <el-table-column label="姓名" prop="user.name" />
        <el-table-column label="用户ID" prop="user_id" />
        <el-table-column label="错误码" prop="error_code" />
        <el-table-column label="错误信息" prop="error_msg" width="300px" />
        <el-table-column label="手机型号" prop="phone_model" />
        <el-table-column label="操作系统" prop="type" />
        <el-table-column label="创建时间" prop="created_at" />
        <el-table-column label="操作" width="80px">
          <template slot-scope="scope">
            <el-button type="text" @click="detail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="query.page"
        :limit.sync="query.limit"
        @pagination="getList"
      />
    </el-card>

    <el-dialog :close-on-click-modal="false" title="查看详情" :visible.sync="dialogFormVisible">
      <el-row>
        <el-col :span="16">
          <el-form :model="form">
            <el-form-item label="创建时间" :label-width="formLabelWidth" style="margin-bottom: 0">
              {{ form.created_at }}
            </el-form-item>
            <el-form-item label="手机号" :label-width="formLabelWidth" style="margin-bottom: 0">
              {{ form.phone }}
            </el-form-item>
            <el-form-item label="姓名" :label-width="formLabelWidth" style="margin-bottom: 0">
              {{ form.user.name }}
            </el-form-item>
            <el-form-item label="用户ID" :label-width="formLabelWidth" style="margin-bottom: 0">
              {{ form.user_id }}
            </el-form-item>
            <el-form-item label="远程接口结果" :label-width="formLabelWidth" style="margin-bottom: 0">
              {{ form.remote_result }}
            </el-form-item>
            <el-form-item label="错误码" :label-width="formLabelWidth" style="margin-bottom: 0">
              {{ form.error_code }}
            </el-form-item>
            <el-form-item label="报错链接" :label-width="formLabelWidth" style="margin-bottom: 0">
              {{ form.error_url }}
            </el-form-item>
            <el-form-item label="请求参数" :label-width="formLabelWidth" style="margin-bottom: 0">
              {{ form.request_param }}
            </el-form-item>
            <el-form-item label="错误信息" :label-width="formLabelWidth" style="margin-bottom: 0">
              <div v-html="form.error_msg" />
            </el-form-item>
            <el-form-item label="手机品牌" :label-width="formLabelWidth" style="margin-bottom: 0">
              {{ form.phone_brand }}
            </el-form-item>
            <el-form-item label="手机型号" :label-width="formLabelWidth" style="margin-bottom: 0">
              {{ form.phone_model }}
            </el-form-item>
            <el-form-item label="操作系统版本号" :label-width="formLabelWidth" style="margin-bottom: 0">
              {{ form.system_version }}
            </el-form-item>
            <el-form-item label="app版本" :label-width="formLabelWidth" style="margin-bottom: 0">
              {{ form.app_version }}
            </el-form-item>
            <el-form-item label="app版本名称" :label-width="formLabelWidth" style="margin-bottom: 0">
              {{ form.app_version_name }}
            </el-form-item>
            <el-form-item label="操作系统" :label-width="formLabelWidth">
              {{ form.type }}
            </el-form-item>
            <el-form-item label="错误等级" :label-width="formLabelWidth">
              {{ form.error_level }}
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogFormVisible = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { fetchList, option } from '@/api/error_records'

export default {
  name: 'Personal',
  components: { Pagination },
  data() {
    return {
      total: 0,
      query: {
        page: 1,
        limit: 10,
        name: null,
        number: null,
        status: null
      },
      options: [],
      tableData: [],
      loading: true,
      form: {
        user: {}
      },
      dialogFormVisible: false,
      formLabelWidth: '120px'
    }
  },
  created() {
    this.getList()
  },
  methods: {
    errorHandler() {
      return true
    },
    getOption() {
      option().then(response => {
        this.options = response.data.FEEDBACK_TYPE
      })
    },
    getList() {
      this.loading = true
      fetchList(this.query).then(response => {
        this.total = response.data.total
        this.tableData = response.data.data
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    detail(row) {
      this.form = row
      this.dialogFormVisible = true
    }
  }
}
</script>

<style scoped>
    .feedback .user{

    }
</style>
