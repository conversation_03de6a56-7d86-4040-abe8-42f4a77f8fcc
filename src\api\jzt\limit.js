import request from '@/utils/requestJzt'

export function limitWord(data) {
  return request({
    url: '/zhymans/wordList',
    method: 'post',
    data
  })
}

export function addLimitWord(data) {
  return request({
    url: '/zhymans/addWord',
    method: 'post',
    data
  })
}

export function delLimitWord(data) {
  return request({
    url: '/zhymans/delWord',
    method: 'post',
    data
  })
}

export function editLimitWord(data) {
  return request({
    url: '/zhymans/editWord',
    method: 'post',
    data
  })
}

export function limitWordLog(data) {
  return request({
    url: '/zhymans/wordlogList',
    method: 'post',
    data
  })
}

export function delLimitWordLog(data) {
  return request({
    url: '/zhymans/delWordlog',
    method: 'post',
    data
  })
}

// 极限词IP白名单列表
export function whiteList(data) {
  return request({
    url: '/zhymans/sitebmdList',
    method: 'post',
    data
  })
}

// 极限词IP白名单 添加
export function whiteListAdd(data) {
  return request({
    url: '/zhymans/addSitebmd',
    method: 'post',
    data
  })
}

// 极限词IP白名单 编辑
export function whiteListEdit(data) {
  return request({
    url: '/sitelimitword/edit',
    method: 'post',
    data
  })
}

// 极限词IP白名单 删除
export function whiteListDel(data) {
  return request({
    url: '/zhymans/delSitebmd',
    method: 'post',
    data
  })
}
