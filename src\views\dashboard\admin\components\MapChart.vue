<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts'

require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'
import mapJson from '@/assets/map/map2.json'

export default {
  name: 'Map<PERSON>hart',
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart(this.dataset)
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions(chartData = []) {
      echarts.registerMap('map', mapJson)
      const options = {
        layoutCenter: ['center', 'center'], // 位置
        layoutSize: '100%', // 大小
        tooltip: {
          trigger: 'item',
          formatter: '{b}<br/>{c}'
        },
        toolbox: {
          show: true,
          orient: 'vertical',
          left: 'right',
          top: 'center'
        },
        visualMap: {
          type: 'piecewise',
          show: true,
          min: 0,
          max: 3000,
          // text: ['高', '低'],
          realtime: false,
          calculable: true,
          /* inRange: {
            color: ['lightskyblue', 'yellow', 'orangered']
          }, */
          pieces: [
            {min: 0, max: 500, color: '#0099ff', label: '0-500'},
            {min: 500, max: 1000, color: '#0066ff', label: '500-1000'},
            {min: 1000, max: 1500, color: '#0033ff', label: '1000-1500'},
            {min: 1500, max: 2000, color: '#0000ff', label: '1500-2000'},
            {min: 2000, max: 2500, color: '#0000cc', label: '2000-2500'},
            {min: 2500, max: 3000, color: '#000099', label: '2000-2500'},
            {min: 3000, color: '#000066', label: '>3000'},
          ],
        },
        series: [
          {
            name: '活跃用户地域分布',
            type: 'map',
            map: 'map',
            // zoom: 1.6,
            label: {
              show: false
            },
            data: this.chartData
          }
        ]
      }

      try {
        this.chart.setOption(options)
      } catch (e) {
        console.log(e)
      }
    }
  }
}
</script>
