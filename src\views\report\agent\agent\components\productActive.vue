<template>
  <div v-loading="loading" class="box-card">
    <div class="header">
      <div />
      <p style="flex: 1">产品活跃度排行</p>
      <el-button type="text" style="float: right" @click="detail">详情</el-button>
    </div>
    <table border="1">
      <tr>
        <th>排名</th>
        <th>产品名称</th>
        <th>浏览量(PV)</th>
        <th>占比</th>
      </tr>
      <tr v-for="(item,index) in list" :key="index">
        <td>{{ index+1 }}</td>
        <td>{{ item.name }}</td>
        <td>{{ item.count }}</td>
        <td>
          <el-progress :percentage="item.proportion" color="#409eff" />
        </td>
      </tr>
    </table>
    <div v-if="list.length===0" class="null">暂无数据</div>
  </div>
</template>

<script>
export default {
  name: 'ProductActive',
  props: {
    list: {
      type: Array,
      default() {
        return []
      }
    },
    loading: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  data() {
    return {

    }
  },
  created() {

  },
  methods: {
    detail() {
      this.$router.push({
        name: 'statisticsModuleListRouter'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .box-card {
    position: relative;
    width: 658px;
    background-color: #ffffff;
    .header {
      display: flex;
      align-items: center;
      margin-top: 30px;
      margin-right: 40px;
      height: 33px;
      div {
        width: 11px;
        height: 100%;
        background-color: #0e90d2;
      }
      p {
        margin-left: 25px;
        font-size: 20px;
        font-weight: bold;
      }
    }
    table {
      margin: 40px auto;
      width: 579px;
      border-collapse: collapse;
      tr {
        height: 40px;
        td {
          border: 1px solid #e0e0e0;
          font-size: 16px;
          text-align: center;
        }
      }
      tr:nth-child(1) {
        background-color: #0e90d2;
        th {
          color: #ffffff;
        }
      }
      tr:nth-child(2n) {
        background-color: #f9f9f9;
      }
    }
    .null{
      position: absolute;
      width: 100%;
      line-height: 200px;
      text-align: center;
      color: #8c939d;
      font-size: 30px;
      font-weight: bold;
    }
  }
</style>
