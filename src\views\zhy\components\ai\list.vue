<!-- 媒体投放列表 -->
<template>
  <div style="height: 100%;">
    <!-- 筛选 -->
    <el-form :inline="true" :model="listQuery" class="demo-form-inline">
      <el-form-item label="名称" props="name">
        <el-input v-model="listQuery.name" style="width: 340px" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="listQuery.page = 1;getList()">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      style="width: 100%"
      row-key="id"
      highlight-current-row
      height="calc(100% - 126px)"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column label="ID" prop="id" width="80" />
      <el-table-column label="分类" prop="category" width="180" />
      <el-table-column prop="title" label="名称" />
      <el-table-column prop="url" label="链接" />
      <el-table-column prop="showOpen" label="推荐" width="180" align="center">
        <template slot-scope="scope">
          {{ scope.row.showOpen == 1 ? '是' : '否' }}
          <!-- <el-switch v-model="scope.row.showOpen" :active-value="1" :inactive-value="0" @change="handleSwitch(scope.row)" /> -->
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180">
        <template slot="header">
          <el-button type="primary" size="small" @click="addDialog">新增工具</el-button>
        </template>
        <template slot-scope="scope">
          <el-link :underline="false" type="primary" @click="editDialog(scope.row)">编辑</el-link>
          <el-popconfirm title="确定删除当前记录吗？" @onConfirm="deleteHandle(scope.row)">
            <el-link slot="reference" :underline="false" type="danger">删除</el-link>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      style="margin-top: 0;"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
    <!-- 新增 修改 -->
    <edit-dialog ref="editDialogRef" :edit-api="editApi" @getList="getList" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import EditDialog from './EditDialog'
import { aiListApi, aiAddApi, aiDelApi } from '@/api/zhy_ai'

export default {
  components: { Pagination, EditDialog },
  data() {
    return {
      listQuery: {
        page: 1,
        limit: 10
      },
      total: 0,
      tableData: [],
      editApi: aiAddApi

    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    // 获取列表
    async getList() {
      const that = this
      const result = await aiListApi(that.listQuery)
      const { code, data } = result
      if (code === 200) {
        const { total, data: list } = data
        that.total = total
        that.tableData = list
      }
    },
    // 添加
    addDialog() {
      this.$refs.editDialogRef.openDialog(null)
    },
    // 编辑
    editDialog(row) {
      this.$refs.editDialogRef.openDialog(row)
    },
    // 删除电商平台
    async deleteHandle(row) {
      const that = this
      const result = await aiDelApi(row.id)
      const { code } = result
      if (code === 200) {
        that.$message.success('删除成功')
        that.getList()
      }
    }
  }
}
</script>

<style scoped lang="scss">
  .list-wrap {
    height: 100%;

    ::v-deep .card-wrap {
      height: 100%;
      border: none;

      .el-card__body {
        height: 100%;
      }

      .box-card {
        height: 100%;
        border: none;

        .el-card__body {
          height: calc(100% - 59px);
          overflow: auto;
        }
      }
    }
  }
</style>
