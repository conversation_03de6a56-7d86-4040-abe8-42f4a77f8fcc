<template>
  <div class="app-container" style="height: 100%;">
    <el-card class="card-wrap" style="height: 100%;">
      <div class="list-wrap">
        <div v-if="!simple" class="filter">
          <el-form ref="form" :inline="true" :model="listQuery" label-width="100px">
            <el-form-item label="订单编号：">
              <el-input v-model="listQuery.no" placeholder="订单编号" clearable />
            </el-form-item>
            <el-form-item label="公司名称：">
              <el-input v-model="listQuery.company_name" placeholder="公司名称" clearable />
            </el-form-item>
            <el-form-item label="订单来源：">
              <el-select v-model="listQuery.source_type" placeholder="请选择" clearable>
                <el-option label="平台" value="pc" />
                <el-option label="代理商" value="agent" />
                <el-option label="App" value="app" />
              </el-select>
            </el-form-item>
            <el-form-item label="开通类型：">
              <el-select v-model="listQuery.open_type" placeholder="请选择" clearable>
                <el-option label="正式" :value="1" />
                <el-option label="赠送" :value="2" />
                <el-option label="体验" :value="3" />
              </el-select>
            </el-form-item>
            <el-form-item label="订单类型：">
              <el-select v-model="listQuery.order_type" placeholder="请选择" clearable>
                <el-option label="开通产品" :value="1" />
                <el-option label="续费产品" :value="2" />
                <el-option label="升级产品" :value="3" />
              </el-select>
            </el-form-item>
            <el-form-item label="支付状态：">
              <el-select v-model="listQuery.status" placeholder="请选择" clearable>
                <el-option label="待付款" :value="1" />
                <el-option label="已付款" :value="2" />
                <el-option label="已取消" :value="3" />
                <el-option label="已超时" :value="4" />
                <el-option label="待退款" :value="5" />
                <el-option label="已退款" :value="6" />
                <el-option label="已关闭" :value="7" />
              </el-select>
            </el-form-item>
            <el-form-item label="支付类型：">
              <el-select v-model="listQuery.pay_type" placeholder="请选择" clearable>
                <el-option label="支付宝" :value="1" />
                <el-option label="微信" :value="2" />
                <el-option label="银联" :value="3" />
                <el-option label="余额" :value="4" />
              </el-select>
            </el-form-item>
            <el-form-item label="产品名称：">
              <el-select v-model="listQuery.app_id" placeholder="请选择" clearable>
                <el-option v-for="app in apps" :key="app.id" :label="app.name" :value="app.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="下单时间：">
              <el-date-picker v-model="listQuery.created_at" type="date" placeholder="选择日期" value-format="yyyy-MM-dd" clearable />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" icon="el-icon-search" @click="orders">确定</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div :class="['table', {mt20: !simple}]" :style="{height: simple ? '100%' : ''}">
          <el-table
            v-loading="tableLoading"
            :data="tableData"
            highlight-current-row
            :height="simple ? '100%' : 'calc(100% - 96px)'"
          >
            <el-table-column
              prop="no"
              label="订单编号"
              :width="simple ? 100 : 180"
            />
            <el-table-column
              prop="type"
              label="订单类型"
            />
            <el-table-column
              prop="title"
              label="订单名称"
            />
            <el-table-column
              prop="amount"
              label="订单金额"
            />
            <el-table-column label="订单状态">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status === '已付款'" type="success" effect="dark">{{ scope.row.status }}</el-tag>
                <el-tag v-if="scope.row.status === '待付款'" type="warning" effect="dark">{{ scope.row.status }}</el-tag>
                <el-tag v-if="scope.row.status === '已取消'" type="info" effect="dark">{{ scope.row.status }}</el-tag>
                <el-tag v-if="scope.row.status === '已超时'" type="info" effect="dark">{{ scope.row.status }}</el-tag>
                <el-tag v-if="scope.row.status === '待退款'" effect="dark">{{ scope.row.status }}</el-tag>
                <el-tag v-if="scope.row.status === '已退款'" type="danger" effect="dark">{{ scope.row.status }}</el-tag>
                <el-tag v-if="scope.row.status === '已关闭'" type="info" effect="dark">{{ scope.row.status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="company_name"
              label="公司名称"
            />
            <el-table-column
              prop="phone"
              label="手机号"
            />
            <el-table-column
              prop="created_at"
              label="下单时间"
            />
            <el-table-column label="支付类型">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.payment_type" effect="dark">{{ scope.row.payment_type }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right">
              <template slot-scope="scope">
                <el-link type="primary" :underline="false" @click="detail(scope.row)">详情</el-link>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="total>0 && !simple" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" style="margin-top: 0;" @pagination="orders" />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { list } from '@/api/order'
import { all } from '@/api/app'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
export default {
  name: 'OrderList',
  components: { Pagination },
  props: {
    simple: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableLoading: false,
      total: 0,
      listQuery: {
        page: 1,
        limit: 20
      },
      form: {},
      tableData: [],
      apps: []
    }
  },
  created() {
    this.getApps()
    this.orders()
  },
  mounted() {
    if (!this.simple) {
      this.$nextTick(() => {
        // 获取.tab-content的高度
        const tabContent = document.querySelector('.list-wrap > .el-card > .el-card__body')
        const tabContentHeight = tabContent.clientHeight
        // 获取.filter的高度
        const filter = document.querySelector('.filter')
        const filterHeight = filter.clientHeight
        // 计算.table的高度
        const tableHeight = tabContentHeight - filterHeight - 21
        // 设置.table的高度
        const table = document.querySelector('.table')
        table.style.height = tableHeight + 'px'
      })
    }
  },
  methods: {
    getApps() {
      all().then(response => {
        this.apps = response.data
      })
    },
    orders() {
      this.tableLoading = false
      list(this.listQuery).then(response => {
        this.total = response.data.total
        this.tableData = response.data.data
        this.tableLoading = false
      })
    },
    detail(row) {
      this.$router.push({
        path: '/orders/detail',
        query: {
          no: row.no
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.card-wrap ::v-deep .el-card__body {
  height: 100%;
}
.list-wrap{
  height: 100%;
  display: flex;
  flex-direction: column;
  .filter{
    border-bottom: 1px solid #e8e8e8;
  }
}
  @media screen and (max-width: 996px)  {
    .list-wrap{
      .filter{
        max-height: 30vh;
        overflow: auto;
      }
    }
  }
</style>
