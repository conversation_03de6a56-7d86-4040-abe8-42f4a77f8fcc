import request from '@/utils/request'

export function agent() {
  return request({
    url: '/dashboard/agent',
    method: 'post'
  })
}

export function system() {
  return request({
    url: '/dashboard/system',
    method: 'post'
  })
}

export function rechargedata(data) {
  return request({
    url: '/dashboard/rechargedata',
    method: 'post',
    data: data
  })
}

export function consumptiondata(data) {
  return request({
    url: '/dashboard/consumptiondata',
    method: 'post',
    data: data
  })
}

export function authfinorder(data) {
  return request({
    url: '/dashboard/finorder',
    method: 'post',
    data: data
  })
}

export function authproorder(data) {
  return request({
    url: '/dashboard/proorder',
    method: 'post',
    data: data
  })
}
export function neworder(data) {
  return request({
    url: '/dashboard/neworder',
    method: 'post',
    data: data
  })
}

export function finorder(data) {
  return request({
    url: '/dashboard/finorder',
    method: 'post',
    data: data
  })
}

export function dbrechargedata(data) {
  return request({
    url: '/dashboard/rechargedata',
    method: 'post',
    data: data
  })
}

export function dbconsumptiondata(data) {
  return request({
    url: '/dashboard/consumptiondata',
    method: 'post',
    data: data
  })
}

export function activecustomer(data) {
  return request({
    url: '/dashboard/activecustomer',
    method: 'post',
    data: data
  })
}
