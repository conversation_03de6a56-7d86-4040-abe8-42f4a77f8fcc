<script>
import SelfFormTemp from '@/views/task/components/form/SelfFormTemp.vue'
import update from '@/views/task/mixins/update'
import { softwareTypeMap } from '../../utils/softwareTypeMap'
import { addAttendanceApi, editAttendanceApi } from '@/api/egt/attendance'

export default {
  name: 'Update',
  components: { SelfFormTemp },
  mixins: [update],
  data() {
    const softwareTypeList = []
    for (const key in softwareTypeMap) {
      if (Object.prototype.hasOwnProperty.call(softwareTypeMap, key)) {
        const element = softwareTypeMap[key]
        softwareTypeList.push({
          label: element,
          value: +key
        })
      }
    }
    return {
      filters: [
        {
          label: '模板标题',
          prop: 'title',
          type: 'input'
        },
        {
          label: '模板展示图',
          prop: 'pic',
          type: 'upload',
          settings: {
            baseUrl: 'https://hrcloud.obs.cn-north-4.myhuaweicloud.com/',
            action: process.env.VUE_APP_IHR_PROXY + '/Api_Docking/imgupload',
            maxSize: 2,
            accept: 'image/*',
            limit: 1
          }
        },
        {
          label: '模板简介',
          prop: 'remarks',
          type: 'textarea'
        },
        {
          label: '模板文件',
          prop: 'view_url',
          type: 'input',
          settings: {
            disabled: false
          }
        },
        {
          label: '软件类型',
          prop: 'exe_type',
          type: 'radio',
          settings: {
            options: softwareTypeList
          }
        }
      ],
      formData: {
        title: '',
        pic: []
      },
      rules: {
        title: [
          { required: true, message: '请输入模板标题', trigger: 'blur' }
        ],
        pic: [
          { required: true, message: '请上传模板展示图', trigger: 'change' }
        ],
        view_url: [
          { required: true, message: '请填写模板文件', trigger: 'blur' }
        ],
        remarks: [
          { required: true, message: '请输入模板简介', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    'data.id'(val) {
      if (val) {
        this.filters.find(item => item.prop === 'view_url').settings.disabled = true
      }
    }
  },
  methods: {
    handleSuccess({ response, prop }) {
      const res = response.response
      if (res.code === 200) {
        if (prop === 'pic') {
          this.formData.pic = [
            {
              uid: response.file.uid || Date.now(),
              url: res.url + res.filepath,
              name: response.file.name,
              postUrl: res.filepath
            }
          ]
        }
      } else {
        this.$message.error(res.msg || res.message || '上传失败')
      }
    },
    async submitCallback() {
      let api = addAttendanceApi
      if (this.data.id) {
        api = editAttendanceApi
        this.formData.id = this.data.id
      }
      try {
        this.submitLoading = true
        const postData = JSON.parse(JSON.stringify(this.formData))
        if (this.data.id) {
          if (postData.pic && postData.pic.length) {
            postData.pic = postData.pic[0].uid
          }
        } else {
          if (postData.pic && postData.pic.length) {
            postData.pic = postData.pic[0].postUrl
          }
        }
        postData.share = 1
        const res = await api(postData)
        if (res.code === 200) {
          this.$emit('submit-success', postData)
          return Promise.resolve({ success: true })
        } else {
          return Promise.reject({ success: false, msg: this.data.id ? '更新失败' : '添加失败' })
        }
      } catch (err) {
        console.log(err)
        return Promise.reject({ success: false, msg: '操作失败' })
      } finally {
        this.submitLoading = false
      }
    },
    handlePreview() {
      const url = this.formData.pic[0].url
      window.open(url)
    }
  }
}
</script>

<template>
  <el-dialog :title="title + '打卡界面'" :visible.sync="_visible" width="50vw" :close-on-click-modal="false">
    <SelfFormTemp ref="formWrap" :filters="filters" :form-data="formData" self-key="attendance" :rules="rules" :inline="false" label-width="110px" @success="handleSuccess" @preview="handlePreview" />
    <div slot="footer">
      <el-button @click="handleCancel()">取 消</el-button>
      <el-button :loading="submitLoading" type="primary" @click="handleSubmit()">确 定</el-button>
    </div>
  </el-dialog>
</template>
