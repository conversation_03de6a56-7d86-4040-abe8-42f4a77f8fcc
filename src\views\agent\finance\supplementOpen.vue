<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <!--<span>补开订单</span>-->
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
        <el-button style="float: right; padding: 3px 0" type="text" @click="openProduct">申请开通</el-button>
      </div>
      <el-form ref="ruleForm" v-loading="formLoading" :model="ruleForm" :rules="rules" label-width="110px" class="demo-ruleForm">
        <el-form-item label="选择用户：" prop="customer_id">
          <el-select v-model="ruleForm.customer_id" placeholder="请选择" filterable clearable @change="selectCustomer">
            <el-option v-for="customer in customers" :key="customer.id" :label="customer.name" :value="customer.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="选择主订单：" prop="finance_id">
          <el-select v-model="ruleForm.finance_id" placeholder="请选择" filterable clearable style="width: 395px">
            <el-option v-for="finance in financeList" :key="finance.id" :label="finance.number+'('+finance.product+'-'+finance.version+')'" :value="finance.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="产品选择：" prop="category_id">
          <el-select v-model="ruleForm.category_id" placeholder="请选择">
            <el-option v-for="category in categoryList" :key="category.id" :label="category.name" :value="category.id" />
          </el-select>
          <el-select v-model="ruleForm.product_id" placeholder="请选择" filterable clearable :disabled="ruleForm.customer_id===''" @click.native="getProducts(ruleForm.category_id)">
            <el-option v-for="product in productsList" :key="product.id" :label="product.name" :value="product.id" />
          </el-select>
          <span v-if="ruleForm.category_id===18">
            <el-input v-model="ruleForm.domain" style="width: 200px" placeholder="请输入域名名称" clearable />
            <el-button type="text" style="padding: 0 10px" :loading="domainSearchLoading" @click="domainSearch(ruleForm)">搜索</el-button>
            <span v-if="domainSearchResult.message==='OK'" style="color: #3a835d" />
            <span v-else-if="domainSearchResult.message" style="color: red">已注册</span>
            <el-select v-if="domainSearchResult.message==='OK' " v-model="ruleForm.template_id" placeholder="请选择域名模板" @click.native="domainTemplate">
              <el-option v-for="it in domainTemplates" :key="it.id" :label="it.base.custName" :value="it.id" />
            </el-select>
            <el-button v-if="domainSearchResult.message==='OK' && domainSearchResult.is_template===false" type="text" @click="pushCreateTemplate">申请模板</el-button>
          </span>
        </el-form-item>
        <el-form-item label="开通期限:" prop="month">
          <el-row>
            <el-col :span="24">
              <el-input v-if="ruleForm.month > 0" v-model="ruleForm.month" disabled style="width: 200px">
                <template slot="append">月</template>
              </el-input>
            </el-col>
            <el-col :span="24">
              <el-radio-group v-model="ruleForm.year" size="mini" :disabled="ruleForm.customer_id===''||ruleForm.product_id===''" @change="sumDay">
                <el-radio-button :label="1">1年</el-radio-button>
                <el-radio-button :label="2">2年</el-radio-button>
                <el-radio-button :label="3">3年</el-radio-button>
                <el-radio-button :label="4">4年</el-radio-button>
                <el-radio-button :label="5">5年</el-radio-button>
              </el-radio-group>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="补开理由:" prop="content">
          <el-input
            v-model="ruleForm.content"
            style="width: 400px"
            type="textarea"
            :rows="5"
            placeholder="请输入补开内容"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { index } from '@/api/agent/customers'
import { products, category } from '@/api/agent/product'
import { search as domainSearch, temindex as domainTemplate } from '@/api/agent/domain'
import { store as openProduct, finance } from '@/api/agent/supplement'

export default {
  name: 'SupplementOpen',
  data() {
    return {
      ruleForm: { year: 1, month: 12 },
      formLoading: false,
      domainSearchLoading: false,
      financeList: [],
      categoryList: [],
      productsList: [],
      customers: [],
      domainTemplates: [],
      rules: {
        customer_id: [{ required: true, message: '必选', trigger: ['blur', 'change'] }],
        finance_id: [{ required: true, message: '必选', trigger: ['blur', 'change'] }],
        category_id: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        product_id: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        month: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        domain: [{ required: false, message: '必填', trigger: ['blur', 'change'] }],
        content: [{ required: true, min: 10, message: '不少于10字', trigger: ['blur', 'change'] }]
      },
      domainForm: {},
      domainRules: {
        name: [{ required: true, message: '必填', trigger: 'blur' }],
        template: [{ required: true, message: '必选', trigger: 'change' }]
      },
      domainSearchResult: { is_template: false }
    }
  },
  created() {
    this.getCustomers()
    this.getCategory()
  },
  methods: {
    getFinance() {
      this.formLoading = true
      finance({ customer_id: this.ruleForm.customer_id }).then(res => {
        if (res.code === 200 && res.data) {
          this.financeList = res.data
        }
      }).catch((e) => {
        console.log(e)
      }).finally(() => {
        this.formLoading = false
      })
    },
    // 获取产品种类列表
    getCategory() {
      category({ all: true, apply: this.ruleForm.type }).then(response => {
        if (response.code === 200 && response.data) {
          this.categoryList = response.data
        }
      })
    },
    // 获取产品列表
    getProducts(category_id) {
      products({ all: true, category_id: category_id }).then(response => {
        if (response.code === 200 && response.data) {
          this.productsList = response.data.list
        }
      })
    },
    // 获取要开通产品的客户
    getCustomers() {
      index({ all: true, type: this.ruleForm.type }).then(response => {
        if (response.code === 200 && response.data) {
          this.customers = response.data
        }
      })
    },
    // 设置购买月份
    sumDay(value) {
      if (value > 0) {
        this.ruleForm.month = value * 12
      } else {
        this.ruleForm.month = -1
      }
    },
    // 搜索域名 是否可以购买
    domainSearch(item) {
      this.domainSearchLoading = true
      domainSearch({ domain: item.domain, product_id: item.product_id }).then(res => {
        this.domainSearchLoading = false
        this.domainSearchResult = res
        item.template_id = null
      }).catch(() => {
        this.domainSearchLoading = false
      })
    },
    // 模板列表
    domainTemplate() {
      domainTemplate({ id: this.ruleForm.customer_id }).then(res => {
        if (res.code === 200 && res.data) {
          this.domainTemplates = res.data
          this.domainSearchResult.is_template = res.data.length !== 0
        }
      })
    },
    selectCustomer() {
      this.domainTemplates = null
      this.ruleForm.template_id = null
      this.financeList = []
      this.getFinance()
    },
    pushCreateTemplate() {
      this.$router.push({
        name: 'CustomersDomainTelementRouter',
        query: { id: this.ruleForm.customer_id }
      })
    },
    openProduct() {
      this.$refs['ruleForm'].validate((ret) => {
        if (ret) {
          openProduct(this.ruleForm).then(res => {
            this.$message.success('申请开通成功')
            this.$router.go(-1)
          }).catch(() => {
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
