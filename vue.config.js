'use strict'
const path = require('path')
const defaultSettings = require('./src/settings.js')

function resolve(dir) {
  return path.join(__dirname, dir)
}

const name = defaultSettings.title || 'vue Element Admin' // page title

// If your port is set to 80,
// use administrator privileges to execute the command line.
// For example, Mac: sudo npm run
// You can change the port by the following method:
// port = 9527 npm run dev OR npm run dev --port = 9527
const port = process.env.port || process.env.npm_config_port || 9527 // dev port

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  /**
   * You will need to set publicPath if you plan to deploy your site under a sub path,
   * for example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,
   * then publicPath should be set to "/bar/".
   * In most cases please use '/' !!!
   * Detail: https://cli.vuejs.org/config/#publicpath
   */
  publicPath: '/',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: process.env.NODE_ENV === 'development',
  productionSourceMap: false,
  devServer: {
    port: port,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      '/manager': {
        target: 'http://localhost'
      },
      '/openApi': {
        target: process.env.VUE_APP_OPEN_API,
        ws: true,
        changOrigin: true,
        pathRewrite: {
          '^/openApi': ''
        },
        onProxyReq: function(proxyReq) {
          proxyReq.removeHeader('origin') // <== 关键
        }
      },
      '/taskApi': {
        target: process.env.VUE_APP_TASK_API, // 这里后台的地址模拟的;应该填写你们真实的后台接口
        ws: true,
        changOrigin: true, // 允许跨域
        pathRewrite: {
          '^/taskApi': '' // 请求的时候使用这个api就可以
        },
        onProxyReq: function(proxyReq) {
          proxyReq.removeHeader('origin') // <== 关键
        }
      },
      '/taskManagerApi': {
        target: process.env.VUE_APP_TASK_MANAGER_API, // 这里后台的地址模拟的;应该填写你们真实的后台接口
        ws: true,
        changOrigin: true, // 允许跨域
        pathRewrite: {
          '^/taskManagerApi': '' // 请求的时候使用这个api就可以
        },
        onProxyReq: function(proxyReq) {
          proxyReq.removeHeader('origin') // <== 关键
        }
      },
      '/jztApi': {
        target: process.env.VUE_APP_JZT_API, // 这里后台的地址模拟的;应该填写你们真实的后台接口
        ws: true,
        changOrigin: true, // 允许跨域
        pathRewrite: {
          '^/jztApi': '' // 请求的时候使用这个api就可以
        },
        onProxyReq: function(proxyReq) {
          proxyReq.removeHeader('origin') // <== 关键
        }
      },
      '/jzt2Api': {
        target: process.env.VUE_APP_JZT2_API, // 这里后台的地址模拟的;应该填写你们真实的后台接口
        ws: true,
        changOrigin: true, // 允许跨域
        pathRewrite: {
          '^/jzt2Api': '' // 请求的时候使用这个api就可以
        },
        onProxyReq: function(proxyReq) {
          proxyReq.removeHeader('origin') // <== 关键
        }
      },
      '/agentApi': {
        target: process.env.VUE_APP_AGENT_API, // 这里后台的地址模拟的;应该填写你们真实的后台接口
        ws: true,
        changOrigin: true, // 允许跨域
        pathRewrite: {
          '^/agentApi': '' // 请求的时候使用这个api就可以
        },
        onProxyReq: function(proxyReq) {
          proxyReq.removeHeader('origin') // <== 关键
        }
      },
      '/apiApi': {
        target: process.env.VUE_APP_API_API, // 这里后台的地址模拟的;应该填写你们真实的后台接口
        ws: true,
        changOrigin: true, // 允许跨域
        pathRewrite: {
          '^/apiApi': '' // 请求的时候使用这个api就可以
        },
        onProxyReq: function(proxyReq) {
          proxyReq.removeHeader('origin') // <== 关键
        }
      },
      '/ihrApi': {
        target: process.env.VUE_APP_IHR_API, // 这里后台的地址模拟的;应该填写你们真实的后台接口
        ws: true,
        changOrigin: true, // 允许跨域
        pathRewrite: {
          '^/ihrApi': '' // 请求的时候使用这个api就可以
        },
        onProxyReq: function(proxyReq) {
          proxyReq.removeHeader('origin') // <== 关键
        }
      }
    }
    // before: require('./mock/mock-server.js')
  },
  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name: name,
    resolve: {
      alias: {
        '@': resolve('src')
      }
    }
  },
  chainWebpack(config) {
    config.plugins.delete('preload') // TODO: need test
    config.plugins.delete('prefetch') // TODO: need test

    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    // set preserveWhitespace
    config.module
      .rule('vue')
      .use('vue-loader')
      .loader('vue-loader')
      .tap(options => {
        options.compilerOptions.preserveWhitespace = true
        return options
      })
      .end()

    config
      .when(process.env.NODE_ENV !== 'development',
        config => {
          config
            .plugin('ScriptExtHtmlWebpackPlugin')
            .after('html')
            .use('script-ext-html-webpack-plugin', [{
            // `runtime` must same as runtimeChunk name. default is `runtime`
              inline: /runtime\..*\.js$/
            }])
            .end()
          config
            .optimization.splitChunks({
              chunks: 'all',
              cacheGroups: {
                libs: {
                  name: 'chunk-libs',
                  test: /[\\/]node_modules[\\/]/,
                  priority: 10,
                  chunks: 'initial' // only package third parties that are initially dependent
                },
                elementUI: {
                  name: 'chunk-elementUI', // split elementUI into a single package
                  priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                  test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
                },
                commons: {
                  name: 'chunk-commons',
                  test: resolve('src/components'), // can customize your rules
                  minChunks: 3, //  minimum common number
                  priority: 5,
                  reuseExistingChunk: true
                }
              }
            })
          config.optimization.runtimeChunk('single')
        }
      )
  }
}
