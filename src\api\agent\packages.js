import request from '@/utils/request'

export function index(data) {
  return request({
    url: '/packages/list',
    method: 'post',
    data: data
  })
}

export function create(data) {
  return request({
    url: '/packages/create',
    method: 'post',
    data: data
  })
}

export function edit(data) {
  return request({
    url: '/packages/edit',
    method: 'post',
    data: data
  })
}

export function sync(data) {
  return request({
    url: '/packages/sync',
    method: 'post',
    data: data
  })
}

export function getYunList(data) {
  return request({
    url: '/packages/yun/list',
    method: 'post',
    data: data
  })
}

