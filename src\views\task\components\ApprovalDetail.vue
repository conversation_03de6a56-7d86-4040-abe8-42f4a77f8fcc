<template>
  <div id="ApprovalDetail" v-loading="loading">
    <el-form :model="form" :inline="true">
      <el-row>
        <el-col :span="12">
          <el-form-item label="姓名">
            <span>{{ form.name }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申请日期">
            <span>{{ form.createtime }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="审批时间">
            <span>{{ form.status_time }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="岗位工种">
            <span>{{ form.workTitle }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="岗位等级">
            <span>{{ form.GradeInfo }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="审批状态">
            <span>{{ form.status }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="审批意见">
            <el-input v-if="Number(form.status === '审核中')" v-model="form.remark" style="width: 300px" type="textarea" rows="5" />
            <span v-else>{{ form.remark || "无" }}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row slot="footer" style="text-align: center;margin-top: 24px;">
      <el-button type="primary" @click="pass">通过</el-button>
      <el-button type="danger" @click="reject">驳回</el-button>
    </el-row>
  </div>
</template>

<script>
/* eslint-disable */

export default {
  name: "ApprovalDetail",
  props: ['id'],
  data() {
    return {
      form: {
        "id": undefined,
        "name": "",
        "unique_id": "",
        "identity_id": null,
        "work_id": 0,
        "grade": "0",
        "status": "",
        "remark": "",
        "status_time": "",
        "createtime": "",
        "updatetime": "",
        "deletetime": null,
        "workTitle": "",
        "GradeInfo": ""
      },
      loading: false
    };
  },
  watch: {
    id: {
      handler(v) {
        if(v){
          this.getDetail(v)
        }
      }
    }
  },
  methods: {
    pass() {
      alert("pass");
      this.approvalApi(1);
    },
    reject() {
      this.approvalApi(2);
    },
    approvalApi(status){
      this.loading = true;
      approval({
        id: this.id,
        status,
        remark: this.form.remark
      }).then(res => {
        if(res.code === 200){
          this.$message.success(res.msg || "审批成功");
          this.closeAlert(true);
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    closeAlert(refresh){
      if(typeof refresh !== "boolean"){
        refresh = false;
      }
      this.$emit("closeAlert")
    },
    getDetail(v = this.id){
      this.loading = true;
      getDetail({id: v}).then(res => {
        if(res.code === 200 && res.data){
          this.form = res.data; // 展示数据到页面中。如果不是数组或对象，则不展示。
        }
      }).finally(() => {
        this.loading = false;
      })
    }
  }
};
</script>

<style lang="scss" scoped>
</style>