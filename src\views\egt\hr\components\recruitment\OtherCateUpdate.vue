<script>
import update from '@/views/task/mixins/update'
import {
  addPsychologyTypeListApi,
  editPsychologyTypeListApi,
  getEditPsychologyTypeListApi
} from '@/api/egt/recruitment'

export default {
  name: 'OtherCateUpdate',
  mixins: [update],
  data() {
    return {
      form: {
        title: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入分类', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    _visible(val) {
      if (val) {
        this.getParams()
      }
    }
  },
  methods: {
    async getParams() {
      const api = this.data.id ? getEditPsychologyTypeListApi : undefined
      if (!api) return
      try {
        this.loading = true
        const res = await api({ id: this.data.id ? this.data.id : undefined })
        if (res.code === 200 && res.data) {
          this.form = res.data.data
        }
      } finally {
        this.loading = false
      }
    },
    async submitCallback() {
      const api = this.data.id ? editPsychologyTypeListApi : addPsychologyTypeListA<PERSON>
      try {
        this.submitLoading = true
        const postData = {
          id: this.data.id ? this.data.id : undefined,
          title: this.form.title
        }
        const res = await api(postData)
        if (res.code === 200) {
          this.$emit('submitSuccess')
          return Promise.resolve({ success: true })
        } else {
          return Promise.reject({ success: false, msg: res.msg || res.message || '保存失败' })
        }
      } catch (e) {
        console.log(e)
        return Promise.reject({ success: false, msg: '保存失败' })
      } finally {
        this.submitLoading = false
      }
    }
  }
}
</script>

<template>
  <el-dialog :close-on-click-modal="false" :title="title + '分类'" :visible.sync="_visible" append-to-body width="500px">
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="分类" prop="title">
        <el-input v-model="form.title" placeholder="请输入分类" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel('form')">取消</el-button>
      <el-button :loading="submitLoading" type="primary" @click="handleSubmit('form')">确定</el-button>
    </div>
  </el-dialog>
</template>
