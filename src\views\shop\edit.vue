<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none;">
      <div slot="header" class="clearfix">
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
      </div>
      <shop-form :type="true" :form-data="formData" @transfer="sonSave" />
    </el-card>
  </div>
</template>

<script>
import { store, detail } from '../../api/shop_goods'
import ShopForm from './components/shop-form'
export default {
  name: 'Add',
  components: { ShopForm },
  data() {
    return {
      formData: {}
    }
  },
  created() {
    this.getDetail()
  },
  methods: {
    getDetail() {
      detail(this.$route.query).then(response => {
        this.formData = response.data
        this.$forceUpdate()
      })
    },
    sonSave(form) {
      console.log(form)
      store(form).then(response => {
        this.$message.success('更新成功')
        this.$router.go(-1)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.box-card ::v-deep .el-card__header{
  padding-top: 0;
}
</style>
