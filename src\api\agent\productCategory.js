import request from '@/utils/request'

export function categories(data) {
  return request({
    url: '/product/category',
    method: 'post',
    data: data
  })
}

export function store(data) {
  return request({
    url: '/product/category/store',
    method: 'post',
    data: data
  })
}

export function update(data) {
  return request({
    url: '/product/category/update',
    method: 'post',
    data: data
  })
}

export function destroy(data) {
  return request({
    url: '/product/category/delete',
    method: 'post',
    data: data
  })
}
