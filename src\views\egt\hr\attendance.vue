<script>
import { deleteAttendance<PERSON>pi, getAttendanceListApi } from '@/api/egt/attendance'
import StatisticsTemplate from '@/views/task/components/StatisticsTemplate.vue'
import { softwareTypeMap } from './utils/softwareTypeMap'
import Update from './components/attendance/Update.vue'

export default {
  name: 'Attendance',
  components: {
    StatisticsTemplate,
    Update
  },
  data() {
    return {
      config: {
        key: 'attendance',
        tableSettings: {
          api: getAttendanceListApi,
          field: {
            page: 'pageIndex',
            limit: 'pageSize',
            total: 'total'
          },
          index: true,
          columns: [
            {
              prop: 'title',
              label: '标题',
              width: '180'
            },
            {
              prop: 'pic',
              label: '展示图',
              type: 'img',
              width: '120',
              settings: {
                baseUrl: 'https://hrcloud.obs.cn-north-4.myhuaweicloud.com/'
              }
            },
            {
              prop: 'remarks',
              label: '模板简介'
            },
            {
              prop: 'view_url',
              label: '模板文件'
            },
            {
              prop: 'exe_type',
              label: '适用软件',
              isSlot: true,
              width: '120'
            },
            {
              prop: 'action',
              label: '操作',
              width: '200',
              align: 'center',
              fixed: 'right',
              isSlot: true
            }
          ]
        }
      },
      updateVisible: false,
      row: {},
      softwareTypeMap: softwareTypeMap
    }
  },
  methods: {
    handleAdd() {
      this.updateVisible = true
      this.row = {}
    },
    handleUpdate(row) {
      this.updateVisible = true
      this.row = row
    },
    async handleDelete(row) {
      if (!row.id) return
      try {
        this.$refs.listWrapRef.setLoading(row.id, true)
        const res = await deleteAttendanceApi({ id: row.id })
        if (res.code === 200) {
          this.$message.success('删除成功')
          await this.$refs.listWrapRef.handleGetData()
        } else {
          this.$message.error(res.msg || res.message || '删除失败')
        }
      } catch (e) {
        console.log(e)
        this.$message.error('删除失败')
      } finally {
        this.$refs.listWrapRef.setLoading(row.id, false)
      }
    },
    handleSubmitSuccess() {
      this.$refs.listWrapRef.handleGetData()
    }
  }
}
</script>

<template>
  <div class="page-wrap">
    <StatisticsTemplate ref="listWrapRef" :config="config">
      <template slot="topActions">
        <div style="text-align: right;">
          <el-button type="primary" @click="handleAdd">新增</el-button>
        </div>
      </template>
      <template #attendance_exe_type="{row}">
        <template v-if="+row.id === 1">标准版</template>
        <template v-else>
          {{ softwareTypeMap[row.exe_type] }}
        </template>
      </template>
      <template #attendance_action="{row}">
        <el-button size="mini" @click="handleUpdate(row)">编辑</el-button>
        <el-popconfirm
          v-if="+row.id !== 1"
          title="确定删除吗？"
          @onConfirm="handleDelete(row)"
        >
          <el-button slot="reference" type="danger" size="mini">删除</el-button>
        </el-popconfirm>
      </template>
    </StatisticsTemplate>

    <Update :visible.sync="updateVisible" :data="row" @submit-success="handleSubmitSuccess" />
  </div>
</template>

<style lang="scss" scoped>
.page-wrap{
  height: 100%;
}
</style>
