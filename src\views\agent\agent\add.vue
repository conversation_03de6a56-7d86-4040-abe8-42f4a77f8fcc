<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none;">
      <div slot="header" class="clearfix">
        <div style="display: flex;justify-content: space-between;align-items: center;">
          <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
          <el-button style="padding: 3px 0" type="text" @click="submitForm">保存</el-button>
        </div>
      </div>
      <!-- 新增代理商表单 -->
      <a-form ref="formRef" :rule-form.sync="ruleForm" />
    </el-card>
  </div>
</template>

<script>
import { store } from '@/api/agent/agent'
import AForm from './form'
export default {
  components: { AForm },
  data() {
    return {
      ruleForm: {}
    }
  },
  created() {},
  methods: {
    submitForm() {
      this.$refs.formRef.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          store(this.ruleForm).then(response => {
            if (response.code === 200) {
              this.$message.success('添加成功')
              this.$router.push({
                name: 'AgentListRouter'
              })
            }
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.list-wrap{
  .box-card {
    ::v-deep .el-card__header{
      padding-top: 0;
    }
    ::v-deep.el-card__body{
      height: calc(100% - 41px);
      padding: 0;
      margin-top: 20px;
    }
  }
}
</style>
