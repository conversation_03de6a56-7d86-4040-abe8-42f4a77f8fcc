<template>
  <el-form ref="submitForm" v-loading="formLoading" :model="ruleForm" :rules="rules" label-width="150px" class="demo-ruleForm">
    <el-divider content-position="left">认证信息</el-divider>
    <el-row>
      <el-col :span="12" style="max-width: 400px">
        <el-form-item label="域名持有者：">{{ custName }}</el-form-item>
        <el-form-item label="域名持有者类型：">
          <span>{{ custType === 1?'个人':'企业' }}</span>
        </el-form-item>
        <el-form-item label="身份证件类型:" prop="idType">
          <el-select v-model="ruleForm.idType">
            <el-option value="SFZ" label="身份证" />
            <el-option value="JGZ" label="军官证" />
            <el-option value="HZ" label="护照" />
            <el-option value="GAJMTX" label="港澳居民来往内地通行证" />
            <el-option value="TWJMTX" label="台湾居民来往大陆通行证" />
            <el-option value="WJLSFZ" label="外国人永久居留身份证" />
            <el-option value="GAJZZ" label="港澳居民居住证" />
            <el-option value="TWJZZ" label="台湾居民居住证" />
          </el-select>
        </el-form-item>
        <el-form-item label="上传身份证件资料：" prop="idImg">
          <upload-img
            :url.sync="ruleForm.idImgUrl"
            :action="imgUpload"
            :size="2000"
            :data="dir"
            accept="image/png, image/jpeg"
            tip="请选择不大于2M的JPG、PNG、图片"
            @success="idCardUploadSuccess"
          />
          <p style="color: #1890ff">需要提交 正面 + 反面 的合成照片</p>
        </el-form-item>
        <el-form-item label="证件号码：" prop="idCode">
          <el-input v-model="ruleForm.idCode" placeholder="请输入证件号码" />
          <p style="color: #1890ff">请填写与域名持有者一致的证件号码</p>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row v-if="custType === 2">
      <el-divider content-position="left">组织认证信息</el-divider>
      <el-col :span="12" style="max-width: 500px">
        <el-form-item label="组织类型:" prop="orgType">
          <el-select v-model="ruleForm.orgType">
            <el-option value="PR" label="个体" />
            <el-option value="PU" label="工商" />
          </el-select>
        </el-form-item>
        <el-form-item label="组织证件类型:" prop="orgProofType">
          <el-select v-model="ruleForm.orgProofType">
            <el-option value="YYZZ" label="营业执照" />
            <el-option value="ORG" label="组织机构代码证" />
            <el-option value="TYDM" label="统一社会信用代码证书" />
            <el-option value="BDDM" label="部队代号" />
            <el-option value="JDDWFW" label="军队单位对外有偿服务许可证" />
            <el-option value="SYDWFR" label="事业单位法人证书" />
            <el-option value="WGCZJG" label="外国企业常驻代表机构登记证" />
            <el-option value="SHTTFR" label="社会团体法人登记证书" />
            <el-option value="ZJCS" label="宗教活动场所登记证" />
            <el-option value="MBFQY" label="民办非企业单位登记证书" />
            <el-option value="JJHFR" label="基金会法人登记证书" />
            <el-option value="LSZY" label="律师事务所执业许可证" />
            <el-option value="WGZHWH" label="外国在华文化中心登记证" />
            <el-option value="WLCZJG" label="外国政府旅游部门常驻代表机构批准登记证" />
            <el-option value="SFJD" label="司法鉴定许可证" />
            <el-option value="JWJG" label="境外机构证件" />
            <el-option value="SHFWJG" label="社会服务机构登记证书" />
            <el-option value="MBXXBX" label="民办学校办学许可" />
            <el-option value="YLJGZY" label="医疗机构执业许可证" />
            <el-option value="GZJGZYZ" label="公证机构执业证" />
            <el-option value="BJWSXX" label="北京市外国驻华使馆人员子女学校办学许可证" />
            <el-option value="QT" label="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="上传实名认证资料：" prop="orgImg">
          <upload-img
            :url.sync="ruleForm.orgImgUrl"
            :action="imgUpload"
            :size="2000"
            :data="dir"
            accept="image/png, image/jpeg"
            tip="请选择不大于2M的JPG、PNG、图片"
            @success="orgUploadSuccess"
          />
        </el-form-item>
        <el-form-item label="证件号码：" prop="orgCode">
          <el-input v-model="ruleForm.orgCode" placeholder="请输入企业营业执照注册号或组织机构代码" />
        </el-form-item>
        <el-form-item label="行政区划代码：" prop="blInfo">
          <el-input v-model="ruleForm.blInfo" placeholder="请输入行政区划代码" />
        </el-form-item>
        <div class="xzqh">
          <span>查看中华人民共和国县以上行政区划代码</span>
          <a style="color: #1890ff" target="_blank" href="http://www.mca.gov.cn/article/sj/xzqh">http://www.mca.gov.cn/article/sj/xzqh</a>
        </div>
      </el-col>
    </el-row>
    <el-button style="margin-top: 40px" type="primary" :loading="btnLoading" @click="submit">{{ status==='WAITING'?'修改':'提交' }}认证信息</el-button>
    <el-button v-if="status==='WAITING'" style="margin-top: 40px" type="danger" :loading="btnLoading" @click="referTo">信息提交认证</el-button>
  </el-form>
</template>

<script>
import moment from 'moment'
import UploadImg from '@/components/Upload/uploadImg'
import { huaweiImg } from '@/api/agent/customers'
import { certification, certificationStore, referTo } from '@/api/agent/domain'
export default {
  name: 'DomainTelementAuth',
  components: { UploadImg },
  data() {
    return {
      id: this.$route.query.id,
      tid: this.$route.query.t_id,
      ruleForm: {
        'custType': '',
        'idType': 'SFZ',
        'orgType': 'PU',
        'orgProofType': 'YYZZ',
        'idCode': '',
        'orgCode': '',
        'blInfo': ''
      },
      imgUpload: process.env.VUE_APP_BASE_API + '/domain/uploadImg',
      formLoading: false,
      btnLoading: false,
      custType: 1,
      custName: '',
      rules: {
        idType: [{ required: true, message: '必选', trigger: 'change' }],
        idCode: [{ required: true, message: '必填', trigger: 'blur' }],
        idImg: [{ required: true, message: '必传', trigger: 'blur' }]
      },
      dir: { 'dir': 'idc/cert/' + moment(new Date()).format('yyyy-MM-DD') },
      status: ''
    }
  },
  created() {
    this.getDetailInfo()
  },
  methods: {
    getDetailInfo() {
      this.formLoading = true
      certification({ id: this.id, t_id: this.tid }).then(res => {
        this.formLoading = false
        this.custType = res.data.custType === 'CUST_PERSONAL' ? 1 : 2
        this.custName = res.data.custName
        this.status = res.data.status
        if (res.data.certification) {
          res.data.certification['idImgUrl'] = 'http://images.china9.cn/' + res.data.certification['idImg']
          res.data.certification['orgImgUrl'] = 'http://images.china9.cn/' + res.data.certification['orgImg']
          this.ruleForm = res.data.certification
        }

        if (this.custType === 2) {
          this.rules['orgType'] = [{ required: true, message: '必选', trigger: 'blur' }]
          this.rules['orgProofType'] = [{ required: true, message: '必选', trigger: 'blur' }]
          this.rules['blInfo'] = [{ required: true, message: '必填', trigger: 'blur' }]
          this.rules['orgCode'] = [{ required: true, message: '必填', trigger: 'blur' }]
          this.rules['orgImg'] = [{ required: true, message: '必传', trigger: 'blur' }]
          this.rules['otherImg'] = [{ required: false, message: '必传', trigger: 'blur' }]
        }
      }).catch(() => {
        this.formLoading = false
      })
    },
    idCardUploadSuccess(file, res) {
      this.ruleForm.idImg = res.data.path
      this.ruleForm.idImgUrl = res.data.url
      var url = res.data.url

      var that = this
      huaweiImg({ id: 0, type: 1, url: url }).then(res => {
        if (res.code) {
          if (res.data) {
            that.ruleForm.idCode = res.data.number
            console.log('idCode', that.ruleForm)
          }
        }
      }).catch(() => {
      })
    },
    orgUploadSuccess(file, res) {
      this.ruleForm.orgImg = res.data.path
      this.ruleForm.orgImgUrl = res.data.url
      var url = res.data.url
      var that = this
      huaweiImg({ id: 0, type: 2, url: url }).then(res => {
        if (res.code) {
          if (res.data) {
            var str = res.data.registration_number
            that.ruleForm.orgCode = str
            that.ruleForm.blInfo = str.substr(0, 5)
            console.log('idCode', that.ruleForm)
          }
        }
      }).catch(() => {
      })
    },
    submit() {
      this.$refs.submitForm.validate((valid) => {
        if (valid) {
          this.ruleForm.custType = this.custType === 1 ? 'CUST_CHANNEL' : 'CUST_CORPORATE'
          this.btnLoading = true

          var form = {
            'template_id': this.tid,
            'custType': this.ruleForm.custType,
            'idType': this.ruleForm.idType,
            'idCode': this.ruleForm.idCode,
            'idImg': this.ruleForm.idImg,
            'otherImg': ''
          }
          if (this.custType === 2) {
            form['orgType'] = this.ruleForm.orgType
            form['orgProofType'] = this.ruleForm.orgProofType
            form['blInfo'] = this.ruleForm.blInfo
            form['orgCode'] = this.ruleForm.orgCode
            form['orgImg'] = this.ruleForm.orgImg
          }

          certificationStore(form).then(res => {
            this.btnLoading = false
            this.$message.success('提交成功')
            this.getDetailInfo()
            this.$confirm('是否提交认证?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.btnLoading = true
              referTo({ template_id: this.tid }).then(res => {
                this.btnLoading = false
                this.$message.success('提交认证成功')
                this.$router.go(-1)
              }).catch(() => {
                this.btnLoading = false
              })
            })
          }).catch(() => {
            this.btnLoading = false
          })
        }
      })
    },
    referTo() {
      this.btnLoading = true
      referTo({ template_id: this.tid }).then(res => {
        this.btnLoading = false
        this.$message.success('提交认证成功')
        this.$router.go(-1)
      }).catch(() => {
        this.btnLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .demo-ruleForm{
    margin: 50px;
  }
  p{
    margin: 0;
  }
  .xzqh{
    margin-left: 150px;
    font-size: 13px;
  }
</style>
