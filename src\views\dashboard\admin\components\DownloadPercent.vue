<template>
  <div id="downloadPercent">
    <el-card shadow="never" class="border-transparent">
      <div class="el-row--flex is-justify-space-between is-align-middle">
        <span class="title">下载量及操作系统占比</span>
      </div>
      <div id="download-percent">
        <el-row>
          <el-col :span="12">
            <pie-chart :show-legend="true" :grid="grid" height="400px" :chart-data="chartData" :legend="legend" rose-type="radius" :radius="radius" :data-type="'normal'" />
          </el-col>
          <el-col :span="12">
            <pie-chart :show-legend="true" height="400px" :chart-data="dataChart2" :rose-type="false" :legend="legend" :radius="radius2" :data-type="'normal'" />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
/* eslint-disable */
import BarChart from './BarChart.vue';
import <PERSON><PERSON><PERSON> from './PieChart.vue';
import {getDownloadData} from "@/api/dashboard";

export default {
  name: "downloadPercent",
  components: { <PERSON><PERSON><PERSON>, Pie<PERSON><PERSON> },
  props: ['date'],
  watch: {
    date: {
      handler(v){
        console.log(v);
        this.handleGetDownload(v);
      },
      immediate: true
    }
  },
  data() {
    return {
      chartData: [],
      legend: {
        left: 'center',
        bottom: '10',
        show: true
      },
      total: 0,
      avarage: 0,
      grid: {
        left: 30,
        right: 30,
        bottom: 20,
        top: 30,
        containLabel: true
      },
      radius: [30, 140],
      dataChart2: [],
      radius2: [60, 100],
    };
  },
  methods: {
    handleGetDownload(date = this.date){
      if(!date){
        date = new Date().getFullYear().toString() + '-' + (new Date().getMonth() + 1).toString().padStart(2, '0');
      }
      this.chartData = [];
      this.dataChart2 = [];
      console.log(date);
      getDownloadData({date}).then(res => {
        if (res.code === 200 && res.data){
          this.dataChart2 = res.data.percent;
          this.dataChart2.forEach(v => {
            v.value = v.percent;
          });
          this.chartData = res.data.percentlist;
          this.chartData.forEach(v => {
            v.value = v.count;
          })
        }
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.bottom-0{
  margin-bottom: 0;
}
.float-right{
  float: right;
  margin-top: 10px;
  ::v-deep.el-form-item--medium .el-form-item__content{
    display: inline-block;
  }
}
.border-transparent{
  border: none;
}

.title{
  font-size: 20px;
  color: #000;
  margin: 0;
  font-weight: bold;
}
</style>
