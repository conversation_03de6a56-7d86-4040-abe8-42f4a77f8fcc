<template>
  <el-form ref="ruleForm" :model="formData" :rules="rules" label-width="140px" class="demo-ruleForm">
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="产品分类:">
          <el-select v-model="formData.category_id" placeholder="请选择">
            <el-option
              v-for="category in appCategory"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="应用类型:">
          <el-select v-model="formData.type" placeholder="请选择">
            <el-option
              v-for="option in options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="应用状态:" prop="status">
          <el-select v-model="formData.status" placeholder="请选择">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="售卖状态:" prop="sale_status">
          <el-select v-model="formData.sale_status" placeholder="请选择">
            <el-option label="在售" :value="1" />
            <el-option label="停售" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="父级应用:" prop="pid">
          <el-select v-model="formData.pid" placeholder="请选择">
            <el-option label="顶级应用" :value="0" />
            <el-option
              v-for="app in apps"
              :key="app.id"
              :label="app.name"
              :value="app.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="应用名称:" prop="name" label-width="140px">
          <el-input v-model="formData.name" style="width: 80%" />
        </el-form-item>
        <el-form-item label="产品标语:" prop="slogan" label-width="140px">
          <el-input v-model="formData.slogan" style="width: 80%" />
        </el-form-item>
        <el-form-item label="产品描述:" prop="introduce" label-width="140px">
          <el-input v-model="formData.introduce" type="textarea" maxlength="200" show-word-limit placeholder="请输入介绍内容用于控制台介绍" :autosize="{ minRows: 8, maxRows: 4}" style="width: 80%" />
        </el-form-item>
        <el-form-item label="排序:" prop="level">
          <el-input-number v-model="formData.level" :min="0" :max="999" label="数字越小越考前" />
        </el-form-item>
        <el-form-item label="允许退款:" prop="is_refund">
          <el-radio v-model="formData.is_refund" :label="1">是</el-radio>
          <el-radio v-model="formData.is_refund" :label="0">否</el-radio>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="介绍URL:" prop="introduce_url">
          <el-input v-model="formData.introduce_url" style="width: 80%" />
        </el-form-item>
        <el-form-item label="手册URL:" prop="doc_url">
          <el-input v-model="formData.doc_url" style="width: 80%" />
        </el-form-item>
        <el-form-item label="入口URL:" prop="entrance_url">
          <el-input v-model="formData.entrance_url" style="width: 80%" />
        </el-form-item>
        <el-form-item label="子菜单URL:" prop="menus_url">
          <el-input v-model="formData.menus_url" placeholder="产品子菜单列表接口URL用于APP控制台展示子功能" style="width: 80%" />
        </el-form-item>
        <el-form-item label="系统事件URL:" prop="notice_url">
          <el-input v-model="formData.notice_url" style="width: 80%" />
        </el-form-item>
        <el-form-item label="订单事件URL:" prop="order_url">
          <el-input v-model="formData.order_url" style="width: 80%" />
        </el-form-item>
        <el-form-item label="权限接口URL:" prop="permission_url">
          <el-input v-model="formData.permission_url" placeholder="资海云平台组织架构获取权限URL" style="width: 80%" />
        </el-form-item>
        <el-form-item label="积分任务:" prop="is_open_integral_task">
          <el-select v-model="formData.is_open_integral_task" placeholder="请选择">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="formData.is_open_integral_task"
          label="任务列表URL:"
          :rules="[
            { required: true, message: '请输入任务列表地址', trigger: 'blur' }
          ]"
        >
          <el-input v-model="formData.integral_task_list_url" style="width: 80%" />
        </el-form-item>
        <el-form-item
          v-if="formData.is_open_integral_task"
          label="任务设置URL:"
          :rules="[
            { required: true, message: '请输入任务设置地址', trigger: 'blur' },
          ]"
        >
          <el-input v-model="formData.integral_task_set_url" style="width: 80%" />
        </el-form-item>
        <el-form-item
          v-if="formData.is_open_integral_task"
          label="任务操作URL:"
          :rules="[
            { required: true, message: '任务操作地址', trigger: 'blur' },
          ]"
        >
          <el-input v-model="formData.integral_task_action_url" style="width: 80%" />
        </el-form-item>
        <el-form-item label="产品图标:" prop="icon">
          <div class="app-upload ">
            <el-upload
              class="avatar-uploader"
              :action="upload_url"
              :headers="headers"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="formData.icon" :src="formData.icon" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon" />
            </el-upload>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item>
      <el-button type="primary" size="small" @click="saveApp">保存</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { mapGetters } from 'vuex'
import { parent_apps } from '../../../api/app'
import { list } from '../../../api/category'
export default {
  name: 'AppForm',
  props: {
    type: {
      type: Boolean,
      default: () => false
    },
    form: {
      type: Object,
      default: () => {
        return {
          icon: '',
          type: 1,
          pid: 0,
          status: 1,
          level: 0,
          is_refund: 1,
          is_open_integral_task: 0
        }
      }
    }
  },
  data() {
    return {
      // dialogTableVisible: false,
      upload_url: process.env.VUE_APP_BASE_API + '/upload',
      headers: {
        Authorization: 'Bearer ' + this.token
      },
      apps: [],
      appCategory: [],
      formData: this.form,
      rules: {
        name: [
          { required: true, message: '请输产品名称', trigger: 'blur' }
        ],
        slogan: [
          { required: true, message: '请输产品一句话介绍', trigger: 'blur' }
        ],
        introduce: [
          { required: true, message: '请填写产品介绍', trigger: 'blur' }
        ],
        icon: [
          { required: true, message: '请上传图标', trigger: 'blur' }
        ],
        introduce_url: [
          { required: true, message: '请填写产品介绍地址', trigger: 'blur' }
        ],
        doc_url: [
          { required: true, message: '请填写产品手册地址', trigger: 'blur' }
        ],
        entrance_url: [
          { required: true, message: '请填写产品控制台入口地址', trigger: 'blur' }
        ],
        notice_url: [
          { required: true, message: '请填写产品系统事件回调地址', trigger: 'blur' }
        ],
        order_url: [
          { required: true, message: '请填写产品订单事件回调地址', trigger: 'blur' }
        ],
        level: [
          { required: true, message: '排序不能为空且只能是数字', trigger: 'blur' }
        ],
        is_open_integral_task: [
          { required: true, message: '积分任务开关必须选择', trigger: 'change' }
        ]
      },
      options: [
        {
          label: '企业产品',
          value: 1
        },
        {
          label: '个人产品',
          value: 2
        },
        {
          label: '无限制',
          value: 3
        }
      ]
    }
  },

  computed: {
    ...mapGetters([
      'token'
    ])
  },
  watch: {
    form: {
      handler(newValue, oldValue) {
        this.formData = newValue
      }
    }
  },
  created() {
    this.getApps()
    this.getCategory()
  },
  methods: {
    getCategory() {
      list().then(response => {
        this.appCategory = response.data
      })
    },
    getApps() {
      parent_apps({ id: this.$route.query.id }).then(response => {
        this.apps = response.data
      })
    },
    saveApp() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('transfer', this.formData)
        } else {
          return false
        }
      })
    },
    handleAvatarSuccess(res, file) {
      this.formData.icon = res.data.url
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG && !isPNG) {
        this.$message.error('上传的图标只能是 JPG 或 PNG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传的图标大小不能超过 2MB!')
      }
      return (isJPG || isPNG) && isLt2M
    }
  }
}
</script>

<style>
    .app-upload .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .app-upload .avatar-uploader .el-upload:hover {
        border-color: #409EFF;
    }

    .app-upload .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 150px;
        height: 150px;
        line-height: 150px;
        text-align: center;
    }

    .app-upload .avatar {
        width: 150px;
        height: 150px;
        display: block;
    }
</style>
