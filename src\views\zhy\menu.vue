<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <!--<span>菜单管理</span>-->
        <el-button style="float: right; padding: 3px 0" type="text" @click="createMenu">新增菜单</el-button>
      </div>
      <el-table
        :data="tableData"
        style="width: 100%"
        row-key="id"
        highlight-current-row
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        :row-class-name="tableRowClassName"
      >
        <el-table-column
          label="ID"
          prop="id"
        />
        <el-table-column label="图标" prop="icon" />
        <el-table-column
          prop="statistic"
          label="名称"
          width="180"
        />
        <el-table-column
          prop="path"
          label="路径"
        />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-link :underline="false" type="primary" @click="editDialog(scope.row)">编辑</el-link>
            <el-popconfirm
              title="确定删除当前记录吗？"
              @onConfirm="deleteHandle(scope.row)"
            >
              <el-link slot="reference" :underline="false" type="danger">删除</el-link>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getTableMenus"
      />
    </el-card>
    <!--表单-->
    <el-dialog
      title="菜单管理"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-form
        v-if="dialogVisible"
        ref="ruleForm"
        :model="formData"
        :rules="rules"
        label-width="160px"
        class="demo-ruleForm"
      >
        <el-form-item label="父级菜单：">
          <el-col :span="20">
            <el-cascader
              v-model="formData.pid"
              placeholder="请选择"
              :options="rootMenus"
              :props="{
                label: 'statistic',
                value: 'id',
                checkStrictly: true,
                expandTrigger: 'hover',
                emitPath: false,
              }"
            />
            <!-- <el-select v-model="formData.pid" placeholder="请选择">
              <el-option label="顶级" :value="0" />
              <el-option
                v-for="item in rootMenus"
                :key="item.id"
                :label="item.statistic"
                :value="item.id"
              />
            </el-select> -->
          </el-col>
        </el-form-item>
        <el-form-item label="中文名称：" prop="statistic">
          <el-col :span="12">
            <el-input v-model="formData.statistic" />
          </el-col>
        </el-form-item>
        <el-form-item label="英文名称：" prop="name">
          <el-col :span="12">
            <el-input v-model="formData.name" />
          </el-col>
        </el-form-item>
        <el-form-item label="图标代码：" prop="icon">
          <el-col :span="12">
            <el-input v-model="formData.icon" />
          </el-col>
        </el-form-item>
        <el-form-item label="显示URL：" prop="path">
          <el-col :span="20">
            <el-input v-model="formData.path" />
          </el-col>
        </el-form-item>
        <el-form-item label="跳转URL：" prop="redirect">
          <el-col :span="20">
            <el-input v-model="formData.redirect" />
          </el-col>
        </el-form-item>
        <el-form-item label="组件类型：" prop="path_type">
          <el-col :span="20">
            <el-select v-model="formData.path_type" placeholder="请选择">
              <el-option label="内嵌" :value="1" />
              <el-option label="外链" :value="2" />
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="组件路径：" prop="component">
          <el-col :span="20">
            <el-input v-model="formData.component" />
          </el-col>
        </el-form-item>
        <el-form-item label="菜单可见范围：" prop="system">
          <el-col :span="20">
            <el-select v-model="formData.system">
              <el-option v-for="item in systems" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item v-if="formData.system === 0" label="选择公司：" prop="company_list">
          <el-col :span="20">
            <el-select
              v-model="formData.company_list"
              multiple
              filterable
              remote
              reserve-keyword
              placeholder="请输入关键词"
              :remote-method="remoteMethod"
              :loading="loading"
              value="id"
              label="title"
              style="width: 100%"
            >
              <el-option
                v-for="item in companyList"
                :key="item.id"
                :label="item.title"
                :value="item.id"
              >
                <span>{{ item.title }}</span>
                <span>{{ item.mobile }}</span>
              </el-option>
              <div style="text-align: center">
                <el-pagination layout="prev, next" :total="companyTotal" @current-change="handleCurrentChange" />
              </div>
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="提示类型：" prop="type">
          <el-col :span="20">
            <el-select v-model="formData.type">
              <el-option label="链接" value="url" />
              <el-option label="弹窗" value="popup" />
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item v-if="formData.type" :label="popupLabel" prop="popup">
          <el-col :span="10">
            <el-input v-model="formData.popup" />
          </el-col>
        </el-form-item>
        <el-form-item label="提示确定按钮跳转链接" prop="popup_success">
          <el-col :span="10">
            <el-input v-model="formData.popup_success" />
          </el-col>
        </el-form-item>
        <el-form-item label="提示取消按钮跳转链接" prop="popup_err">
          <el-col :span="10">
            <el-input v-model="formData.popup_err" />
          </el-col>
        </el-form-item>
        <el-form-item label="排序位置" prop="sort_id">
          <el-col :span="10">
            <el-input-number v-model="formData.sort_id" :min="0" :max="9999999" :step="1" />
          </el-col>
        </el-form-item>
        <el-form-item label="是否隐藏" prop="hidden">
          <el-col :span="10">
            <el-switch v-model="formData.hidden" :active-value="0" :inactive-value="1" />
          </el-col>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="saveHandle">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getTreeTableMenus, createMenu, updateMenu, deleteMenu, getCompanyList } from '../../api/zhymenu'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
const defaultForm = {
  pid: 0,
  name: '', // 菜单英文名称
  statistic: '', // 中文名称
  icon: '', // 图标代码
  type: '', // 跳转类型：url：链接，popup：弹窗
  path: '', // 显示URL
  redirect: '', // 跳转URL
  popup: '', // 提示或跳转地址
  popup_success: '', // 提示或跳转操作
  popup_err: '', // 提示或跳转取消操作
  sort_id: 0, // 排序位置
  path_type: 1, // 跳转类型
  component: '', // 组件路径
  company_list: '' // 选择公司
}
export default {
  components: { Pagination },
  data() {
    return {
      loading: false,
      listQuery: {
        page: 1,
        limit: 10
      },
      total: 0,
      rootMenus: [],
      dialogVisible: false,
      formData: {
        pid: 0,
        name: '', // 菜单英文名称
        statistic: '', // 中文名称
        icon: '', // 图标代码
        type: '', // 跳转类型：url：链接，popup：弹窗
        path: '', // 显示URL
        redirect: '', // 跳转URL
        popup: '', // 提示或跳转地址
        popup_success: '', // 提示或跳转操作
        popup_err: '', // 提示或跳转取消操作
        sort_id: 0, // 排序位置
        system: 1, // 菜单可见范围
        company_list: '' // 选择公司
      },
      tableData: [],
      rules: {
        name: [
          { required: true, message: '缺少菜单英文名称', trigger: 'blur' }
        ],
        statistic: [
          { required: true, message: '缺少菜单中文名称', trigger: 'blur' }
        ],
        path: [
          { required: true, message: '显示URL必填', trigger: 'blur' }
        ],
        component: [
          { required: true, message: '组件路径必填', trigger: 'blur' }
        ],
        system: [
          { required: true, message: '菜单可见范围必填', trigger: 'blur' }
        ],
        company_list: [
          { required: true, message: '选择公司必填', trigger: 'change' }
        ]
      },
      systems: [
        { label: '所有公司可见', value: 1 },
        { label: '仅所选公司可见', value: 0 }
      ],
      companyList: [],
      companyPage: {
        page: 1,
        limit: 50,
        name: ''
      },
      companyloading: false,
      companyTotal: 0
    }
  },
  computed: {
    popupLabel() {
      return this.formData.type === 'popup' ? '提示内容' : '跳转地址'
    }
  },
  watch: {
    dialogVisible: {
      handler(val) {
        if (!val) {
          this.formData = JSON.parse(JSON.stringify(defaultForm))
          this.companyPage.page = 1
          this.companyPage.name = ''
        }
      }
    }
  },
  created() {
    this.getTableMenus()
    this.getCompanyList()
  },
  methods: {
    getTableMenus() {
      getTreeTableMenus(this.listQuery).then(response => {
        console.log(response, 'response')
        if (response.code === 200 && response.data) {
          this.tableData = response.data
          this.rootMenus = response.data
        }
      })
    },
    createMenu() {
      this.dialogVisible = true
    },
    editDialog(row) {
      row.company_list = row.company_id
      this.formData = JSON.parse(JSON.stringify(row))
      this.dialogVisible = true
    },
    saveHandle() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.loading = true
          const data = JSON.parse(JSON.stringify(this.formData))
          data.company_list = data.company_list ? data.company_list.join(',') : ''
          if (this.formData.id) {
            // eslint-disable-next-line valid-typeof
            if (typeof data.pid === 'array') {
              data.pid = data.pid[0]
            }
            updateMenu(data).then(response => {
              if (response.code === 200) {
                this.dialogVisible = false
                this.loading = false
                this.getTableMenus()
              }
            })
          } else {
            createMenu(data).then(response => {
              if (response.code === 200) {
                this.dialogVisible = false
                this.loading = false
                this.getTableMenus()
              }
            }).catch(error => {
              console.log(error)
              this.loading = false
            })
          }
        }
      })
    },
    deleteHandle(row) {
      deleteMenu(row).then(response => {
        if (response.code === 200) {
          this.getTableMenus()
        }
      })
    },
    tableRowClassName({ row, index }) {
      if (row.pid === 0) {
        return 'success-row'
      }
      return ''
    },
    /**
     * 筛选公司
     */
    remoteMethod(query) {
      if (query !== '') {
        this.companyloading = true
        this.companyPage.name = query
        this.getCompanyList()
      }
    },
    /**
     * 获取公司列表
     */
    async getCompanyList() {
      const res = await getCompanyList(this.companyPage)
      if (res.code === 200 && res.data && res.data.list) {
        this.companyList = Object.values(res.data.list)
        this.companyTotal = res.data.total
      }
    },
    handleCurrentChange(val) {
      this.companyPage.page = val
      this.getCompanyList()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-table .success-row {
  background: #f0f9eb;
}
</style>
<style scoped lang="scss">
.list-wrap {
  height: 100%;

  .box-card {
    border: none;
    height: 100%;
    ::v-deep .el-card__header{
      padding-top: 0;
    }
    ::v-deep .el-card__body {
      height: calc(100% - 59px);
      overflow: auto;
      padding-left: 0;
      padding-right: 0;
      padding-bottom: 0;
    }
  }
}
</style>
