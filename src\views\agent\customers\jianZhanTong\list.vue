<template>
  <div class="list-wrap">
    <el-card class="box-card" style="border: none" shadow="never">
      <div slot="header" class="clearfix">
        <!--<span>建站通用户列表</span>-->
        <el-button style="float: right; padding: 0" type="text" :loading="downloadLoading" @click="exportExcel">导出</el-button>
      </div>
      <el-form ref="form" :inline="true" :model="searchForm" label-width="12ss0px" size="small">
        <el-row>
          <el-col :span="24">
            <el-form-item label="用户名称">
              <el-input v-model="searchForm.customer" placeholder="请输入用户名称" clearable />
            </el-form-item>
            <el-form-item label="到期时间">
              <el-date-picker
                v-model="searchForm.date"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="dateChange"
              />
            </el-form-item>
            <el-form-item label="空间到期时间">
              <el-date-picker
                v-model="searchForm.kjdate"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="dateChange"
              />
            </el-form-item>
            <el-form-item label="素材空间到期时间">
              <el-date-picker
                v-model="searchForm.scdate"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="dateChange"
              />
            </el-form-item>
            <el-form-item label="ssl证书到期时间">
              <el-date-picker
                v-model="searchForm.ssldate"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="dateChange"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" icon="el-icon-search" @click="getList">搜索</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-divider />
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column
          prop="id"
          label="用户编号"
          width="80"
        />
        <el-table-column
          label="用户名称"
          prop="customer"
        />
        <el-table-column
          prop="agent"
          label="所属代理商"
        />
        <el-table-column
          label="建站通版本"
          prop="proname"
        />
        <el-table-column
          label="到期时间"
          prop="expire_end"
        />
        <el-table-column
          label="空间到期时间"
          prop="kjendtime"
        />
        <el-table-column
          label="ssl证书到期时间"
          prop="expire_sslend"
        />
        <el-table-column
          label="操作"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="showDetail(scope.row)">站点管理</el-button>
            <el-button type="text" @click="updateip(scope.row)">修改空间ip</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="searchForm.total>0"
        :total="searchForm.total"
        :page.sync="searchForm.page"
        :limit.sync="searchForm.perPage"
        style="text-align:center;"
        @pagination="getList"
      />
    </el-card>
    <el-dialog
      title="修改空间ip"
      :visible.sync="dialogUploadipVisible"
      width="600px"
      top="10vh"
    >
      <el-form ref="form" :model="ipform" label-width="120px" :rules="iprules">
        <el-form-item label="订单">
          <ul style="max-height: 300px;min-height: 50px;overflow:auto;padding: 0;margin: 0">
            <li v-for="item in ip_list" :key="item.id" style="list-style: none">
              <div>
                <span>{{ item.domain }}</span>
                <span>{{ item.name }}</span>
                <span>&nbsp;{{ item.created_at }}&nbsp;</span>
              </div>
            </li>
          </ul>
        </el-form-item>
        <el-form-item label="请选择主机ip" prop="ip" style="width: 70%">
          <el-select v-model="ipform.ip" placeholder="请选择" filterable clearable>
            <el-option v-for="product in jzthoustlists" :key="product.id" :label="product.name" :value="product.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogUploadipVisible = false">取 消</el-button>
        <el-button type="primary" :loading="dialogipLoading" @click="dialogUodateipConfirm">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="配套域名"
      :visible.sync="dialogFormVisible"
      width="600px"
      top="10vh"
    >
      <el-form ref="form" :model="form" label-width="120px" :rules="domrules">
        <el-form-item label="域名订单">
          <ul style="max-height: 300px;min-height: 50px;overflow:auto;padding: 0;margin: 0">
            <li v-for="item in history_list" :key="item.id" style="list-style: none">
              <div style="height: 23px">
                <span>{{ item.domain }}</span>
                <span>{{ item.name }}</span>
                <span>&nbsp;{{ item.created_at }}&nbsp;</span>
                <span v-if="item.status===0" style="color: deepskyblue">审核中</span>
                <span v-if="item.status===1" style="color: forestgreen">已通过</span>
                <span v-if="item.status===-1" style="color: red">已拒绝</span>
              </div>
            </li>
          </ul>
        </el-form-item>
        <el-form-item label="域名类型" prop="product_id" style="width: 70%">
          <el-select v-model="form.product_id" :disabled="isdomain" placeholder="请选择" filterable clearable>
            <el-option v-for="product in productsList" :key="product.id" :label="product.name" :value="product.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="配套域名" prop="domain" style="width: 70%">
          <el-input
            v-model="form.domain"
            :disabled="isdomain"
            placeholder="请输入域名名称"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" :disabled="isdomain" :loading="dialogLoading" @click="dialogConfirm">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="申请域名ssl"
      :visible.sync="dialogsslFormVisible"
      width="600px"
      top="10vh"
    >
      <el-form ref="form" :model="sslform" label-width="120px" :rules="rules">
        <el-form-item label="ssl申请">
          <ul style="max-height: 300px;min-height: 50px;overflow:auto;padding: 0;margin: 0">
            <li v-for="item in ssfiles_list" :key="item.id" style="list-style: none">
              <div style="height: 23px">
                <span>{{ item.domain }}</span>
                <span>{{ item.name }}</span>
                <span>&nbsp;{{ item.created_at }}&nbsp;</span>
                <span>&nbsp;{{ item.endtime }}&nbsp;</span>
                <span v-if="item.status===0" style="color: deepskyblue">申请中</span>
                <span v-if="item.status===1&&item.dostatus===0" style="color: forestgreen">已上传未下载</span>
                <span v-if="item.dostatus===1&&item.upstatus===0" style="color: forestgreen">已下载未上传</span>
                <span v-if="item.dostatus===1&&item.upstatus===1" style="color: forestgreen">已上传</span>
                <span v-if="item.status===-1" style="color: red">已拒绝</span>
              </div>
            </li>
          </ul>
        </el-form-item>
        <el-form-item label="申请域名" prop="domain" style="width: 70%">
          <el-input
            v-model="sslform.domain"
            placeholder="请输入申请域名"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogsslFormVisible = false">取 消</el-button>
        <el-button type="primary" :loading="dialogsslLoading" @click="dialogsslConfirm">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="上传文件"
      :visible.sync="dialogUploadsslVisible"
      width="600px"
      top="10vh"
    >
      <el-form ref="form" :model="uploadsslform" label-width="120px" :rules="rules">
        <el-form-item label="域名" prop="domain" style="width: 70%">
          <el-input
            v-model="uploadsslform.domain"
            placeholder="请输入域名名称"
          />
        </el-form-item>
        <el-form-item label="ssl证书：" prop="file">
          <div>{{ filename }}</div>
          <el-upload
            accept=""
            :file-list="fileList"
            :action="actionUrl"
            :data="info"
            :before-upload="beforeUploadFile"
            :on-exceed="exceedFile"
            :on-success="uploadSuccess"
            :show-file-list="isShowFile"
            multiple
            :limit="limitNum"
          >
            <el-button
              v-if="detailIndex === 0"
              type="text"
              icon="el-icon-upload2"
              size="mini"
            >上传</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogUploadsslVisible = false">取 消</el-button>
        <el-button type="primary" :loading="dialoguploadLoading" @click="dialoguploadsslConfirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import { beforeDomainList, beforeJztipList, products, jzthoustlists } from '@/api/agent/product'
import Pagination from '@/components/Pagination'
import moment from 'moment'
import { mapGetters } from 'vuex'
import { jianzhantongIndex, jianzhantongDestory, jianzhantongDomain, ipstore, sslstore, ssfileList, ssldown, sslupload } from '@/api/agent/otherProduct'
import { jztUserListExport } from '@/api/agent/downloads'
export default {
  components: { Pagination },
  data() {
    return {
      actionUrl: process.env.VUE_APP_BASE_API + '/auth/image', // 自己上传文件的地址
      info: {
        type: 'ssl'
      },
      detailIndex: 0,
      fileList: [], // 文件列表
      limitNum: 1, // 选择文件个数
      isShowFile: false,
      rules: {
        file: [
          { required: true, message: '请上传文件', trigger: 'blur' }
        ]
      },
      iprules: {
        ip: [
          { required: true, message: '选择主机ip', trigger: 'blur' }
        ]
      },
      uploadSuccessFiles: [],
      dialogFormVisible: false,
      dialogUploadsslVisible: false,
      dialogUploadipVisible: false,
      dialogsslFormVisible: false,
      tableLoading: false,
      dialogLoading: false,
      dialogsslLoading: false,
      dialogipLoading: false,
      dialoguploadLoading: false,
      isdomain: true,
      form: {},
      sslform: {},
      uploadsslform: {},
      ipform: {},
      filename: '',
      searchForm: {
        page: 1,
        perPage: 10,
        total: 0
      },
      domrules: {
        domain: [
          { required: true, message: '请输入域名名称,不要带后缀', trigger: 'blur' }
        ],
        product_id: [
          { required: true, message: '请选择域名类型', trigger: 'blur' }
        ]
      },
      productsList: [],
      jzthoustlists: [],
      tableData: [],
      history_list: [],
      ip_list: [],
      ssfiles_list: [],
      form_loading: false,
      downloadLoading: false
    }
  },
  computed: {
    // 别名处理防止冲突
    ...mapGetters({
      userType: 'type',
      username: 'name'
    })
  },
  created() {
    this.yun_api = process.env.VUE_APP_YUN_API + '/api/upload/image'
    if (this.$route.query) {
      if (parseInt(this.$route.query['page']) > -2) {
        this.searchForm.page = parseInt(this.$route.query['page'])
      }
    }
    this.jzthoustlist()
    this.getList()
  },
  methods: {
    getList() {
      this.tableLoading = true
      jianzhantongIndex(this.searchForm).then(response => {
        if (response.code === 200 && response.data) {
          this.tableData = response.data.list
          this.searchForm.total = response.data.meta.total
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    // 获取产品列表
    getProducts(category_id, product_id) {
      this.productsList = []
      products({ all: true, category_id: category_id }).then(response => {
        if (response.data) {
          var list = []
          var productsList = []
          list = response.data.list
          list.forEach(function(it) {
            if (it.id === product_id) {
              productsList = it.list
              console.log(it.list)
            }
          })
          this.productsList = productsList
        }
      })
    },
    showDetail(row) {
      this.$router.push({
        name: 'agentCustomersJztDetail',
        query: {
          finance_id: row.finance_id,
          id: row.id
        }
      })
    },
    getBeforeList() {
      this.dialogLoading = true
      beforeDomainList(this.form).then(response => {
        console.log(response.data)
        if (response.code === 200 && response.data) {
          this.history_list = response.data
        }
      }).catch(() => {
        this.dialogLoading = false
      }).finally(() => {
        this.dialogLoading = false
      })
    },
    getjianzhantongDomain() {
      this.dialogLoading = true
      jianzhantongDomain(this.form).then(response => {
        console.log(response.data)
        if (response.data.domain) {
          this.form.domain = response.data.domain
          this.isdomain = true
          this.$message.error('订单已配套域名！')
        } else {
          this.isdomain = false
        }
        if (response.data.product_id) {
          this.getProducts(1, response.data.product_id)
        }
        this.dialogLoading = false
      }).catch(() => {
        // this.$message.error('未找到建站通订单！ ')
        this.isdomain = true
        this.dialogLoading = false
      })
    },
    showDialog(row) {
      this.form = {
        id: row.finance_id,
        domain: '',
        product_id: ''
      }
      this.getBeforeList()
      this.getjianzhantongDomain()
      this.dialogFormVisible = true
    },
    // 模板列表
    jzthoustlist() {
      jzthoustlists({ id: 201 }).then(res => {
        if (res.code === 200 && res.data) {
          this.jzthoustlists = res.data
        }
      })
    },
    updateip(row) {
      this.ipform = {
        id: row.finance_id,
        ip: row.guid
      }
      this.getjztipList()
      this.dialogUploadipVisible = true
    },
    getjztipList() {
      beforeJztipList(this.ipform).then(response => {
        if (response.code === 200 && response.data) {
          this.ip_list = response.data
        }
      }).catch(() => {
      })
    },
    dialogUodateipConfirm() {
      console.log('ipform', this.ipform)
      this.dialogipLoading = true
      ipstore(this.ipform).then(response => {
        if (response.code === 200) {
          this.dialogipLoading = false
          this.$message.success('修改成功')
          this.dialogUploadipVisible = false
        }
      }).catch(() => {
        this.dialogipLoading = false
      })
    },
    dialoguploadsslConfirm() {
      console.log('sslform', this.sslform)
      this.dialoguploadLoading = true
      sslupload(this.uploadsslform).then(response => {
        if (response.code === 200) {
          this.dialoguploadLoading = false
          this.$message.success('上传成功')
          this.dialogUploadsslVisible = false
        }
      }).catch(() => {
        this.dialoguploadLoading = false
      })
    },
    // 文件上传之前的钩子
    beforeUploadFile(file) {
      // var FileExt = file.name.replace(/.+\./, '')
      // if (["i"].indexOf(FileExt.toLowerCase()) === -1) {
      //    this.$message({
      //      type: "warning",
      //      message: "请上传后缀名为xls、xlsx的附件！"
      //    });
      //    return false;
      // }
    },
    // 文件超出个数时的钩子
    exceedFile(files, fileList) {
      console.log('文件超出个数：', files)
      this.$message.warning(`只能选择 ${this.limitNum} 个文件，当前共选择了 ${files.length +
              fileList.length} 个`)
    },
    // 文件上传成功的钩子
    uploadSuccess(response, file, fileList) {
      if (response.code !== 200) {
        return this.$message.error('上传失败！')
      } else {
        this.uploadsslform['file'] = response.data.id
        this.filename = response.data.name
        console.log(this.uploadsslformss)
        // 上传成功之后在这里写逻辑，比如：重新调用查询列表接口
        return this.$message.success('上传成功！')
      }
    },
    dialogsslConfirm() {
      console.log('sslform', this.sslform)
      this.dialogsslLoading = true
      sslstore(this.sslform).then(response => {
        if (response.code === 200) {
          this.dialogsslLoading = false
          this.$message.success('提交成功')
          this.dialogsslFormVisible = false
        }
      }).catch(() => {
        this.dialogsslLoading = false
      })
    },
    dialogConfirm() {
      this.dialogLoading = true
      jianzhantongDestory(this.form).then(response => {
        if (response.code === 200) {
          this.dialogLoading = false
          this.$message.success('配套成功')
          this.dialogFormVisible = false
          this.getList()
        }
      }).catch(() => {
        this.dialogLoading = false
      })
    },
    async exportExcel() {
      try {
        this.downloadLoading = true
        const params = {
          date: this.searchForm.date,
          kjdate: this.searchForm.kjdate,
          ssldate: this.searchForm.ssldate,
          agent: this.searchForm.agent,
          agent_exact: this.searchForm.agent_exact,
          customer: this.searchForm.customer
        }
        const res = await jztUserListExport(params)
        if (res.code === 200) {
          this.$message.success('导出成功，请到下载中心下载')
        } else {
          this.$message.error(res.msg || '导出失败')
        }
      } catch (error) {
        this.$message.error(error.message || '导出失败')
      } finally {
        this.downloadLoading = false
      }
    },
    dateChange() {
      if (this.searchForm.date) {
        this.searchForm.date[0] = moment(this.searchForm.date[0]).format('YYYY-MM-DD H:m:s')
        this.searchForm.date[1] = moment(this.searchForm.date[1]).format('YYYY-MM-DD H:m:s')
      }
      if (this.searchForm.kjdate) {
        this.searchForm.kjdate[0] = moment(this.searchForm.kjdate[0]).format('YYYY-MM-DD H:m:s')
        this.searchForm.kjdate[1] = moment(this.searchForm.kjdate[1]).format('YYYY-MM-DD H:m:s')
      }
      if (this.searchForm.ssldate) {
        this.searchForm.ssldate[0] = moment(this.searchForm.ssldate[0]).format('YYYY-MM-DD H:m:s')
        this.searchForm.ssldate[1] = moment(this.searchForm.ssldate[1]).format('YYYY-MM-DD H:m:s')
      }
    }
  }

}
</script>

<style scoped>

</style>
