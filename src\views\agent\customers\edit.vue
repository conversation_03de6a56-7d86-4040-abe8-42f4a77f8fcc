<template>
  <div class="list-wrap">
    <el-card class="box-card" shadow="never" style="border: none">
      <div slot="header" class="clearfix">
        <!--<span>编辑客户</span>-->
        <el-button icon="el-icon-arrow-left" style="padding: 3px 0" type="text" @click="$router.go(-1)">返回</el-button>
        <el-button style="float: right; padding: 3px 0" type="text" @click="saveHandle">保存</el-button>
      </div>
      <customer-form ref="ruleForm" :rule-form.sync="ruleForm" />
    </el-card>
  </div>
</template>

<script>
import CustomerForm from './form'
import { update, detail } from '@/api/agent/customers'
export default {
  components: { CustomerForm },
  data() {
    return {
      ruleForm: {
        type: ['1', '2']
      }
    }
  },
  created() {
    this.getDetail()
  },
  methods: {
    getDetail() {
      detail({ id: this.$route.query.id, edit: true }).then(response => {
        response.data.legal_type = response.data.legal_type + ''
        this.ruleForm = response.data
      })
    },
    saveHandle() {
      this.$refs['ruleForm'].$refs['ruleForm'].validate((valida) => {
        if (valida) {
          this.$confirm('更新后信息将进入待审核状态, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            update(this.ruleForm).then(response => {
              if (response.code === 200) {
                this.$message.success('更新成功，请等待审核')
                this.$router.push({
                  name: 'CustomersListRouter'
                })
              }
            })
          }).catch(() => {})
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
