<template>
  <div class="app-container" style="height: 100%; padding-bottom: 0;">
    <div class="list-wrap integral">
      <el-table
          v-loading="loading"
          :data="tableData"
          stripe
          style="width: 100%"
          :default-sort="{ prop: 'created_at', order: 'descending' }"
          height="calc(100% - 96px)"
      >
        <el-table-column label="ID" prop="id" width="80" />
        <el-table-column label="商品名称" prop="title" />
        <el-table-column label="购买链接" prop="url" />
        <el-table-column label="商品图片" width="200">
          <template slot-scope="scope">
            <el-row v-if="scope.row.comimgurllist.length > 0" :gutter="50">
              <el-col
                  v-for="item in scope.row.comimgurllist"
                  :key="item"
                  :span="3"
              >
                <div>
                  <el-image
                      style="width: 50px;height: 50px"
                      :src="item"
                      :preview-src-list="scope.row.comimgurllist"
                      fit="fill"
                  />
                </div>
              </el-col>
            </el-row>
          </template>
        </el-table-column>
        <el-table-column label="推荐理由" prop="reason" />
        <el-table-column label="创建时间" prop="created_at" />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-divider direction="vertical" />
            <el-popconfirm
                title="确定删除吗并且无法恢复？"
                @onConfirm="destroy(scope.row)"
            >
              <el-button slot="reference" type="text">删除</el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="query.page"
          :limit.sync="query.limit"
          @pagination="getList"
      />
    </div>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { fetchList, store, remove } from '@/api/collects'
import { mapGetters } from 'vuex'

export default {
  name: 'Personal',
  components: { Pagination },
  data() {
    return {
      total: 0,
      query: {
        page: 1,
        limit: 10,
        name: null,
        number: null,
        status: null
      },
      options: [],
      tableData: [],
      loading: true,
      form: {
        reply: ''
      },
      rules: {
        reply: [
          {
            required: false,
            message: '规则描述不能为空',
            max: 100,
            trigger: 'blur'
          }
        ]
      },
      dialogFormVisible: false,
      formLabelWidth: '150px',
      title: '新增规则',
      circulation_disabled: false,
      upload_url: process.env.VUE_APP_BASE_API + '/uploadReturnId',
      fileList: []
    }
  },
  computed: {
    ...mapGetters(['token'])
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      fetchList(this.query)
        .then(response => {
          this.total = response.data.total
          this.tableData = response.data.data
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    handelUnit() {
      if (this.form.unit === 'forever') {
        this.form.circulation = 0
        this.circulation_disabled = true
      } else {
        if (this.form.circulation <= 0) {
          this.form.circulation = 1
        }
        this.circulation_disabled = false
      }
    },
    handelSwitch(row) {
      store(row)
    },
    handelCreate() {
      this.title = '新增规则'
      delete this.form.id
      this.dialogFormVisible = true
      this.fileList = []
    },
    handelEdit(row) {
      this.title = '回复'
      this.fileList = []
      this.form = row
      this.dialogFormVisible = true
      if (this.form.icon) {
        this.form.icon.name = this.form.icon.url.split('/').pop()
        this.fileList.push(this.form.icon)
      }
    },
    beforeIconUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG && !isPNG) {
        this.$message.error('上传的图标只能是 JPG 或 PNG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传的图片大小不能超过 2MB!')
      }
      return (isJPG || isPNG) && isLt2M
    },
    handleIconSuccess(res, file) {
      if (res && res.code === 200) {
        this.form.att_id = res.data[0]
      }
    },
    handleIconRemove(file, fileList) {
      this.form.att_id = null
    },
    submit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          store(this.form).then(_ => {
            this.dialogFormVisible = false
            this.getList()
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    destroy(row) {
      remove({ id: row.id }).then(_ => {
        this.getList()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.card-wrap ::v-deep .el-card__body {
  height: 100%;
}
.list-wrap {
  height: 100%;

  ::v-deep .card-wrap {
    height: 100%;
    border: none;

    .el-card__body {
      height: 100%;
    }

    .box-card {
      height: 100%;
      border: none;

      .el-card__body {
        height: calc(100% - 59px);
        overflow: auto;
      }
    }
  }
}
</style>
<style scoped lang="scss">
.integral {
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    &:hover {
      border-color: #409eff;
    }
  }
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
